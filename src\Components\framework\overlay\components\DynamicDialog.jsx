import React, { useState, useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Dialog } from 'primereact/dialog';
import DynamicComponent from '../../core/DynamicComponent';

const DynamicDialog = ({
  visible = false,
  onHide,
  onShow,
  onMaskClick,
  header,
  headerTemplate,
  footerTemplate,
  modal = true,
  resizable = true,
  draggable = true,
  closable = true,
  dismissableMask = false,
  closeOnEscape = true,
  showHeader = true,
  baseZIndex = 0,
  autoZIndex = true,
  position = 'center', 
  maximizable = false,
  maximized = false,
  onMaximize,
  onMinimize,
  blockScroll = false,
  keepInViewport = true,
  minX = 0,
  minY = 0,
  className = '',
  style = {},
  contentClassName = '',
  contentStyle = {},
  headerClassName = '',
  headerStyle = {},
  footerClassName = '',
  footerStyle = {},
  maskClassName = '',
  maskStyle = {},
  id,
  children,
  content = [],
  config = {},
  size = 'normal', 
  variant = 'default',
  actions = [],
  onEvent,
  debug = false,
  ...props
}) => {
  const [isVisible, setIsVisible] = useState(visible);
  const [isMaximized, setIsMaximized] = useState(maximized);

  useEffect(() => {
    setIsVisible(visible);
  }, [visible]);

  const handleHide = useCallback(() => {
    setIsVisible(false);

    if (debug) {
      console.log('DynamicDialog hide');
    }

    if (onHide) {
      onHide();
    }

    if (onEvent) {
      onEvent('hide', { visible: false });
    }
  }, [onHide, onEvent, debug]);

  const handleShow = useCallback(() => {
    if (debug) {
      console.log('DynamicDialog show');
    }

    if (onShow) {
      onShow();
    }

    if (onEvent) {
      onEvent('show', { visible: true });
    }
  }, [onShow, onEvent, debug]);

  const handleMaskClick = useCallback((e) => {
    if (onMaskClick) {
      onMaskClick(e);
    }

    if (onEvent) {
      onEvent('maskClick', { event: e });
    }
  }, [onMaskClick, onEvent]);

  const handleMaximize = useCallback((e) => {
    const newMaximized = e.maximized;
    setIsMaximized(newMaximized);

    if (newMaximized) {
      if (onMaximize) {
        onMaximize(e);
      }
      if (onEvent) {
        onEvent('maximize', { maximized: true, event: e });
      }
    } else {
      if (onMinimize) {
        onMinimize(e);
      }
      if (onEvent) {
        onEvent('minimize', { maximized: false, event: e });
      }
    }
  }, [onMaximize, onMinimize, onEvent]);

  const dialogClasses = [
    'dynamic-dialog',
    `dynamic-dialog--${size}`,
    `dynamic-dialog--${variant}`,
    `dynamic-dialog--${position}`,
    isMaximized ? 'dynamic-dialog--maximized' : '',
    className
  ].filter(Boolean).join(' ');

  const getDialogStyle = () => {
    const baseStyle = { ...style };
    
    switch (size) {
      case 'small':
        return { ...baseStyle, width: '30vw', minWidth: '300px' };
      case 'large':
        return { ...baseStyle, width: '70vw', minWidth: '600px' };
      case 'fullscreen':
        return { ...baseStyle, width: '100vw', height: '100vh' };
      default:
        return { ...baseStyle, width: '50vw', minWidth: '400px' };
    }
  };

  const renderHeaderTemplate = useCallback((options) => {
    if (headerTemplate && typeof headerTemplate === 'function') {
      return headerTemplate(options);
    }

    return (
      <div className="dialog-header-content">
        {typeof header === 'string' ? (
          <span className="dialog-title">{header}</span>
        ) : (
          header
        )}
        <div className="dialog-header-actions">
          {maximizable && (
            <button
              type="button"
              className="dialog-maximize-button"
              onClick={options.onMaximize}
              title={isMaximized ? 'Restore' : 'Maximize'}
            >
              <i className={`pi ${isMaximized ? 'pi-window-minimize' : 'pi-window-maximize'}`} />
            </button>
          )}
          {closable && (
            <button
              type="button"
              className="dialog-close-button"
              onClick={options.onClose}
              title="Close"
            >
              <i className="pi pi-times" />
            </button>
          )}
        </div>
      </div>
    );
  }, [headerTemplate, header, maximizable, closable, isMaximized]);

  const renderFooterTemplate = useCallback(() => {
    if (footerTemplate && typeof footerTemplate === 'function') {
      return footerTemplate();
    }

    if (actions && actions.length > 0) {
      return (
        <div className="dialog-actions">
          {actions.map((action, index) => (
            <DynamicComponent
              key={index}
              type="button"
              config={{
                label: action.label,
                icon: action.icon,
                severity: action.severity || 'secondary',
                size: 'small',
                onClick: () => {
                  if (action.onClick) {
                    action.onClick();
                  }
                  if (action.closeDialog) {
                    handleHide();
                  }
                },
                disabled: action.disabled,
                className: `dialog-action ${action.className || ''}`
              }}
              onEvent={onEvent}
              debug={debug}
            />
          ))}
        </div>
      );
    }

    if (config.footer) {
      return (
        <div className="dialog-footer-content">
          {config.footer.map((item, index) => (
            <DynamicComponent
              key={index}
              type={item.type}
              config={item.config}
              data={item.data}
              onEvent={onEvent}
              debug={debug}
            />
          ))}
        </div>
      );
    }

    return null;
  }, [footerTemplate, actions, config.footer, handleHide, onEvent, debug]);

  const renderContent = useCallback(() => {
    if (children) {
      return children;
    }

    if (Array.isArray(content) && content.length > 0) {
      return (
        <div className="dialog-dynamic-content">
          {content.map((item, index) => (
            <DynamicComponent
              key={index}
              type={item.type}
              config={item.config}
              data={item.data}
              onEvent={onEvent}
              debug={debug}
            />
          ))}
        </div>
      );
    }

    if (content && typeof content === 'object' && content.type) {
      return (
        <DynamicComponent
          type={content.type}
          config={content.config}
          data={content.data}
          onEvent={onEvent}
          debug={debug}
        />
      );
    }

    return null;
  }, [children, content, onEvent, debug]);

  const dialogProps = {
    id,
    visible: isVisible,
    onHide: handleHide,
    onShow: handleShow,
    onMaskClick: handleMaskClick,
    header: showHeader ? (typeof header === 'string' ? header : null) : null,
    headerTemplate: showHeader ? renderHeaderTemplate : null,
    footer: renderFooterTemplate(),
    modal,
    resizable: size !== 'fullscreen' && resizable,
    draggable: size !== 'fullscreen' && draggable,
    closable,
    dismissableMask,
    closeOnEscape,
    showHeader,
    baseZIndex,
    autoZIndex,
    position: size === 'fullscreen' ? 'center' : position,
    maximizable: size !== 'fullscreen' && maximizable,
    maximized: isMaximized,
    onMaximize: handleMaximize,
    blockScroll,
    keepInViewport: size !== 'fullscreen' && keepInViewport,
    minX,
    minY,
    className: dialogClasses,
    style: getDialogStyle(),
    contentClassName,
    contentStyle,
    headerClassName,
    headerStyle,
    footerClassName,
    footerStyle,
    maskClassName,
    maskStyle,
    'data-component-type': 'dialog',
    'data-dialog-size': size,
    'data-dialog-variant': variant,
    ...props
  };

  Object.keys(dialogProps).forEach(key => {
    if (dialogProps[key] === undefined) {
      delete dialogProps[key];
    }
  });

  return (
    <>
      {debug && isVisible && (
        <div className="dynamic-dialog__debug" style={{ position: 'fixed', top: '10px', right: '10px', zIndex: 9999 }}>
          <small style={{ background: 'rgba(0,0,0,0.8)', color: 'white', padding: '4px 8px', borderRadius: '4px' }}>
            Size: {size} | 
            Position: {position} | 
            Maximized: {isMaximized ? 'Yes' : 'No'} | 
            Actions: {actions.length}
          </small>
        </div>
      )}
      <Dialog {...dialogProps}>
        {renderContent()}
      </Dialog>
    </>
  );
};

DynamicDialog.propTypes = {
  visible: PropTypes.bool,
  onHide: PropTypes.func,
  onShow: PropTypes.func,
  onMaskClick: PropTypes.func,
  header: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  headerTemplate: PropTypes.func,
  footerTemplate: PropTypes.func,
  modal: PropTypes.bool,
  resizable: PropTypes.bool,
  draggable: PropTypes.bool,
  closable: PropTypes.bool,
  dismissableMask: PropTypes.bool,
  closeOnEscape: PropTypes.bool,
  showHeader: PropTypes.bool,
  baseZIndex: PropTypes.number,
  autoZIndex: PropTypes.bool,
  position: PropTypes.oneOf(['center', 'top', 'bottom', 'left', 'right', 'top-left', 'top-right', 'bottom-left', 'bottom-right']),
  maximizable: PropTypes.bool,
  maximized: PropTypes.bool,
  onMaximize: PropTypes.func,
  onMinimize: PropTypes.func,
  blockScroll: PropTypes.bool,
  keepInViewport: PropTypes.bool,
  minX: PropTypes.number,
  minY: PropTypes.number,
  className: PropTypes.string,
  style: PropTypes.object,
  contentClassName: PropTypes.string,
  contentStyle: PropTypes.object,
  headerClassName: PropTypes.string,
  headerStyle: PropTypes.object,
  footerClassName: PropTypes.string,
  footerStyle: PropTypes.object,
  maskClassName: PropTypes.string,
  maskStyle: PropTypes.object,
  id: PropTypes.string,
  children: PropTypes.node,
  content: PropTypes.oneOfType([
    PropTypes.array,
    PropTypes.object
  ]),
  config: PropTypes.object,
  size: PropTypes.oneOf(['small', 'normal', 'large', 'fullscreen']),
  variant: PropTypes.oneOf(['default', 'primary', 'secondary', 'success', 'info', 'warning', 'danger']),
  actions: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string,
    icon: PropTypes.string,
    severity: PropTypes.string,
    onClick: PropTypes.func,
    closeDialog: PropTypes.bool,
    disabled: PropTypes.bool,
    className: PropTypes.string
  })),
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicDialog;
