

class SecureLogger {
  constructor() {
    this.sensitivePatterns = [
      /password[=:]\s*[^\s&]+/gi,
      /token[=:]\s*[^\s&]+/gi,
      /key[=:]\s*[^\s&]+/gi,
      /secret[=:]\s*[^\s&]+/gi,
      /authorization:\s*[^\s&]+/gi,
      /bearer\s+[^\s&]+/gi,
      /\b\d{4}[-\s]?\d{4}[-\s]?\d{4}[-\s]?\d{4}\b/g, // card number
      /\b\d{3}-\d{2}-\d{4}\b/g, // ssn
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g // email
    ];

    this.isBrowser = typeof window !== 'undefined';
    this.isProduction = import.meta.env?.MODE === 'production' || false;
    this.logLevel = import.meta.env?.VITE_LOG_LEVEL || 'info';

    this.levels = {
      error: 0,
      warn: 1,
      info: 2,
      debug: 3
    };

    if (this.isBrowser) {
      this.initBrowserStorage();
    }
  }

  initBrowserStorage() {
    try {
      if (!sessionStorage.getItem('app_logs')) {
        sessionStorage.setItem('app_logs', JSON.stringify([]));
      }
      if (!sessionStorage.getItem('security_logs')) {
        sessionStorage.setItem('security_logs', JSON.stringify([]));
      }
    } catch (error) {
      console.warn('Logger: Storage not available, using console only');
    }
  }

  shouldLog(level) {
    const currentLevelValue = this.levels[this.logLevel] || this.levels.info;
    const messageLevelValue = this.levels[level] || 0;
    return messageLevelValue <= currentLevelValue;
  }

  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const sanitizedMessage = this.sanitizeMessage(message);
    const sanitizedMeta = this.sanitizeMeta(meta);

    return {
      timestamp,
      level,
      message: sanitizedMessage,
      service: 'dynamic-framework',
      environment: this.isProduction ? 'production' : 'development',
      ...sanitizedMeta
    };
  }

  logToConsole(level, formattedMessage) {
    const { timestamp, message, ...meta } = formattedMessage;
    const hasMetadata = Object.keys(meta).length > 0;

    const consoleMethod = console[level] || console.log;

    if (hasMetadata) {
      consoleMethod(`[${timestamp}] ${level.toUpperCase()}: ${message}`, meta);
    } else {
      consoleMethod(`[${timestamp}] ${level.toUpperCase()}: ${message}`);
    }
  }

  logToStorage(storageKey, formattedMessage) {
    if (!this.isBrowser) return;

    try {
      const logs = JSON.parse(sessionStorage.getItem(storageKey) || '[]');
      logs.push(formattedMessage);

      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }

      sessionStorage.setItem(storageKey, JSON.stringify(logs));
    } catch (error) {
    }
  }

  log(level, message, meta = {}) {
    if (!this.shouldLog(level)) return;

    const formattedMessage = this.formatMessage(level, message, meta);

    this.logToConsole(level, formattedMessage);

    this.logToStorage('app_logs', formattedMessage);
  }

  sanitizeMessage(message) {
    if (typeof message !== 'string') {
      return message;
    }

    let sanitized = message;
    this.sensitivePatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '[REDACTED]');
    });

    return sanitized;
  }

  sanitizeMeta(meta) {
    if (!meta || typeof meta !== 'object') {
      return meta;
    }

    const sanitized = {};
    for (const [key, value] of Object.entries(meta)) {
      if (typeof value === 'string') {
        sanitized[key] = this.sanitizeMessage(value);
      } else if (typeof value === 'object' && value !== null) {
        sanitized[key] = this.sanitizeMeta(value);
      } else {
        sanitized[key] = value;
      }
    }

    return sanitized;
  }

  error(message, meta = {}) {
    this.log('error', message, {
      ...meta,
      stack: meta.stack || new Error().stack
    });
  }

  warn(message, meta = {}) {
    this.log('warn', message, meta);
  }

  info(message, meta = {}) {
    this.log('info', message, meta);
  }

  debug(message, meta = {}) {
    this.log('debug', message, meta);
  }

  security(event, details = {}) {
    const securityEvent = {
      event,
      timestamp: new Date().toISOString(),
      userAgent: this.isBrowser ? window.navigator?.userAgent : 'server',
      url: this.isBrowser ? window.location?.href : 'server',
      ...details
    };

    this.logToConsole('warn', this.formatMessage('warn', 'Security Event', securityEvent));

    this.logToStorage('security_logs', this.formatMessage('warn', 'Security Event', securityEvent));
  }

  xssAttempt(details) {
    this.security('xss_attempt', {
      severity: 'high',
      ...details
    });
  }

  csrfAttempt(details) {
    this.security('csrf_attempt', {
      severity: 'high',
      ...details
    });
  }

  rateLimitExceeded(details) {
    this.security('rate_limit_exceeded', {
      severity: 'medium',
      ...details
    });
  }

  suspiciousActivity(details) {
    this.security('suspicious_activity', {
      severity: 'medium',
      ...details
    });
  }

  authenticationFailure(details) {
    this.security('authentication_failure', {
      severity: 'medium',
      ...details
    });
  }

  dataExfiltrationAttempt(details) {
    this.security('data_exfiltration_attempt', {
      severity: 'critical',
      ...details
    });
  }

  performance(metric, value, meta = {}) {
    this.info('Performance Metric', {
      metric,
      value,
      timestamp: Date.now(),
      ...meta
    });
  }

  audit(action, details = {}) {
    this.info('Audit Event', {
      action,
      timestamp: new Date().toISOString(),
      ...details
    });
  }

  getLogs(type = 'app_logs') {
    if (!this.isBrowser) return [];

    try {
      return JSON.parse(sessionStorage.getItem(type) || '[]');
    } catch (error) {
      return [];
    }
  }

  clearLogs(type = 'app_logs') {
    if (!this.isBrowser) return;

    try {
      sessionStorage.setItem(type, JSON.stringify([]));
    } catch (error) {
    }
  }

  exportLogs(type = 'app_logs') {
    const logs = this.getLogs(type);
    const blob = new Blob([JSON.stringify(logs, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const a = document.createElement('a');
    a.href = url;
    a.download = `${type}_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }
}

const logger = new SecureLogger();

export { logger };
export default logger;
