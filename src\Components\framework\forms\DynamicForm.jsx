import React, { useState, useEffect, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Button } from 'primereact/button';
import { Message } from 'primereact/message';
import { ProgressBar } from 'primereact/progressbar';
import DynamicComponent from '../core/DynamicComponent';
import ConfigValidator from '../core/ConfigValidator';
import componentRegistry from '../core/ComponentRegistry';
import GlassyWhiteButton from '../../common/GlassyWhiteButton';
import GlassyBlueButton from '../../common/GlassyBlueButton';

const DynamicForm = ({
  config,
  initialData = {},
  onSubmit,
  onValidate,
  onChange,
  onError,
  className = '',
  style = {},
  disabled = false,
  loading = false,
  debug = false
}) => {
  const [formData, setFormData] = useState(initialData);
  const [errors, setErrors] = useState({});
  const [touched, setTouched] = useState({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitAttempted, setSubmitAttempted] = useState(false);

  const configValidation = useMemo(() => {
    if (!config) {
      return { isValid: false, errors: ['Form configuration is required'] };
    }

    const schema = {
      type: 'object',
      required: ['fields'],
      properties: {
        fields: {
          type: 'array',
          minItems: 1,
          items: {
            type: 'object',
            required: ['name', 'type'],
            properties: {
              name: { type: 'string' },
              type: { type: 'string' },
              label: { type: 'string' },
              required: { type: 'boolean' },
              validation: { type: 'object' },
              props: { type: 'object' }
            }
          }
        },
        layout: { type: 'object' },
        validation: { type: 'object' },
        submission: { type: 'object' }
      }
    };

    return ConfigValidator.validate(config, schema);
  }, [config]);

  const formProgress = useMemo(() => {
    if (!config?.fields) return 0;
    
    const totalFields = config.fields.length;
    const filledFields = config.fields.filter(field => {
      const value = formData[field.name];
      return value !== null && value !== undefined && value !== '';
    }).length;
    
    return totalFields > 0 ? (filledFields / totalFields) * 100 : 0;
  }, [config, formData]);

  const validateField = useCallback((fieldConfig, value) => {
    const fieldErrors = [];

    if (fieldConfig.required && (value === null || value === undefined || value === '')) {
      fieldErrors.push(`${fieldConfig.label || fieldConfig.name} is required`);
    }

    if (fieldConfig.validation && value !== null && value !== undefined && value !== '') {
      const validation = ConfigValidator.validate(value, fieldConfig.validation);
      if (!validation.isValid) {
        fieldErrors.push(...validation.errors);
      }
    }

    if (value !== null && value !== undefined && value !== '') {
      switch (fieldConfig.type) {
        case 'email':
          if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
            fieldErrors.push('Please enter a valid email address');
          }
          break;
        case 'url':
          if (!/^https?:\/\/.+/.test(value)) {
            fieldErrors.push('Please enter a valid URL');
          }
          break;
        case 'number':
          if (isNaN(Number(value))) {
            fieldErrors.push('Please enter a valid number');
          }
          break;
        case 'date':
          if (isNaN(Date.parse(value))) {
            fieldErrors.push('Please enter a valid date');
          }
          break;
      }
    }

    return fieldErrors;
  }, []);

  const validateForm = useCallback(() => {
    if (!config?.fields) return {};

    const formErrors = {};

    config.fields.forEach(field => {
      const fieldErrors = validateField(field, formData[field.name]);
      if (fieldErrors.length > 0) {
        formErrors[field.name] = fieldErrors;
      }
    });

    if (config.validation && typeof config.validation.validate === 'function') {
      try {
        const customValidation = config.validation.validate(formData);
        if (customValidation && typeof customValidation === 'object') {
          Object.assign(formErrors, customValidation);
        }
      } catch (error) {
        console.error('Custom form validation error:', error);
      }
    }

    return formErrors;
  }, [config, formData, validateField]);

  const handleFieldChange = useCallback((fieldName, value, event) => {
    const newFormData = { ...formData, [fieldName]: value };
    setFormData(newFormData);

    setTouched(prev => ({ ...prev, [fieldName]: true }));

    if (touched[fieldName] || submitAttempted) {
      const field = config?.fields?.find(f => f.name === fieldName);
      if (field) {
        const fieldErrors = validateField(field, value);
        setErrors(prev => ({
          ...prev,
          [fieldName]: fieldErrors.length > 0 ? fieldErrors : undefined
        }));
      }
    }

    if (onChange) {
      onChange(fieldName, value, newFormData, event);
    }

    if (onValidate) {
      const formErrors = validateForm();
      onValidate(formErrors, newFormData);
    }
  }, [formData, touched, submitAttempted, config, validateField, validateForm, onChange, onValidate]);

  const handleFieldBlur = useCallback((fieldName) => {
    setTouched(prev => ({ ...prev, [fieldName]: true }));

    const field = config?.fields?.find(f => f.name === fieldName);
    if (field) {
      const fieldErrors = validateField(field, formData[fieldName]);
      setErrors(prev => ({
        ...prev,
        [fieldName]: fieldErrors.length > 0 ? fieldErrors : undefined
      }));
    }
  }, [config, formData, validateField]);

  const handleSubmit = useCallback(async (event) => {
    event.preventDefault();
    setSubmitAttempted(true);

    const formErrors = validateForm();
    setErrors(formErrors);

    const hasErrors = Object.keys(formErrors).length > 0;

    if (hasErrors) {
      if (onError) {
        onError({
          type: 'validation',
          errors: formErrors,
          message: 'Please fix the validation errors'
        });
      }
      return;
    }

    if (!onSubmit) {
      console.warn('DynamicForm: No onSubmit handler provided');
      return;
    }

    setIsSubmitting(true);

    try {
      await onSubmit(formData, { config, errors: formErrors });
    } catch (error) {
      console.error('Form submission error:', error);
      if (onError) {
        onError({
          type: 'submission',
          error: error.message,
          message: 'Failed to submit form'
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, validateForm, onSubmit, onError, config]);

  const resetForm = useCallback(() => {
    setFormData(initialData);
    setErrors({});
    setTouched({});
    setSubmitAttempted(false);
    setIsSubmitting(false);
  }, [initialData]);

  useEffect(() => {
    setFormData(initialData);
  }, []);

  const renderField = useCallback((field, index) => {
    const fieldProps = {
      ...field.props,
      value: formData[field.name],
      onChange: (value, event) => handleFieldChange(field.name, value, event),
      onBlur: () => handleFieldBlur(field.name),
      disabled: disabled || field.disabled,
      className: `form-field form-field--${field.type} ${field.props?.className || ''}`,
      'data-field-name': field.name,
      'data-field-type': field.type
    };

    if (errors[field.name]) {
      fieldProps.className += ' form-field--error';
      fieldProps['aria-invalid'] = true;
      fieldProps['aria-describedby'] = `${field.name}-error`;
    }

    return (
      <div key={field.name} className={`form-group form-group--${field.type}`}>
        {field.label && (
          <label 
            htmlFor={field.name}
            className={`form-label ${field.required ? 'form-label--required' : ''}`}
          >
            {field.label}
            {field.required && <span className="form-label__required">*</span>}
          </label>
        )}
        
        <div className="form-field-wrapper">
          <DynamicComponent
            type={field.type}
            config={fieldProps}
            onError={(error) => {
              console.error(`Field ${field.name} error:`, error);
              if (onError) {
                onError({
                  type: 'field',
                  field: field.name,
                  error: error.message,
                  message: `Error in field ${field.label || field.name}`
                });
              }
            }}
            debug={debug}
          />
        </div>

        {errors[field.name] && (
          <div id={`${field.name}-error`} className="form-error">
            {errors[field.name].map((error, errorIndex) => (
              <Message 
                key={errorIndex}
                severity="error" 
                text={error}
                className="form-error__message"
              />
            ))}
          </div>
        )}

        {field.help && (
          <div className="form-help">
            <small>{field.help}</small>
          </div>
        )}
      </div>
    );
  }, [formData, errors, disabled, handleFieldChange, handleFieldBlur, onError, debug]);

  if (!configValidation.isValid) {
    return (
      <div className={`dynamic-form dynamic-form--error ${className}`} style={style}>
        <Message 
          severity="error" 
          text="Invalid form configuration"
          className="form-config-error"
        />
        {debug && (
          <ul className="form-config-errors">
            {configValidation.errors.map((error, index) => (
              <li key={index}>{error}</li>
            ))}
          </ul>
        )}
      </div>
    );
  }

  const formLayout = config.layout || {};
  const showProgress = formLayout.showProgress !== false;
  const showSubmitButton = formLayout.showSubmitButton !== false;
  const submitButtonText = formLayout.submitButtonText || 'Submit';

  return (
    <div className={`dynamic-form ${className}`} style={style}>
      {debug && (
        <div className="dynamic-form__debug">
          <small>
            Fields: {config.fields?.length || 0} | 
            Progress: {formProgress.toFixed(1)}% | 
            Errors: {Object.keys(errors).length}
          </small>
        </div>
      )}

      {showProgress && (
        <div className="form-progress">
          <ProgressBar 
            value={formProgress} 
            className="form-progress__bar"
            showValue={false}
          />
          <small className="form-progress__text">
            {Math.round(formProgress)}% complete
          </small>
        </div>
      )}

      <form onSubmit={handleSubmit} className="dynamic-form__form" noValidate>
        <div className={`form-fields form-fields--${formLayout.columns || 1}-column`}>
          {config.fields?.map(renderField)}
        </div>

        {showSubmitButton && (
          <div className="form-actions">
            <GlassyBlueButton
              type="submit"
              label={submitButtonText}
              loading={isSubmitting || loading}
              disabled={disabled}
              className="form-submit-button"
              icon="pi pi-check"
            />
            
            {formLayout.showResetButton && (
              <GlassyWhiteButton
                type="button"
                label="Reset"
                onClick={resetForm}
                disabled={disabled || isSubmitting || loading}
                className="form-reset-button"
                icon="pi pi-refresh"
                severity="secondary"
              />
            )}
          </div>
        )}
      </form>
    </div>
  );
};

DynamicForm.propTypes = {
  config: PropTypes.object.isRequired,
  initialData: PropTypes.object,
  onSubmit: PropTypes.func,
  onValidate: PropTypes.func,
  onChange: PropTypes.func,
  onError: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object,
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
  debug: PropTypes.bool
};

export default DynamicForm;
