import React, { useState, useContext, useEffect } from 'react';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { useNavigate } from 'react-router-dom';
import { useAuthContext } from '@contexts/AuthContext';
import authHeader from '@services/AuthHeader';
import { getAxiosInstance } from '@utils/loader/axiosInstance';
import '../../../../styles/glassy/glassy-ui.css';
import '../../../../styles/glassy/global.css';
import Paginator from '@components/common/Paginator';
import GlassyBlueButton from '@components/common/Buttons/GlassyBlueButton';
import GlassyWhiteButton from '@components/common/Buttons/GlassyWhiteButton';
import 'primeflex/primeflex.css'; // Import PrimeFlex CSS


const AddUser = () => {
    const navigate = useNavigate();
    const authContext = useAuthContext();
    const { user } = authContext;

    // Debug logging
    console.log('Auth Context:', authContext);
    console.log('User object:', user);

    const isAdmin = authContext.isAsmAdmin?.() || user?.role === 'admin';

    // State declarations
    const [selectedUsers, setSelectedUsers] = useState([]);
    const [department, setDepartment] = useState(null);
    const [searchTerm, setSearchTerm] = useState('');
    const [defaultRole, setDefaultRole] = useState(null);
    const [azureUsers, setAzureUsers] = useState([]);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);
    const [paginatedUsers, setPaginatedUsers] = useState([]);

    // Pagination config
    const itemsPerPage = 10;
    
    const onPageChange = (page, paginatedData) => {
        console.log("Page changed to:", page, "Data:", paginatedData);
        setPaginatedUsers(paginatedData);
    };

    // Fetch Azure AD users
    useEffect(() => {
        const fetchAzureUsers = async () => {
            try {
                setLoading(true);

                const axios = getAxiosInstance();
                const headers = {};

                // Add authorization header using the same method as other services
                const token = authHeader();
                if (token) {
                    headers['Authorization'] = `Bearer ${token}`;
                }

                const response = await axios.get(
                    '/api/azure-test/all-users',
                    { headers }
                );

                if (response.data.isSuccess) {
                    setAzureUsers(
                        response.data.data.users.map(user => ({
                            id: user.id,
                            name: user.displayName,
                            email: user.email,
                            department: user.department || 'Not Assigned',
                            role: null,
                            status: 'Pending',
                            givenName: user.givenName,
                            surname: user.surname,
                            jobTitle: user.jobTitle || 'Not Assigned'
                        }))
                    );
                } else {
                    setError('Failed to fetch Azure users');
                }
            } catch (err) {
                // Handle token expiry or authentication errors
                if (err.response?.status === 401 || err.response?.data?.message?.includes('token') || err.response?.data?.message?.includes('session')) {
                    console.error('Authentication error - redirecting to login:', err);
                    localStorage.removeItem('user');
                    localStorage.removeItem('userToken');
                    navigate('/login');
                    return;
                }

                setError(err.response?.data?.message || err.message);
                console.error('Error fetching Azure users:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchAzureUsers();
    }, [user]);

    // Get unique departments from Azure users
    const uniqueDepartments = [...new Set(azureUsers
        .map(user => user.department)
        .filter(dept => dept && dept !== 'Not Assigned'))];

    const departmentOptions = [
        { label: 'All Departments', value: 'all' },
        ...uniqueDepartments.map(dept => ({ label: dept, value: dept.toLowerCase() }))
    ];

    const roles = [
        { label: 'Admin', value: 'admin' },
        { label: 'Employee', value: 'employee' },
        { label: 'Manager', value: 'manager' }
    ];

    // Filter Azure users based on search term and department
    const getFilteredEmployees = () => {
        let filtered = azureUsers;

        if (searchTerm.trim()) {
            filtered = filtered.filter(user => 
                user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                (user.department && user.department.toLowerCase().includes(searchTerm.toLowerCase())) ||
                (user.jobTitle && user.jobTitle.toLowerCase().includes(searchTerm.toLowerCase()))
            );
        }

        if (department && department !== 'all') {
            filtered = filtered.filter(user => 
                user.department && user.department.toLowerCase() === department.toLowerCase()
            );
        }

        return filtered;
    };

    const handleSearch = (value) => {
        setSearchTerm(value);
    };

    const handleDepartmentFilter = (value) => {
        setDepartment(value);
    };

    const handleImportAll = () => {
        const filtered = getFilteredEmployees();
        setSelectedUsers(filtered);
    };

    const handleAddSelectedUsers = () => {
        console.log('Adding selected users:', selectedUsers);
        setSelectedUsers([]);
    };

    // Template functions
    const roleBodyTemplate = (rowData) => {
        return <span className="glass-badge">{rowData.role || 'Not Assigned'}</span>;
    };

    const statusBodyTemplate = (rowData) => {
        return (
            <span className={`glass-badge ${rowData.status === 'Active' ? 'success' : 'warning'}`}>
                {rowData.status}
            </span>
        );
    };

    const actionBodyTemplate = (rowData) => {
        return (
            <div className="action-buttons">
                <Button
                    icon="pi pi-pencil"
                    className="glass-btn-icon"
                    tooltip="Edit User"
                />
                <Button
                    icon="pi pi-trash"
                    className="glass-btn-icon"
                    tooltip="Delete User"
                />
            </div>
        );
    };

    return (
        <div className="glassy-page">
        {isAdmin ? (
            <div className="glass-container m-3 p-4" >
                <div className="flex justify-content-between align-items-start mb-3">
                    <div>
                        <h2 className="glass-title text-xl mb-2">Dynamic 365 Integration</h2>
                        <p className="glass-subtitle">Connect and import employees from your Dynamic 365</p>
                    </div>
                    <span className="glass-badge flex align-items-center gap-2">
                        <i className="pi pi-check-circle" />
                        Connected
                    </span>
                </div>

                <div className="custom-grid">
                    <div>
                        <label className="custom-text-style">
                            Search Users
                        </label>
                        <div className="glass-search-container">
                            <i className="pi pi-search glass-search-icon" />
                            <InputText
                                value={searchTerm}
                                onChange={(e) => handleSearch(e.target.value)}
                                placeholder="Search by name, email, or department"
                                className="glass-search-input w-full"
                                
                            />
                        </div>
                    </div>

                    <div>
                        <label className="custom-text-style">
                            Department Filter
                        </label>
                        <div className="glass-dropdown">
                            <Dropdown
                                value={department}
                                options={departmentOptions}
                                onChange={(e) => handleDepartmentFilter(e.value)}
                                placeholder="All Departments"
                                className='w-full'
                              
                            />
                        </div>
                    </div>
                </div>




                    <div className="glass-table-container">
                        {error ? (
                            <div className="p-4 text-red-500">{error}</div>
                        ) : (
                            <DataTable
                                value={paginatedUsers}
                                selection={selectedUsers}
                                onSelectionChange={(e) => setSelectedUsers(e.value)}
                                className="glass-table"
                                loading={loading}
                                emptyMessage={loading ? "Loading Azure users..." : "No users found"}
                            >
                                <Column selectionMode="multiple" className="w-3em"/>
                                <Column field="name" header="Name" body={(rowData) => (
                                    <div className="p-d-flex p-ai-center p-gap-3">
                                        <img
                                            src={`https://ui-avatars.com/api/?name=${rowData.name}&background=random`}
                                            className="p-mr-2 w-2rem h-2rem border-circle"
                                            alt={rowData.name}
                                        />
                                        <div>
                                            <div className="p-font-bold p-text-secondary">{rowData.name}</div>
                                            <div className="p-text-muted p-text-xs">{rowData.email}</div>
                                        </div>
                                    </div>
                                )} />
                                <Column field="department" header="Department" />
                                <Column field="jobTitle" header="Job Title" />
                                <Column field="status" header="Status" body={statusBodyTemplate} />
                                <Column header="Actions" body={actionBodyTemplate} />
                            </DataTable>
                        )}

                        {/* ✅ Custom Pagination */}
                        <Paginator
                            data={getFilteredEmployees()}
                            itemsPerPage={itemsPerPage}
                            onPageChange={onPageChange}
                        />
                    </div>

                    <div className="flex justify-content-between">
                        <GlassyWhiteButton
                            label="Cancel"
                            className="glass-btn-secondary"
                            onClick={() => setSelectedUsers([])}
                        />
                        <GlassyBlueButton
                            label={`Add Selected Users (${selectedUsers.length})`}
                            icon="pi pi-users"
                            className="glass-btn-primary"
                            disabled={selectedUsers.length === 0}
                            onClick={handleAddSelectedUsers}
                        />
                    </div>
                </div>
            ) : (
                <div>Non-admin content</div>
            )}
        </div>
    );
};

export default AddUser;
