// jsconfig.json
{
  "compilerOptions": {
    "baseUrl": "src",
    "paths": {
      "@components/*": ["components/*"],
      "@hooks/*": ["hooks/*"],
      "@utils/*": ["utils/*"],
      "@assets/*": ["assets/*"],
      "@styles/*": ["styles/*"],
      "@contexts/*": ["contexts/*"],
      "@views/*": ["mvc/views/*"],
      "@services/*": ["mvc/services/*"],
      "@controllers/*": ["mvc/controllers/*"],
      "@store/*": ["store/*"],
      "@temp-data/*": ["temp-data/*"],
    }
  },
  "include": ["src"]
}