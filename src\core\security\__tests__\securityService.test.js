import securityService from '../securityService.js';

// Mock DOMPurify
jest.mock('dompurify', () => ({
  sanitize: jest.fn((html) => html.replace(/<script.*?<\/script>/gi, '')),
  addHook: jest.fn()
}));

// Mock CryptoJS
jest.mock('crypto-js', () => ({
  AES: {
    encrypt: jest.fn(() => ({ toString: () => 'encrypted_data' })),
    decrypt: jest.fn(() => ({ toString: () => '{"test":"data"}' }))
  },
  SHA256: jest.fn(() => ({ toString: () => 'hashed_token' })),
  enc: {
    Utf8: 'utf8'
  }
}));

describe('SecurityService', () => {
  beforeEach(() => {
    // Clear any existing tokens and rate limits
    securityService.csrfTokens.clear();
    securityService.rateLimitMap.clear();
    
    // Mock environment variable
    process.env.REACT_APP_ENCRYPTION_KEY = 'test_key';
  });

  describe('sanitizeHTML', () => {
    it('should sanitize malicious HTML', () => {
      const maliciousHTML = '<script>alert("xss")</script><p>Safe content</p>';
      const result = securityService.sanitizeHTML(maliciousHTML);
      
      expect(result).not.toContain('<script>');
      expect(result).toContain('<p>Safe content</p>');
    });

    it('should allow safe HTML tags', () => {
      const safeHTML = '<p>Safe <strong>content</strong></p>';
      const result = securityService.sanitizeHTML(safeHTML);
      
      expect(result).toBe(safeHTML);
    });

    it('should respect custom options', () => {
      const html = '<div>Content</div>';
      const options = { ALLOWED_TAGS: ['div'] };
      const result = securityService.sanitizeHTML(html, options);
      
      expect(result).toContain('<div>');
    });
  });

  describe('validateInput', () => {
    it('should validate input within length limits', () => {
      const validInput = 'Valid input';
      expect(() => securityService.validateInput(validInput)).not.toThrow();
    });

    it('should reject input that is too long', () => {
      const longInput = 'a'.repeat(300);
      expect(() => securityService.validateInput(longInput)).toThrow('Input too long');
    });

    it('should detect XSS attempts', () => {
      const xssInput = '<script>alert("xss")</script>';
      expect(() => securityService.validateInput(xssInput)).toThrow('XSS attempt detected');
    });

    it('should reject invalid characters', () => {
      const invalidInput = 'test<>input';
      const rules = { allowedChars: /^[a-zA-Z0-9\s]+$/ };
      expect(() => securityService.validateInput(invalidInput, rules)).toThrow('Invalid characters detected');
    });

    it('should accept valid characters', () => {
      const validInput = 'test input 123';
      const rules = { allowedChars: /^[a-zA-Z0-9\s]+$/ };
      expect(() => securityService.validateInput(validInput, rules)).not.toThrow();
    });
  });

  describe('encryptData and decryptData', () => {
    it('should encrypt data successfully', () => {
      const data = { sensitive: 'information' };
      const encrypted = securityService.encryptData(data);
      
      expect(encrypted).toBe('encrypted_data');
    });

    it('should decrypt data successfully', () => {
      const encryptedData = 'encrypted_data';
      const decrypted = securityService.decryptData(encryptedData);
      
      expect(decrypted).toEqual({ test: 'data' });
    });

    it('should handle encryption errors gracefully', () => {
      // Mock encryption failure
      const CryptoJS = require('crypto-js');
      CryptoJS.AES.encrypt.mockImplementationOnce(() => {
        throw new Error('Encryption failed');
      });

      const data = { test: 'data' };
      const result = securityService.encryptData(data);
      
      expect(result).toBeNull();
    });
  });

  describe('CSRF Token Management', () => {
    it('should generate CSRF token', () => {
      const token = securityService.generateCSRFToken('session123');
      
      expect(token).toBe('hashed_token');
      expect(securityService.csrfTokens.has(token)).toBe(true);
    });

    it('should validate valid CSRF token', () => {
      const token = securityService.generateCSRFToken('session123');
      const isValid = securityService.validateCSRFToken(token, 'session123');
      
      expect(isValid).toBe(true);
    });

    it('should reject invalid CSRF token', () => {
      const isValid = securityService.validateCSRFToken('invalid_token', 'session123');
      
      expect(isValid).toBe(false);
    });

    it('should reject expired CSRF token', () => {
      const token = securityService.generateCSRFToken('session123');
      
      // Mock expired token
      const tokenData = securityService.csrfTokens.get(token);
      tokenData.expiry = Date.now() - 1000; // 1 second ago
      
      const isValid = securityService.validateCSRFToken(token, 'session123');
      
      expect(isValid).toBe(false);
      expect(securityService.csrfTokens.has(token)).toBe(false);
    });

    it('should reject token with wrong session ID', () => {
      const token = securityService.generateCSRFToken('session123');
      const isValid = securityService.validateCSRFToken(token, 'wrong_session');
      
      expect(isValid).toBe(false);
    });
  });

  describe('Rate Limiting', () => {
    it('should allow requests within rate limit', () => {
      const identifier = 'user123';
      const allowed = securityService.checkRateLimit(identifier, 5, 60000);
      
      expect(allowed).toBe(true);
    });

    it('should block requests exceeding rate limit', () => {
      const identifier = 'user123';
      
      // Make requests up to the limit
      for (let i = 0; i < 5; i++) {
        securityService.checkRateLimit(identifier, 5, 60000);
      }
      
      // This should be blocked
      const blocked = securityService.checkRateLimit(identifier, 5, 60000);
      expect(blocked).toBe(false);
    });

    it('should reset rate limit after time window', () => {
      const identifier = 'user123';
      
      // Fill up the rate limit
      for (let i = 0; i < 5; i++) {
        securityService.checkRateLimit(identifier, 5, 1000); // 1 second window
      }
      
      // Mock time passage
      jest.spyOn(Date, 'now').mockReturnValue(Date.now() + 2000); // 2 seconds later
      
      const allowed = securityService.checkRateLimit(identifier, 5, 1000);
      expect(allowed).toBe(true);
      
      Date.now.mockRestore();
    });
  });

  describe('generateSecureRandom', () => {
    it('should generate random string of specified length', () => {
      const random = securityService.generateSecureRandom(16);
      
      expect(random).toHaveLength(32); // 16 bytes = 32 hex characters
      expect(random).toMatch(/^[0-9a-f]+$/);
    });

    it('should generate different values on subsequent calls', () => {
      const random1 = securityService.generateSecureRandom(16);
      const random2 = securityService.generateSecureRandom(16);
      
      expect(random1).not.toBe(random2);
    });
  });

  describe('handleCSPViolation', () => {
    it('should handle CSP violation reports', () => {
      const violationReport = {
        blockedURI: 'https://evil.com/script.js',
        violatedDirective: 'script-src',
        originalPolicy: "default-src 'self'",
        documentURI: 'https://example.com'
      };

      // Mock console methods
      const consoleSpy = jest.spyOn(console, 'warn').mockImplementation();
      const errorSpy = jest.spyOn(console, 'error').mockImplementation();

      securityService.handleCSPViolation(violationReport);

      expect(consoleSpy).toHaveBeenCalledWith('CSP Violation:', violationReport);
      
      consoleSpy.mockRestore();
      errorSpy.mockRestore();
    });
  });

  describe('validateSecureHeaders', () => {
    it('should validate response with all required headers', () => {
      const mockResponse = {
        headers: new Map([
          ['x-content-type-options', 'nosniff'],
          ['x-frame-options', 'DENY'],
          ['x-xss-protection', '1; mode=block'],
          ['strict-transport-security', 'max-age=31536000']
        ]),
        url: 'https://example.com'
      };

      mockResponse.headers.has = jest.fn((header) => 
        mockResponse.headers.get(header) !== undefined
      );

      const isValid = securityService.validateSecureHeaders(mockResponse);
      expect(isValid).toBe(true);
    });

    it('should detect missing security headers', () => {
      const mockResponse = {
        headers: new Map([
          ['x-content-type-options', 'nosniff']
        ]),
        url: 'https://example.com'
      };

      mockResponse.headers.has = jest.fn((header) => 
        mockResponse.headers.get(header) !== undefined
      );

      const isValid = securityService.validateSecureHeaders(mockResponse);
      expect(isValid).toBe(false);
    });
  });

  describe('cleanupExpiredTokens', () => {
    it('should remove expired tokens', () => {
      // Add some tokens
      const token1 = securityService.generateCSRFToken('session1');
      const token2 = securityService.generateCSRFToken('session2');
      
      // Expire one token
      const tokenData = securityService.csrfTokens.get(token1);
      tokenData.expiry = Date.now() - 1000;
      
      securityService.cleanupExpiredTokens();
      
      expect(securityService.csrfTokens.has(token1)).toBe(false);
      expect(securityService.csrfTokens.has(token2)).toBe(true);
    });
  });
});

// Integration tests
describe('SecurityService Integration', () => {
  it('should handle complete security workflow', () => {
    // Generate CSRF token
    const token = securityService.generateCSRFToken('user123');

    // Validate input
    const userInput = 'Safe user input';
    expect(() => securityService.validateInput(userInput)).not.toThrow();

    // Encrypt sensitive data
    const sensitiveData = { userId: 123, email: '<EMAIL>' };
    const encrypted = securityService.encryptData(sensitiveData);
    expect(encrypted).toBeTruthy();

    // Validate CSRF token
    const isValidToken = securityService.validateCSRFToken(token, 'user123');
    expect(isValidToken).toBe(true);

    // Check rate limiting
    const rateLimitOk = securityService.checkRateLimit('user123', 10, 60000);
    expect(rateLimitOk).toBe(true);
  });

  it('should handle security violations appropriately', () => {
    const consoleSpy = jest.spyOn(console, 'error').mockImplementation();

    // Test XSS attempt
    expect(() => {
      securityService.validateInput('<script>alert("xss")</script>');
    }).toThrow('XSS attempt detected');

    // Test rate limiting
    const identifier = 'attacker';
    for (let i = 0; i < 6; i++) {
      securityService.checkRateLimit(identifier, 5, 60000);
    }

    const blocked = securityService.checkRateLimit(identifier, 5, 60000);
    expect(blocked).toBe(false);

    consoleSpy.mockRestore();
  });
});
