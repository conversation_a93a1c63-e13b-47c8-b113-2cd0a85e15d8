import { createIntl, createIntlCache } from 'react-intl';
import messages from '../../locales';

class I18nService {
  constructor() {
    this.cache = createIntlCache();
    this.currentLocale = this.getUserLocale();
    this.intl = this.createIntlInstance();
  }

  getUserLocale() {
    const savedLocale = localStorage.getItem('userLocale');
    if (savedLocale) return savedLocale;

    const browserLocale = navigator.language || navigator.userLanguage;
    const supportedLocales = ['en', 'es', 'fr', 'de', 'ja', 'zh'];

    const baseLocale = browserLocale.split('-')[0];
    return supportedLocales.includes(baseLocale) ? baseLocale : 'en';
  }

  createIntlInstance(locale = this.currentLocale) {
    return createIntl({
      locale: locale,
      messages: messages[locale],
      defaultLocale: 'en'
    }, this.cache);
  }

  changeLocale(locale) {
    localStorage.setItem('userLocale', locale);
    this.currentLocale = locale;
    this.intl = this.createIntlInstance(locale);
    window.location.reload();
  }

  formatDate(date, options = {}) {
    return this.intl.formatDate(date, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      ...options
    });
  }

  formatTime(time, options = {}) {
    return this.intl.formatTime(time, {
      hour: '2-digit',
      minute: '2-digit',
      ...options
    });
  }

  formatDateTime(datetime, options = {}) {
    return this.intl.formatDateTime(datetime, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      ...options
    });
  }

  formatNumber(number, options = {}) {
    return this.intl.formatNumber(number, {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      ...options
    });
  }

  formatCurrency(amount, currency = 'USD', options = {}) {
    return this.intl.formatNumber(amount, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      ...options
    });
  }

  translate(key, values = {}) {
    return this.intl.formatMessage({ id: key }, values);
  }

  pluralize(count, options) {
    return this.intl.formatPlural(count, options);
  }

  formatRelativeTime(value, unit, options = {}) {
    return this.intl.formatRelativeTime(value, unit, options);
  }
}

export default new I18nService();