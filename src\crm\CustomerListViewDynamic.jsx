import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Toast } from 'primereact/toast';
import { Tag } from 'primereact/tag';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Calendar } from 'primereact/calendar';
import { Card } from 'primereact/card';
import DynamicComponent from '../components/framework/core/DynamicComponent';
import componentRegistry from '../components/framework/core/ComponentRegistry';
import Paginator from '../components/common/Paginator';
import GlassyBlueButton from '../components/common/GlassyBlueButton';
import CRMNavigation from './CRMNavigation';
import './CRMViews.css';
import '../styles/glassy/glassy-ui.css';

const CustomerListViewDynamic = () => {
  const navigate = useNavigate();
  const toast = useRef(null);
  const [globalFilter, setGlobalFilter] = useState('');
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);
  const [paginatedUsers, setPaginatedUsers] = useState([]);

  useEffect(() => {
    if (!componentRegistry.initialized) {
      componentRegistry.init();
    }
  }, []);

  const customers = [
    {
      id: 'CUST-001',
      companyName: 'SkyHaven Airways',
      contactNumber: '+1 555 0101',
      email: '<EMAIL>',
      location: 'New York, USA',
      salesperson: 'John Doe',
      lastInvoice: '2024-03-15',
      totalDue: '$15,000',
      customerGroup: 'Enterprise',
      complianceStatus: 'Compliant'
    },
    {
      id: 'CUST-002',
      companyName: 'AeroVista Airlines',
      contactNumber: '+44 20 7946 0958',
      email: '<EMAIL>',
      location: 'London, UK',
      salesperson: 'Jane Smith',
      lastInvoice: '2024-03-10',
      totalDue: '$8,500',
      customerGroup: 'SMB',
      complianceStatus: 'Compliant'
    },
    {
      id: 'CUST-003',
      companyName: 'CloudLiner Aviation',
      contactNumber: '+971 4 123 4567',
      email: '<EMAIL>',
      location: 'Dubai, UAE',
      salesperson: 'Mike Johnson',
      lastInvoice: '2024-03-20',
      totalDue: '$22,000',
      customerGroup: 'Enterprise',
      complianceStatus: 'Non-Compliant'
    },
    {
      id: 'CUST-004',
      companyName: 'Horizon Flight Services',
      contactNumber: '+33 1 42 86 82 00',
      email: '<EMAIL>',
      location: 'Paris, France',
      salesperson: 'Sarah Williams',
      lastInvoice: '2024-03-18',
      totalDue: '$12,500',
      customerGroup: 'SMB',
      complianceStatus: 'Compliant'
    },
    {
      id: 'CUST-005',
      companyName: 'Pacific Wings Co.',
      contactNumber: '+81 3 5250 8888',
      email: '<EMAIL>',
      location: 'Tokyo, Japan',
      salesperson: 'David Chen',
      lastInvoice: '2024-03-12',
      totalDue: '$18,750',
      customerGroup: 'Enterprise',
      complianceStatus: 'Compliant'
    }
  ];

  const showToast = useCallback((severity, summary, detail) => {
    toast.current?.show({ severity, summary, detail, life: 3000 });
  }, []);

  const handleExport = useCallback(() => {
    showToast('success', 'Export', 'Exporting customer data...');
  }, [showToast]);

  const handleDelete = useCallback(() => {
    showToast('warn', 'Delete', 'Delete functionality not implemented');
  }, [showToast]);

  const handleView = useCallback((rowData) => {
    showToast('info', 'View Customer', `Viewing ${rowData.companyName}`);
  }, [showToast]);

  const handleEdit = useCallback((rowData) => {
    showToast('info', 'Edit Customer', `Editing ${rowData.companyName}`);
  }, [showToast]);

  const columns = [
    { field: 'companyName', header: 'Company Name', sortable: true },
    { field: 'contactNumber', header: 'Contact Number', sortable: true },
    { field: 'email', header: 'Email', sortable: true },
    { field: 'location', header: 'Location', sortable: true },
    { field: 'salesperson', header: 'Salesperson', sortable: true },
    { field: 'lastInvoice', header: 'Last Invoice', sortable: true },
    { field: 'totalDue', header: 'Total Due', sortable: true },
    { field: 'customerGroup', header: 'Customer Group', sortable: true },
    {
      field: 'complianceStatus',
      header: 'Compliance Status',
      sortable: true,
      body: (rowData) => {
        const complianceStatus = rowData.complianceStatus === 'Compliant' ? 'success' : 'danger';
        return (
          // <Tag
          //   value={rowData.complianceStatus}
          //   severity={complianceStatus}
          // />
          
          <span className={`glass-badge ${complianceStatus[rowData.complianceStatus] || 'bg-gray-100 text-gray-800'}`}>
        {rowData.complianceStatus}
      </span>
        );
      }
    }
  ];

  const tableConfig = {
    pagination: {
      enabled: false
    },
    sorting: { enabled: true },
    filtering: {
      enabled: false,
      globalFilter: globalFilter
    },
    selection: {
      enabled: true,
      mode: 'multiple'
    },
    actions: [
      {
        name: 'view',
        label: '',
        icon: 'pi pi-eye',
        // severity: 'info',
        className: 'glass-badge bg-blue-100 text-blue-800 hover:bg-blue-200',
        onClick: handleView
      },
      {
        name: 'edit',
        label: '',
        icon: 'pi pi-pencil',
        // severity: 'secondary',
        className: 'glass-badge bg-blue-100 text-blue-800 hover:bg-blue-200',
        onClick: handleEdit
      }
    ],
    rowHover: true,
    stripedRows: true,
    showGridlines: true,
    responsiveLayout: 'scroll',
    emptyMessage: 'No customers found'
  };

  const handleTableEvent = useCallback((eventName, eventData) => {
    console.log('Table event:', eventName, eventData);

    switch (eventName) {
      case 'selectionChange':
        showToast('info', 'Selection Changed', `Selected ${eventData.selection?.length || 0} customers`);
        break;
      default:
        break;
    }
  }, [showToast]);

  const onPageChange = (page, paginatedData) => {
    console.log("Page changed to:", page, "Data:", paginatedData);
    setPaginatedUsers(paginatedData);
  };

  return (
    <div className="customer-list-view">
      <Toast ref={toast} />
      {/* <CRMNavigation /> */}

      <div className="mb-4">
        {/* <div className="breadcrumb-nav">
          <Button
            icon="pi pi-home"
            text
            className="p-button-sm"
            onClick={() => navigate('/')}
          />
          <span className="breadcrumb-separator">/</span>
          <span>Customer</span>
        </div> */}

        <h1>CRM - Customer</h1>
        <p>Manage your customers</p>
      </div>

      <div className="filter-section glass-card-doc-management" >
        <div className="filter-controls w-full gap-2 d-flex" style={{ marginTop: '1.5rem',display:"flex",gap:"10px" }}>
          <div className="filter-row d-flex gap-2" style={{display:"flex",gap:"10px" }}>
            <Dropdown
              options={[
                { label: 'All Groups', value: null },
                { label: 'Enterprise', value: 'Enterprise' },
                { label: 'SMB', value: 'SMB' }
              ]}
              placeholder="Customer Group"
              className="filter-dropdown"
            />
            <Dropdown
              options={[
                { label: 'All Salespersons', value: null },
                { label: 'John Doe', value: 'John Doe' },
                { label: 'Jane Smith', value: 'Jane Smith' },
                { label: 'Mike Johnson', value: 'Mike Johnson' }
              ]}
              placeholder="Salesperson"
              className="filter-dropdown"
            />
            <InputText
              placeholder="Search customers..."
              value={globalFilter}
              onChange={(e) => setGlobalFilter(e.target.value)}
              className="search-input"
            />
          </div>
          <div className="filter-row" style={{display:"flex",gap:"10px" }}>
            <Calendar
              placeholder="From Date"
              value={fromDate}
              onChange={(e) => setFromDate(e.value)}
              showIcon
            />
            <Calendar
              placeholder="To Date"
              value={toDate}
              onChange={(e) => setToDate(e.value)}
              showIcon
            />
            <GlassyBlueButton
              label="Export"
              icon="pi pi-download"
              onClick={handleExport}
            />
            <GlassyBlueButton
              label="Delete"
              icon="pi pi-trash"
              onClick={handleDelete}
            />
          </div>
        </div>
      </div>

      <div className="glass-table-container" style={{ marginTop: '1.5rem' }}>
        <DynamicComponent
          type="datatable"
          config={{
            data: paginatedUsers.length > 0 ? paginatedUsers : customers,
            columns: columns,
            config: tableConfig,
            onEvent: handleTableEvent
          }}
          debug={true}
        />
        
        <Paginator
          data={customers}
          itemsPerPage={10}
          onPageChange={onPageChange}
        />
      </div>
    </div>
  );
};

export default CustomerListViewDynamic;

