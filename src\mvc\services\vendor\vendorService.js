// import authHeader from "../AuthHeader";
import authHeader from "@services/AuthHeader";
import { getAxiosInstance } from "@utils/Loader/axiosInstance";

/**
 * Get all vendor details
 * @returns {Promise} - Promise resolving to vendor data
 */
const getVendorDetails = async () => {
  try {
    const axios = getAxiosInstance();
    const headers = {};

    // Add authorization header if available
    const token = authHeader();
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    const response = await axios.get("/api/VendorDetails/getAll", {
      headers,
    });

    // Transform the API response to match the expected format
    if (response.data && response.data.statusCode === 200) {
      return {
        success: true,
        data: response.data.data,
        total: response.data.data?.length || 0,
        statusCode: response.data.statusCode,
      };
    } else {
      throw new Error(`API response indicates failure: ${response.data?.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('Error in getVendorDetails:', error);
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

/**
 * Get vendor details by ID
 * @param {number} vendorId - Vendor ID
 * @returns {Promise} - Promise resolving to vendor data
 */
const getVendorById = async (vendorId) => {
  try {
    const axios = getAxiosInstance();
    const headers = {};

    const token = authHeader();
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    const response = await axios.get(`/api/VendorDetails/${vendorId}`, {
      headers,
    });

    if (response.data && response.data.statusCode === 200) {
      return {
        success: true,
        data: response.data.data,
        statusCode: response.data.statusCode,
      };
    } else {
      throw new Error(`API response indicates failure: ${response.data?.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('Error in getVendorById:', error);
    return {
      success: false,
      error: error.message,
      data: null,
    };
  }
};

/**
 * Create new vendor
 * @param {Object} vendorData - Vendor data to create
 * @returns {Promise} - Promise resolving to creation result
 */
const createVendor = async (vendorData) => {
  try {
    const axios = getAxiosInstance();
    const headers = {};

    const token = authHeader();
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    const response = await axios.post("/api/VendorDetails", vendorData, {
      headers,
    });

    if (response.data && response.data.statusCode === 200) {
      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
        statusCode: response.data.statusCode,
      };
    } else {
      throw new Error(`API response indicates failure: ${response.data?.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('Error in createVendor:', error);
    return {
      success: false,
      error: error.response?.data?.message || error.message,
    };
  }
};

/**
 * Update vendor by ID
 * @param {number} vendorId - Vendor ID
 * @param {Object} vendorData - Updated vendor data
 * @returns {Promise} - Promise resolving to update result
 */
const updateVendor = async (vendorId, vendorData) => {
  try {
    const axios = getAxiosInstance();
    const headers = {};

    const token = authHeader();
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    const response = await axios.put(`/api/VendorDetails/${vendorId}`, vendorData, {
      headers,
    });

    if (response.data && response.data.statusCode === 200) {
      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
        statusCode: response.data.statusCode,
      };
    } else {
      throw new Error(`API response indicates failure: ${response.data?.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('Error in updateVendor:', error);
    return {
      success: false,
      error: error.response?.data?.message || error.message,
    };
  }
};

/**
 * Delete vendor by ID
 * @param {number} vendorId - Vendor ID
 * @returns {Promise} - Promise resolving to deletion result
 */
const deleteVendor = async (vendorId) => {
  try {
    const axios = getAxiosInstance();
    const headers = {};

    const token = authHeader();
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    const response = await axios.delete(`/api/VendorDetails/${vendorId}`, {
      headers,
    });

    if (response.data && response.data.statusCode === 200) {
      return {
        success: true,
        message: response.data.message,
        statusCode: response.data.statusCode,
      };
    } else {
      throw new Error(`API response indicates failure: ${response.data?.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('Error in deleteVendor:', error);
    return {
      success: false,
      error: error.response?.data?.message || error.message,
    };
  }
};

/**
 * Search vendors by term
 * @param {string} searchTerm - Search term
 * @returns {Promise} - Promise resolving to filtered vendor data
 */
const searchVendors = async (searchTerm) => {
  try {
    const result = await getVendorDetails();

    if (!result.success) {
      return result;
    }

    // Filter vendors based on search term
    const filteredVendors = result.data.filter((vendor) => {
      const searchLower = searchTerm.toLowerCase();
      return (
        vendor.companyName?.toLowerCase().includes(searchLower) ||
        vendor.firstname?.toLowerCase().includes(searchLower) ||
        vendor.lastname?.toLowerCase().includes(searchLower) ||
        vendor.displayName?.toLowerCase().includes(searchLower) ||
        vendor.email?.toLowerCase().includes(searchLower) ||
        vendor.phone?.toLowerCase().includes(searchLower)
      );
    });

    return {
      success: true,
      data: filteredVendors,
      total: filteredVendors.length,
      statusCode: result.statusCode,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

/**
 * Update vendor status
 * @param {number} vendorId - Vendor ID
 * @param {number} statusId - New status ID
 * @returns {Promise} - Promise resolving to update result
 */
const updateVendorStatus = async (vendorId, statusId) => {
  try {
    const axios = getAxiosInstance();
    const headers = {};

    const token = authHeader();
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    const response = await axios.put(
      `/api/VendorDetails/${vendorId}/status`,
      { statusId: statusId },
      { headers }
    );

    if (response.data && response.data.statusCode === 200) {
      return {
        success: true,
        data: response.data.data,
        message: response.data.message,
        statusCode: response.data.statusCode,
      };
    } else {
      throw new Error(`API response indicates failure: ${response.data?.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('Error in updateVendorStatus:', error);
    return {
      success: false,
      error: error.response?.data?.message || error.message,
    };
  }
};

const VendorService = {
  getVendorDetails,
  getVendorById,
  createVendor,
  updateVendor,
  deleteVendor,
  searchVendors,
  updateVendorStatus,
};

export default VendorService;
