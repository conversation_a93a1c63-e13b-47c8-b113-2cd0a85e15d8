import React from 'react';
import DOMPurify from 'dompurify';
import { logger } from '../logging/logger';

class SecureComponent extends React.Component {
  constructor(props) {
    super(props);
    
    DOMPurify.addHook('uponSanitizeAttribute', (node, data) => {
      return this.sanitizeAttributesHook(node, data);
    });
  }

  sanitizeAttributesHook = (node, data) => {
    const dangerousAttributes = ['onclick', 'onload', 'onerror', 'onmouseover'];
    if (dangerousAttributes.includes(data.attrName)) {
      return false; 
    }

    if (data.attrName === 'href' || data.attrName === 'src') {
      if (data.attrValue && data.attrValue.toLowerCase().startsWith('javascript:')) {
        return false;
      }
    }
  };

  sanitizeInput = (input, options = {}) => {
    if (typeof input !== 'string') return input;

    const sanitizeOptions = {
      ALLOWED_TAGS: options.allowedTags || ['b', 'i', 'em', 'strong', 'a', 'p', 'br', 'ul', 'ol', 'li'],
      ALLOWED_ATTR: options.allowedAttrs || ['href', 'target', 'rel', 'class'],
      FORBID_ATTR: ['style', 'onerror', 'onclick', 'onload'],
      FORBID_TAGS: ['script', 'style', 'iframe', 'frame'],
      ...options
    };

    return DOMPurify.sanitize(input, sanitizeOptions);
  };

  safeSetHTML = (element, html) => {
    if (!element) return;
    const cleanHTML = this.sanitizeInput(html, {
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: []
    });
    element.textContent = cleanHTML;
  };

  validatePropsForXSS = (props) => {
    const xssPatterns = [
      /<script/i,
      /javascript:/i,
      /on\w+\s*=/i,
      /data:/i,
      /vbscript:/i
    ];

    Object.entries(props).forEach(([key, value]) => {
      if (typeof value === 'string') {
        xssPatterns.forEach(pattern => {
          if (pattern.test(value)) {
            logger.warn(`XSS attempt detected in prop: ${key}`, { value });
            throw new Error(`Security violation: XSS attempt in ${key}`);
          }
        });
      }
    });
  };

  render() {
    try {
      this.validatePropsForXSS(this.props);
      return this.renderSecureContent();
    } catch (error) {
      logger.error('XSS prevention triggered:', error);
      return this.renderSecurityError();
    }
  }

  renderSecureContent() {
    return null;
  }

  renderSecurityError() {
    return (
      <div className="security-error">
        <h3>Security Error</h3>
        <p>Potential security issue detected. Please refresh the page.</p>
      </div>
    );
  }
}

export default SecureComponent;