import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import PropTypes from 'prop-types';
import componentRegistry from './ComponentRegistry';
import ConfigValidator from './ConfigValidator';
import EventManager from '../utils/EventManager';
import DataTransformer from '../utils/DataTransformer';

const DynamicComponent = ({
  type,
  config = {},
  data = null,
  onEvent = null,
  onError = null,
  className = '',
  style = {},
  id = null,
  testId = null,
  debug = false,
  ...additionalProps
}) => {
  const [componentState, setComponentState] = useState({
    loading: true,
    error: null,
    initialized: false,
    renderCount: 0
  });
  
  const [componentData, setComponentData] = useState(data);
  const componentRef = useRef(null);
  const eventManagerRef = useRef(new EventManager());
  const mountedRef = useRef(false);

  console.log(data)

  useEffect(() => {
    if (!componentRegistry.initialized) {
      componentRegistry.init();
    }
  }, []);

  const componentConfig = useMemo(() => {
    if (!type) return null;
    
    const registeredComponent = componentRegistry.get(type);
    if (!registeredComponent) {
      console.error(`Component type '${type}' not found in registry`);
      return null;
    }

    const mergedConfig = {
      ...registeredComponent.defaultProps,
      ...config
    };

    const filteredConfig = componentRegistry.applyFilters(
      'component:config',
      mergedConfig,
      { type, componentConfig: registeredComponent }
    );

    return {
      ...registeredComponent,
      props: filteredConfig
    };
  }, [type, config]);

  const validationResult = useMemo(() => {
    if (!componentConfig) return { isValid: false, errors: ['Component not found'] };
    
    try {
      return ConfigValidator.validate(componentConfig.props, componentConfig.validation);
    } catch (error) {
      return { isValid: false, errors: [error.message] };
    }
  }, [componentConfig]);

  const transformedData = useMemo(() => {
    if (!componentData || !componentConfig?.dataTransformer) {
      return componentData;
    }

    try {
      return DataTransformer.transform(componentData, componentConfig.dataTransformer);
    } catch (error) {
      console.error('Data transformation error:', error);
      return componentData;
    }
  }, [componentData, componentConfig]);

  const handleEvent = useCallback((eventName, eventData) => {
    if (debug) {
      console.log(`DynamicComponent[${type}] Event:`, eventName, eventData);
    }

    eventManagerRef.current.emit(eventName, {
      component: type,
      config: componentConfig,
      data: eventData,
      timestamp: new Date().toISOString()
    });

    componentRegistry.emit(`component:${eventName}`, {
      type,
      config: componentConfig,
      data: eventData
    });

    if (onEvent) {
      onEvent(eventName, eventData);
    }
  }, [type, componentConfig, onEvent, debug]);

  const handleError = useCallback((error, errorInfo = {}) => {
    const errorData = {
      component: type,
      error: error.message || error,
      stack: error.stack,
      config: componentConfig,
      data: transformedData,
      timestamp: new Date().toISOString(),
      ...errorInfo
    };

    if (debug) {
      console.error('DynamicComponent Error:', errorData);
    }

    setComponentState(prev => ({
      ...prev,
      error: errorData,
      loading: false
    }));

    componentRegistry.emit('component:error', errorData);

    if (onError) {
      onError(errorData);
    }
  }, [type, componentConfig, transformedData, onError, debug]);

  useEffect(() => {
    if (!componentConfig) {
      setComponentState(prev => ({
        ...prev,
        loading: false,
        error: { message: `Component type '${type}' not found` }
      }));
      return;
    }

    if (!validationResult.isValid) {
      setComponentState(prev => ({
        ...prev,
        loading: false,
        error: { message: 'Configuration validation failed', errors: validationResult.errors }
      }));
      return;
    }

    setComponentState(prev => ({
      ...prev,
      loading: false,
      error: null,
      initialized: true
    }));

    if (debug) {
      console.log(`DynamicComponent[${type}] Event:`, 'beforeMount', { config: componentConfig.props });
    }

    eventManagerRef.current.emit('beforeMount', {
      component: type,
      config: componentConfig,
      data: { config: componentConfig.props },
      timestamp: new Date().toISOString()
    });

    componentRegistry.emit(`component:beforeMount`, {
      type,
      config: componentConfig,
      data: { config: componentConfig.props }
    });

    if (onEvent) {
      onEvent('beforeMount', { config: componentConfig.props });
    }

    mountedRef.current = true;

    return () => {
      mountedRef.current = false;

      if (debug) {
        console.log(`DynamicComponent[${type}] Event:`, 'beforeUnmount', { config: componentConfig.props });
      }

      eventManagerRef.current.emit('beforeUnmount', {
        component: type,
        config: componentConfig,
        data: { config: componentConfig.props },
        timestamp: new Date().toISOString()
      });

      componentRegistry.emit(`component:beforeUnmount`, {
        type,
        config: componentConfig,
        data: { config: componentConfig.props }
      });

      if (onEvent) {
        onEvent('beforeUnmount', { config: componentConfig.props });
      }
    };
  }, [componentConfig, validationResult, type, debug, onEvent]);

  useEffect(() => {
    setComponentData(data);
  }, [data]);

  useEffect(() => {
    setComponentState(prev => ({
      ...prev,
      renderCount: prev.renderCount + 1
    }));
  }, [componentConfig, validationResult]); 

  useEffect(() => {
    if (componentState.initialized && mountedRef.current) {
      if (debug) {
        console.log(`DynamicComponent[${type}] Event:`, 'afterMount', { config: componentConfig.props });
      }

      eventManagerRef.current.emit('afterMount', {
        component: type,
        config: componentConfig,
        data: { config: componentConfig.props },
        timestamp: new Date().toISOString()
      });

      componentRegistry.emit(`component:afterMount`, {
        type,
        config: componentConfig,
        data: { config: componentConfig.props }
      });

      if (onEvent) {
        onEvent('afterMount', { config: componentConfig.props });
      }
    }
  }, [componentState.initialized, componentConfig, type, debug, onEvent]);

  if (componentState.loading) {
    return (
      <div className={`dynamic-component dynamic-component--loading ${className}`} style={style}>
        <div className="dynamic-component__loader">
          <i className="pi pi-spin pi-spinner" />
          <span>Loading component...</span>
        </div>
      </div>
    );
  }

  if (componentState.error) {
    return (
      <div className={`dynamic-component dynamic-component--error ${className}`} style={style}>
        <div className="dynamic-component__error">
          <i className="pi pi-exclamation-triangle" />
          <h4>Component Error</h4>
          <p>{componentState.error.message}</p>
          {debug && componentState.error.errors && (
            <ul>
              {componentState.error.errors.map((error, index) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          )}
        </div>
      </div>
    );
  }

  if (!componentConfig) {
    return (
      <div className={`dynamic-component dynamic-component--not-found ${className}`} style={style}>
        <div className="dynamic-component__not-found">
          <i className="pi pi-question-circle" />
          <h4>Component Not Found</h4>
          <p>Component type '{type}' is not registered</p>
        </div>
      </div>
    );
  }

  handleEvent('beforeRender', { config: componentConfig.props });

  const Component = componentConfig.component;

  const componentProps = {
    ...componentConfig.props,
    ...additionalProps,
    ...(transformedData !== null && transformedData !== undefined ? { data: transformedData } : {}),
    onEvent: handleEvent,
    onError: handleError,
    className: `dynamic-component__content ${componentConfig.props.className || ''} ${className}`,
    style: { ...componentConfig.props.style, ...style },
    id: id || componentConfig.props.id,
    'data-testid': testId || componentConfig.props.testId || `dynamic-component-${type}`,
    'data-component-type': type,
    'data-render-count': debug ? componentState.renderCount : undefined
  };

  if (debug) {
    console.log('=== DynamicComponent DEBUG ===');
    console.log('Type:', type);
    console.log('Config prop:', config);
    console.log('Data prop:', data);
    console.log('componentConfig.props:', componentConfig.props);
    console.log('transformedData:', transformedData);
    console.log('Final componentProps.data:', componentProps.data);
    console.log('Final componentProps.columns:', componentProps.columns);
    console.log('Final componentProps.config:', componentProps.config);
  }

  try {
    return (
      <div 
        ref={componentRef}
        className={`dynamic-component dynamic-component--${type} ${className}`}
        style={style}
        data-component-type={type}
        data-component-id={id}
        data-testid={`dynamic-component-wrapper-${type}`}
      >
        {/* {debug && (
          <div className="dynamic-component__debug">
            <small>
              Type: {type} | Renders: {componentState.renderCount} | 
              Props: {Object.keys(componentProps).length}
            </small>
          </div>
        )} */}
        <Component {...componentProps} />
      </div>
    );
  } catch (error) {
    handleError(error, { phase: 'render' });
    return null;
  }
};

DynamicComponent.propTypes = {
  type: PropTypes.string.isRequired,
  config: PropTypes.object,
  data: PropTypes.any,
  onEvent: PropTypes.func,
  onError: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object,
  id: PropTypes.string,
  testId: PropTypes.string,
  debug: PropTypes.bool
};

DynamicComponent.defaultProps = {
  config: {},
  data: null,
  onEvent: null,
  onError: null,
  className: '',
  style: {},
  id: null,
  testId: null,
  debug: false
};

export default DynamicComponent;
