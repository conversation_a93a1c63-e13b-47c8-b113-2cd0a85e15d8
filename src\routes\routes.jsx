import { lazy } from 'react';
import { ROLES } from '@utils/auth/roles';



// const RoutesList = lazy(() => import('@views/asm-admin/MasterManagement/RoutesList.jsx'));
// const PointList = lazy(() => import('@views/asm-admin/MasterManagement/PointList.jsx'));
// const CustomerConflict = lazy(() => import('@views/asm-admin/MasterManagement/CustomerConflict.jsx'));
// const AddConflicts = lazy(() => import('@views/asm-admin/MasterManagement/AddConflicts.jsx'));
// // Lazy load all components
const LandingPage = lazy(() => import("@views/auth/LandingPage"));
const LoginPage = lazy(() => import("@views/auth/LoginPage"));
const ForgotPasswordPage = lazy(() => import("@views/auth/ForgotPasswordPage"));
// const SignIn = lazy(() => import("@views/auth/SignIn.jsx"));
const Dashboard = lazy(() => import("@views/asm-admin/dashboard/Dashboard.jsx"));
// const AsmEmployeeDashboard = lazy(() => import("@views/asm-employee/AsmEmployeeDashboard.jsx"));
// const ClientDashboard = lazy(() => import("@views/shared/ClientDashboard.jsx"));
const ManageUsers = lazy(() => import("@views/asm-admin/manageUsers/ManageUsers.jsx"));
// // const AddEmployee = lazy(() => import("@views/asm-admin/ManageUsers/AddEmployee.jsx"));
const AddUser = lazy(() => import("@views/shared/add-user/AddUser.jsx"));
// const RoleManagement = lazy(() => import("@views/asm-admin/RoleManagement/RoleManagement.jsx"));
// const AddRole = lazy(() => import("@views/asm-admin/RoleManagement/AddRole.jsx"));
// const ManageStaticContent = lazy(() => import("@views/asm-admin/ManageStaticContent/ManageStaticContent.jsx"));
// const AddNewContent = lazy(() => import("@views/asm-admin/ManageStaticContent/AddNewContent.jsx"));
// const MasterManagement = lazy(() => import("@views/asm-admin/MasterManagement/MasterManagement.jsx"));
// const AddMaster = lazy(() => import("@views/asm-admin/MasterManagement/AddMaster.jsx"));
// const AddMasterDynamic = lazy(() => import("@views/asm-admin/MasterManagement/AddMasterDynamic.jsx"));
// const ImportMaster = lazy(() => import("@views/asm-admin/MasterManagement/ImportMaster.jsx"));
// const ManageTickets = lazy(() => import("@views/asm-admin/ManageTickets/ManageTickets.jsx"));
// const ManageApproval = lazy(() => import("@views/asm-admin/ManageApproval/ManageApproval.jsx"));
// const ManageOffers = lazy(() => import("@views/asm-admin/MangeOffers/ManageOffers.jsx"));
// const NewOffer = lazy(() => import("@views/asm-admin/MangeOffers/NewOffer.jsx"));
// const DocumentMangement = lazy(() => import("@views/asm-admin/DocumentManagement/DocumentMangement.jsx"));
// const Reports = lazy(() => import("@views/asm-admin/Reports/Reports.jsx"));
// const DynamicFormManagement = lazy(() => import("@views/asm-admin/DynamicFormManagement/DynamicFormManagement.jsx"));
// const Customer = lazy(() => import("@views/asm-admin/Customer/Customer.jsx"));
const Vendor = lazy(() => import("@views/asm-admin/Vendor/Vendor.jsx"));
const NewVendor = lazy(() => import("@views/asm-admin/Vendor/NewVendor.jsx"));
// const LeadsList = lazy(() => import("@views/asm-employee/LeadsList.jsx"));
// const SalesPipeline = lazy(() => import("@views/asm-employee/SalesPipeline.jsx"));
// const EmployeeDashboard = lazy(() => import("@views/asm-employee/EmployeeDashboard.jsx"));
// const MasterSettings = lazy(() => import("@views/MasterSettings/MasterDashboard.jsx"))
// const SamplePage = lazy(()=>import("@views/asm-content-creation/SamplePage.jsx"))
// const DynamicFormBuilder = lazy(()=>import("@views/asm-form-creation/DynamicDraggableForm.jsx"))
// const KeyData = lazy(()=>import("@views/shared/KeyData.jsx"))
// const KeyDataDocVerify = lazy(()=>import("@views/shared/KeyDataDocVerify.jsx"))
// const PdfReport = lazy(()=>import("@views/shared/PdfReport.jsx"))

const LeadManagementRootDynamic = lazy(() => import('../lead-management/LeadManagementRootDynamic'));
const LeadDetailViewDynamic = lazy(() => import('../lead-management/LeadDetailViewDynamic'));
const ProspectListViewDynamic = lazy(() => import('../crm/ProspectListViewDynamic'));
const ProspectDetailViewDynamic = lazy(() => import('../crm/ProspectDetailViewDynamic'));
const CustomerListViewDynamic = lazy(() => import('../crm/CustomerListViewDynamic'));
const CustomDataTablePage = lazy(() => import('../pages/CustomDataTablePage'));
const ServiceOrdersPage = lazy(() => import('../pages/ServiceOrdersPage'));
const ServiceOrdersView = lazy(() => import('../views/ServiceOrdersView'));
// const DemoApp = lazy(() => import('../DemoApp')); // Adjust path if needed


// // Public routes (no authentication required)
export const publicRoutes = [
  { path: "/", component: LandingPage },
  { path: "/login", component: LoginPage },
  // { path: "/test-cms", component: SignIn },
  { path: "/forgot-password", component: ForgotPasswordPage },

  // // Master settings
  // { path: "/master-settings", component: MasterSettings },
  // { path: "/root", component: SamplePage },
  // { path: "/root-form", component: DynamicFormBuilder },

  // //keydata
  // { path: "/keydata", component: KeyData },
  // { path: "/keydata-doc-verify", component: KeyDataDocVerify },
  // { path: "/pdf-report", component: PdfReport },

];

// // Protected routes (require authentication and roles)
export const protectedRoutes = [
  // Dashboard Routes
  { path: "/dashboard", component: Dashboard, roles: [ROLES.ASM_ADMIN] },
  // { path: "/asm-employee-dashboard", component: AsmEmployeeDashboard, roles: [ROLES.ASM_EMPLOYEE] },
  // { path: "/client-dashboard", component: ClientDashboard, roles: [ROLES.CLIENT, ROLES.CLIENT_EMPLOYEE] },

  // // User Management
  { path: "/manage-users", component: ManageUsers, roles: [ROLES.ASM_ADMIN] },
  // { path: "/manage-users/add-employee", component: AddEmployee, roles: [ROLES.ASM_ADMIN] },
  { path: "/manage-users/add-user", component: AddUser, roles: [ROLES.ASM_ADMIN, ROLES.CLIENT] },
  // //Customer
  // { path: "/customer", component: Customer, roles: [ROLES.ASM_ADMIN, ROLES.CLIENT] },

  // //Vendor
  { path: "/vendors", component: Vendor, roles: [ROLES.ASM_ADMIN, ROLES.ASM_EMPLOYEE] },
  { path: "/vendors/new-vendor", component: NewVendor, roles: [ROLES.ASM_ADMIN, ROLES.ASM_EMPLOYEE] },

  

  // // Role Management
  // { path: "/role-management", component: RoleManagement, roles: [ROLES.ASM_ADMIN] },
  // { path: "/role-management/add-role", component: AddRole, roles: [ROLES.ASM_ADMIN] },

  // // Static Content Management
  // { path: "/manage-static-content", component: ManageStaticContent, roles: [ROLES.ASM_ADMIN] },
  // { path: "/manage-static-content/add-new-content", component: AddNewContent, roles: [ROLES.ASM_ADMIN] },

  // // Master Management
  // { path: "/master-management", component: MasterManagement, roles: [ROLES.ASM_ADMIN] },
  // { path: "/master-management/add-master", component: AddMaster, roles: [ROLES.ASM_ADMIN] },
  // { path: "/master-management/add-master-dynamic", component: AddMasterDynamic, roles: [ROLES.ASM_ADMIN] },
  // { path: "/master-management/import-master", component: ImportMaster, roles: [ROLES.ASM_ADMIN] },
  // { path: "/master-management/routes-list", component: RoutesList, roles: [ROLES.ASM_ADMIN] },
  // { path: "/master-management/point-list", component: PointList, roles: [ROLES.ASM_ADMIN] },
  // { path: "/master-management/customer-conflict", component: CustomerConflict, roles: [ROLES.ASM_ADMIN] },
  // { path: "/master-management/customer-conflict/add-conflicts", component: AddConflicts, roles: [ROLES.ASM_ADMIN] },

  // // Ticket Management
  // { path: "/manage-tickets", component: ManageTickets, roles: [ROLES.ASM_ADMIN] },

  // // Approval Management
  // { path: "/manage-approval", component: ManageApproval, roles: [ROLES.ASM_ADMIN] },

  // // Offers Mangement
  // { path: "/manage-offers", component: ManageOffers, roles: [ROLES.ASM_ADMIN] },
  // { path: "/manage-offers/new-offer", component: NewOffer, roles: [ROLES.ASM_ADMIN] },

  // // Document Mangement
  // { path: "/document-management", component: DocumentMangement, roles: [ROLES.ASM_ADMIN] },

  // // Reports
  // { path: "/reports", component: Reports, roles: [ROLES.ASM_ADMIN] },

  // // Dynamic Form Management
  // { path: "/dynamic-form-management", component: DynamicFormManagement, roles: [ROLES.ASM_ADMIN] },

  // //ASM Employee pages
  // //Dashboard
  // { path: "/employee-dashboard", component:EmployeeDashboard, roles: [ROLES.ASM_EMPLOYEE] },
  // //Leads
  // { path: "/leads", component: LeadsList, roles: [ROLES.ASM_EMPLOYEE] },
  // { path: "/sales-pipeline", component: SalesPipeline, roles: [ROLES.ASM_EMPLOYEE] }, 

  // // Business Routes (placeholder components)
  // { path: "/orders", component: () => <div>Orders Page</div>, roles: [ROLES.ASM_ADMIN, ROLES.ASM_EMPLOYEE, ROLES.CLIENT, ROLES.CLIENT_EMPLOYEE] },
  // { path: "/companies", component: () => <div>Companies Page</div>, roles: [ROLES.ASM_ADMIN, ROLES.ASM_EMPLOYEE] },

  // { path: "/clients", component: () => <div>Clients Page</div>, roles: [ROLES.ASM_ADMIN, ROLES.ASM_EMPLOYEE] },
  // { path: "/sales", component: () => <div>Sales Page</div>, roles: [ROLES.ASM_ADMIN, ROLES.ASM_EMPLOYEE] },
  // { path: "/subscription", component: () => <div>Subscription Page</div>, rol
  { path: "/leadlist", component: LeadManagementRootDynamic },
  { path: "/lead/:leadId", component: LeadDetailViewDynamic },
  { path: "/prospects", component: ProspectListViewDynamic },
  { path: "/prospect/:id", component: ProspectDetailViewDynamic },
  { path: "/customers", component: CustomerListViewDynamic },
  { path: "/custom-datatable", component: CustomDataTablePage },
  { path: "/service-orders", component: ServiceOrdersPage },
  { path: "/service-orders-dynamic", component: ServiceOrdersView },

];