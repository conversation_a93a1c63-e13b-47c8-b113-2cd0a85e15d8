import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { useI18n } from './I18nProvider';

const LanguageSelector = ({
  showLabel = true,
  showFlag = true,
  showNativeName = true,
  variant = 'dropdown', 
  size = 'normal',
  className = '',
  style = {},
  onLanguageChange,
  debug = false
}) => {
  const {
    currentLocale,
    locales,
    setLocale,
    formatMessage,
    getCurrentLocaleData
  } = useI18n();

  const handleLanguageChange = useCallback((localeCode) => {
    setLocale(localeCode);
    
    if (onLanguageChange) {
      onLanguageChange(localeCode);
    }

    if (debug) {
      console.log('Language changed via selector:', localeCode);
    }
  }, [setLocale, onLanguageChange, debug]);

  const languageOptions = locales.map(locale => ({
    label: showNativeName ? locale.nativeName : locale.name,
    value: locale.code,
    flag: locale.flag,
    name: locale.name,
    nativeName: locale.nativeName,
    rtl: locale.rtl
  }));

  const currentLanguageData = getCurrentLocaleData();

  const renderDropdown = () => (
    <div className={`language-selector language-selector--dropdown ${className}`} style={style}>
      {showLabel && (
        <label className="language-selector__label">
          <i className="pi pi-globe" />
          {formatMessage({ id: 'language.selectLanguage', defaultMessage: 'Select Language' })}
        </label>
      )}
      
      <Dropdown
        value={currentLocale}
        options={languageOptions}
        onChange={(e) => handleLanguageChange(e.value)}
        placeholder={formatMessage({ id: 'language.selectLanguage', defaultMessage: 'Select Language' })}
        className={`language-dropdown language-dropdown--${size}`}
        itemTemplate={(option) => (
          <div className="language-option">
            {showFlag && <span className="language-flag">{option.flag}</span>}
            <div className="language-info">
              <span className="language-name">{option.name}</span>
              {showNativeName && option.name !== option.nativeName && (
                <span className="language-native">({option.nativeName})</span>
              )}
            </div>
            {option.rtl && <i className="pi pi-arrow-left language-rtl-indicator" />}
          </div>
        )}
        valueTemplate={(option) => (
          <div className="language-option">
            {showFlag && <span className="language-flag">{option?.flag || currentLanguageData?.flag}</span>}
            <span className="language-name">
              {option?.name || currentLanguageData?.name}
              {showNativeName && option?.nativeName !== option?.name && (
                <span className="language-native"> ({option?.nativeName || currentLanguageData?.nativeName})</span>
              )}
            </span>
          </div>
        )}
      />
    </div>
  );

  const renderButtons = () => (
    <div className={`language-selector language-selector--buttons ${className}`} style={style}>
      {showLabel && (
        <label className="language-selector__label">
          <i className="pi pi-globe" />
          {formatMessage({ id: 'language.selectLanguage', defaultMessage: 'Select Language' })}
        </label>
      )}
      
      <div className="language-selector__button-group">
        {locales.slice(0, 6).map(locale => (
          <Button
            key={locale.code}
            className={`language-btn ${currentLocale === locale.code ? 'active' : ''}`}
            onClick={() => handleLanguageChange(locale.code)}
            tooltip={`${locale.name} (${locale.nativeName})`}
            tooltipOptions={{ position: 'bottom' }}
            size={size}
          >
            {showFlag && <span className="language-flag">{locale.flag}</span>}
            <span className="language-code">{locale.code.toUpperCase()}</span>
          </Button>
        ))}
        
        {locales.length > 6 && (
          <Dropdown
            value={currentLocale}
            options={languageOptions.slice(6)}
            onChange={(e) => handleLanguageChange(e.value)}
            placeholder="More..."
            className="language-dropdown-more"
            itemTemplate={(option) => (
              <div className="language-option">
                {showFlag && <span className="language-flag">{option.flag}</span>}
                <span className="language-name">{option.name}</span>
              </div>
            )}
          />
        )}
      </div>
    </div>
  );

  const renderSelector = () => {
    switch (variant) {
      case 'buttons':
        return renderButtons();
      default:
        return renderDropdown();
    }
  };

  return renderSelector();
};

LanguageSelector.propTypes = {
  showLabel: PropTypes.bool,
  showFlag: PropTypes.bool,
  showNativeName: PropTypes.bool,
  variant: PropTypes.oneOf(['dropdown', 'buttons']),
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  className: PropTypes.string,
  style: PropTypes.object,
  onLanguageChange: PropTypes.func,
  debug: PropTypes.bool
};

export default LanguageSelector;
