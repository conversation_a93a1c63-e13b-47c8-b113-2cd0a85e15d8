
async function createBrowserHash(data, algorithm = 'SHA-256') {
  if (typeof window === 'undefined') {
    return btoa(data).slice(0, 32);
  }

  try {
    const encoder = new TextEncoder();
    const dataBuffer = encoder.encode(data);
    const hashBuffer = await crypto.subtle.digest(algorithm, dataBuffer);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  } catch (error) {
    return simpleHash(data);
  }
}

function simpleHash(str) {
  let hash = 0;
  if (str.length === 0) return hash.toString(16);

  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; 
  }

  return Math.abs(hash).toString(16).padStart(8, '0');
}

class CachePersistence {
  constructor() {
    this.storageQuota = null;
    this.compressionEnabled = true;
    this.encryptionEnabled = false;
    this.storageKey = 'mvc_cache_data';
    this.metadataKey = 'mvc_cache_metadata';
    
    this.initializeStorage();
  }

  async initializeStorage() {
    if (typeof window === 'undefined') return;

    try {
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        const estimate = await navigator.storage.estimate();
        this.storageQuota = estimate;
        
        console.log('[Cache Persistence] Storage quota:', {
          quota: this.formatBytes(estimate.quota),
          usage: this.formatBytes(estimate.usage),
          available: this.formatBytes(estimate.quota - estimate.usage)
        });
      }
    } catch (error) {
      console.warn('[Cache Persistence] Failed to get storage estimate:', error);
    }
  }

  async persistCache(cacheType, data) {
    if (typeof window === 'undefined') return false;

    try {
      const storageKey = `${this.storageKey}_${cacheType}`;
      const metadata = {
        timestamp: Date.now(),
        version: '1.0',
        cacheType,
        compressed: this.compressionEnabled,
        encrypted: this.encryptionEnabled,
        checksum: null
      };

      let processedData = data;

      if (this.compressionEnabled) {
        processedData = await this.compressData(processedData);
        console.log('[Cache Persistence] Compressed data size:', this.formatBytes(JSON.stringify(processedData).length));
      }

      if (this.encryptionEnabled) {
        processedData = await this.encryptData(processedData);
      }

      metadata.checksum = await this.generateChecksum(processedData);

      localStorage.setItem(storageKey, JSON.stringify(processedData));
      localStorage.setItem(`${storageKey}_meta`, JSON.stringify(metadata));

      console.log(`[Cache Persistence] Persisted ${cacheType} cache`);
      return true;
    } catch (error) {
      console.error(`[Cache Persistence] Failed to persist ${cacheType}:`, error);
      
      if (error.name === 'QuotaExceededError') {
        await this.handleQuotaExceeded(cacheType);
      }
      
      return false;
    }
  }

  async loadCache(cacheType) {
    if (typeof window === 'undefined') return null;

    try {
      const storageKey = `${this.storageKey}_${cacheType}`;
      const dataStr = localStorage.getItem(storageKey);
      const metadataStr = localStorage.getItem(`${storageKey}_meta`);

      if (!dataStr || !metadataStr) {
        return null;
      }

      const metadata = JSON.parse(metadataStr);
      let data = JSON.parse(dataStr);

      const currentChecksum = await this.generateChecksum(data);
      if (currentChecksum !== metadata.checksum) {
        console.warn(`[Cache Persistence] Checksum mismatch for ${cacheType}, data may be corrupted`);
        this.clearPersistedCache(cacheType);
        return null;
      }

      if (metadata.encrypted) {
        data = await this.decryptData(data);
      }

      if (metadata.compressed) {
        data = await this.decompressData(data);
      }

      console.log(`[Cache Persistence] Loaded ${cacheType} cache from storage`);
      return { data, metadata };
    } catch (error) {
      console.error(`[Cache Persistence] Failed to load ${cacheType}:`, error);
      
      this.clearPersistedCache(cacheType);
      return null;
    }
  }

  clearPersistedCache(cacheType) {
    if (typeof window === 'undefined') return;

    try {
      const storageKey = `${this.storageKey}_${cacheType}`;
      localStorage.removeItem(storageKey);
      localStorage.removeItem(`${storageKey}_meta`);
      
      console.log(`[Cache Persistence] Cleared persisted ${cacheType} cache`);
    } catch (error) {
      console.error(`[Cache Persistence] Failed to clear ${cacheType}:`, error);
    }
  }

  async handleQuotaExceeded(cacheType) {
    console.warn('[Cache Persistence] Storage quota exceeded, attempting cleanup');

    try {
      const cacheKeys = Object.keys(localStorage).filter(key => 
        key.startsWith(this.storageKey)
      );

      const keyMetadata = [];
      for (const key of cacheKeys) {
        if (key.endsWith('_meta')) continue;
        
        const metaKey = `${key}_meta`;
        const metaStr = localStorage.getItem(metaKey);
        
        if (metaStr) {
          const metadata = JSON.parse(metaStr);
          keyMetadata.push({
            key,
            metaKey,
            timestamp: metadata.timestamp,
            cacheType: metadata.cacheType
          });
        }
      }

      keyMetadata.sort((a, b) => a.timestamp - b.timestamp);

      const toRemove = Math.ceil(keyMetadata.length * 0.5);
      for (let i = 0; i < toRemove && i < keyMetadata.length; i++) {
        const { key, metaKey, cacheType: oldCacheType } = keyMetadata[i];
        
        if (oldCacheType === cacheType) continue;
        
        localStorage.removeItem(key);
        localStorage.removeItem(metaKey);
        
        console.log(`[Cache Persistence] Removed old cache: ${oldCacheType}`);
      }

      return true;
    } catch (error) {
      console.error('[Cache Persistence] Failed to handle quota exceeded:', error);
      return false;
    }
  }

  async compressData(data) {
    const jsonStr = JSON.stringify(data);
    
    const compressed = jsonStr
      .replace(/\s+/g, ' ')
      .replace(/,\s*}/g, '}')
      .replace(/{\s*/g, '{')
      .replace(/\s*}/g, '}')
      .replace(/\[\s*/g, '[')
      .replace(/\s*\]/g, ']');

    return {
      compressed: true,
      data: compressed,
      originalSize: jsonStr.length,
      compressedSize: compressed.length,
      ratio: compressed.length / jsonStr.length
    };
  }

  async decompressData(compressedData) {
    if (!compressedData.compressed) {
      return compressedData;
    }

    return JSON.parse(compressedData.data);
  }

  async encryptData(data) {
    const jsonStr = JSON.stringify(data);
    const encrypted = btoa(jsonStr);
    
    return {
      encrypted: true,
      data: encrypted,
      algorithm: 'base64' 
    };
  }

  async decryptData(encryptedData) {
    if (!encryptedData.encrypted) {
      return encryptedData;
    }

    const decrypted = atob(encryptedData.data);
    return JSON.parse(decrypted);
  }

  async generateChecksum(data) {
    const jsonStr = JSON.stringify(data);
    return await createBrowserHash(jsonStr);
  }

  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  async getStorageStats() {
    if (typeof window === 'undefined') return null;

    try {
      const stats = {
        quota: this.storageQuota,
        caches: {},
        totalSize: 0
      };

      const cacheKeys = Object.keys(localStorage).filter(key => 
        key.startsWith(this.storageKey) && !key.endsWith('_meta')
      );

      for (const key of cacheKeys) {
        const data = localStorage.getItem(key);
        const metaKey = `${key}_meta`;
        const metaStr = localStorage.getItem(metaKey);
        
        if (data && metaStr) {
          const metadata = JSON.parse(metaStr);
          const size = data.length;
          
          stats.caches[metadata.cacheType] = {
            size,
            timestamp: metadata.timestamp,
            compressed: metadata.compressed,
            encrypted: metadata.encrypted
          };
          
          stats.totalSize += size;
        }
      }

      return stats;
    } catch (error) {
      console.error('[Cache Persistence] Failed to get storage stats:', error);
      return null;
    }
  }
}

const cachePersistence = new CachePersistence();

const persistenceMiddleware = (store) => (next) => (action) => {
  const result = next(action);
  
  if (action.type && shouldPersist(action)) {
    handlePersistence(store, action);
  }
  
  return result;
};

function shouldPersist(action) {
  const persistTriggers = [
    'set',
    'delete',
    'clear',
    'cleanupExpired'
  ];
  
  return action.type.includes('Cache/') && 
         persistTriggers.some(trigger => action.type.endsWith(trigger));
}

function handlePersistence(store, action) {
  const cacheType = getCacheTypeFromAction(action);
  
  if (!persistenceTimers[cacheType]) {
    persistenceTimers[cacheType] = setTimeout(async () => {
      const state = store.getState();
      const cacheData = state[cacheType];
      
      if (cacheData && shouldPersistCacheType(cacheType)) {
        await cachePersistence.persistCache(cacheType, cacheData);
      }
      
      delete persistenceTimers[cacheType];
    }, 1000); 
  }
}

const persistenceTimers = {};

function shouldPersistCacheType(cacheType) {
  const nonPersistentCaches = ['apiCache'];
  return !nonPersistentCaches.includes(cacheType);
}

function getCacheTypeFromAction(action) {
  if (action.type.startsWith('userCache/')) return 'userCache';
  if (action.type.startsWith('apiCache/')) return 'apiCache';
  if (action.type.startsWith('themeCache/')) return 'themeCache';
  if (action.type.startsWith('i18nCache/')) return 'i18nCache';
  return 'cache';
}

export const loadPersistedCaches = async (store) => {
  const cacheTypes = ['cache', 'userCache', 'themeCache', 'i18nCache'];
  
  for (const cacheType of cacheTypes) {
    try {
      const persistedData = await cachePersistence.loadCache(cacheType);
      
      if (persistedData && persistedData.data) {
        store.dispatch({
          type: `${cacheType}/importEntries`,
          payload: persistedData.data.entries || {}
        });
      }
    } catch (error) {
      console.error(`Failed to load persisted ${cacheType}:`, error);
    }
  }
};

export const getCacheStorageStats = () => cachePersistence.getStorageStats();
export const clearAllPersistedCaches = () => {
  const cacheTypes = ['cache', 'userCache', 'apiCache', 'themeCache', 'i18nCache'];
  cacheTypes.forEach(cacheType => {
    cachePersistence.clearPersistedCache(cacheType);
  });
};

export default persistenceMiddleware;
