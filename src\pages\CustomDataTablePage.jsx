import React, { useState } from 'react';
import CustomDataTable from '../components/custom/CustomDataTable';
import '../components/custom/CustomDataTable.css';
import { Tag } from 'primereact/tag';
import { Button } from 'primereact/button';

const CustomDataTablePage = () => {
  // Sample data for service orders
  const [serviceOrders] = useState([
    {
      id: 1,
      time: '14:00',
      arrivalTime: '16:30',
      flightNumber: 'VG573',
      registration: 'UK-73',
      route: 'DMDW → DRSV',
      crew: 4,
      pax: 150,
      transport: 'NA',
      hotels: 'NR',
      catering: 'TBD',
      customs: 'Ok',
      migration: 'Ok',
      slotPPR: '14:00z',
      permits: 'NR',
      fuel: '3.33 USD/USG',
      tasks: 'N',
      cateringProgress: 75,
      migrationProgress: 25
    },
    {
      id: 2,
      time: '16:00',
      arrivalTime: '18:45',
      flightNumber: 'VG574',
      registration: 'UK-74',
      route: 'DRSV → DMDW',
      crew: 4,
      pax: 142,
      transport: 'Ok',
      hotels: 'Ok',
      catering: 'Ok',
      customs: 'TBD',
      migration: 'NR',
      slotPPR: '16:00z',
      permits: 'Ok',
      fuel: '3.45 USD/USG',
      tasks: 'Y',
      cateringProgress: 100,
      migrationProgress: 50
    },
    {
      id: 3,
      time: '18:30',
      arrivalTime: '21:15',
      flightNumber: 'VG575',
      registration: 'UK-75',
      route: 'DMDW → EGLL',
      crew: 5,
      pax: 180,
      transport: 'TBD',
      hotels: 'NA',
      catering: 'Ok',
      customs: 'Ok',
      migration: 'Ok',
      slotPPR: '18:30z',
      permits: 'TBD',
      fuel: '3.28 USD/USG',
      tasks: 'N',
      cateringProgress: 90,
      migrationProgress: 80
    }
  ]);

  // Status tag template
  const statusBodyTemplate = (rowData, field) => {
    const value = rowData[field];
    let severity = 'info';
    
    switch (value) {
      case 'Ok':
        severity = 'success';
        break;
      case 'TBD':
        severity = 'warning';
        break;
      case 'NR':
        severity = 'danger';
        break;
      case 'NA':
        severity = 'secondary';
        break;
      default:
        severity = 'info';
    }
    
    return <Tag value={value} severity={severity} />;
  };

  // Progress bar template
  const progressBodyTemplate = (rowData, field) => {
    const value = rowData[field];
    const color = field === 'cateringProgress' ? '#22c55e' : '#ef4444';
    
    return (
      <div style={{ width: '100%', backgroundColor: '#e5e7eb', borderRadius: '4px', height: '8px' }}>
        <div
          style={{
            width: `${value}%`,
            backgroundColor: color,
            height: '100%',
            borderRadius: '4px',
            transition: 'width 0.3s ease'
          }}
        />
      </div>
    );
  };

  // Action buttons template
  const actionBodyTemplate = (rowData) => {
    return (
      <div style={{ display: 'flex', gap: '4px' }}>
        <Button
          icon="pi pi-check"
          className="p-button-rounded p-button-text p-button-sm"
          tooltip="Approve"
          onClick={() => console.log('Approve', rowData)}
        />
        <Button
          icon="pi pi-copy"
          className="p-button-rounded p-button-text p-button-sm"
          tooltip="Copy"
          onClick={() => console.log('Copy', rowData)}
        />
        <Button
          icon="pi pi-ellipsis-v"
          className="p-button-rounded p-button-text p-button-sm"
          tooltip="More"
          onClick={() => console.log('More', rowData)}
        />
      </div>
    );
  };

  // Define columns
  const columns = [
    {
      field: 'time',
      header: 'Time',
      sortable: true,
      style: { minWidth: '80px' }
    },
    {
      field: 'flightNumber',
      header: 'Flight',
      sortable: true,
      filter: true,
      style: { minWidth: '100px' }
    },
    {
      field: 'registration',
      header: 'Reg',
      sortable: true,
      style: { minWidth: '80px' }
    },
    {
      field: 'route',
      header: 'Route',
      sortable: true,
      filter: true,
      style: { minWidth: '150px' }
    },
    {
      field: 'crew',
      header: 'Crew',
      sortable: true,
      style: { minWidth: '70px' }
    },
    {
      field: 'pax',
      header: 'Pax',
      sortable: true,
      style: { minWidth: '70px' }
    },
    {
      field: 'transport',
      header: 'Transport',
      body: (rowData) => statusBodyTemplate(rowData, 'transport'),
      sortable: true,
      style: { minWidth: '100px' }
    },
    {
      field: 'hotels',
      header: 'Hotels',
      body: (rowData) => statusBodyTemplate(rowData, 'hotels'),
      sortable: true,
      style: { minWidth: '100px' }
    },
    {
      field: 'catering',
      header: 'Catering',
      body: (rowData) => statusBodyTemplate(rowData, 'catering'),
      sortable: true,
      style: { minWidth: '100px' }
    },
    {
      field: 'cateringProgress',
      header: 'Catering %',
      body: (rowData) => progressBodyTemplate(rowData, 'cateringProgress'),
      sortable: true,
      style: { minWidth: '120px' }
    },
    {
      field: 'customs',
      header: 'Customs',
      body: (rowData) => statusBodyTemplate(rowData, 'customs'),
      sortable: true,
      style: { minWidth: '100px' }
    },
    {
      field: 'migration',
      header: 'Migration',
      body: (rowData) => statusBodyTemplate(rowData, 'migration'),
      sortable: true,
      style: { minWidth: '100px' }
    },
    {
      field: 'migrationProgress',
      header: 'Migration %',
      body: (rowData) => progressBodyTemplate(rowData, 'migrationProgress'),
      sortable: true,
      style: { minWidth: '120px' }
    },
    {
      field: 'slotPPR',
      header: 'Slot/PPR',
      sortable: true,
      style: { minWidth: '100px' }
    },
    {
      field: 'permits',
      header: 'Permits',
      body: (rowData) => statusBodyTemplate(rowData, 'permits'),
      sortable: true,
      style: { minWidth: '100px' }
    },
    {
      field: 'fuel',
      header: 'Fuel',
      sortable: true,
      style: { minWidth: '120px' }
    },
    {
      field: 'tasks',
      header: 'Tasks',
      sortable: true,
      style: { minWidth: '70px' }
    },
    {
      field: 'actions',
      header: 'Actions',
      body: actionBodyTemplate,
      exportable: false,
      style: { minWidth: '120px' }
    }
  ];

  // Table configuration
  const config = {
    pagination: {
      enabled: true,
      rows: 10,
      rowsPerPageOptions: [5, 10, 25, 50]
    },
    sorting: {
      enabled: true,
      defaultSortField: 'time',
      defaultSortOrder: 1
    },
    filtering: {
      enabled: true
    },
    globalFilter: {
      enabled: true,
      placeholder: 'Search flights, routes, customers...',
      fields: ['flightNumber', 'registration', 'route']
    },
    export: {
      enabled: true
    },
    selection: {
      mode: 'multiple'
    },
    rowHover: true,
    showGridlines: false,
    stripedRows: false,
    size: 'normal',
    responsiveLayout: 'scroll',
    scrollable: true,
    scrollHeight: '600px',
    emptyMessage: 'No service orders found'
  };

  return (
    <div style={{ padding: '24px' }}>
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: '600', marginBottom: '8px' }}>
          Service Orders
        </h1>
        <p style={{ color: '#6c757d', fontSize: '14px' }}>
          Manage and track all service orders with advanced filtering and sorting
        </p>
      </div>

      <CustomDataTable
        data={serviceOrders}
        columns={columns}
        config={config}
        onRowClick={(rowData) => console.log('Row clicked:', rowData)}
        onSelectionChange={(selection) => console.log('Selection changed:', selection)}
        debug={false}
      />
    </div>
  );
};

export default CustomDataTablePage;

