import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { InputText } from 'primereact/inputtext';
import FloatLabel from './FloatLabel';

const DynamicInputText = ({
  value = '',
  onChange,
  onBlur,
  onFocus,
  onKeyDown,
  placeholder = '',
  disabled = false,
  readOnly = false,
  required = false,
  maxLength,
  minLength,
  pattern,
  autoComplete = 'off',
  autoFocus = false,
  className = '',
  style = {},
  id,
  name,
  label,
  floatLabel = false,
  leftIcon,
  rightIcon,
  tooltip,
  size = 'normal', // small, normal, large
  variant = 'outlined', // outlined, filled
  keyfilter,
  validateOnly = false,
  onEvent,
  debug = false,
  ...props
}) => {
  const [focused, setFocused] = useState(false);

  const handleChange = useCallback((e) => {
    const newValue = e.target.value;
    
    if (debug) {
      console.log('DynamicInputText change:', { name, value: newValue });
    }

    if (onChange) {
      onChange(newValue, e);
    }

    if (onEvent) {
      onEvent('change', { value: newValue, name, event: e });
    }
  }, [onChange, onEvent, name, debug]);

  const handleBlur = useCallback((e) => {
    setFocused(false);
    
    if (onBlur) {
      onBlur(e);
    }

    if (onEvent) {
      onEvent('blur', { value: e.target.value, name, event: e });
    }
  }, [onBlur, onEvent, name]);

  const handleFocus = useCallback((e) => {
    setFocused(true);
    
    if (onFocus) {
      onFocus(e);
    }

    if (onEvent) {
      onEvent('focus', { value: e.target.value, name, event: e });
    }
  }, [onFocus, onEvent, name]);

  const handleKeyDown = useCallback((e) => {
    if (onKeyDown) {
      onKeyDown(e);
    }

    if (onEvent) {
      onEvent('keydown', { 
        value: e.target.value, 
        name, 
        key: e.key, 
        keyCode: e.keyCode,
        event: e 
      });
    }
  }, [onKeyDown, onEvent, name]);

  // Build CSS classes
  const inputClasses = [
    'dynamic-input-text',
    `dynamic-input-text--${size}`,
    `dynamic-input-text--${variant}`,
    focused ? 'dynamic-input-text--focused' : '',
    disabled ? 'dynamic-input-text--disabled' : '',
    readOnly ? 'dynamic-input-text--readonly' : '',
    required ? 'dynamic-input-text--required' : '',
    leftIcon ? 'dynamic-input-text--left-icon' : '',
    rightIcon ? 'dynamic-input-text--right-icon' : '',
    className
  ].filter(Boolean).join(' ');

  // Input props
  const inputProps = {
    id: id || name,
    name,
    value: value || '',
    onChange: handleChange,
    onBlur: handleBlur,
    onFocus: handleFocus,
    onKeyDown: handleKeyDown,
    placeholder,
    disabled,
    readOnly,
    required,
    maxLength,
    pattern,
    autoComplete,
    autoFocus,
    className: inputClasses,
    style,
    keyfilter,
    validateOnly,
    tooltip,
    'aria-label': label || placeholder,
    'aria-required': required,
    'data-component-type': 'input-text',
    'data-field-name': name,
    ...props
  };

  // Remove undefined props
  Object.keys(inputProps).forEach(key => {
    if (inputProps[key] === undefined) {
      delete inputProps[key];
    }
  });

  const renderInput = () => <InputText {...inputProps} />;

  const renderWithIcons = (input) => {
    if (!leftIcon && !rightIcon) {
      return input;
    }

    return (
      <div className="p-inputgroup">
        {leftIcon && (
          <span className="p-inputgroup-addon">
            <i className={leftIcon} />
          </span>
        )}
        {input}
        {rightIcon && (
          <span className="p-inputgroup-addon">
            <i className={rightIcon} />
          </span>
        )}
      </div>
    );
  };

  const renderWithFloatLabel = (input) => {
    if (!floatLabel || !label) {
      return input;
    }

    return (
      <FloatLabel>
        {input}
        <label htmlFor={id || name}>{label}</label>
      </FloatLabel>
    );
  };

  let finalInput = renderInput();
  finalInput = renderWithIcons(finalInput);
  finalInput = renderWithFloatLabel(finalInput);

  return (
    <div className="dynamic-input-text-wrapper">
      {debug && (
        <div className="dynamic-input-text__debug">
          <small>
            Value: "{value}" | Length: {(value || '').length} | 
            Focused: {focused ? 'Yes' : 'No'}
          </small>
        </div>
      )}
      {finalInput}
    </div>
  );
};

DynamicInputText.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onFocus: PropTypes.func,
  onKeyDown: PropTypes.func,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  readOnly: PropTypes.bool,
  required: PropTypes.bool,
  maxLength: PropTypes.number,
  minLength: PropTypes.number,
  pattern: PropTypes.string,
  autoComplete: PropTypes.string,
  autoFocus: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object,
  id: PropTypes.string,
  name: PropTypes.string,
  label: PropTypes.string,
  floatLabel: PropTypes.bool,
  leftIcon: PropTypes.string,
  rightIcon: PropTypes.string,
  tooltip: PropTypes.string,
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  variant: PropTypes.oneOf(['outlined', 'filled']),
  keyfilter: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(RegExp)]),
  validateOnly: PropTypes.bool,
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicInputText;
