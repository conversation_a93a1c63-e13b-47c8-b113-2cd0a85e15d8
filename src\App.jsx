import React, { Suspense } from 'react';
import { IntlProvider } from 'react-intl';
import { PrimeReactProvider } from "primereact/api"
import { Provider } from 'react-redux';
import { Route, Routes } from 'react-router-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'
import SecureErrorBoundary from './core/error-handling/SecureErrorBoundary.jsx';
import BrowserSupport from './core/compatibility/browserSupport';
import I18nService from './core/i18n/i18nService';
import ThemeService from './core/theme/ThemeService';
import ResponsiveService from './core/responsive/ResponsiveService';
import SecurityMonitor from './core/security/SecurityMonitor';
import HTTPSService from './core/security/HTTPSService';
import store from './store/index.jsx';
import ThemeProvider from './components/framework/theme/ThemeProvider';
import I18nProvider from './components/framework/i18n/I18nProvider';
import DynamicNavbar from './components/framework/navigation/DynamicNavbar';
import DemoApp from './components/framework/demo/DemoApp';
import AppRoute from './AppRoute.jsx';
import './styles/main.css';
import './components/framework/styles/framework.css';
import { registerDefaultComponents } from './components/framework/core/DefaultComponents.js';
import "primereact/resources/themes/lara-light-blue/theme.css";
import "primereact/resources/primereact.min.css";
import "primeicons/primeicons.css";
import "primeflex/primeflex.css";
import { publicRoutes } from './routes/routes.jsx';
import { AuthProvider } from '@contexts/AuthContext.jsx';
import { LoadingProvider, useLoading } from '@utils/loader/LoadingContext.jsx';
import { configurePublicAxios } from '@utils/loader/publicAxiosInstance.js';
import { configureAxios } from '@utils/loader/axiosInstance.js';
import "./styles/glassy/global.css";
// import "@styles/glassy/glassy-ui.css";
import "./styles/glassy/glassy-ui.css";



HTTPSService; 
BrowserSupport.applyPolyfills();
ThemeService; 
ResponsiveService; 


class App extends React.Component {
  state = {
    isBrowserSupported: true,
    currentTheme: ThemeService.currentTheme,
    currentLocale: I18nService.currentLocale
  };

  

  componentDidMount() {
    if (!BrowserSupport.isBrowserSupported()) {
      this.setState({ isBrowserSupported: false });
      return;
    }
    registerDefaultComponents();
    window.addEventListener('themechange', this.handleThemeChange);
    window.addEventListener('breakpointchange', this.handleBreakpointChange);

    SecurityMonitor.initMonitoring();
  }

  

  componentWillUnmount() {
    window.removeEventListener('themechange', this.handleThemeChange);
    window.removeEventListener('breakpointchange', this.handleBreakpointChange);
  }

  handleThemeChange = (event) => {
    this.setState({ currentTheme: event.detail });
  };

  handleBreakpointChange = (event) => {
    console.log('Breakpoint changed:', event.detail);
  };

  renderUnsupportedBrowser() {
    return (
      <div className="unsupported-browser">
        <h1>Browser Not Supported</h1>
        <p>Please update your browser to the latest version or use one of the following:</p>
        <ul>
          <li>Chrome 80+</li>
          <li>Firefox 78+</li>
          <li>Safari 13+</li>
          <li>Edge 80+</li>
        </ul>
      </div>
    );
  }

// Loading fallback for public routes
 PublicLoadingFallback = () => (
  <div className="flex justify-content-center align-items-center" style={{ height: '100vh' }}>
    <i className="pi pi-spin pi-spinner" style={{ fontSize: '2rem' }}></i>
  </div>
);


 AxiosInitializer({ children }) {
  const { setLoading } = useLoading();
  //configure public axios instance
  configurePublicAxios(setLoading);
  configureAxios(setLoading);
  return children;
}

  render() {
    if (!this.state.isBrowserSupported) {
      return this.renderUnsupportedBrowser();
    }
    registerDefaultComponents();

    const queryClient = new QueryClient();


    return (
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
        <ThemeProvider
          defaultTheme="lara-light-blue"
          enableCustomThemes={true}
          enableDarkModeToggle={true}
          persistTheme={true}
          debug={false}
        >
          <I18nProvider
            defaultLocale="en"
            persistLocale={true}
            debug={false}
          >
            <SecureErrorBoundary>
               <LoadingProvider>
              <this.AxiosInitializer>
              <div className="app">
                {/* <DynamicNavbar
                  brandText="Dynamic Framework"
                  brandIcon="pi pi-bolt"
                  showThemeSelector={true}
                  showLanguageSelector={true}
                  showUserMenu={true}
                  variant="default"
                  position="sticky"
                  user={{
                    name: "John Doe",
                    email: "<EMAIL>",
                    role: "Developer"
                  }}
                  onMenuItemClick={(key) => console.log('Menu clicked:', key)}
                  onUserMenuClick={(action) => console.log('User action:', action)}
                  /> */}
                  <AuthProvider>
                <main className="app-content">
                  <AppRoute />
                </main></AuthProvider>
              </div>
              </this.AxiosInitializer>
              </LoadingProvider>
            </SecureErrorBoundary>
          </I18nProvider>
        </ThemeProvider>
        </QueryClientProvider>
      </Provider>
    );
  }
}

export default App;