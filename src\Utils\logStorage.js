/**
 * Log storage utility for persisting logs
 * Provides methods to store logs locally or send to a server
 */
import { consoleLog, getLogs, clearLogs as clearLogBuffer } from './logUtils';

// Simple console logging for this module to avoid circular dependencies
const log = (level, message, data) => {
  consoleLog(level, `[LogStorage] ${message}`, data);
};

/**
 * Clear the log buffer and log the action
 */
export const clearLogs = () => {
  clearLogBuffer();
  log('INFO', 'Log buffer cleared');
};

/**
 * Save logs to local storage
 * @returns {boolean} Success status
 */
export const saveLogsToLocalStorage = () => {
  try {
    const logs = getLogs();
    localStorage.setItem('application_logs', JSON.stringify(logs));
    log('INFO', `Saved ${logs.length} logs to local storage`);
    return true;
  } catch (error) {
    log('ERROR', 'Failed to save logs to local storage', error);
    return false;
  }
};

/**
 * Load logs from local storage
 * @returns {Array} Array of log entries
 */
export const loadLogsFromLocalStorage = () => {
  try {
    const storedLogs = localStorage.getItem('application_logs');
    if (storedLogs) {
      const logs = JSON.parse(storedLogs);
      log('INFO', `Loaded ${logs.length} logs from local storage`);
      return logs;
    }
    return [];
  } catch (error) {
    log('ERROR', 'Failed to load logs from local storage', error);
    return [];
  }
};

/**
 * Send logs to a server
 * @param {string} url - Server URL
 * @param {Array} logs - Array of log entries to send
 * @returns {Promise} Promise that resolves when logs are sent
 */
export const sendLogsToServer = async (url, logs = null) => {
  const logsToSend = logs || getLogs();

  if (!url) {
    log('ERROR', 'No URL provided for sending logs');
    return Promise.reject(new Error('No URL provided'));
  }

  if (!logsToSend || logsToSend.length === 0) {
    log('WARN', 'No logs to send');
    return Promise.resolve({ success: true, message: 'No logs to send' });
  }

  try {
    log('INFO', `Sending ${logsToSend.length} logs to server: ${url}`);

    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        logs: logsToSend,
        clientInfo: {
          userAgent: navigator.userAgent,
          timestamp: new Date().toISOString(),
          url: window.location.href
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Server responded with status: ${response.status}`);
    }

    const result = await response.json();
    log('INFO', 'Logs sent successfully', result);
    return result;
  } catch (error) {
    log('ERROR', 'Failed to send logs to server', error);
    return Promise.reject(error);
  }
};

/**
 * Download logs as a JSON file
 */
export const downloadLogs = () => {
  try {
    const logs = getLogs();
    const logsJson = JSON.stringify(logs, null, 2);
    const blob = new Blob([logsJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const now = new Date();
    const timestamp = now.toISOString().replace(/[:.]/g, '-');
    const filename = `application-logs-${timestamp}.json`;

    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();

    // Cleanup
    setTimeout(() => {
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }, 100);

    log('INFO', `Downloaded ${logs.length} logs to ${filename}`);
    return true;
  } catch (error) {
    log('ERROR', 'Failed to download logs', error);
    return false;
  }
};

export default {
  getLogs,
  clearLogs,
  saveLogsToLocalStorage,
  loadLogsFromLocalStorage,
  sendLogsToServer,
  downloadLogs
};
