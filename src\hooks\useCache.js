import { useState, useEffect, useCallback, useRef } from 'react';
import { useSelector, useDispatch } from 'react-redux';
import { fetchAndCache, invalidateCache } from '../store/slices/cacheSlice';
import cacheController from '../mvc/controllers/CacheController';

export const useCache = (key, fetchFn, options = {}) => {
  const {
    cacheType = 'cache',
    ttl = 5 * 60 * 1000, 
    tags = [],
    priority = 1,
    strategy = 'lru',
    enabled = true,
    staleWhileRevalidate = false,
    onSuccess,
    onError
  } = options;

  const dispatch = useDispatch();
  const [localLoading, setLocalLoading] = useState(false);
  const [localError, setLocalError] = useState(null);
  const fetchFnRef = useRef(fetchFn);
  
  useEffect(() => {
    fetchFnRef.current = fetchFn;
  }, [fetchFn]);

  const cacheEntry = useSelector(state => {
    const entries = state[cacheType]?.entries || {};
    return entries[key];
  });

  const isLoading = useSelector(state => state[cacheType]?.loading?.[key] || false);
  const storeError = useSelector(state => state[cacheType]?.errors?.[key]);

  const isValid = cacheEntry && (!cacheEntry.expiresAt || cacheEntry.expiresAt > Date.now());
  const isStale = cacheEntry && cacheEntry.expiresAt && cacheEntry.expiresAt <= Date.now();

  const fetchData = useCallback(async (force = false) => {
    if (!enabled || (!force && isValid)) return;

    try {
      setLocalLoading(true);
      setLocalError(null);

      const result = await dispatch(fetchAndCache({
        key,
        fetchFn: fetchFnRef.current,
        options: {
          ttl,
          tags,
          priority,
          strategy,
          staleWhileRevalidate
        }
      })).unwrap();

      if (onSuccess) {
        onSuccess(result.data, result.fromCache);
      }

      return result.data;
    } catch (error) {
      setLocalError(error.message);
      if (onError) {
        onError(error);
      }
      throw error;
    } finally {
      setLocalLoading(false);
    }
  }, [dispatch, key, enabled, isValid, ttl, tags, priority, strategy, staleWhileRevalidate, onSuccess, onError]);

  const invalidate = useCallback(() => {
    dispatch(invalidateCache({ keys: [key] }));
  }, [dispatch, key]);

  const refetch = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  useEffect(() => {
    if (enabled && !isValid && !isLoading && !localLoading) {
      fetchData();
    }
  }, [enabled, isValid, isLoading, localLoading, fetchData]);

  return {
    data: cacheEntry?.data || null,
    isLoading: isLoading || localLoading,
    error: storeError || localError,
    isValid,
    isStale,
    isCached: !!cacheEntry,
    fetchData,
    refetch,
    invalidate,
    cacheEntry
  };
};

export const useApiCache = (endpoint, options = {}) => {
  const {
    method = 'GET',
    params = {},
    data = null,
    headers = {},
    ttl = 2 * 60 * 1000, 
    staleWhileRevalidate = true,
    backgroundRefresh = true,
    tags = [],
    enabled = true,
    onSuccess,
    onError
  } = options;

  const dispatch = useDispatch();
  const [localLoading, setLocalLoading] = useState(false);
  const [localError, setLocalError] = useState(null);

  const cacheKey = generateApiCacheKey(endpoint, method, params, data);

  const cacheEntry = useSelector(state => {
    const entries = state.apiCache?.entries || {};
    return entries[cacheKey];
  });

  const isLoading = useSelector(state => state.apiCache?.loading?.[cacheKey] || false);
  const storeError = useSelector(state => state.apiCache?.errors?.[cacheKey]);

  const isValid = cacheEntry && (!cacheEntry.expiresAt || cacheEntry.expiresAt > Date.now());
  const isStale = cacheEntry && cacheEntry.expiresAt && cacheEntry.expiresAt <= Date.now();

  const fetchData = useCallback(async (force = false) => {
    if (!enabled || (!force && isValid)) return;

    try {
      setLocalLoading(true);
      setLocalError(null);

      const result = await dispatch(fetchWithCache({
        endpoint,
        options: { method, params, data, headers },
        cacheOptions: {
          ttl,
          staleWhileRevalidate,
          backgroundRefresh,
          tags
        }
      })).unwrap();

      if (onSuccess) {
        onSuccess(result.data, result.fromCache, result.stale);
      }

      return result.data;
    } catch (error) {
      setLocalError(error.message);
      if (onError) {
        onError(error);
      }
      throw error;
    } finally {
      setLocalLoading(false);
    }
  }, [dispatch, endpoint, method, params, data, headers, enabled, isValid, ttl, staleWhileRevalidate, backgroundRefresh, tags, onSuccess, onError]);


  const invalidate = useCallback(() => {
    dispatch(invalidateApiCache({ endpoints: [endpoint] }));
  }, [dispatch, endpoint]);

  const refetch = useCallback(() => {
    return fetchData(true);
  }, [fetchData]);

  useEffect(() => {
    if (enabled && !isValid && !isLoading && !localLoading) {
      fetchData();
    }
  }, [enabled, isValid, isLoading, localLoading, fetchData]);

  return {
    data: cacheEntry?.data || null,
    isLoading: isLoading || localLoading,
    error: storeError || localError,
    isValid,
    isStale,
    isCached: !!cacheEntry,
    fetchData,
    refetch,
    invalidate,
    cacheEntry
  };
};

export const useUserCache = (userId, dataType, options = {}) => {
  const {
    ttl = 30 * 60 * 1000, 
    encrypt = false,
    enabled = true,
    onSuccess,
    onError
  } = options;

  const dispatch = useDispatch();
  const [localLoading, setLocalLoading] = useState(false);
  const [localError, setLocalError] = useState(null);

  const cacheEntry = useSelector(state => {
    const key = `user:${userId}:${dataType}`;
    const entries = state.userCache?.entries || {};
    return entries[key];
  });

  const isLoading = useSelector(state => {
    const key = `user:${userId}:${dataType}`;
    return state.userCache?.loading?.[key] || false;
  });

  const storeError = useSelector(state => {
    const key = `user:${userId}:${dataType}`;
    return state.userCache?.errors?.[key];
  });

  const isValid = cacheEntry && (!cacheEntry.expiresAt || cacheEntry.expiresAt > Date.now());

  const setData = useCallback(async (data) => {
    if (!enabled) return false;

    try {
      setLocalLoading(true);
      setLocalError(null);

      const result = await dispatch(cacheUserData({
        userId,
        dataType,
        data,
        options: { ttl, encrypt }
      })).unwrap();

      if (onSuccess) {
        onSuccess(result.data);
      }

      return true;
    } catch (error) {
      setLocalError(error.message);
      if (onError) {
        onError(error);
      }
      return false;
    } finally {
      setLocalLoading(false);
    }
  }, [dispatch, userId, dataType, enabled, ttl, encrypt, onSuccess, onError]);

  const getData = useCallback(async () => {
    if (!enabled) return null;

    try {
      setLocalLoading(true);
      setLocalError(null);

      const result = await dispatch(getUserData({
        userId,
        dataType
      })).unwrap();

      return result?.data || null;
    } catch (error) {
      setLocalError(error.message);
      if (onError) {
        onError(error);
      }
      return null;
    } finally {
      setLocalLoading(false);
    }
  }, [dispatch, userId, dataType, enabled, onError]);

  const invalidate = useCallback(() => {
    dispatch(invalidateUserCache({ userId, dataTypes: [dataType] }));
  }, [dispatch, userId, dataType]);

  return {
    data: cacheEntry?.data || null,
    isLoading: isLoading || localLoading,
    error: storeError || localError,
    isValid,
    isCached: !!cacheEntry,
    setData,
    getData,
    invalidate,
    cacheEntry
  };
};

export const useCacheAnalytics = () => {
  const [analytics, setAnalytics] = useState(null);
  const [loading, setLoading] = useState(false);

  const refreshAnalytics = useCallback(async () => {
    try {
      setLoading(true);
      const data = await cacheController.getAnalytics();
      setAnalytics(data);
    } catch (error) {
      console.error('Failed to get cache analytics:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    refreshAnalytics();
    
    const interval = setInterval(refreshAnalytics, 30000); 
    
    return () => clearInterval(interval);
  }, [refreshAnalytics]);

  const resetAnalytics = useCallback(() => {
    cacheController.resetAnalytics();
    refreshAnalytics();
  }, [refreshAnalytics]);

  return {
    analytics,
    loading,
    refreshAnalytics,
    resetAnalytics
  };
};

export const useCacheManager = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(false);

  const refreshStats = useCallback(async () => {
    try {
      setLoading(true);
      const data = await cacheController.refreshStats();
      setStats(data);
    } catch (error) {
      console.error('Failed to refresh cache stats:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  const clearCache = useCallback(async (cacheType = null) => {
    try {
      setLoading(true);
      await cacheController.clear(cacheType);
      await refreshStats();
    } catch (error) {
      console.error('Failed to clear cache:', error);
    } finally {
      setLoading(false);
    }
  }, [refreshStats]);

  const performCleanup = useCallback(async () => {
    try {
      setLoading(true);
      await cacheController.performCleanup();
      await refreshStats();
    } catch (error) {
      console.error('Failed to perform cleanup:', error);
    } finally {
      setLoading(false);
    }
  }, [refreshStats]);

  const exportCache = useCallback(async (cacheType = null) => {
    try {
      return await cacheController.exportCache(cacheType);
    } catch (error) {
      console.error('Failed to export cache:', error);
      throw error;
    }
  }, []);

  const importCache = useCallback(async (cacheData, cacheType = null) => {
    try {
      setLoading(true);
      await cacheController.importCache(cacheData, cacheType);
      await refreshStats();
    } catch (error) {
      console.error('Failed to import cache:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  }, [refreshStats]);

  useEffect(() => {
    refreshStats();
  }, [refreshStats]);

  return {
    stats,
    loading,
    refreshStats,
    clearCache,
    performCleanup,
    exportCache,
    importCache
  };
};

function generateApiCacheKey(endpoint, method, params, data) {
  const keyData = {
    endpoint,
    method: method.toUpperCase(),
    params: params || {},
    data: data || null
  };
  
  return btoa(JSON.stringify(keyData)).replace(/[+/=]/g, '');
}

import { fetchWithCache, invalidateApiCache } from '../store/slices/apiCacheSlice';
import { cacheUserData, getUserData, invalidateUserCache } from '../store/slices/userCacheSlice';
