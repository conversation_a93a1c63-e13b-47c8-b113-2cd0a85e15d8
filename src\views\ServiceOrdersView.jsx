import React from 'react';
import DynamicServiceOrderTable from '../components/framework/data/DynamicServiceOrderTable';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import { Calendar } from 'primereact/calendar';
import { Button } from 'primereact/button';
import '../components/custom/ServiceOrdersDataTable.css';

const ServiceOrdersView = () => {
  const calculateStatusBar = (order) => {
    const serviceFields = ['transport', 'hotels', 'catering', 'customs', 'migration', 'permits'];

    const hasServiceData = serviceFields.some(field => order[field] !== undefined);

    if (!hasServiceData) {
      return {
        shouldShow: false,
        progress: 0,
        color: 'green'
      };
    }

    const allCompleted = serviceFields.every(field =>
      order[field] === 'Ok' || order[field] === 'NA'
    );

    const completedCount = serviceFields.filter(field =>
      order[field] === 'Ok' || order[field] === 'NA'
    ).length;
    const progress = (completedCount / serviceFields.length) * 100;

    return {
      shouldShow: true,
      progress: progress,
      color: allCompleted ? 'green' : 'red'
    };
  };

  const rawData = [
    {
      id: 1,
      date: 'Tue 5 Aug 2025',
      time: '14:00',
      flightNumber: 'VG5373',
      registration: 'UK-73',
      route: { from: 'DMDW', to: 'DRSV' },
      crew: 4,
      pax: 140,
      fao: '',
      transport: 'NA',
      hotels: 'NR',
      catering: 'TBD',
      customs: 'Ok',
      migration: 'Ok',
      slotPPR: '14:00z',
      permits: 'NR',
      fuel: { price: 3.33, unit: 'USD/USG' },
      tasks: 'N',
    },
    {
      id: 2,
      date: 'Tue 5 Aug 2025',
      time: '16:00',
      flightNumber: 'ANUBI',
      registration: 'Aerolineas Jet',
      route: { from: 'DRSV', to: 'DRSV' },
      crew: 4,
      pax: 140,
      fao: '',
      transport: 'NR',
      hotels: 'NR',
      catering: 'TBD',
      customs: 'Ok',
      migration: 'Ok',
      slotPPR: '14:00z',
      permits: 'NR',
      fuel: { price: 3.33, unit: 'USD/USG' },
      tasks: 'N',
    },
    {
      id: 3,
      date: 'Wed 6 Aug 2025',
      time: '09:00',
      flightNumber: 'BA4521',
      registration: 'G-EUXY',
      route: { from: 'EGLL', to: 'LFPG' },
      crew: 6,
      pax: 180,
      fao: '',
      transport: 'Ok',
      hotels: 'Ok',
      catering: 'Ok',
      customs: 'Ok',
      migration: 'Ok',
      slotPPR: '09:00z',
      permits: 'Ok',
      fuel: { price: 2.45, unit: 'USD/USG' },
      tasks: 'M',
    },
    {
      id: 4,
      date: 'Wed 6 Aug 2025',
      time: '11:30',
      flightNumber: 'LH8832',
      registration: 'D-AIUE',
      route: { from: 'EDDF', to: 'LIRF' },
      crew: 5,
      pax: 165,
      fao: '',
      transport: 'TBD',
      hotels: 'NA',
      catering: 'Ok',
      customs: 'Ok',
      migration: 'Ok',
      slotPPR: '11:30z',
      permits: 'TBD',
      fuel: { price: 2.38, unit: 'USD/USG' },
      tasks: 'L',
    },
    {
      id: 5,
      date: 'Thu 7 Aug 2025',
      time: '15:45',
      flightNumber: 'AF1234',
      registration: 'F-HPJA',
      route: { from: 'LFPG', to: 'LEMD' },
      crew: 7,
      pax: 200,
      fao: '',
      transport: 'Ok',
      hotels: 'TBD',
      catering: 'NR',
      customs: 'TBD',
      migration: 'NR',
      slotPPR: '15:45z',
      permits: 'Ok',
      fuel: { price: 2.50, unit: 'USD/USG' },
      tasks: 'H',
    },
  ];

  const data = rawData.map(order => ({
    ...order,
    statusBar: calculateStatusBar(order)
  }));

  const renderTabContent = (rowData, tabIndex, tabName) => {
    if (tabIndex === 0) {
      return (
        <div className="info-card">
          <h3>
            <i className="pi pi-car"></i>
            Transport Information
          </h3>
          <p>Manage transportation services for crew and passengers.</p>
          <div className="form-row">
            <label>Service</label>
            <Dropdown
              options={[
                { label: 'Not Required', value: 'NR' },
                { label: 'To Be Determined', value: 'TBD' },
                { label: 'Confirmed', value: 'Ok' },
              ]}
              placeholder="Select service status"
              className="w-full"
            />
          </div>
          <div className="form-row">
            <label>Provider</label>
            <InputTextarea rows={3} className="w-full" placeholder="Enter provider details..." />
          </div>
          <div className="form-actions">
            <Button label="Save" className="p-button-sm" />
            <Button label="Cancel" className="p-button-outlined p-button-sm" />
          </div>
        </div>
      );
    }

    if (tabIndex === 1) {
      return (
        <div className="info-card">
          <h3>
            <i className="pi pi-building"></i>
            Hotel Accommodations
          </h3>
          <p>Manage hotel bookings for crew and passengers.</p>
          <div className="form-row">
            <label>Hotel Name</label>
            <InputTextarea rows={2} className="w-full" placeholder="Enter hotel name..." />
          </div>
          <div className="form-row">
            <label>Check-in Date</label>
            <Calendar className="w-full" showIcon />
          </div>
          <div className="form-row">
            <label>Check-out Date</label>
            <Calendar className="w-full" showIcon />
          </div>
          <div className="form-actions">
            <Button label="Save" className="p-button-sm" />
            <Button label="Cancel" className="p-button-outlined p-button-sm" />
          </div>
        </div>
      );
    }

    if (tabIndex === 2) {
      return (
        <div className="catering-panel">
          <div className="catering-section">
            <div className="catering-header">
              <i className="pi pi-shopping-cart"></i>
              Catering - {rowData.route.from}
            </div>
            <div className="catering-form">
              <div className="form-row">
                <label>Service</label>
                <Dropdown
                  options={[
                    { label: 'Not Required', value: 'NR' },
                    { label: 'To Be Determined', value: 'TBD' },
                    { label: 'Confirmed', value: 'Ok' },
                  ]}
                  placeholder="Select"
                  className="w-full"
                />
              </div>
              <div className="form-row">
                <label>For</label>
                <Dropdown
                  options={[
                    { label: 'Aviation Services1', value: 'as1' },
                    { label: 'Aviation Services2', value: 'as2' },
                  ]}
                  placeholder="Select provider"
                  className="w-full"
                />
              </div>
              <div className="form-row">
                <label>Order</label>
                <InputTextarea rows={3} className="w-full" />
              </div>
              <div className="file-upload-area">
                <p>
                  Drop your file(s) here or <a>browse</a>
                </p>
              </div>
              <div className="form-actions">
                <Button label="Send" className="p-button-sm" />
                <Button label="Cancel" className="p-button-outlined p-button-sm" />
              </div>
            </div>
          </div>

          <div className="catering-section">
            <div className="catering-header">
              <i className="pi pi-shopping-cart"></i>
              Catering - {rowData.route.to}
            </div>
            <div className="catering-form">
              <div className="form-row">
                <label>Service</label>
                <Dropdown
                  options={[
                    { label: 'Not Required', value: 'NR' },
                    { label: 'To Be Determined', value: 'TBD' },
                    { label: 'Confirmed', value: 'Ok' },
                  ]}
                  placeholder="Select"
                  className="w-full"
                />
              </div>
              <div className="form-row">
                <label>For</label>
                <Dropdown
                  options={[
                    { label: 'Aviation Services1', value: 'as1' },
                    { label: 'Aviation Services2', value: 'as2' },
                  ]}
                  placeholder="Select provider"
                  className="w-full"
                />
              </div>
              <div className="form-row">
                <label>Order</label>
                <InputTextarea rows={3} className="w-full" />
              </div>
              <div className="file-upload-area">
                <p>
                  Drop your file(s) here or <a>browse</a>
                </p>
              </div>
              <div className="form-actions">
                <Button label="Send" className="p-button-sm" />
                <Button label="Cancel" className="p-button-outlined p-button-sm" />
              </div>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="info-card">
        <h3>
          <i className="pi pi-info-circle"></i>
          {tabName}
        </h3>
        <p>Content for {tabName} tab.</p>
        <div className="form-row">
          <label>Status</label>
          <Dropdown
            options={[
              { label: 'Not Required', value: 'NR' },
              { label: 'To Be Determined', value: 'TBD' },
              { label: 'Confirmed', value: 'Ok' },
              { label: 'Not Applicable', value: 'NA' },
            ]}
            placeholder="Select status"
            className="w-full"
          />
        </div>
        <div className="form-row">
          <label>Notes</label>
          <InputTextarea rows={4} className="w-full" placeholder="Enter notes..." />
        </div>
        <div className="form-actions">
          <Button label="Save" className="p-button-sm" />
          <Button label="Cancel" className="p-button-outlined p-button-sm" />
        </div>
      </div>
    );
  };

  const handleEvent = (eventType, eventData) => {
    console.log('Event:', eventType, eventData);

    switch (eventType) {
      case 'statusClick':
        console.log('Status clicked:', eventData.rowData, 'Tab:', eventData.tabIndex);
        break;
      case 'action':
        console.log('Action clicked:', eventData.action, 'Row:', eventData.rowData);
        if (eventData.action === 'approve') {
          alert(`Approve order ${eventData.rowData.flightNumber}`);
        }
        break;
      case 'headerAction':
        console.log('Header action:', eventData.action);
        if (eventData.action === 'addServiceOrder') {
          alert('Add new service order');
        }
        break;
      case 'tabChange':
        console.log('Tab changed:', eventData.tabIndex);
        break;
      default:
        console.log('Unknown event:', eventType, eventData);
    }
  };

  // Configuration object (props-based)
  const config = {
    title: 'Service Orders',
    data: data,
    serviceFields: ['transport', 'hotels', 'catering', 'customs', 'migration', 'permits'],
    groupBy: {
      field: 'date',
      actions: {
        icon: 'pi pi-cog',
      },
    },
    groupStatistics: [
      {
        name: 'Total',
        type: 'count',
      },
      {
        name: 'Active',
        type: 'countIf',
        condition: (row) =>
          ['TBD', 'NR'].includes(row.catering) ||
          ['TBD', 'NR'].includes(row.hotels) ||
          ['TBD', 'NR'].includes(row.transport),
      },
      {
        name: 'Issues',
        type: 'countIf',
        condition: (row) => row.transport === 'NR' || row.permits === 'NR',
      },
      {
        name: 'Completed',
        type: 'countIf',
        condition: (row) => row.catering === 'Ok' && row.hotels === 'Ok' && row.transport === 'Ok',
      },
    ],
    statusBar: {
      enabled: true,
      field: 'statusBar',
      idField: 'id',
    },
    expansion: {
      enabled: true,
      tabs: [
        {
          header: 'Transport',
          icon: 'pi pi-car',
          render: (rowData) => renderTabContent(rowData, 0, 'Transport'),
        },
        {
          header: 'Hotels',
          icon: 'pi pi-building',
          render: (rowData) => renderTabContent(rowData, 1, 'Hotels'),
        },
        {
          header: 'Catering',
          icon: 'pi pi-shopping-cart',
          render: (rowData) => renderTabContent(rowData, 2, 'Catering'),
        },
        {
          header: 'Customs',
          icon: 'pi pi-shield',
          render: (rowData) => renderTabContent(rowData, 3, 'Customs'),
        },
        {
          header: 'Migration',
          icon: 'pi pi-users',
          render: (rowData) => renderTabContent(rowData, 4, 'Migration'),
        },
        {
          header: 'Permits',
          icon: 'pi pi-file',
          render: (rowData) => renderTabContent(rowData, 5, 'Permits'),
        },
      ],
    },
    header: {
      enabled: true,
      search: {
        enabled: true,
        placeholder: 'Search flights, tails, customers...',
      },
      actions: [
        {
          name: 'addServiceOrder',
          icon: 'pi pi-plus',
          label: 'Add Flight/Service Order',
          className: 'p-button-primary p-button-sm',
          onClick: () => alert('Add new service order'),
        },
      ],
    },
    filters: [
      {
        name: 'date',
        placeholder: 'Today',
        value: 'today',
        options: [
          { label: 'Today', value: 'today' },
          { label: 'Tomorrow', value: 'tomorrow' },
          { label: 'This Week', value: 'week' },
          { label: 'This Month', value: 'month' },
        ],
        onChange: (value) => console.log('Date filter changed:', value),
      },
      {
        name: 'timezone',
        placeholder: 'UTC',
        value: 'UTC',
        options: [
          { label: 'UTC', value: 'UTC' },
          { label: 'Local', value: 'local' },
          { label: 'EST', value: 'EST' },
          { label: 'PST', value: 'PST' },
        ],
        onChange: (value) => console.log('Timezone changed:', value),
      },
    ],
    columns: [
      {
        field: 'time',
        header: 'Time/Flight/Customer',
        type: 'composite',
        className: 'time-cell',
        fields: [
          { field: 'time', className: 'time-main' },
          { field: 'flightNumber', className: 'time-sub' },
          { field: 'registration', className: 'time-detail' },
        ],
        style: { minWidth: '150px' },
      },
      {
        field: 'route',
        header: 'Route',
        type: 'route',
        style: { width: '120px' },
      },
      {
        field: 'crew',
        header: 'Crew',
        style: { width: '70px' },
      },
      {
        field: 'pax',
        header: 'Pax',
        style: { width: '70px' },
      },
      {
        field: 'fao',
        header: 'FAO',
        style: { width: '80px' },
      },
      {
        field: 'transport',
        header: 'Transport',
        type: 'status',
        clickable: true,
        tabIndex: 0,
        tabName: 'Transport',
        style: { width: '100px' },
      },
      {
        field: 'hotels',
        header: 'Hotels',
        type: 'status',
        clickable: true,
        tabIndex: 1,
        tabName: 'Hotels',
        style: { width: '100px' },
      },
      {
        field: 'catering',
        header: 'Catering',
        type: 'status',
        clickable: true,
        tabIndex: 2,
        tabName: 'Catering',
        style: { width: '100px' },
      },
      {
        field: 'customs',
        header: 'Customs',
        type: 'status',
        clickable: true,
        tabIndex: 3,
        tabName: 'Customs',
        style: { width: '100px' },
      },
      {
        field: 'migration',
        header: 'Migration',
        type: 'status',
        clickable: true,
        tabIndex: 4,
        tabName: 'Migration',
        style: { width: '100px' },
      },
      {
        field: 'slotPPR',
        header: 'Slot PPR',
        style: { width: '100px' },
      },
      {
        field: 'permits',
        header: 'Permits',
        type: 'status',
        clickable: true,
        tabIndex: 5,
        tabName: 'Permits',
        style: { width: '100px' },
      },
      {
        field: 'fuel',
        header: 'FH, WB8',
        type: 'object',
        className: 'fuel-cell',
        displayFields: [
          { field: 'price', className: 'fuel-price' },
          { field: 'unit', className: 'fuel-unit' },
        ],
        style: { width: '100px' },
      },
      {
        field: 'tasks',
        header: 'Tasks',
        style: { width: '70px' },
      },
    ],
    actions: {
      enabled: true,
      buttons: [
        {
          name: 'approve',
          icon: 'pi pi-check',
          className: 'p-button-rounded p-button-text p-button-sm',
          tooltip: 'Approve',
          onClick: (rowData) => alert(`Approve ${rowData.flightNumber}`),
        },
        {
          name: 'copy',
          icon: 'pi pi-copy',
          className: 'p-button-rounded p-button-text p-button-sm',
          tooltip: 'Copy',
          onClick: (rowData) => alert(`Copy ${rowData.flightNumber}`),
        },
        {
          name: 'more',
          icon: 'pi pi-ellipsis-v',
          className: 'p-button-rounded p-button-text p-button-sm',
          tooltip: 'More options',
          onClick: (rowData) => console.log('More options for', rowData),
        },
      ],
      style: { width: '120px' },
    },
  };

  return (
    <div className="service-orders-view">
      <DynamicServiceOrderTable config={config} data={data} onEvent={handleEvent} />
    </div>
  );
};

export default ServiceOrdersView;

