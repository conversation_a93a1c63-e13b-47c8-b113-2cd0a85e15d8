import RCMSService from '@services/RCMSService';
// import { data as alabamaCorpData } from './DemoResponse';
// import { multiComponentData as businessFormationData } from './MultiComponentDemoResponse';

import { useEffect, useState } from 'react';
// // Collection of all available form data
// export const formCollections = [
//   alabamaCorpData,
//   businessFormationData
// ];


// // Collection of all available form dataexport 
const formCollection = async () => {
  try {
    const res = await RCMSService.getAllCollection();
    return res.data;
  } catch (err) {
    console.error(err);
    return null;
  }
};


const useFormCollections = () => {
  const [data, setData] = useState([]);

  useEffect(() => {
    const loadData = async () => {
      try {
        const res = await RCMSService.getAllCollection();
        setData(res.data);
      } catch (error) {
        console.error(error);
      }
    };

    loadData();
  }, []);

  return data;
};

export default useFormCollections;


// Usage
// export const formCollections = await formCollection();

export const formCollections = [];

// Function to match product name with collection name
export const getCollectionNameForProduct = async (id) => {
  if (!id) return null;

  const data = {
    queryId: 1004,
    parameters: {
      productId: parseInt(id),
    },
  };

  try {
    const res = await RCMSService.getAllDynamicQuery(data);
    const mappingRules = res.data;
    return Array.isArray(mappingRules) && mappingRules.length>0 ? mappingRules[0].cmsApiId : null;
  } catch (err) {
    console.error(err);
    return null;
  }
};


// Function to get form data for a product
export const getFormDataForProduct = (productName) => {
  const collectionName = getCollectionNameForProduct(productName);
  if (!collectionName) return null;
  
  return formCollections.find(collection => 
    collection.collectionName === collectionName
  );
};
