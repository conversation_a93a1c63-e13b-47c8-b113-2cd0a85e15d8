import React, { useState, useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Message } from 'primereact/message';

const DynamicMessage = ({
  severity = 'info', 
  text,
  content,
  icon,
  closable = false,
  sticky = true,
  life = 3000,
  onClose,
  onRemove,
  className = '',
  style = {},
  id,
  size = 'normal',
  variant = 'filled',
  showIcon = true,
  autoHide = false,
  position = 'static', 
  onEvent,
  debug = false,
  ...props
}) => {
  const [visible, setVisible] = useState(true);
  const [timeoutId, setTimeoutId] = useState(null);

  useEffect(() => {
    if (autoHide && life > 0 && visible) {
      const id = setTimeout(() => {
        handleClose();
      }, life);
      
      setTimeoutId(id);
      
      return () => {
        if (id) {
          clearTimeout(id);
        }
      };
    }
  }, [autoHide, life, visible]);

  useEffect(() => {
    return () => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
    };
  }, [timeoutId]);

  const handleClose = useCallback(() => {
    setVisible(false);

    if (debug) {
      console.log('DynamicMessage close:', { severity, text });
    }

    if (onClose) {
      onClose();
    }

    if (onEvent) {
      onEvent('close', { severity, text, content });
    }

    setTimeout(() => {
      if (onRemove) {
        onRemove();
      }
      
      if (onEvent) {
        onEvent('remove', { severity, text, content });
      }
    }, 300);
  }, [onClose, onRemove, onEvent, severity, text, content, debug]);

  const handleMouseEnter = useCallback(() => {
    if (timeoutId) {
      clearTimeout(timeoutId);
      setTimeoutId(null);
    }

    if (onEvent) {
      onEvent('mouseEnter', { severity, text, content });
    }
  }, [timeoutId, onEvent, severity, text, content]);

  const handleMouseLeave = useCallback(() => {
    if (autoHide && life > 0 && visible) {
      const id = setTimeout(() => {
        handleClose();
      }, life);
      setTimeoutId(id);
    }

    if (onEvent) {
      onEvent('mouseLeave', { severity, text, content });
    }
  }, [autoHide, life, visible, handleClose, onEvent, severity, text, content]);

  const messageClasses = [
    'dynamic-message',
    `dynamic-message--${severity}`,
    `dynamic-message--${size}`,
    `dynamic-message--${variant}`,
    `dynamic-message--${position}`,
    !visible ? 'dynamic-message--hidden' : '',
    closable ? 'dynamic-message--closable' : '',
    className
  ].filter(Boolean).join(' ');

  const getSeverityIcon = () => {
    if (icon) return icon;
    
    switch (severity) {
      case 'success':
        return 'pi pi-check-circle';
      case 'info':
        return 'pi pi-info-circle';
      case 'warn':
        return 'pi pi-exclamation-triangle';
      case 'error':
        return 'pi pi-times-circle';
      default:
        return 'pi pi-info-circle';
    }
  };

  const renderContent = useCallback(() => {
    if (content) {
      if (typeof content === 'string') {
        return <span className="message-text">{content}</span>;
      }
      return content;
    }

    if (text) {
      return <span className="message-text">{text}</span>;
    }

    return null;
  }, [content, text]);

  const renderCloseButton = useCallback(() => {
    if (!closable) return null;

    return (
      <button
        type="button"
        className="message-close-button"
        onClick={handleClose}
        title="Close"
        aria-label="Close message"
      >
        <i className="pi pi-times" />
      </button>
    );
  }, [closable, handleClose]);

  const messageProps = {
    id,
    severity,
    text: typeof content === 'string' ? content : text,
    content: typeof content !== 'string' ? renderContent() : undefined,
    icon: showIcon ? getSeverityIcon() : null,
    className: messageClasses,
    style: {
      ...style,
      display: visible ? 'flex' : 'none'
    },
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave,
    'data-component-type': 'message',
    'data-message-severity': severity,
    'data-message-size': size,
    'data-message-variant': variant,
    ...props
  };

  Object.keys(messageProps).forEach(key => {
    if (messageProps[key] === undefined) {
      delete messageProps[key];
    }
  });

  if (!visible && !sticky) {
    return null;
  }

  return (
    <div className="dynamic-message-wrapper">
      {debug && (
        <div className="dynamic-message__debug">
          <small>
            Severity: {severity} | 
            Size: {size} | 
            Variant: {variant} | 
            AutoHide: {autoHide ? `${life}ms` : 'No'} |
            Visible: {visible ? 'Yes' : 'No'}
          </small>
        </div>
      )}
      <div className="message-container">
        <Message {...messageProps} />
        {renderCloseButton()}
      </div>
    </div>
  );
};

DynamicMessage.propTypes = {
  severity: PropTypes.oneOf(['success', 'info', 'warn', 'error']),
  text: PropTypes.string,
  content: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  icon: PropTypes.string,
  closable: PropTypes.bool,
  sticky: PropTypes.bool,
  life: PropTypes.number,
  onClose: PropTypes.func,
  onRemove: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object,
  id: PropTypes.string,
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  variant: PropTypes.oneOf(['filled', 'outlined', 'simple']),
  showIcon: PropTypes.bool,
  autoHide: PropTypes.bool,
  position: PropTypes.oneOf(['static', 'fixed-top', 'fixed-bottom']),
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicMessage;
