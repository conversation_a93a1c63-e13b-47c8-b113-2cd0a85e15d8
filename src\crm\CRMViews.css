.crm-navigation {
  background-color: white;
  border-bottom: 2px solid #e2e8f0;
  padding: 1rem 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.nav-buttons {
  display: flex;
  gap: 0.5rem;
}

.nav-button {
  background-color: transparent;
  border: 2px solid #e2e8f0;
  color: white;
  font-weight: 500;
  transition: all 0.3s ease;
}

.nav-button:hover {
  background-color:white;
  border-color: #3b82f6;
  color: #3b82f6;
}

.nav-button.active {
  background-color: #3b82f6;
  border-color: #3b82f6;
  color: white;
}

.nav-button.active:hover {
  background-color: #2563eb;
  border-color: #2563eb;
}

/* Common Styles */
.page-header {
  margin-bottom: 1.5rem;
}

.page-header h1 {
  font-size: 1.75rem;
  font-weight: 600;
  margin-bottom: 0.25rem;
  color: #1e293b;
}

.page-header p {
  color: #64748b;
  font-size: 0.875rem;
}

.breadcrumb-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  font-size: 0.875rem;
  color: #64748b;
}

.breadcrumb-separator {
  color: #cbd5e1;
}

.breadcrumb-link {
  color: #3b82f6;
  cursor: pointer;
}

.breadcrumb-link:hover {
  text-decoration: underline;
}

/* Prospect List View */
.prospect-list-view {
  padding: 1.5rem;
  background: url('/src/assets/airoplane-1.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  min-height: 100vh;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-card {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
/* Override PrimeReact card styles */
.p-card, 
.p-card.p-component 
 {
  background: transparent !important;
  box-shadow: none !important;
  border: none !important;
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stat-details {
  flex: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: #1e293b;
}

.stat-label {
  font-size: 0.875rem;
  color: #64748b;
  margin-top: 0.25rem;
}

.prospects-table-card {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-left h2 {
  font-size: 1.25rem;
  font-weight: 600;
  margin: 0;
  color: #1e293b;
}

.prospect-count {
  font-size: 0.875rem;
  color: #64748b;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.account-cell strong {
  display: block;
  color: #1e293b;
  font-weight: 600;
}

.account-cell .text-sm {
  font-size: 0.75rem;
  color: #64748b;
}

.text-sm {
  font-size: 0.875rem;
}

.text-gray-600 {
  color: #64748b;
}

.tags-cell {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.action-buttons {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.prospects-datatable tbody tr {
  cursor: pointer;
}

.prospects-datatable tbody tr:hover {
  background-color: #f8fafc;
}

/* Customer List View */
.customer-list-view {
  padding: 1.5rem;
  background: url('/src/assets/airoplane-1.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  min-height: 100vh;
}

.customers-table-card {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.company-cell strong {
  display: block;
  color: #1e293b;
  font-weight: 600;
}

.customers-datatable tbody tr {
  cursor: pointer;
}

.customers-datatable tbody tr:hover {
  background-color: #f8fafc;
}

.filter-dropdown {
  min-width: 150px;
}

/* Prospect Detail View */
.prospect-detail-view {
  padding: 1.5rem;
}

.pipeline-stages {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin: 2rem 0;
  padding: 1.5rem;
  background-color: #f8fafc;
  border-radius: 8px;
}

.pipeline-stage {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background-color: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.pipeline-stage.active {
  border-color: #3b82f6;
  background-color: #eff6ff;
}

.stage-number {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #e2e8f0;
  color: #64748b;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 1.125rem;
}

.pipeline-stage.active .stage-number {
  background-color: #3b82f6;
  color: white;
}

.stage-info {
  text-align: left;
}

.stage-label {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.stage-sublabel {
  font-size: 0.75rem;
  color: #64748b;
}

.action-buttons-bar {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
}

.prospect-detail-card {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.detail-section {
  margin-bottom: 1.5rem;
}

.detail-section h3 {
  font-size: 1.125rem;
  font-weight: 600;
  margin-bottom: 1rem;
  color: #1e293b;
}

.section-title-blue {
  color: #3b82f6 !important;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
}

.form-field {
  display: flex;
  /* flex-direction: column; */
}

.form-field label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #475569;
  margin-bottom: 0.5rem;
}

.form-field .p-inputtext,
.form-field .p-dropdown {
  width: 100%;
}

.tags-input {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
  padding: 0.5rem;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  min-height: 42px;
  align-items: center;
}

.tab-content {
  padding: 1.5rem;
  min-height: 200px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .table-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-right {
    flex-direction: column;
    align-items: stretch;
  }

  .pipeline-stages {
    flex-direction: column;
    gap: 1rem;
  }

  .action-buttons-bar {
    flex-direction: column;
  }

  .action-buttons-bar .p-button {
    width: 100%;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }
}

/* PrimeReact Overrides */
.p-datatable .p-datatable-thead > tr > th {
  /* background-color: #334155;
  color: white; */
  font-weight: 600;
  padding: 1rem;
}

.p-datatable .p-datatable-tbody > tr > td {
  padding: 0.75rem 1rem;
}

.p-card .p-card-body {
  padding: 1.5rem;
}

.p-tag {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.p-button-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

