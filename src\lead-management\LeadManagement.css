/* Lead Management Root Styles */
.lead-management-root {
  padding: 2rem;
  background: url('/src/assets/airoplane-1.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.page-header p {
  color: #6c757d;
  font-size: 1rem;
}

/* Statistics Grid */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
}

.stat-details {
  flex: 1;
}

.stat-value {
  font-size: 1.75rem;
  font-weight: 700;
  color: #2c3e50;
  display: block;
}

.stat-label {
  font-size: 0.875rem;
  color: #6c757d;
  display: block;
  margin-top: 0.25rem;
}

/* Leads Table */
.leads-table-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  flex-wrap: wrap;
  gap: 1rem;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-left h2 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.lead-count {
  background-color: #e3f2fd;
  color: #2196F3;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 600;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.company-cell strong {
  display: block;
  color: #2c3e50;
  font-weight: 600;
}

.text-sm {
  font-size: 0.875rem;
}

.text-gray-600 {
  color: #6c757d;
}

.rating-stars {
  display: flex;
  gap: 0.25rem;
}

.action-buttons {
  display: flex;
  gap: 0.25rem;
  justify-content: center;
}

/* Lead Detail View */
.lead-detail-view {
  padding: 2rem;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.lead-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.lead-breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.breadcrumb-text {
  font-size: 1rem;
  color: #6c757d;
}

.company-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.company-selector label {
  font-weight: 600;
  color: #2c3e50;
}

.company-dropdown {
  min-width: 300px;
}

/* Wizard Steps */
.wizard-steps {
  background: white;
  padding: 1.5rem;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  align-items: center;
}

.activities-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
  padding: 0.5rem 1rem;
  background-color: #f8f9fa;
  border-radius: 6px;
}

.activity-count {
  background-color: #2196F3;
  color: white;
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* General Section */
.general-section {
  background: white;
  border-radius: 8px;
  margin-bottom: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.form-column {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 0.875rem;
}

.field-hint {
  font-size: 0.75rem;
  color: #6c757d;
  font-style: italic;
  margin-top: 0.25rem;
}

.field-hint.text-warning {
  color: #ff9800;
}

.lead-status-field {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-badge {
  flex: 1;
  padding: 0.5rem 1rem;
  background-color: #e3f2fd;
  color: #2196F3;
  border-radius: 6px;
  text-align: center;
  font-weight: 600;
}

/* Tabs Section */
.tabs-section {
  background: white;
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Address Section */
.address-section {
  margin-top: 1rem;
}

.address-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1.5rem;
}

.address-form-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.address-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

/* Contact Information Tab */
.contact-information-tab {
  margin-top: 1rem;
}

.contact-name-cell {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.contact-details {
  display: flex;
  flex-direction: column;
}

.contact-name {
  font-weight: 600;
  color: #2c3e50;
}

.contact-title {
  font-size: 0.875rem;
  color: #6c757d;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.contact-info i {
  margin-right: 0.5rem;
  color: #6c757d;
}

/* Compliance Tab */
.compliance-information-tab {
  margin-top: 1rem;
}

.compliance-card {
  margin-top: 1rem;
}

.compliance-section {
  margin-bottom: 2rem;
}

.compliance-section h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1rem;
}

.score-display {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.score-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: white;
}

.score-value {
  font-size: 2rem;
  font-weight: 700;
}

.score-label {
  font-size: 0.875rem;
}

.score-details {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.score-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 1rem;
}

.score-item i {
  font-size: 1.5rem;
}

.document-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.document-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.document-info {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.document-details {
  display: flex;
  flex-direction: column;
}

.document-name {
  font-weight: 600;
  color: #2c3e50;
}

.document-date {
  font-size: 0.875rem;
  color: #6c757d;
}

.key-data-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
}

.key-data-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.key-data-item label {
  font-weight: 600;
  color: #6c757d;
  font-size: 0.875rem;
}

.key-data-value {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.compliance-actions {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

/* Notes & Messages Tab */
.notes-messages-tab {
  margin-top: 1rem;
}

.notes-messages-card {
  margin-top: 1rem;
}

.message-input-section {
  margin-bottom: 2rem;
}

.message-input {
  width: 100%;
  margin-bottom: 1rem;
}

.message-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.messages-thread {
  margin-top: 2rem;
}

.messages-thread h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 1.5rem;
}

.message-item {
  margin-bottom: 1.5rem;
}

.message-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 0.75rem;
}

.message-meta {
  flex: 1;
}

.message-user {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.user-role {
  font-size: 0.875rem;
  color: #6c757d;
  font-weight: normal;
}

.message-timestamp {
  font-size: 0.875rem;
  color: #6c757d;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.message-type i {
  font-size: 1.25rem;
  color: #6c757d;
}

.message-content {
  padding-left: 3.5rem;
}

.message-content p {
  color: #2c3e50;
  line-height: 1.6;
  margin: 0;
}

.quick-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
  justify-content: center;
}

/* Activity Logs Tab */
.activity-logs-tab {
  margin-top: 1rem;
}

.activity-logs-card {
  margin-top: 1rem;
}

.activity-filters {
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
}

.filter-dropdown,
.filter-calendar {
  min-width: 200px;
}

.activity-timeline {
  margin-top: 2rem;
}

.activity-marker {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.25rem;
}

.activity-card {
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.activity-title {
  display: flex;
  align-items: center;
}

.activity-timestamp {
  font-size: 0.875rem;
  color: #6c757d;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.activity-description {
  color: #2c3e50;
  margin-bottom: 0.75rem;
  line-height: 1.6;
}

.activity-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-user {
  font-size: 0.875rem;
  color: #6c757d;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.load-more-section {
  display: flex;
  justify-content: center;
  margin-top: 2rem;
}

/* Documentation Tab */
.documentation-tab {
  margin-top: 1rem;
}

.documentation-card {
  margin-top: 1rem;
}

.upload-section {
  margin-bottom: 2rem;
}

.upload-placeholder {
  text-align: center;
  color: #6c757d;
  padding: 2rem;
}

.file-name-cell {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.file-details {
  display: flex;
  flex-direction: column;
}

.file-name {
  font-weight: 600;
  color: #2c3e50;
}

.file-size {
  font-size: 0.875rem;
  color: #6c757d;
}

.upload-info {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-size: 0.875rem;
}

.upload-info i {
  margin-right: 0.5rem;
  color: #6c757d;
}

.document-stats {
  display: flex;
  gap: 2rem;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid #dee2e6;
  justify-content: center;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.stat-item i {
  font-size: 1.25rem;
  color: #2196F3;
}

/* Footer */
.lead-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2rem;
  padding: 1rem;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #2c3e50;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .lead-management-root,
  .lead-detail-view {
    padding: 1rem;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .address-row {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .table-header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header-right {
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
  }

  .activities-section {
    margin-left: 0;
  }
}

