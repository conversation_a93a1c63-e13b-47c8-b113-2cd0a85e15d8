import React from "react";
import { Button } from "primereact/button";
import PropTypes from "prop-types";
import { useLocation } from "react-router-dom";

const PageHeader = ({ onBack, base = "Dashboard", title = "", showActions = false }) => {
  const { pathname } = useLocation(); 
  const lastPart = pathname?.split("/")?.pop(); // "manage-users"
  title = title || lastPart;

  // add pages to skip where we don't want to show the header
  const skipPages = ["dashboard", "asm-employee-dashboard", "client-dashboard"];

  if (!lastPart || skipPages.includes(lastPart)) return null;

  // format the title to display in the header
  const formatTitle = (title) => {
    return title.replace(/-/g, " ").replace(/\b\w/g, (c) => c.toUpperCase());
  };

  return (
    <div className="glass-header">
      <div className="glass-header-left">
        <Button
          icon="pi pi-arrow-left"
          className="glass-btn-text"
          onClick={onBack}
          tooltip="Go Back"
        />
        <span
          style={{
            color: "#193276",
            fontSize: "14px",
            fontWeight: "400",
          }}
        >
          {base} /{" "}
          <span>{formatTitle(title)}</span>
        </span>
      </div>

      {showActions && (
        <div className="glass-header-right">
          <Button
            icon="pi pi-bell"
            className="glass-btn-text"
            tooltip="Notifications"
          />
          <Button
            icon="pi pi-cog"
            className="glass-btn-text"
            tooltip="Settings"
          />
        </div>
      )}
    </div>
  );
};

PageHeader.propTypes = {
  onBack: PropTypes.func.isRequired,
  title: PropTypes.string,
  showActions: PropTypes.bool,
};

export default PageHeader;
