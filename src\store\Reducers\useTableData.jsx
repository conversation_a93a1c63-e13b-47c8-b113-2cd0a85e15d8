import { useQuery } from "@tanstack/react-query";
import { useDispatch, useSelector } from "react-redux";
import axios from "axios";
import { useEffect, useRef } from "react";
import { setTableData } from "../tableSlice";
import logger from "@utils/logger";
import { tableJson } from "@utils/TableConfig";



// Create a logger for table data operations
const tableLogger = logger.createLogger('TableData');

/**
 * Fetch table data from the API
 * @param {string} tableName - The name of the table to fetch
 * @returns {Promise<Object>} - The fetched data or an empty array if there's an error
 */
const fetchTableData = async (tableName) => {
    // Check if the table configuration exists
    if (!tableJson[tableName]) {
        tableLogger.error(`Table configuration not found for: ${tableName}`);
        return []; // Return empty array instead of throwing error
    }

    const TENANTID = JSON.parse(localStorage.getItem("X-TenantID"));

    const endpoint = tableJson[tableName];

    try {
        // Add timestamp to track API call duration
        const startTime = Date.now();

        tableLogger.debug(`Starting API call for table: ${tableName}`, {
            endpoint: tableJson[tableName],
            timestamp: new Date().toISOString()
        });

        // Make the API request
        const response = await axios.get(`${endpoint}`, {
            // headers: authHeader(),
            // Add timeout to prevent hanging requests
            timeout: 30000 // 30 seconds
        });

        const duration = Date.now() - startTime;

        // Log successful API call with duration
        tableLogger.info(`Fetched ${tableName} in ${duration}ms`, {
            duration,
            recordCount: Array.isArray(response.data) ? response.data.length : 'unknown',
            timestamp: new Date().toISOString()
        });

        // Return the data (ensure it's always an array)
        return Array.isArray(response.data) ? response.data : (response.data ? [response.data] : []);
    } catch (error) {
        // Log detailed error information
        const errorInfo = {
            message: error.message,
            status: error.response?.status,
            statusText: error.response?.statusText,
            endpoint: tableJson[tableName],
            timestamp: new Date().toISOString()
        };

        tableLogger.error(`Error fetching ${tableName}`, errorInfo);

        // Return empty array instead of throwing error to prevent blank pages
        return [];
    }
};

const useTableData = (tableName) => {
    const dispatch = useDispatch();
    const tableData = useSelector((state) => {
      console.log(state)
      state.table[tableName]
    });

    // Use a ref to track if this is the first render
    const isFirstRender = useRef(true);
    // Track if we've already dispatched for this table
    const hasDispatched = useRef(false);

    // Use React Query with proper caching strategy
    const {
      data,
      isLoading,
      isError,
      error,
      refetch,
    } = useQuery({
      queryKey: [tableName],
      queryFn: async () => {
        tableLogger.info(`Fetching data for table: ${tableName}`);        

        // Only fetch if we don't have data in Redux store
        if (tableData && tableData.length > 0) {
          tableLogger.debug(`Using cached data for table: ${tableName}`);
          return tableData;
        }

        // Fetch data from API
        const data = await fetchTableData(tableName);
        // Only dispatch if we haven't already for this table
        if (!hasDispatched.current) {
          tableLogger.debug(`Updating Redux store for table: ${tableName}`);
          dispatch(setTableData({ tableName, data }));
          hasDispatched.current = true;
        }

        return data;
      },
      // Cache for 4 hours (14400000 ms)
      staleTime: 14400000,
      // Only run the query if the table configuration exists
      enabled: !!tableJson[tableName],
      // Prevent refetching on window focus to avoid infinite loops
      refetchOnWindowFocus: false,
      // Prevent refetching when the component remounts
      refetchOnMount: false,
      // Log errors
      onError: (err) => {
        tableLogger.error(`Error fetching ${tableName}:`, {
          message: err.message,
          stack: err.stack,
          timestamp: new Date().toISOString()
        });
      },
    });

    // Effect to handle component mount/unmount and prevent unnecessary API calls
    useEffect(() => {
      if (isFirstRender.current) {
        tableLogger.debug(`Component mounted for table: ${tableName}`);
        isFirstRender.current = false;
      }

      // Cleanup function
      return () => {
        tableLogger.debug(`Component unmounted for table: ${tableName}`);
        // Reset the dispatch flag when component unmounts
        hasDispatched.current = false;
      };
    }, [tableName]);

    // Return the query results
    return { data, isLoading, isError, error, refetch };
  };

  export default useTableData;