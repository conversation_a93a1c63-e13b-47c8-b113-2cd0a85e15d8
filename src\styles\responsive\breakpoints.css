
/* Breakpoint Variables */
:root {
  --breakpoint-xs: 0px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;
}

/* Base Mobile-First Styles */
.container {
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

/* Container Max Widths */
@media (min-width: 640px) {
  .container {
    max-width: 640px;
    padding-left: var(--spacing-6);
    padding-right: var(--spacing-6);
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px;
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px;
    padding-left: var(--spacing-8);
    padding-right: var(--spacing-8);
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px;
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px;
  }
}

/* Container Variants */
.container-fluid {
  width: 100%;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

.container-sm {
  max-width: 640px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

.container-md {
  max-width: 768px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

.container-lg {
  max-width: 1024px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

.container-xl {
  max-width: 1280px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

.container-2xl {
  max-width: 1536px;
  margin-left: auto;
  margin-right: auto;
  padding-left: var(--spacing-4);
  padding-right: var(--spacing-4);
}

/* Responsive Display Utilities */
.hidden {
  display: none !important;
}

.block {
  display: block !important;
}

.inline-block {
  display: inline-block !important;
}

.inline {
  display: inline !important;
}

.flex {
  display: flex !important;
}

.inline-flex {
  display: inline-flex !important;
}

.grid {
  display: grid !important;
}

.inline-grid {
  display: inline-grid !important;
}

/* Small screens and up (sm) */
@media (min-width: 640px) {
  .sm\:hidden {
    display: none !important;
  }
  
  .sm\:block {
    display: block !important;
  }
  
  .sm\:inline-block {
    display: inline-block !important;
  }
  
  .sm\:inline {
    display: inline !important;
  }
  
  .sm\:flex {
    display: flex !important;
  }
  
  .sm\:inline-flex {
    display: inline-flex !important;
  }
  
  .sm\:grid {
    display: grid !important;
  }
  
  .sm\:inline-grid {
    display: inline-grid !important;
  }
}

/* Medium screens and up (md) */
@media (min-width: 768px) {
  .md\:hidden {
    display: none !important;
  }
  
  .md\:block {
    display: block !important;
  }
  
  .md\:inline-block {
    display: inline-block !important;
  }
  
  .md\:inline {
    display: inline !important;
  }
  
  .md\:flex {
    display: flex !important;
  }
  
  .md\:inline-flex {
    display: inline-flex !important;
  }
  
  .md\:grid {
    display: grid !important;
  }
  
  .md\:inline-grid {
    display: inline-grid !important;
  }
}

/* Large screens and up (lg) */
@media (min-width: 1024px) {
  .lg\:hidden {
    display: none !important;
  }
  
  .lg\:block {
    display: block !important;
  }
  
  .lg\:inline-block {
    display: inline-block !important;
  }
  
  .lg\:inline {
    display: inline !important;
  }
  
  .lg\:flex {
    display: flex !important;
  }
  
  .lg\:inline-flex {
    display: inline-flex !important;
  }
  
  .lg\:grid {
    display: grid !important;
  }
  
  .lg\:inline-grid {
    display: inline-grid !important;
  }
}

/* Extra large screens and up (xl) */
@media (min-width: 1280px) {
  .xl\:hidden {
    display: none !important;
  }
  
  .xl\:block {
    display: block !important;
  }
  
  .xl\:inline-block {
    display: inline-block !important;
  }
  
  .xl\:inline {
    display: inline !important;
  }
  
  .xl\:flex {
    display: flex !important;
  }
  
  .xl\:inline-flex {
    display: inline-flex !important;
  }
  
  .xl\:grid {
    display: grid !important;
  }
  
  .xl\:inline-grid {
    display: inline-grid !important;
  }
}

/* 2X Large screens and up (2xl) */
@media (min-width: 1536px) {
  .\32xl\:hidden {
    display: none !important;
  }
  
  .\32xl\:block {
    display: block !important;
  }
  
  .\32xl\:inline-block {
    display: inline-block !important;
  }
  
  .\32xl\:inline {
    display: inline !important;
  }
  
  .\32xl\:flex {
    display: flex !important;
  }
  
  .\32xl\:inline-flex {
    display: inline-flex !important;
  }
  
  .\32xl\:grid {
    display: grid !important;
  }
  
  .\32xl\:inline-grid {
    display: inline-grid !important;
  }
}

/* Responsive Text Alignment */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.text-justify {
  text-align: justify;
}

@media (min-width: 640px) {
  .sm\:text-left {
    text-align: left;
  }
  
  .sm\:text-center {
    text-align: center;
  }
  
  .sm\:text-right {
    text-align: right;
  }
  
  .sm\:text-justify {
    text-align: justify;
  }
}

@media (min-width: 768px) {
  .md\:text-left {
    text-align: left;
  }
  
  .md\:text-center {
    text-align: center;
  }
  
  .md\:text-right {
    text-align: right;
  }
  
  .md\:text-justify {
    text-align: justify;
  }
}

@media (min-width: 1024px) {
  .lg\:text-left {
    text-align: left;
  }
  
  .lg\:text-center {
    text-align: center;
  }
  
  .lg\:text-right {
    text-align: right;
  }
  
  .lg\:text-justify {
    text-align: justify;
  }
}

@media (min-width: 1280px) {
  .xl\:text-left {
    text-align: left;
  }
  
  .xl\:text-center {
    text-align: center;
  }
  
  .xl\:text-right {
    text-align: right;
  }
  
  .xl\:text-justify {
    text-align: justify;
  }
}

@media (min-width: 1536px) {
  .\32xl\:text-left {
    text-align: left;
  }
  
  .\32xl\:text-center {
    text-align: center;
  }
  
  .\32xl\:text-right {
    text-align: right;
  }
  
  .\32xl\:text-justify {
    text-align: justify;
  }
}
