import ApiService from '../ApiService.js';

class LeadAddressService {
	async save(addressData) {
		return await new ApiService().request('POST', '/LeadAddress/save', addressData);
	}
	async update(id, addressData) {
		return await new ApiService().request('PUT', `/LeadAddress/update/${id}`, addressData);
	}
	async deleteById(id) {
		return await new ApiService().request('DELETE', `/LeadAddress/deleteById/${id}`);
	}
		async getAll() {
			return await new ApiService().request('GET', '/LeadAddress/getAll');
		}
		async getById(id) {
			return await new ApiService().request('GET', `/LeadAddress/getById/${id}`);
		}
}

export default new LeadAddressService();
