/* Service Orders Table Styles */

.service-orders-container {
  padding: 1.5rem;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Table Header */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  margin-bottom: 1rem;
}

.header-left h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #1e293b;
}

.header-right {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.header-right .p-inputtext {
  min-width: 300px;
}

/* Table Customization */
.service-orders-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.service-orders-table .p-datatable-thead > tr > th {
  background: #f1f5f9;
  color: #64748b;
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  padding: 0.75rem;
  border: none;
}

.service-orders-table .p-datatable-tbody > tr {
  background: white;
  border-bottom: 1px solid #e2e8f0;
}

.service-orders-table .p-datatable-tbody > tr:hover {
  background: #f8fafc;
}

.service-orders-table .p-datatable-tbody > tr > td {
  padding: 1rem 0.75rem;
  border: none;
}

/* Time Cell */
.time-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.departure-time {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.arrival-time {
  font-size: 0.75rem;
  color: #64748b;
}

/* Flight Cell */
.flight-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.flight-number {
  font-weight: 600;
  color: #3b82f6;
  font-size: 0.875rem;
}

.registration {
  font-size: 0.75rem;
  color: #64748b;
}

/* Route Cell */
.route-cell {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.route-from,
.route-to {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.route-arrow {
  color: #94a3b8;
  font-size: 0.75rem;
}

/* Status Tags */
.status-tag {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
}

/* Fuel Cell */
.fuel-cell {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.fuel-price {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
}

.fuel-unit {
  font-size: 0.75rem;
  color: #64748b;
}

/* Tasks Cell */
.tasks-cell {
  text-align: center;
}

.task-indicator {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e2e8f0;
  color: #64748b;
  font-size: 0.75rem;
  font-weight: 600;
}

.task-indicator.has-tasks {
  background: #dbeafe;
  color: #3b82f6;
}

/* Actions Cell */
.actions-cell {
  display: flex;
  gap: 0.25rem;
  justify-content: flex-end;
}

.actions-cell .p-button {
  width: 2rem;
  height: 2rem;
}

/* Progress Bar */
.progress-bar-container {
  padding: 0.5rem 0.75rem;
  background: white;
}

.progress-bar {
  position: relative;
  height: 4px;
  background: #e2e8f0;
  border-radius: 2px;
  overflow: hidden;
}

.progress-catering {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, #10b981, #059669);
  transition: width 0.3s ease;
}

.progress-migration {
  position: absolute;
  right: 0;
  top: 0;
  height: 100%;
  background: linear-gradient(90deg, #ef4444, #dc2626);
  transition: width 0.3s ease;
}

/* Expanded Row Content */
.expanded-row-content {
  background: #f8fafc;
  padding: 1.5rem;
  border-top: 2px solid #e2e8f0;
}

.expanded-row-content .p-tabview {
  background: transparent;
}

.expanded-row-content .p-tabview-nav {
  background: white;
  border-radius: 8px 8px 0 0;
  border: none;
  padding: 0.5rem 1rem;
}

.expanded-row-content .p-tabview-panels {
  background: white;
  border-radius: 0 0 8px 8px;
  padding: 1.5rem;
}

/* Catering Expansion */
.catering-expansion {
  padding: 1rem 0;
}

.catering-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.catering-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 1.5rem;
}

.catering-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e2e8f0;
}

.catering-title {
  font-weight: 600;
  color: #1e293b;
  font-size: 1rem;
}

.catering-location {
  font-weight: 600;
  color: #64748b;
  font-size: 0.875rem;
}

.catering-header i {
  color: #94a3b8;
  font-size: 0.875rem;
}

/* Catering Form */
.catering-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 100px 1fr;
  gap: 0.75rem;
  align-items: center;
}

.form-row.two-cols {
  grid-template-columns: 100px 1fr 80px 1fr;
}

.form-row label {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

.form-row .p-dropdown,
.form-row .p-inputtext {
  width: 100%;
}

.file-upload-section {
  border: 2px dashed #cbd5e1;
  border-radius: 6px;
  padding: 2rem;
  text-align: center;
  color: #64748b;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
}

.file-upload-section:hover {
  border-color: #3b82f6;
  background: #f8fafc;
}

/* Form Actions */
.form-actions {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.send-quote-only {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #64748b;
  cursor: pointer;
  margin-left: auto;
}

.send-quote-only input[type="checkbox"] {
  cursor: pointer;
}

/* Expenses Section */
.expenses-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 1rem;
  border-top: 1px solid #e2e8f0;
}

.expenses-count {
  font-size: 0.875rem;
  color: #64748b;
  font-weight: 500;
}

/* Expansion Section */
.expansion-section {
  padding: 1rem;
}

/* Responsive */
@media (max-width: 1200px) {
  .catering-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .header-right {
    flex-wrap: wrap;
    width: 100%;
  }

  .header-right .p-inputtext {
    min-width: 100%;
  }
}

