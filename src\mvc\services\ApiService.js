import { logger } from '../../core/logging/logger.js';
import securityService from '../../core/security/securityService.js';
import sqlInjectionProtectionService from './SQLInjectionProtectionService.js';

class ApiService {
  constructor() {
    this.baseURL = import.meta.env?.VITE_API_BASE_URL || '/api';
    this.timeout = 30000; 
    this.retryAttempts = 3;
    this.retryDelay = 1000; 
    this.csrfToken = null;
    this.requestInterceptors = [];
    this.responseInterceptors = [];
    this.initializeCSRF();
  }

  async initializeCSRF() {
    try {
      this.csrfToken = await securityService.generateCSRFToken();
      logger.info('CSRF token initialized for API service');
    } catch (error) {
      logger.error('Failed to initialize CSRF token', { error: error.message });
    }
  }

  validateRequestData(data, options = {}) {
    if (!data || typeof data !== 'object') {
      return { isValid: true, sanitized: data, threats: [] };
    }

    const result = {
      isValid: true,
      sanitized: {},
      threats: [],
      riskLevel: 'none'
    };

    const processValue = (value, key) => {
      if (typeof value === 'string') {
        const validation = sqlInjectionProtectionService.validateInput(value, {
          ...options,
          field: key
        });
        
        if (!validation.isValid) {
          result.isValid = false;
          result.threats.push(...validation.threats.map(threat => ({
            ...threat,
            field: key
          })));
        }
        
        if (validation.riskLevel === 'high') {
          result.riskLevel = 'high';
        } else if (validation.riskLevel === 'medium' && result.riskLevel !== 'high') {
          result.riskLevel = 'medium';
        }
        
        return validation.sanitized;
      } else if (Array.isArray(value)) {
        return value.map((item, index) => processValue(item, `${key}[${index}]`));
      } else if (value && typeof value === 'object') {
        const sanitizedObj = {};
        Object.keys(value).forEach(subKey => {
          sanitizedObj[subKey] = processValue(value[subKey], `${key}.${subKey}`);
        });
        return sanitizedObj;
      }
      
      return value;
    };

    Object.keys(data).forEach(key => {
      result.sanitized[key] = processValue(data[key], key);
    });

    return result;
  }

  async request(method, endpoint, data = null, options = {}) {
    const requestId = this.generateRequestId();
    const startTime = Date.now();

    try {
      let validatedData = data;
      if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH')) {
        const validation = this.validateRequestData(data, {
          userAgent: navigator.userAgent,
          endpoint: endpoint,
          method: method
        });

        if (!validation.isValid) {
          const error = new Error('Request blocked due to security threats');
          error.securityThreats = validation.threats;
          error.riskLevel = validation.riskLevel;
          throw error;
        }

        validatedData = validation.sanitized;
        
        if (validation.threats.length > 0) {
          logger.warn('Security threats detected in API request', {
            requestId,
            endpoint,
            method,
            threats: validation.threats.length,
            riskLevel: validation.riskLevel
          });
        }
      }

      const config = {
        method: method.toUpperCase(),
        headers: {
          'Content-Type': 'application/json',
          'X-Requested-With': 'XMLHttpRequest',
          'X-Request-ID': requestId,
          ...options.headers
        },
        ...options
      };

      if (['POST', 'PUT', 'PATCH', 'DELETE'].includes(config.method) && this.csrfToken) {
        config.headers['X-CSRF-Token'] = this.csrfToken;
      }

      if (validatedData && ['POST', 'PUT', 'PATCH'].includes(config.method)) {
        config.body = JSON.stringify(validatedData);
      }

      const url = this.buildURL(endpoint, method === 'GET' ? validatedData : null);

      await this.applyRequestInterceptors(config, url);

      const response = await this.makeRequestWithRetry(url, config, requestId);

      await this.applyResponseInterceptors(response, requestId);

      const result = await this.parseResponse(response, requestId);

      const duration = Date.now() - startTime;
      logger.info('API request completed', {
        requestId,
        method: config.method,
        endpoint,
        status: response.status,
        duration
      });

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      
      logger.error('API request failed', {
        requestId,
        method,
        endpoint,
        error: error.message,
        duration,
        securityThreats: error.securityThreats || [],
        riskLevel: error.riskLevel || 'none'
      });

      if (error.securityThreats) {
        logger.security('api_request_blocked', {
          requestId,
          endpoint,
          method,
          threats: error.securityThreats,
          riskLevel: error.riskLevel
        });
      }

      throw this.enhanceError(error, requestId, endpoint, method);
    }
  }

  async makeRequestWithRetry(url, config, requestId, attempt = 1) {
    try {
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), this.timeout);

      const response = await fetch(url, {
        ...config,
        signal: controller.signal
      });

      clearTimeout(timeoutId);

      if (response.status === 429) {
        const retryAfter = response.headers.get('Retry-After');
        const delay = retryAfter ? parseInt(retryAfter) * 1000 : this.retryDelay * attempt;
        
        if (attempt < this.retryAttempts) {
          logger.warn('Rate limited, retrying request', {
            requestId,
            attempt,
            delay,
            retryAfter
          });
          
          await this.delay(delay);
          return this.makeRequestWithRetry(url, config, requestId, attempt + 1);
        }
      }

      if (response.status >= 500 && attempt < this.retryAttempts) {
        logger.warn('Server error, retrying request', {
          requestId,
          status: response.status,
          attempt
        });
        
        await this.delay(this.retryDelay * attempt);
        return this.makeRequestWithRetry(url, config, requestId, attempt + 1);
      }

      return response;

    } catch (error) {
      if (error.name === 'AbortError') {
        throw new Error(`Request timeout after ${this.timeout}ms`);
      }

      if (attempt < this.retryAttempts && this.isRetryableError(error)) {
        logger.warn('Network error, retrying request', {
          requestId,
          error: error.message,
          attempt
        });
        
        await this.delay(this.retryDelay * attempt);
        return this.makeRequestWithRetry(url, config, requestId, attempt + 1);
      }

      throw error;
    }
  }

  buildURL(endpoint, params = null) {
    let url = `${this.baseURL}${endpoint}`;

    console.log(this.baseURL)
    
    if (params && typeof params === 'object') {
      const searchParams = new URLSearchParams();
      
      Object.keys(params).forEach(key => {
        const value = params[key];
        if (value !== null && value !== undefined) {
          if (Array.isArray(value)) {
            value.forEach(item => searchParams.append(key, item));
          } else {
            searchParams.append(key, value);
          }
        }
      });
      
      const queryString = searchParams.toString();
      if (queryString) {
        url += `?${queryString}`;
      }
    }
    
    return url;
  }

  async parseResponse(response, requestId) {
    const contentType = response.headers.get('content-type');
    
    if (!response.ok) {
      const error = new Error(`HTTP ${response.status}: ${response.statusText}`);
      error.status = response.status;
      error.statusText = response.statusText;
      error.requestId = requestId;
      
      try {
        if (contentType && contentType.includes('application/json')) {
          error.data = await response.json();
        } else {
          error.data = await response.text();
        }
      } catch (parseError) {
        logger.warn('Failed to parse error response', { 
          requestId, 
          parseError: parseError.message 
        });
      }
      
      throw error;
    }

    if (contentType && contentType.includes('application/json')) {
      return await response.json();
    } else if (contentType && contentType.includes('text/')) {
      return await response.text();
    } else {
      return await response.blob();
    }
  }

  async applyRequestInterceptors(config, url) {
    for (const interceptor of this.requestInterceptors) {
      try {
        await interceptor(config, url);
      } catch (error) {
        logger.error('Request interceptor failed', { error: error.message });
      }
    }
  }

  async applyResponseInterceptors(response, requestId) {
    for (const interceptor of this.responseInterceptors) {
      try {
        await interceptor(response, requestId);
      } catch (error) {
        logger.error('Response interceptor failed', { 
          requestId, 
          error: error.message 
        });
      }
    }
  }

  generateRequestId() {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  isRetryableError(error) {
    return error.name === 'TypeError' || 
           error.name === 'NetworkError' ||
           error.message.includes('fetch');
  }

  enhanceError(error, requestId, endpoint, method) {
    error.requestId = requestId;
    error.endpoint = endpoint;
    error.method = method;
    error.timestamp = new Date().toISOString();
    return error;
  }

  async get(endpoint, params, options) {
    return this.request('GET', endpoint, params, options);
  }

  async post(endpoint, data, options) {
    return this.request('POST', endpoint, data, options);
  }

  async put(endpoint, data, options) {
    return this.request('PUT', endpoint, data, options);
  }

  async patch(endpoint, data, options) {
    return this.request('PATCH', endpoint, data, options);
  }

  async delete(endpoint, options) {
    return this.request('DELETE', endpoint, null, options);
  }

  addRequestInterceptor(interceptor) {
    this.requestInterceptors.push(interceptor);
  }

  addResponseInterceptor(interceptor) {
    this.responseInterceptors.push(interceptor);
  }

  removeRequestInterceptor(interceptor) {
    const index = this.requestInterceptors.indexOf(interceptor);
    if (index > -1) {
      this.requestInterceptors.splice(index, 1);
    }
  }

  removeResponseInterceptor(interceptor) {
    const index = this.responseInterceptors.indexOf(interceptor);
    if (index > -1) {
      this.responseInterceptors.splice(index, 1);
    }
  }

  setBaseURL(url) {
    this.baseURL = url;
  }

  setTimeout(timeout) {
    this.timeout = timeout;
  }

  setRetryConfig(attempts, delay) {
    this.retryAttempts = attempts;
    this.retryDelay = delay;
  }

  getStats() {
    return {
      baseURL: this.baseURL,
      timeout: this.timeout,
      retryAttempts: this.retryAttempts,
      retryDelay: this.retryDelay,
      requestInterceptors: this.requestInterceptors.length,
      responseInterceptors: this.responseInterceptors.length,
      csrfEnabled: !!this.csrfToken
    };
  }
}


export default ApiService;
