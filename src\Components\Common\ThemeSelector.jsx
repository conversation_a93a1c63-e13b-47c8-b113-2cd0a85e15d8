import React, { useState, useEffect } from 'react';
import { Dropdown } from 'primereact/dropdown';
import { useTranslation } from 'react-i18next';
import logger from '../../Utils/logger';
import DynamicBackground from './DynamicBackground';

const themeLogger = logger.createLogger('ThemeSelector');

const ThemeSelector = () => {
  const { t } = useTranslation();
  const [selectedTheme, setSelectedTheme] = useState(localStorage.getItem('theme') || 'default');

  const themes = [
    {
      name: 'Default',
      value: 'default',
      color: '#E2085D',
      description: 'default color',
      variables: {
        '--button-primary-bg': '#E2085D',
        '--accent-color': '#E2085D'
      }
    },
    {
      name: 'Dark Mode',
      value: 'dark',
      color: '#1e293b',
      description: 'Dark color mode',
      variables: {
        '--app-background': 'linear-gradient(to bottom, #1e293b 0%, #0f172a 50%, #020617 100%)',
        '--content-background': '#1e293b',
        '--text-color': '#f8fafc',
        '--card-background': '#334155',
        '--card-border': '1px solid #475569',
        '--header-color': '#f8fafc',
        '--subheader-color': '#cbd5e1',
        '--accent-color': '#3b82f6',
        '--table-header-bg': '#334155',
        '--table-header-color': '#f8fafc',
        '--table-row-bg': '#1e293b',
        '--table-row-color': '#f8fafc',
        '--table-row-hover': '#334155',
        '--table-border': '#475569',
        '--input-background': '#334155',
        '--input-border': '#475569',
        '--input-text': '#f8fafc',
        '--button-primary-bg': '#3b82f6',
        '--button-primary-text': '#ffffff',
        '--button-secondary-bg': '#475569',
        '--button-secondary-text': '#f8fafc',
        '--shadow-color': 'rgba(0, 0, 0, 0.5)'
      }
    },
    {
      name: 'Light Blue',
      value: 'light-blue',
      color: '#e0f2fe',
      description: 'Light theme with blue accents',
      variables: {
        '--app-background': 'linear-gradient(to bottom, #f0f9ff 0%, #e0f2fe 50%, #bae6fd 100%)',
        '--content-background': '#f0f9ff',
        '--text-color': '#0c4a6e',
        '--card-background': '#ffffff',
        '--card-border': '1px solid #bae6fd',
        '--header-color': '#0c4a6e',
        '--subheader-color': '#0369a1',
        '--accent-color': '#0284c7',
        '--table-header-bg': '#e0f2fe',
        '--table-header-color': '#0c4a6e',
        '--table-row-bg': '#ffffff',
        '--table-row-color': '#0c4a6e',
        '--table-row-hover': '#f0f9ff',
        '--table-border': '#bae6fd',
        '--input-background': '#ffffff',
        '--input-border': '#bae6fd',
        '--input-text': '#0c4a6e',
        '--button-primary-bg': '#0284c7',
        '--button-primary-text': '#ffffff',
        '--button-secondary-bg': '#e0f2fe',
        '--button-secondary-text': '#0c4a6e',
        '--shadow-color': 'rgba(0, 0, 0, 0.1)'
      }
    },
    {
      name: 'Corporate',
      value: 'corporate',
      color: '#f1f5f9',
      description: 'Professional corporate theme',
      variables: {
        '--app-background': 'linear-gradient(to bottom, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%)',
        '--content-background': '#f8fafc',
        '--text-color': '#334155',
        '--card-background': '#ffffff',
        '--card-border': '1px solid #cbd5e1',
        '--header-color': '#1e293b',
        '--subheader-color': '#475569',
        '--accent-color': '#0f172a',
        '--table-header-bg': '#f1f5f9',
        '--table-header-color': '#334155',
        '--table-row-bg': '#ffffff',
        '--table-row-color': '#334155',
        '--table-row-hover': '#f8fafc',
        '--table-border': '#e2e8f0',
        '--input-background': '#ffffff',
        '--input-border': '#cbd5e1',
        '--input-text': '#334155',
        '--button-primary-bg': '#334155',
        '--button-primary-text': '#ffffff',
        '--button-secondary-bg': '#f1f5f9',
        '--button-secondary-text': '#334155',
        '--shadow-color': 'rgba(0, 0, 0, 0.1)'
      }
    },
    {
      name: 'Nature',
      value: 'nature',
      color: '#dcfce7',
      description: 'Green nature-inspired theme',
      variables: {
        '--app-background': 'linear-gradient(to bottom, #f0fdf4 0%, #dcfce7 50%, #bbf7d0 100%)',
        '--content-background': '#f0fdf4',
        '--text-color': '#166534',
        '--card-background': '#ffffff',
        '--card-border': '1px solid #bbf7d0',
        '--header-color': '#166534',
        '--subheader-color': '#15803d',
        '--accent-color': '#16a34a',
        '--table-header-bg': '#dcfce7',
        '--table-header-color': '#166534',
        '--table-row-bg': '#ffffff',
        '--table-row-color': '#166534',
        '--table-row-hover': '#f0fdf4',
        '--table-border': '#bbf7d0',
        '--input-background': '#ffffff',
        '--input-border': '#bbf7d0',
        '--input-text': '#166534',
        '--button-primary-bg': '#16a34a',
        '--button-primary-text': '#ffffff',
        '--button-secondary-bg': '#dcfce7',
        '--button-secondary-text': '#166534',
        '--shadow-color': 'rgba(0, 0, 0, 0.1)'
      }
    },
    {
      name: 'Purple Elegance',
      value: 'purple',
      color: '#ede9fe',
      description: 'Elegant purple theme',
      variables: {
        '--app-background': 'linear-gradient(to bottom, #faf5ff 0%, #f3e8ff 50%, #ede9fe 100%)',
        '--content-background': '#faf5ff',
        '--text-color': '#581c87',
        '--card-background': '#ffffff',
        '--card-border': '1px solid #e9d5ff',
        '--header-color': '#581c87',
        '--subheader-color': '#7e22ce',
        '--accent-color': '#9333ea',
        '--table-header-bg': '#f3e8ff',
        '--table-header-color': '#581c87',
        '--table-row-bg': '#ffffff',
        '--table-row-color': '#581c87',
        '--table-row-hover': '#faf5ff',
        '--table-border': '#e9d5ff',
        '--input-background': '#ffffff',
        '--input-border': '#e9d5ff',
        '--input-text': '#581c87',
        '--button-primary-bg': '#9333ea',
        '--button-primary-text': '#ffffff',
        '--button-secondary-bg': '#f3e8ff',
        '--button-secondary-text': '#581c87',
        '--shadow-color': 'rgba(0, 0, 0, 0.1)'
      }
    },
    {
      name: 'Image Bg',
      value: 'dynamic',
      color: 'linear-gradient(45deg, #3b82f6, #10b981, #8b5cf6)',
      description: 'diiferent bg images',
      variables: {
        '--app-background': 'var(--dynamic-background-image)',
        '--app-background-overlay': 'rgba(0, 0, 0, 0.5)',
        '--content-background': 'rgba(255, 255, 255, 0.1)',
        '--text-color': '#ffffff',
        '--card-background': 'rgba(255, 255, 255, 0.15)',
        '--card-border': '1px solid rgba(255, 255, 255, 0.2)',
        '--card-backdrop-filter': 'blur(10px)',
        '--header-color': '#ffffff',
        '--subheader-color': 'rgba(255, 255, 255, 0.8)',
        '--accent-color': '#3b82f6',
        '--table-header-bg': 'rgba(0, 0, 0, 0.3)',
        '--table-header-color': '#ffffff',
        '--table-row-bg': 'rgba(255, 255, 255, 0.1)',
        '--table-row-color': '#ffffff',
        '--table-row-hover': 'rgba(255, 255, 255, 0.2)',
        '--table-border': 'rgba(255, 255, 255, 0.2)',
        '--input-background': 'rgba(255, 255, 255, 0.1)',
        '--input-border': 'rgba(255, 255, 255, 0.2)',
        '--input-text': '#ffffff',
        '--button-primary-bg': 'rgba(59, 130, 246, 0.8)',
        '--button-primary-text': '#ffffff',
        '--button-secondary-bg': 'rgba(255, 255, 255, 0.1)',
        '--button-secondary-text': '#ffffff',
        '--shadow-color': 'rgba(0, 0, 0, 0.3)',
        '--sidebar-background': 'rgba(0, 0, 0, 0.5)',
        '--dialog-background': 'rgba(0, 0, 0, 0.7)',
        '--dialog-backdrop-filter': 'blur(15px)'
      }
    }
  ];

  useEffect(() => {
    applyTheme(selectedTheme);
    localStorage.setItem('theme', selectedTheme);
  }, [selectedTheme]);

  const applyTheme = (themeName) => {
    themeLogger.info(`Applying theme: ${themeName}`);

    const theme = themes.find(t => t.value === themeName);

    if (themeName === 'default') {
      themes.forEach(t => {
        if (t.value !== 'default' && t.variables) {
          Object.keys(t.variables).forEach(variable => {
            document.documentElement.style.removeProperty(variable);
          });
        }
      });

      const defaultTheme = themes.find(t => t.value === 'default');
      if (defaultTheme && defaultTheme.variables) {
        Object.entries(defaultTheme.variables).forEach(([key, value]) => {
          document.documentElement.style.setProperty(key, value);
        });
      }

      document.body.className = document.body.className
        .split(' ')
        .filter(cls => !cls.startsWith('theme-'))
        .join(' ');

      document.body.classList.add('theme-default');
    }
    else if (theme && theme.variables) {
      Object.entries(theme.variables).forEach(([key, value]) => {
        document.documentElement.style.setProperty(key, value);
      });

      document.body.className = document.body.className
        .split(' ')
        .filter(cls => !cls.startsWith('theme-'))
        .join(' ');

      document.body.classList.add(`theme-${themeName}`);
    }
  };

  const handleThemeChange = (e) => {
    themeLogger.info(`Theme changed to: ${e.value}`);
    setSelectedTheme(e.value);
  };

  const themeItemTemplate = (option) => {
    return (
      <div className="theme-item">
        <div
          className="theme-color-sample"
          style={{ backgroundColor: option.color }}
        ></div>
        <div className="theme-item-text">
          <div className="theme-item-name">{option.name}</div>
          <div className="theme-item-description">{option.description}</div>
        </div>
      </div>
    );
  };

  const themeValueTemplate = (option) => {
    if (!option) {
      return <span>Select Theme</span>;
    }

    return (
      <div className="theme-value">
        <div
          className="theme-color-sample"
          style={{ backgroundColor: option.color }}
        ></div>
        <span>{option.name}</span>
      </div>
    );
  };

  const isDynamicTheme = selectedTheme === 'dynamic';

  return (
    <div className="theme-selector">
      <Dropdown
        value={selectedTheme}
        options={themes.map(theme => theme.value)}
        onChange={handleThemeChange}
        placeholder={t('theme.select')}
        className="theme-dropdown"
        itemTemplate={(option) => {
          const theme = themes.find(t => t.value === option);
          return themeItemTemplate(theme);
        }}
        valueTemplate={(option) => {
          const theme = themes.find(t => t.value === option);
          return themeValueTemplate(theme);
        }}
      />
      {isDynamicTheme && <DynamicBackground />}
    </div>
  );
};

export default ThemeSelector;
