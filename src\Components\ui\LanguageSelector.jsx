import React, { useState, useEffect, useRef } from 'react';
import i18nController from '../../mvc/controllers/I18nController.js';
import './LanguageSelector.css';

const LanguageSelector = ({ 
  className = '',
  showFlags = true,
  compact = false,
  dropdownPosition = 'bottom'
}) => {
  const [currentLocale, setCurrentLocale] = useState('en');
  const [supportedLocales, setSupportedLocales] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const dropdownRef = useRef(null);
  const buttonRef = useRef(null);

  useEffect(() => {
    const initializeI18nController = async () => {
      try {
        await i18nController.initialize();
        
        const state = i18nController.getState();
        setCurrentLocale(state.currentLocale);
        setSupportedLocales(state.supportedLocales);
      } catch (error) {
        setError(error.message);
      }
    };

    initializeI18nController();

    const unsubscribe = i18nController.addEventListener((event) => {
      switch (event.type) {
        case 'loading':
          setLoading(event.newValue);
          break;
        case 'error':
          setError(event.newValue?.message || null);
          break;
        case 'currentLocale':
          setCurrentLocale(event.newValue);
          break;
        case 'supportedLocales':
          setSupportedLocales(event.newValue);
          break;
      }
    });

    return unsubscribe;
  }, []);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('focusin', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('focusin', handleClickOutside);
    };
  }, [isOpen]);

  useEffect(() => {
    const handleKeyDown = (event) => {
      if (!isOpen) return;

      switch (event.key) {
        case 'Escape':
          setIsOpen(false);
          buttonRef.current?.focus();
          break;
        case 'ArrowDown':
          event.preventDefault();
          focusNextOption();
          break;
        case 'ArrowUp':
          event.preventDefault();
          focusPreviousOption();
          break;
        case 'Enter':
        case ' ':
          event.preventDefault();
          const focusedOption = document.activeElement;
          if (focusedOption && focusedOption.dataset.locale) {
            handleLocaleChange(focusedOption.dataset.locale);
          }
          break;
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleKeyDown);
    }

    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [isOpen]);

  const handleLocaleChange = async (locale) => {
    try {
      setIsOpen(false);
      await i18nController.changeLocale(locale);
    } catch (error) {
      setError(error.message);
    }
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const getLocaleDisplayName = (locale) => {
    const localeNames = {
      en: 'English',
      es: 'Español',
      fr: 'Français',
      de: 'Deutsch',
      it: 'Italiano',
      pt: 'Português',
      ru: 'Русский',
      zh: '中文',
      ja: '日本語',
      ko: '한국어',
      ar: 'العربية',
      hi: 'हिन्दी'
    };
    
    return localeNames[locale] || locale.toUpperCase();
  };

  const getLocaleFlag = (locale) => {
    const flags = {
      en: '🇺🇸',
      es: '🇪🇸',
      fr: '🇫🇷',
      de: '🇩🇪',
      it: '🇮🇹',
      pt: '🇵🇹',
      ru: '🇷🇺',
      zh: '🇨🇳',
      ja: '🇯🇵',
      ko: '🇰🇷',
      ar: '🇸🇦',
      hi: '🇮🇳'
    };
    
    return flags[locale] || '🌐';
  };

  const focusNextOption = () => {
    const options = dropdownRef.current?.querySelectorAll('[data-locale]');
    if (!options) return;

    const currentIndex = Array.from(options).findIndex(option => 
      option === document.activeElement
    );
    const nextIndex = (currentIndex + 1) % options.length;
    options[nextIndex]?.focus();
  };

  const focusPreviousOption = () => {
    const options = dropdownRef.current?.querySelectorAll('[data-locale]');
    if (!options) return;

    const currentIndex = Array.from(options).findIndex(option => 
      option === document.activeElement
    );
    const prevIndex = currentIndex <= 0 ? options.length - 1 : currentIndex - 1;
    options[prevIndex]?.focus();
  };

  if (compact) {
    return (
      <div className={`language-selector language-selector--compact ${className}`}>
        <button
          ref={buttonRef}
          className="language-toggle-btn"
          onClick={toggleDropdown}
          disabled={loading}
          aria-expanded={isOpen}
          aria-haspopup="listbox"
          aria-label={i18nController.t('language.selectLanguage')}
        >
          {showFlags && (
            <span className="language-flag" aria-hidden="true">
              {getLocaleFlag(currentLocale)}
            </span>
          )}
          <span className="language-code">
            {currentLocale.toUpperCase()}
          </span>
          <span className="dropdown-arrow" aria-hidden="true">
            {isOpen ? '▲' : '▼'}
          </span>
        </button>

        {isOpen && (
          <div 
            ref={dropdownRef}
            className={`language-dropdown language-dropdown--${dropdownPosition}`}
            role="listbox"
            aria-label={i18nController.t('language.availableLanguages')}
          >
            {supportedLocales.map((locale) => (
              <button
                key={locale}
                data-locale={locale}
                className={`language-option ${currentLocale === locale ? 'language-option--selected' : ''}`}
                onClick={() => handleLocaleChange(locale)}
                disabled={loading}
                role="option"
                aria-selected={currentLocale === locale}
              >
                {showFlags && (
                  <span className="language-flag" aria-hidden="true">
                    {getLocaleFlag(locale)}
                  </span>
                )}
                <span className="language-name">
                  {getLocaleDisplayName(locale)}
                </span>
                {currentLocale === locale && (
                  <span className="language-check" aria-hidden="true">
                    ✓
                  </span>
                )}
              </button>
            ))}
          </div>
        )}

        {error && (
          <div className="language-selector__error" role="alert">
            {error}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`language-selector ${className}`}>
      <div className="language-selector__header">
        <h3 className="language-selector__title">
          {i18nController.t('language.languageSelector')}
        </h3>
        {loading && (
          <div className="language-selector__loading" aria-live="polite">
            {i18nController.t('common.loading')}
          </div>
        )}
      </div>

      {error && (
        <div className="language-selector__error" role="alert">
          {error}
        </div>
      )}

      <div className="language-selector__current">
        <div className="current-language">
          {showFlags && (
            <span className="current-language__flag" aria-hidden="true">
              {getLocaleFlag(currentLocale)}
            </span>
          )}
          <div className="current-language__info">
            <span className="current-language__name">
              {getLocaleDisplayName(currentLocale)}
            </span>
            <span className="current-language__code">
              {currentLocale}
            </span>
          </div>
          <div className="current-language__direction">
            {i18nController.isRTL() ? (
              <span title={i18nController.t('language.rtl')}>RTL</span>
            ) : (
              <span title={i18nController.t('language.ltr')}>LTR</span>
            )}
          </div>
        </div>
      </div>

      <div className="language-selector__options">
        <div className="language-grid">
          {supportedLocales.map((locale) => {
            const isSelected = currentLocale === locale;
            const localeInfo = i18nController.getLocaleInfo(locale);
            
            return (
              <button
                key={locale}
                className={`language-card ${isSelected ? 'language-card--selected' : ''}`}
                onClick={() => handleLocaleChange(locale)}
                disabled={loading}
                aria-pressed={isSelected}
                aria-describedby={`lang-${locale}-desc`}
              >
                {showFlags && (
                  <div className="language-card__flag">
                    {getLocaleFlag(locale)}
                  </div>
                )}
                <div className="language-card__info">
                  <div className="language-card__name">
                    {getLocaleDisplayName(locale)}
                  </div>
                  <div className="language-card__code">
                    {locale}
                  </div>
                  {localeInfo.isRTL && (
                    <div className="language-card__rtl">
                      RTL
                    </div>
                  )}
                </div>
                {isSelected && (
                  <div className="language-card__check" aria-hidden="true">
                    ✓
                  </div>
                )}
                
                <div 
                  id={`lang-${locale}-desc`} 
                  className="language-card__description sr-only"
                >
                  {i18nController.t('language.selectLanguageDescription', {
                    language: getLocaleDisplayName(locale),
                    code: locale,
                    direction: localeInfo.isRTL ? 'right-to-left' : 'left-to-right'
                  })}
                </div>
              </button>
            );
          })}
        </div>
      </div>

      <div className="language-selector__actions">
        <button
          className="language-selector__action-btn"
          onClick={() => i18nController.applyBrowserLocale()}
          disabled={loading}
        >
          {i18nController.t('language.useBrowserLanguage')}
        </button>
        
        <button
          className="language-selector__action-btn"
          onClick={() => handleLocaleChange('en')}
          disabled={loading}
        >
          {i18nController.t('language.useEnglish')}
        </button>
      </div>

      <div className="language-selector__info">
        <div className="language-info">
          <div className="language-info__item">
            <span className="language-info__label">
              {i18nController.t('language.currentDirection')}:
            </span>
            <span className="language-info__value">
              {i18nController.getDirection().toUpperCase()}
            </span>
          </div>
          <div className="language-info__item">
            <span className="language-info__label">
              {i18nController.t('language.dateFormat')}:
            </span>
            <span className="language-info__value">
              {i18nController.getDateFormat()}
            </span>
          </div>
          <div className="language-info__item">
            <span className="language-info__label">
              {i18nController.t('language.timeFormat')}:
            </span>
            <span className="language-info__value">
              {i18nController.getTimeFormat()}-hour
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LanguageSelector;
