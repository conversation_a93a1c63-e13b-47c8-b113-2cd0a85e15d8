# Security Reports Directory

This directory contains security scan reports and analysis results.

## Report Types

### Dependency Security
- `npm-audit.json` - NPM audit results in JSON format
- `npm-audit-readable.txt` - Human-readable NPM audit report
- `snyk-test.json` - Snyk vulnerability scan results
- `snyk-readable.txt` - Human-readable Snyk report
- `dependency-check.log` - Dependency check execution log
- `dependency-check-summary.json` - Summary of all dependency checks

### Static Analysis Security Testing (SAST)
- `semgrep.json` - Semgrep static analysis results
- `eslint-security.json` - ESLint security plugin results

### Dynamic Analysis Security Testing (DAST)
- `zap-scan.json` - OWASP ZAP scan results
- `security-headers.json` - Security headers analysis

### Secrets Detection
- `secrets-scan.json` - Detected secrets scan results
- `secrets-baseline.json` - Baseline for secrets detection

### License Compliance
- `licenses.json` - License information for all dependencies
- `license-summary.txt` - License compliance summary
- `outdated-packages.json` - Outdated packages report

### Security Monitoring
- `security-incidents.json` - Real-time security incidents
- `security-metrics.json` - Security metrics and analytics

## Report Retention

- Reports are automatically generated during CI/CD pipeline
- Historical reports are kept for 30 days
- Critical security findings are archived separately

## Usage

Run security scans using npm scripts:

```bash
# Full security scan
npm run security:scan

# Individual scans
npm run security:audit
npm run security:sast
npm run security:dast
npm run security:secrets
npm run security:dependency-check
```

## Integration

These reports are consumed by:
- CI/CD pipeline for security gates
- Security dashboard for monitoring
- Automated alerting systems
- Compliance reporting tools

## File Formats

- JSON files are machine-readable for automation
- TXT files are human-readable for manual review
- LOG files contain execution details and timestamps
