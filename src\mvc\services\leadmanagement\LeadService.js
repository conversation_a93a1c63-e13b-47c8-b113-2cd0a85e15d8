import ApiService from '../ApiService.js';
import LeadModel from '../../models/leadmanagement/LeadModel.js';

class LeadService {
	constructor() {
		this.api = new ApiService();
	}

	async getAll() {
		const data = await this.api.request('GET', '/Lead/getAll');
        console.log("saumya data",data)
		return Array.isArray(data) ? data.map(item => new LeadModel(item)) : [];
	}

	async getById(id) {
		const data = await this.api.request('GET', `/Lead/getById/${id}`);
		return new LeadModel(data);
	}

	async save(lead) {
		const data = await this.api.request('POST', '/Lead/save', lead);
		return new LeadModel(data);
	}

	async update(id, lead) {
		const data = await this.api.request('PUT', `/Lead/update/${id}`, lead);
		return new LeadModel(data);
	}

	async updatePartial(id, lead) {
		const data = await this.api.request('PATCH', `/Lead/updatePartial/${id}`, lead);
		return new LeadModel(data);
	}

	async deleteById(id) {
		return await this.api.request('DELETE', `/Lead/deleteById/${id}`);
	}

	async getByField(fieldName, fieldValue) {
		const data = await this.api.request('GET', `/Lead/getBy${fieldName}/${fieldValue}`);
		return Array.isArray(data) ? data.map(item => new LeadModel(item)) : [];
	}

	async sort(sortBy, sortOrder) {
		const data = await this.api.request('GET', `/Lead/sort/${sortBy}/${sortOrder}`);
		return Array.isArray(data) ? data.map(item => new LeadModel(item)) : [];
	}

	async executeQuery(query) {
		const data = await this.api.request('POST', '/Lead/execute-query', query);
		return Array.isArray(data) ? data.map(item => new LeadModel(item)) : [];
	}
}

export default new LeadService();
