import axios from "axios";
import { getAxiosInstance } from "@utils/loader/axiosInstance"; 
import { getPublicAxiosInstance } from "@utils/loader/publicAxiosInstance";

const loginApi = async (credentials) => {
  const axios = getPublicAxiosInstance(); // no need of auth token
  // Prepare the request body with correct structure
  const requestBody = {
    email: credentials.email,
    password: credentials.password,
  };

  try {
    const response = await axios.post("/api/aviation-auth/login", requestBody);
    console.log("Login API response:", response);

    if (response.data && response.data.isSuccess) {
      // Store the entire response in localStorage under 'user' key
      localStorage.setItem("user", JSON.stringify(response.data));

      return response.data;
    }
    return response?.data;
  } catch (error) {
    throw error;
  }
};

const forgotPassword = async (email) => {
  try {
    const response = await axios.post(
      "/api/PasswordManagement/forgot-password",
      {
        email: email,
      }
    );

    if (response.data && response.data.isSuccess) {
      return response.data;
    } else {
      throw new Error(response.data?.message || "Failed to send reset code");
    }
  } catch (error) {
    throw error;
  }
};

const verifyOtp = async (email, otp) => {
  try {
    const response = await axios.post("/api/PasswordManagement/verify-otp", {
      email: email,
      otp: otp,
    });

    if (response.data && response.data.isSuccess) {
      return response.data;
    } else {
      throw new Error(response.data?.message || "Invalid verification code");
    }
  } catch (error) {
    throw error;
  }
};

const resetPasswordNew = async (email, newPassword, confirmPassword) => {
  try {
    const response = await axios.post(
      "/api/PasswordManagement/reset-password",
      {
        email: email,
        newPassword: newPassword,
        confirmPassword: confirmPassword,
      }
    );

    if (response.data && response.data.isSuccess) {
      return response.data;
    } else {
      throw new Error(response.data?.message || "Failed to reset password");
    }
  } catch (error) {
    throw error;
  }
};

const oauth2Callback = async (callbackData) => {
  const axios = getAxiosInstance();

  try {
    const response = await axios.post("/api/aviation-auth/oauth2-callback", {
      code: callbackData.code,
      state: callbackData.state,
      redirectUri: callbackData.redirectUri,
    });

    if (response.data && response.data.isSuccess) {
      // Store the entire response in localStorage under 'user' key
      localStorage.setItem("user", JSON.stringify(response.data));

      return response.data;
    } else {
      throw new Error(response.data?.errorMessage || "OAuth2 callback failed");
    }
  } catch (error) {
    throw error;
  }
};

const logoutApi = async (refreshToken, accessToken) => {
  const axios = getAxiosInstance();

  try {
    const headers = {};
    if (accessToken) {
      headers["Authorization"] = `Bearer ${accessToken}`;
    }

    const response = await axios.post(
      "/api/aviation-auth/logout",
      { refreshToken },
      { headers }
    );

    if (response.data == 200) {
      localStorage.removeItem("user");
      return response.data;
    } else {
      throw new Error(response.data?.errorMessage || "Logout failed");
    }
  } catch (error) {
    localStorage.removeItem("user");
    throw error;
  }
};

const AuthService = {
  loginApi,
  forgotPassword,
  verifyOtp,
  resetPasswordNew,
  oauth2Callback,
  logoutApi,
};

export default AuthService;
