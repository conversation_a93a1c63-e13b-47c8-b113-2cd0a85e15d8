{"name": "asm-react-app", "version": "1.0.0", "description": "Enterprise React application with security best practices", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview", "test": "jest --config=config/jest.config.js", "test:coverage": "jest --coverage", "test:functional": "cypress run --env profile=default", "test:performance": "jmeter -n -t jmeter/load-test.jmx", "lint": "eslint .", "lint:security": "eslint . --config .eslint-security.js", "lint:fix": "eslint . --fix", "security:scan": "./security/scripts/security-scan.sh", "security:dependency-check": "./security/scripts/dependency-check.sh", "security:sast": "npx semgrep scan --config=auto --json -o security/reports/semgrep.json", "security:dast": "npx zaproxy -cmd -quickurl http://localhost:3000 -quickout security/reports/zap-scan.json", "security:secrets": "npx detect-secrets scan --baseline .secrets.baseline", "security:audit": "npm audit --audit-level=high", "security:snyk": "npx snyk test --severity-threshold=high", "security:test": "npm run security:audit && npm run security:snyk && npm run security:sast", "precommit": "npm run security:scan && npm run lint:security", "prettier": "prettier --check \"src/**/*.js\"", "prettier:fix": "prettier --write \"src/**/*.js\"", "docker:build": "docker build -t enterprise-react-app .", "docker:run": "docker run -p 3000:3000 enterprise-react-app"}, "dependencies": {"@reduxjs/toolkit": "^2.9.0", "@tanstack/react-query": "^5.90.2", "axios": "^1.12.2", "chart.js": "^4.5.0", "crypto-js": "^4.1.1", "dompurify": "^3.0.5", "intersection-observer": "^0.12.0", "primeflex": "^3.3.1", "primeicons": "^6.0.1", "primereact": "^9.6.5", "promise-polyfill": "^8.2.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.64.0", "react-icons": "^5.5.0", "react-intl": "^6.3.2", "react-redux": "^8.1.1", "react-router-dom": "^7.9.3", "react-toastify": "^11.0.5", "redux": "^4.2.1", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "resize-observer-polyfill": "^1.5.1", "whatwg-fetch": "^3.6.2"}, "devDependencies": {"@vitejs/plugin-react": "^5.0.0", "cssnano": "^5.1.15", "cypress": "^15.3.0", "detect-secrets": "^1.0.6", "eslint": "^9.33.0", "eslint-plugin-no-unsanitized": "^4.0.1", "eslint-plugin-react": "^7.32.2", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "eslint-plugin-react-security": "^0.1.0", "eslint-plugin-security": "^1.7.1", "framer-motion": "^12.23.22", "globals": "^16.3.0", "husky": "^8.0.3", "jest": "^29.5.0", "jest-environment-jsdom": "^29.5.0", "license-checker": "^25.0.1", "npm-check-updates": "^16.10.12", "prettier": "^2.8.8", "snyk": "^1.1200.0", "vite": "^4.5.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all", "chrome >= 80", "firefox >= 78", "safari >= 13", "edge >= 80"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}