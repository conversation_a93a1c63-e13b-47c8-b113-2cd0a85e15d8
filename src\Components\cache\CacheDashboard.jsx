import React, { useState, useEffect } from 'react';
import { useCacheAnalytics, useCacheManager, useCacheContext } from '../../hooks/useCache';
import { useCacheHealth, useCachePerformance, useCacheStorage } from '../../hooks/useCacheProvider';
import './CacheDashboard.css';

const CacheDashboard = () => {
  const { isInitialized, error } = useCacheContext();
  const { analytics, loading: analyticsLoading, refreshAnalytics, resetAnalytics } = useCacheAnalytics();
  const { stats, loading: statsLoading, refreshStats, clearCache, performCleanup, exportCache, importCache } = useCacheManager();
  const { healthStatus, performHealthCheck } = useCacheHealth();
  const performance = useCachePerformance();
  const { storageInfo, clearStorage } = useCacheStorage();

  const [activeTab, setActiveTab] = useState('overview');
  const [exportData, setExportData] = useState(null);
  const [importFile, setImportFile] = useState(null);

  if (!isInitialized) {
    return (
      <div className="cache-dashboard cache-dashboard--loading">
        <div className="loading-spinner"></div>
        <p>Initializing cache system...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="cache-dashboard cache-dashboard--error">
        <h2>Cache System Error</h2>
        <p>{error}</p>
      </div>
    );
  }

  const handleExportCache = async (cacheType = null) => {
    try {
      const data = await exportCache(cacheType);
      setExportData(data);
      
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `cache-export-${cacheType || 'all'}-${Date.now()}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Export failed:', error);
    }
  };

  const handleImportCache = async () => {
    if (!importFile) return;

    try {
      const text = await importFile.text();
      const data = JSON.parse(text);
      await importCache(data);
      setImportFile(null);
    } catch (error) {
      console.error('Import failed:', error);
    }
  };

  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (ms) => {
    const seconds = Math.floor(ms / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    
    if (hours > 0) return `${hours}h ${minutes % 60}m`;
    if (minutes > 0) return `${minutes}m ${seconds % 60}s`;
    return `${seconds}s`;
  };

  return (
    <div className="cache-dashboard">
      <div className="cache-dashboard__header">
        <h1 className="cache-dashboard__title">Cache Management Dashboard</h1>
        <div className="cache-dashboard__actions">
          <button 
            className="btn btn--secondary"
            onClick={refreshStats}
            disabled={statsLoading}
          >
            {statsLoading ? 'Refreshing...' : 'Refresh'}
          </button>
          <button 
            className="btn btn--secondary"
            onClick={performHealthCheck}
          >
            Health Check
          </button>
        </div>
      </div>

      <div className={`cache-health-status cache-health-status--${healthStatus.status}`}>
        <div className="cache-health-status__icon">
          {healthStatus.status === 'healthy' && '✅'}
          {healthStatus.status === 'warning' && '⚠️'}
          {healthStatus.status === 'error' && '❌'}
          {healthStatus.status === 'unknown' && '❓'}
        </div>
        <div className="cache-health-status__info">
          <h3>System Health: {healthStatus.status.toUpperCase()}</h3>
          {healthStatus.issues.length > 0 && (
            <ul className="cache-health-issues">
              {healthStatus.issues.map((issue, index) => (
                <li key={index} className={`cache-health-issue cache-health-issue--${issue.severity}`}>
                  {issue.message}
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>

      <div className="cache-dashboard__tabs">
        {['overview', 'analytics', 'storage', 'management'].map(tab => (
          <button
            key={tab}
            className={`cache-tab ${activeTab === tab ? 'cache-tab--active' : ''}`}
            onClick={() => setActiveTab(tab)}
          >
            {tab.charAt(0).toUpperCase() + tab.slice(1)}
          </button>
        ))}
      </div>

      <div className="cache-dashboard__content">
        {activeTab === 'overview' && (
          <div className="cache-overview">
            <div className="cache-metrics-grid">
              <div className="cache-metric-card">
                <h3>Hit Rate</h3>
                <div className="cache-metric-value">
                  {performance.hitRate.toFixed(1)}%
                </div>
                <div className={`cache-metric-status ${performance.hitRate > 70 ? 'good' : 'warning'}`}>
                  {performance.hitRate > 70 ? 'Good' : 'Needs Improvement'}
                </div>
              </div>

              <div className="cache-metric-card">
                <h3>Total Requests</h3>
                <div className="cache-metric-value">
                  {performance.totalRequests.toLocaleString()}
                </div>
                <div className="cache-metric-status">
                  {formatDuration(performance.uptime)} uptime
                </div>
              </div>

              <div className="cache-metric-card">
                <h3>Error Rate</h3>
                <div className="cache-metric-value">
                  {performance.errorRate.toFixed(1)}%
                </div>
                <div className={`cache-metric-status ${performance.errorRate < 5 ? 'good' : 'error'}`}>
                  {performance.errorRate < 5 ? 'Good' : 'High'}
                </div>
              </div>

              <div className="cache-metric-card">
                <h3>Response Time</h3>
                <div className="cache-metric-value">
                  {performance.averageResponseTime.toFixed(1)}ms
                </div>
                <div className={`cache-metric-status ${performance.averageResponseTime < 100 ? 'good' : 'warning'}`}>
                  Average
                </div>
              </div>
            </div>

            {stats && stats.cacheStats && (
              <div className="cache-types-overview">
                <h3>Cache Types</h3>
                <div className="cache-types-grid">
                  {Object.entries(stats.cacheStats.cacheTypes).map(([type, typeStats]) => (
                    <div key={type} className="cache-type-card">
                      <h4>{type}</h4>
                      <div className="cache-type-stats">
                        <div>Entries: {typeStats?.entries || 0}</div>
                        <div>Size: {formatBytes(typeStats?.size || 0)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="cache-analytics">
            <div className="cache-analytics__header">
              <h3>Cache Analytics</h3>
              <button 
                className="btn btn--secondary"
                onClick={resetAnalytics}
              >
                Reset Analytics
              </button>
            </div>

            {analytics && (
              <>
                <div className="analytics-summary">
                  <div className="analytics-stat">
                    <label>Uptime:</label>
                    <span>{formatDuration(analytics.summary.uptime)}</span>
                  </div>
                  <div className="analytics-stat">
                    <label>Requests/Hour:</label>
                    <span>{analytics.summary.requestsPerHour.toFixed(1)}</span>
                  </div>
                  <div className="analytics-stat">
                    <label>Hit Rate:</label>
                    <span>{analytics.summary.hitRate.toFixed(1)}%</span>
                  </div>
                  <div className="analytics-stat">
                    <label>Miss Rate:</label>
                    <span>{analytics.summary.missRate.toFixed(1)}%</span>
                  </div>
                </div>

                <div className="analytics-operations">
                  <h4>Operations</h4>
                  <div className="operations-grid">
                    {Object.entries(analytics.operations).map(([operation, count]) => (
                      <div key={operation} className="operation-stat">
                        <label>{operation}:</label>
                        <span>{count.toLocaleString()}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {analytics.topKeyPatterns && analytics.topKeyPatterns.length > 0 && (
                  <div className="analytics-patterns">
                    <h4>Top Key Patterns</h4>
                    <div className="patterns-list">
                      {analytics.topKeyPatterns.slice(0, 10).map((pattern, index) => (
                        <div key={index} className="pattern-item">
                          <div className="pattern-key">{pattern.pattern}</div>
                          <div className="pattern-stats">
                            <span>Frequency: {pattern.frequency.toFixed(2)}/min</span>
                            <span>Hits: {pattern.hits}</span>
                            <span>Misses: {pattern.misses}</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {analytics.recommendations && analytics.recommendations.length > 0 && (
                  <div className="analytics-recommendations">
                    <h4>Recommendations</h4>
                    <div className="recommendations-list">
                      {analytics.recommendations.map((rec, index) => (
                        <div key={index} className={`recommendation recommendation--${rec.severity}`}>
                          <div className="recommendation-type">{rec.type}</div>
                          <div className="recommendation-message">{rec.message}</div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </>
            )}
          </div>
        )}

        {activeTab === 'storage' && (
          <div className="cache-storage">
            <h3>Storage Management</h3>
            
            <div className="storage-overview">
              <div className="storage-stat">
                <label>Total Cache Size:</label>
                <span>{formatBytes(storageInfo.totalSize)}</span>
              </div>
              <div className="storage-stat">
                <label>Available Space:</label>
                <span>{formatBytes(storageInfo.availableSpace)}</span>
              </div>
              <div className="storage-stat">
                <label>Usage:</label>
                <span>{storageInfo.usagePercentage.toFixed(1)}%</span>
              </div>
            </div>

            <div className="storage-breakdown">
              <h4>Storage by Cache Type</h4>
              <div className="storage-types">
                {Object.entries(storageInfo.caches).map(([type, info]) => (
                  <div key={type} className="storage-type">
                    <div className="storage-type-header">
                      <span className="storage-type-name">{type}</span>
                      <span className="storage-type-size">{formatBytes(info.size)}</span>
                    </div>
                    <div className="storage-type-actions">
                      <button 
                        className="btn btn--small btn--secondary"
                        onClick={() => clearStorage(type)}
                      >
                        Clear {type}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="storage-actions">
              <button 
                className="btn btn--warning"
                onClick={() => clearStorage()}
              >
                Clear All Storage
              </button>
            </div>
          </div>
        )}

        {activeTab === 'management' && (
          <div className="cache-management">
            <h3>Cache Management</h3>

            <div className="management-section">
              <h4>Operations</h4>
              <div className="management-actions">
                <button 
                  className="btn btn--primary"
                  onClick={performCleanup}
                  disabled={statsLoading}
                >
                  Perform Cleanup
                </button>
                <button 
                  className="btn btn--secondary"
                  onClick={() => clearCache()}
                  disabled={statsLoading}
                >
                  Clear All Caches
                </button>
                <button 
                  className="btn btn--secondary"
                  onClick={refreshStats}
                  disabled={statsLoading}
                >
                  Refresh Stats
                </button>
              </div>
            </div>

            <div className="management-section">
              <h4>Import/Export</h4>
              <div className="import-export-actions">
                <div className="export-section">
                  <button 
                    className="btn btn--secondary"
                    onClick={() => handleExportCache()}
                  >
                    Export All Caches
                  </button>
                  <button 
                    className="btn btn--secondary"
                    onClick={() => handleExportCache('userCache')}
                  >
                    Export User Cache
                  </button>
                </div>
                
                <div className="import-section">
                  <input
                    type="file"
                    accept=".json"
                    onChange={(e) => setImportFile(e.target.files[0])}
                    className="file-input"
                  />
                  <button 
                    className="btn btn--secondary"
                    onClick={handleImportCache}
                    disabled={!importFile}
                  >
                    Import Cache
                  </button>
                </div>
              </div>
            </div>

            <div className="management-section">
              <h4>Configuration</h4>
              <div className="config-info">
                <p>Cache configuration can be managed through the CacheController.</p>
                <p>Current settings include automatic cleanup, monitoring, and persistence.</p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default CacheDashboard;
