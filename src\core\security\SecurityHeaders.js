import { logger } from '../logging/logger.js';

class SecurityHeaders {
  constructor() {
    this.headers = new Map();
    this.violations = [];
    this.initializeHeaders();
    this.setupCSPReporting();
  }

  initializeHeaders() {
    const isDevelopment = process.env.NODE_ENV === 'development';
    const isProduction = process.env.NODE_ENV === 'production';

    const cspDirectives = {
      'default-src': ["'self'"],
      'script-src': isDevelopment 
        ? ["'self'", "'unsafe-eval'", "'unsafe-inline'"]
        : ["'self'", "'unsafe-eval'", "'unsafe-inline'", "https://apis.google.com", "https://www.googletagmanager.com"],
      'style-src': ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      'img-src': ["'self'", "data:", "https:"],
      'font-src': ["'self'", "https://fonts.gstatic.com"],
      'connect-src': isDevelopment
        ? ["'self'", "ws:", "wss:", "http://localhost:*"]
        : ["'self'", "https://api.example.com", "https://sentry.io"],
      'frame-src': ["'self'", "https://www.youtube.com"],
      'media-src': ["'self'"],
      'object-src': ["'none'"],
      'base-uri': ["'self'"],
      'form-action': ["'self'"],
      'frame-ancestors': ["'none'"],
      'upgrade-insecure-requests': []
    };

    const cspString = Object.entries(cspDirectives)
      .map(([directive, sources]) => `${directive} ${sources.join(' ')}`)
      .join('; ');

    this.headers.set('Content-Security-Policy', cspString);

    if (isProduction) {
      this.headers.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains; preload');
    }

    this.headers.set('X-Content-Type-Options', 'nosniff');

    this.headers.set('X-Frame-Options', 'DENY');

    this.headers.set('X-XSS-Protection', '1; mode=block');

    this.headers.set('Referrer-Policy', 'strict-origin-when-cross-origin');

    const permissionsPolicies = [
      'camera=()',
      'microphone=()',
      'geolocation=()',
      'payment=()',
      'usb=()',
      'magnetometer=()',
      'accelerometer=()',
      'gyroscope=()',
      'fullscreen=(self)',
      'picture-in-picture=()'
    ];
    this.headers.set('Permissions-Policy', permissionsPolicies.join(', '));

    this.headers.set('Cross-Origin-Embedder-Policy', 'require-corp');
    this.headers.set('Cross-Origin-Opener-Policy', 'same-origin');
    this.headers.set('Cross-Origin-Resource-Policy', 'same-origin');

    logger.info('Security headers initialized', {
      environment: process.env.NODE_ENV,
      headersCount: this.headers.size
    });
  }

  setupCSPReporting() {
    if (typeof window === 'undefined') {
      return;
    }

    document.addEventListener('securitypolicyviolation', (event) => {
      this.handleCSPViolation(event);
    });

    const reportToConfig = {
      group: 'csp-endpoint',
      max_age: 10886400,
      endpoints: [{ url: '/api/csp-report' }]
    };

    if ('ReportingObserver' in window) {
      const observer = new ReportingObserver((reports) => {
        reports.forEach(report => {
          this.handleSecurityReport(report);
        });
      });
      observer.observe();
    }
  }

  handleCSPViolation(event) {
    const violation = {
      type: 'csp-violation',
      timestamp: new Date().toISOString(),
      blockedURI: event.blockedURI,
      violatedDirective: event.violatedDirective,
      originalPolicy: event.originalPolicy,
      disposition: event.disposition,
      documentURI: event.documentURI,
      referrer: event.referrer,
      statusCode: event.statusCode,
      userAgent: navigator.userAgent
    };

    this.violations.push(violation);
    
    logger.security('csp_violation', violation);

    this.sendViolationReport(violation);

    this.checkViolationThreshold();
  }

  handleSecurityReport(report) {
    const securityReport = {
      type: 'security-report',
      timestamp: new Date().toISOString(),
      reportType: report.type,
      url: report.url,
      body: report.body
    };

    logger.security('security_report', securityReport);
  }

  sendViolationReport(violation) {
    fetch('/api/csp-report', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(violation)
    }).catch(error => {
      logger.debug('Failed to send CSP violation report', { error: error.message });
    });
  }

  checkViolationThreshold() {
    const recentViolations = this.violations.filter(violation => 
      Date.now() - new Date(violation.timestamp).getTime() < 300000 // 5 minutes
    );

    if (recentViolations.length > 10) {
      logger.security('csp_violation_threshold_exceeded', {
        violationCount: recentViolations.length,
        timeWindow: '5 minutes'
      });

      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('securityAlert', {
          detail: {
            type: 'csp_violations',
            severity: 'high',
            count: recentViolations.length
          }
        }));
      }
    }
  }

  applyToFetch() {
    if (typeof window === 'undefined') {
      return;
    }

    const originalFetch = window.fetch;
    window.fetch = (url, options = {}) => {
      const headers = new Headers(options.headers);
      
      headers.set('X-Requested-With', 'XMLHttpRequest');
      
      const csrfToken = this.getCSRFToken();
      if (csrfToken) {
        headers.set('X-CSRF-Token', csrfToken);
      }

      return originalFetch(url, {
        ...options,
        headers: headers
      });
    };
  }

  applyToXHR() {
    if (typeof window === 'undefined') {
      return;
    }

    const originalXHRSend = XMLHttpRequest.prototype.send;
    XMLHttpRequest.prototype.send = function(data) {
      this.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
      
      const csrfToken = SecurityHeaders.instance.getCSRFToken();
      if (csrfToken) {
        this.setRequestHeader('X-CSRF-Token', csrfToken);
      }

      return originalXHRSend.call(this, data);
    };
  }

  getCSRFToken() {
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
      return metaToken.getAttribute('content');
    }

    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'csrf_token' || name === 'XSRF-TOKEN') {
        return decodeURIComponent(value);
      }
    }

    return null;
  }

  validateResponseHeaders(response) {
    const requiredHeaders = [
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection'
    ];

    const missingHeaders = requiredHeaders.filter(header => 
      !response.headers.has(header)
    );

    if (missingHeaders.length > 0) {
      logger.security('missing_security_headers', {
        url: response.url,
        missingHeaders: missingHeaders
      });
    }

    return missingHeaders.length === 0;
  }

  checkCurrentPageHeaders() {
    if (typeof window === 'undefined') {
      return;
    }

    fetch(window.location.href, { method: 'HEAD' })
      .then(response => {
        this.validateResponseHeaders(response);
        
        const securityHeadersPresent = Array.from(this.headers.keys())
          .filter(header => response.headers.has(header.toLowerCase()));

        logger.info('Page security headers check', {
          url: window.location.href,
          securityHeadersPresent: securityHeadersPresent,
          totalExpected: this.headers.size
        });
      })
      .catch(error => {
        logger.debug('Could not check page headers', { error: error.message });
      });
  }

  getHeadersForServer() {
    const serverHeaders = {};
    
    for (const [name, value] of this.headers.entries()) {
      serverHeaders[name] = value;
    }

    return serverHeaders;
  }

  getExpressMiddleware() {
    return (req, res, next) => {
      for (const [name, value] of this.headers.entries()) {
        res.setHeader(name, value);
      }
      next();
    };
  }

  getNginxConfig() {
    let config = '# Security Headers Configuration\n';
    
    for (const [name, value] of this.headers.entries()) {
      config += `add_header ${name} "${value}" always;\n`;
    }
    
    config += '\n# Remove server information\n';
    config += 'server_tokens off;\n';
    config += 'more_clear_headers Server;\n';
    
    return config;
  }

  getSecurityStatus() {
    return {
      headersConfigured: this.headers.size,
      violationsCount: this.violations.length,
      recentViolations: this.violations.filter(v => 
        Date.now() - new Date(v.timestamp).getTime() < 3600000 
      ).length,
      environment: process.env.NODE_ENV
    };
  }

  cleanupViolations() {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); 
    this.violations = this.violations.filter(violation => 
      new Date(violation.timestamp).getTime() > cutoff
    );
  }
}

SecurityHeaders.instance = new SecurityHeaders();

export default SecurityHeaders.instance;
