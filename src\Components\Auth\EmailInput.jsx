import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { InputText } from 'primereact/inputtext';

const EmailInput = ({ 
  value, 
  onChange, 
  onValidationChange,
  disabled = false, 
  required = true,
  placeholder = "Enter your email",
  className = ""
}) => {
  const [error, setError] = useState('');

  const validateEmail = (email) => {
    // Reset error
    setError('');

    // Check for empty value if required
    if (required && (!email || !email.trim())) {
      setError('Please enter a valid Employee ID or Email ID');
      return false;
    }

    // Check length (max 254 characters as per RFC 5321)
    if (email.length > 254) {
      setError('Email must not exceed 254 characters');
      return false;
    }

    // Check for spaces
    if (email.includes(' ')) {
      setError('Email must not contain spaces');
      return false;
    }

    // Check for emojis using regex
    const emojiRegex = /[\u{1F300}-\u{1F9FF}]/u;
    if (emojiRegex.test(email)) {
      setError('Email must not contain emojis');
      return false;
    }

    // Basic email structure validation
    const emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid Employee ID or Email ID');
      return false;
    }

    // Check local part and domain part separately
    const [localPart, domainPart] = email.split('@');

    // Local part validation (before @)
    if (localPart.length === 0 || localPart.length > 64) {
      setError('Invalid email format in local part');
      return false;
    }

    // Domain part validation (after @)
    if (domainPart.length === 0 || domainPart.length > 255) {
      setError('Invalid email format in domain part');
      return false;
    }

    // Check for special characters (except @ and allowed ones)
    const specialCharRegex = /[!#$%^&*()+={}\[\]|\\/:;'"<>,? ]/;
    if (specialCharRegex.test(email)) {
      setError('Email contains invalid special characters');
      return false;
    }

    return true;
  };

  const handleChange = (e) => {
    const newValue = e.target.value;
    const isValid = validateEmail(newValue);
    onChange(e);
    if (onValidationChange) {
      onValidationChange(isValid);
    }
  };

  return (
    <div className="email-input-container">
      <div className="input-wrapper">
        <InputText
          type="email"
          value={value}
          onChange={handleChange}
          className={`input-with-icon ${className} ${error ? 'p-invalid' : ''}`}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
        />
        <i className="pi pi-envelope input-icon"></i>
      </div>
      {error && (
        <small className="p-error block mt-1">
          {error}
        </small>
      )}
    </div>
  );
};

EmailInput.propTypes = {
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  onValidationChange: PropTypes.func,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  placeholder: PropTypes.string,
  className: PropTypes.string
};

export default EmailInput;
