import { logger } from '../../core/logging/logger.js';
import securityService from '../../core/security/securityService.js';

class BaseModel {
  constructor(data = {}) {
    this.data = {};
    this.errors = {};
    this.validationRules = {};
    this.sanitizationRules = {};
    this.isNew = true;
    this.isDirty = false;
    
    this.setData(data);
  }

  setData(data) {
    Object.keys(data).forEach(key => {
      this.setAttribute(key, data[key]);
    });
  }

  setAttribute(key, value) {
    const sanitizedValue = this.sanitizeValue(key, value);
    
    if (this.validateAttribute(key, sanitizedValue)) {
      const oldValue = this.data[key];
      this.data[key] = sanitizedValue;
      
      if (oldValue !== sanitizedValue) {
        this.isDirty = true;
      }
    }
  }

  getAttribute(key) {
    return this.data[key];
  }

  getData() {
    return { ...this.data };
  }

  sanitizeValue(key, value) {
    if (value === null || value === undefined) {
      return value;
    }

    const rules = this.sanitizationRules[key] || {};
    let sanitized = value;

    if (typeof value === 'string') {
      if (rules.preventXSS !== false) {
        sanitized = securityService.sanitizeHTML(sanitized);
      }

      if (rules.preventSQLInjection !== false) {
        sanitized = this.preventSQLInjection(sanitized);
      }

      if (rules.trim !== false) {
        sanitized = sanitized.trim();
      }

      if (rules.toLowerCase) {
        sanitized = sanitized.toLowerCase();
      }

      if (rules.alphanumericOnly) {
        sanitized = sanitized.replace(/[^a-zA-Z0-9\s]/g, '');
      }
    }

    if (rules.type === 'number') {
      const num = parseFloat(sanitized);
      sanitized = isNaN(num) ? 0 : num;
    }

    if (rules.type === 'boolean') {
      sanitized = Boolean(sanitized);
    }

    return sanitized;
  }

  preventSQLInjection(value) {
    if (typeof value !== 'string') {
      return value;
    }

    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|EXECUTE|UNION|SCRIPT)\b)/gi,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/gi,
      /(\b(OR|AND)\s+['"]\w+['"]?\s*=\s*['"]\w+['"]?)/gi,
      /(--|\/\*|\*\/|;)/g,
      /(\bxp_\w+)/gi,
      /(\bsp_\w+)/gi,
      /(\b(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)\b)/gi
    ];

    let sanitized = value;

    sqlPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });

    sanitized = sanitized.replace(/'/g, "''");

    if (sanitized !== value) {
      logger.security('sql_injection_attempt', {
        original: value,
        sanitized: sanitized,
        model: this.constructor.name
      });
    }

    return sanitized;
  }

  validateAttribute(key, value) {
    const rules = this.validationRules[key];
    if (!rules) {
      return true;
    }

    const errors = [];

    if (rules.required && (value === null || value === undefined || value === '')) {
      errors.push(`${key} is required`);
    }

    if (value !== null && value !== undefined && rules.type) {
      if (!this.validateType(value, rules.type)) {
        errors.push(`${key} must be of type ${rules.type}`);
      }
    }

    if (typeof value === 'string') {
      if (rules.minLength && value.length < rules.minLength) {
        errors.push(`${key} must be at least ${rules.minLength} characters`);
      }
      if (rules.maxLength && value.length > rules.maxLength) {
        errors.push(`${key} must be no more than ${rules.maxLength} characters`);
      }
    }

    if (typeof value === 'number') {
      if (rules.min !== undefined && value < rules.min) {
        errors.push(`${key} must be at least ${rules.min}`);
      }
      if (rules.max !== undefined && value > rules.max) {
        errors.push(`${key} must be no more than ${rules.max}`);
      }
    }

    if (rules.pattern && typeof value === 'string') {
      if (!rules.pattern.test(value)) {
        errors.push(`${key} format is invalid`);
      }
    }

    if (rules.validator && typeof rules.validator === 'function') {
      const customError = rules.validator(value, this.data);
      if (customError) {
        errors.push(customError);
      }
    }

    if (errors.length > 0) {
      this.errors[key] = errors;
      return false;
    } else {
      delete this.errors[key];
      return true;
    }
  }

  validateType(value, expectedType) {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'email':
        return typeof value === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      case 'url':
        try {
          new URL(value);
          return true;
        } catch {
          return false;
        }
      case 'date':
        return value instanceof Date || !isNaN(Date.parse(value));
      default:
        return true;
    }
  }

  validate() {
    this.errors = {};
    let isValid = true;

    Object.keys(this.validationRules).forEach(key => {
      if (!this.validateAttribute(key, this.data[key])) {
        isValid = false;
      }
    });

    return isValid;
  }

  isValid() {
    return Object.keys(this.errors).length === 0;
  }

  getErrors() {
    return { ...this.errors };
  }

  getError(key) {
    return this.errors[key] ? this.errors[key][0] : null;
  }

  clearErrors() {
    this.errors = {};
  }

  toJSON() {
    return this.getData();
  }

  clone() {
    const ModelClass = this.constructor;
    return new ModelClass(this.getData());
  }

  reset() {
    this.data = {};
    this.errors = {};
    this.isDirty = false;
    this.isNew = true;
  }

  markAsSaved() {
    this.isNew = false;
    this.isDirty = false;
  }

  hasChanges() {
    return this.isDirty;
  }

  getMetadata() {
    return {
      isNew: this.isNew,
      isDirty: this.isDirty,
      isValid: this.isValid(),
      errorCount: Object.keys(this.errors).length
    };
  }
}
