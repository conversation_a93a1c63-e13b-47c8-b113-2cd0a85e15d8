import React, { useState } from 'react';
import GlassyTabs from '../../components/common/GlassyTabs.jsx';
import '../../styles/glassy/glassy-ui.css';
import '../../styles/glassy/global.css';
import { But<PERSON> } from 'primereact/button';
import GlassyBlueButton from '../../components/common/GlassyBlueButton.jsx';

/*
  KeyDataDocVerify
  - Glassy side panel positioned absolutely (scoped class)
  - Uses PrimeFlex utilities for layout/spacing
  - Tabs via GlassyTabs: Key Data | Documents (Documents active)
  - Responsive: on small screens panel becomes full-width and static
*/

const tabs = [
  { label: 'Key Data', value: 'keydata' },
  { label: 'Documents', value: 'documents'},
];

export default function KeyDataDocVerify({ onUpload = () => {} }) {
  const [activeIndex, setActiveIndex] = useState(1); // Documents active by default

  return (
    <aside className=" glass-card-global glass-shadow-light flex flex-column" style={{ width: '100%', maxWidth: '100%' }}>
      {/* Tabs */}
      <div className="w-full">
        <GlassyTabs
          tabs={tabs}
          activeIndex={activeIndex}
          onTabChange={(idx) => setActiveIndex(idx)}
          className="w-full"
          hidePanels
        />
      </div>

      {/* Body */}
      <div className="p-4 md:p-5">
        <div className="grid">
          {/* Left icon rail */}
          <div className="col-fixed">
            <div className="flex align-items-start pr-3">
              <i className="pi pi-file text-300 text-2xl" aria-hidden></i>
            </div>
          </div>

          {/* Main content */}
          <div className="col">
            <div className="flex flex-column align-items-center text-center gap-2">
              <h3 className="m-0 text-900">Document Verification</h3>
              <p className="m-0 text-600">Upload or scan documents for compliance verification</p>
              <GlassyBlueButton
                label="Upload Documents"
                icon="pi pi-upload"
                onClick={onUpload}
              />
            </div>
          </div>
        </div>
      </div>
    </aside>
  );
}
