import React, { useState, useEffect } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { InputTextarea } from 'primereact/inputtextarea';
import { Dropdown } from 'primereact/dropdown';
import { Calendar } from 'primereact/calendar';
import { TabView, TabPanel } from 'primereact/tabview';
import { FileUpload } from 'primereact/fileupload';
import './ServiceOrdersDataTable.css';

const ServiceOrdersDataTable = () => {
  const [expandedRows, setExpandedRows] = useState(null);
  const [globalFilter, setGlobalFilter] = useState('');
  const [selectedDate, setSelectedDate] = useState('Today');
  const [selectedTimezone, setSelectedTimezone] = useState('UTC');
  const [activeTabIndex, setActiveTabIndex] = useState({});

  // Calculate status bar based on all service statuses
  const calculateStatusBar = (order) => {
    const serviceFields = ['transport', 'hotels', 'catering', 'customs', 'migration', 'permits'];

    // Check if this row has service data (not general info)
    const hasServiceData = serviceFields.some(field => order[field] !== undefined);

    // If no service data, don't show status bar
    if (!hasServiceData) {
      return {
        shouldShow: false,
        progress: 0,
        color: 'green'
      };
    }

    // Check if all services are completed (Ok or NA)
    const allCompleted = serviceFields.every(field =>
      order[field] === 'Ok' || order[field] === 'NA'
    );

    // Calculate progress percentage
    const completedCount = serviceFields.filter(field =>
      order[field] === 'Ok' || order[field] === 'NA'
    ).length;
    const progress = (completedCount / serviceFields.length) * 100;

    return {
      shouldShow: true,
      progress: progress,
      color: allCompleted ? 'green' : 'red'
    };
  };

  const rawServiceOrders = [
    {
      id: 1,
      date: 'Tue 5 Aug 2025',
      time: '14:00',
      flightNumber: 'VG5373',
      registration: 'UK-73',
      route: { from: 'DMDW', to: 'DRSV' },
      crew: 4,
      pax: 140,
      transport: 'NA',
      hotels: 'NR',
      catering: 'TBD',
      customs: 'Ok',
      migration: 'Ok',
      slotPPR: '14:00z',
      permits: 'NR',
      fuel: { price: 3.33, unit: 'USD/USG' },
      tasks: 'N',
    },
    {
      id: 2,
      date: 'Tue 5 Aug 2025',
      time: '16:00',
      flightNumber: 'ANUBI',
      registration: 'Aerolineas Jet',
      route: { from: 'DRSV', to: 'DRSV' },
      crew: 4,
      pax: 140,
      transport: 'NR',
      hotels: 'NR',
      catering: 'TBD',
      customs: 'Ok',
      migration: 'Ok',
      slotPPR: '14:00z',
      permits: 'NR',
      fuel: { price: 3.33, unit: 'USD/USG' },
      tasks: 'N',
    },
    {
      id: 3,
      date: 'Wed 6 Aug 2025',
      time: '09:00',
      flightNumber: 'BA4521',
      registration: 'G-EUXY',
      route: { from: 'EGLL', to: 'LFPG' },
      crew: 6,
      pax: 180,
      transport: 'Ok',
      hotels: 'Ok',
      catering: 'Ok',
      customs: 'Ok',
      migration: 'Ok',
      slotPPR: '09:00z',
      permits: 'Ok',
      fuel: { price: 2.45, unit: 'USD/USG' },
      tasks: 'M',
    },
    {
      id: 4,
      date: 'Wed 6 Aug 2025',
      time: '11:30',
      flightNumber: 'LH8832',
      registration: 'D-AIUE',
      route: { from: 'EDDF', to: 'LIRF' },
      crew: 5,
      pax: 165,
      transport: 'TBD',
      hotels: 'NA',
      catering: 'Ok',
      customs: 'Ok',
      migration: 'Ok',
      slotPPR: '11:30z',
      permits: 'TBD',
      fuel: { price: 2.38, unit: 'USD/USG' },
      tasks: 'L',
    },
    {
      id: 5,
      date: 'Thu 7 Aug 2025',
      time: '15:45',
      flightNumber: 'AF1234',
      registration: 'F-HPJA',
      route: { from: 'LFPG', to: 'LEMD' },
      crew: 7,
      pax: 200,
      transport: 'Ok',
      hotels: 'TBD',
      catering: 'NR',
      customs: 'TBD',
      migration: 'NR',
      slotPPR: '15:45z',
      permits: 'Ok',
      fuel: { price: 2.50, unit: 'USD/USG' },
      tasks: 'H',
    },
  ];

  // Add calculated status bars to data
  const serviceOrders = rawServiceOrders.map(order => ({
    ...order,
    statusBar: calculateStatusBar(order)
  }));

  // Calculate statistics for each date group
  const getDateStatistics = (orders) => {
    const total = orders.length;
    const active = orders.filter(o =>
      ['TBD', 'NR'].includes(o.catering) ||
      ['TBD', 'NR'].includes(o.hotels) ||
      ['TBD', 'NR'].includes(o.transport)
    ).length;
    const issues = orders.filter(o => o.catering === 'NR' || o.hotels === 'NR' || o.migration === 'NR').length;
    const completed = orders.filter(o =>
      o.catering === 'Ok' &&
      o.hotels === 'Ok' &&
      o.customs === 'Ok' &&
      o.migration === 'Ok'
    ).length;

    return { total, active, issues, completed };
  };

  const dateOptions = [
    { label: 'Today', value: 'Today' },
    { label: 'Tomorrow', value: 'Tomorrow' },
    { label: 'This Week', value: 'This Week' }
  ];

  const timezoneOptions = [
    { label: 'UTC', value: 'UTC' },
    { label: 'Local', value: 'Local' }
  ];

  // const statusBodyTemplate = (value) => {
  //   const statusClass = `status-badge status-${value.toLowerCase()}`;
  //   return <span className={statusClass}>{value}</span>;
  // };

  const progressBarTemplate = (value, color) => {
    return (
      <div className="progress-bar-container">
        <div className="progress-bar" style={{ width: `${value}%`, backgroundColor: color }}></div>
      </div>
    );
  };

  const rowExpansionTemplate = (data) => {
    const currentTabIndex = activeTabIndex[data.id] || 0;

    return (
      <div className="expansion-panel">
        <div className="expansion-header-bar">
          <Button
            icon="pi pi-times"
            className="p-button-rounded p-button-text p-button-sm close-expansion-btn"
            onClick={() => setExpandedRows(null)}
            tooltip="Close"
            tooltipOptions={{ position: 'left' }}
          />
        </div>
        <TabView
          activeIndex={currentTabIndex}
          onTabChange={(e) => setActiveTabIndex({ ...activeTabIndex, [data.id]: e.index })}
        >
          <TabPanel header="Transport">
            <div className="expansion-content">
              <div className="info-card">
                <h3><i className="pi pi-car"></i> Transport Information</h3>
                <p><strong>Flight:</strong> {data.flightNumber} | <strong>Status:</strong> <span className={`status-badge status-${data.transport.toLowerCase()}`}>{data.transport}</span></p>
                <div className="form-row">
                  <label>Transport Type</label>
                  <Dropdown placeholder="Select transport type" className="w-full" />
                </div>
                <div className="form-row">
                  <label>Provider</label>
                  <InputText placeholder="Enter provider" className="w-full" />
                </div>
                <div className="form-row">
                  <label>Notes</label>
                  <InputTextarea rows={3} placeholder="Enter notes" className="w-full" />
                </div>
                <div className="form-actions">
                  <Button label="Save" className="p-button-sm" />
                  <Button label="Cancel" className="p-button-outlined p-button-sm" />
                </div>
              </div>
            </div>
          </TabPanel>

          <TabPanel header="Hotels">
            <div className="expansion-content">
              <div className="info-card">
                <h3><i className="pi pi-building"></i> Hotel Information</h3>
                <p><strong>Flight:</strong> {data.flightNumber} | <strong>Status:</strong> <span className={`status-badge status-${data.hotels.toLowerCase()}`}>{data.hotels}</span></p>
                <div className="form-row">
                  <label>Hotel Name</label>
                  <InputText placeholder="Enter hotel name" className="w-full" />
                </div>
                <div className="form-row">
                  <label>Check-in Date</label>
                  <Calendar placeholder="Select date" className="w-full" />
                </div>
                <div className="form-row">
                  <label>Number of Rooms</label>
                  <InputText type="number" placeholder="Enter number" className="w-full" />
                </div>
                <div className="form-row">
                  <label>Special Requests</label>
                  <InputTextarea rows={3} placeholder="Enter requests" className="w-full" />
                </div>
                <div className="form-actions">
                  <Button label="Save" className="p-button-sm" />
                  <Button label="Cancel" className="p-button-outlined p-button-sm" />
                </div>
              </div>
            </div>
          </TabPanel>
          
          <TabPanel header={<span>Catering <i className="pi pi-check" style={{ marginLeft: '4px', fontSize: '10px' }}></i></span>}>
            <div className="catering-panel">
              <div className="catering-section">
                <div className="catering-header">
                  <span>Catering - <i className="pi pi-plane"></i> DMDW</span>
                </div>
                <div className="catering-form">
                  <div className="form-row">
                    <label>NR</label>
                    <Dropdown value="Aviation Service1" options={[{ label: 'Aviation Service1', value: 'Aviation Service1' }]} />
                    <Button icon="pi pi-star" className="p-button-text p-button-sm" />
                    <Button icon="pi pi-plus" className="p-button-text p-button-sm" />
                  </div>
                  <div className="form-row">
                    <label>Service</label>
                    <label>For</label>
                  </div>
                  <div className="form-row">
                    <Dropdown value="TBD" options={[{ label: 'TBD', value: 'TBD' }]} style={{ flex: 1 }} />
                    <Dropdown value="Pax" options={[{ label: 'Pax', value: 'Pax' }]} style={{ flex: 1 }} />
                  </div>
                  <div className="form-row">
                    <label>Order</label>
                  </div>
                  <div className="form-row">
                    <InputText placeholder="Order details" style={{ width: '100%' }} />
                  </div>
                  <div className="form-row">
                    <label>Pax Remarks</label>
                  </div>
                  <div className="form-row">
                    <InputText placeholder="Remarks" style={{ width: '100%' }} />
                    <Button icon="pi pi-copy" className="p-button-text p-button-sm" />
                  </div>
                  <div className="form-row">
                    <label>Confirmation</label>
                  </div>
                  <div className="file-upload-area">
                    <p>Drop your file(s) here or <a href="#">browse</a></p>
                  </div>
                  <div className="form-actions">
                    <Button label="Send" className="p-button-sm" />
                    <Button label="Cancel" className="p-button-outlined p-button-sm" />
                    <Button label="Send Quote Only" className="p-button-outlined p-button-sm" />
                  </div>
                  <div className="expenses-section">
                    <a href="#"><i className="pi pi-chevron-right"></i> Expenses (0)</a>
                  </div>
                </div>
              </div>

              <div className="catering-section">
                <div className="catering-header">
                  <span>Catering - <i className="pi pi-plane"></i> DERK</span>
                </div>
                <div className="catering-form">
                  <div className="form-row">
                    <label>NR</label>
                    <Dropdown value="Aviation Service1" options={[{ label: 'Aviation Service1', value: 'Aviation Service1' }]} />
                    <Button icon="pi pi-star" className="p-button-text p-button-sm" />
                    <Button icon="pi pi-plus" className="p-button-text p-button-sm" />
                  </div>
                  <div className="form-row">
                    <label>Service</label>
                    <label>For</label>
                  </div>
                  <div className="form-row">
                    <Dropdown value="TBD" options={[{ label: 'TBD', value: 'TBD' }]} style={{ flex: 1 }} />
                    <Dropdown value="Pax" options={[{ label: 'Pax', value: 'Pax' }]} style={{ flex: 1 }} />
                  </div>
                  <div className="form-row">
                    <label>Order</label>
                  </div>
                  <div className="form-row">
                    <InputText placeholder="Order details" style={{ width: '100%' }} />
                  </div>
                  <div className="form-row">
                    <label>Pax Remarks</label>
                  </div>
                  <div className="form-row">
                    <InputText placeholder="Remarks" style={{ width: '100%' }} />
                    <Button icon="pi pi-copy" className="p-button-text p-button-sm" />
                  </div>
                  <div className="form-row">
                    <label>Confirmation</label>
                  </div>
                  <div className="file-upload-area">
                    <p>Drop your file(s) here or <a href="#">browse</a></p>
                  </div>
                  <div className="form-actions">
                    <Button label="Send" className="p-button-sm" />
                    <Button label="Cancel" className="p-button-outlined p-button-sm" />
                    <Button label="Send Quote Only" className="p-button-outlined p-button-sm" />
                  </div>
                  <div className="expenses-section">
                    <a href="#"><i className="pi pi-plus"></i> Add Expense</a>
                    <a href="#"><i className="pi pi-chevron-right"></i> Expenses (0)</a>
                    <a href="#"><i className="pi pi-plus"></i> Add Expense</a>
                  </div>
                </div>
              </div>
            </div>
          </TabPanel>

          <TabPanel header="Customs">
            <div className="expansion-content">
              <div className="info-card">
                <h3><i className="pi pi-shield"></i> Customs Information</h3>
                <p><strong>Flight:</strong> {data.flightNumber} | <strong>Status:</strong> <span className={`status-badge status-${data.customs.toLowerCase()}`}>{data.customs}</span></p>
                <div className="form-row">
                  <label>Customs Declaration</label>
                  <Dropdown placeholder="Select declaration type" className="w-full" />
                </div>
                <div className="form-row">
                  <label>Customs Officer</label>
                  <InputText placeholder="Enter officer name" className="w-full" />
                </div>
                <div className="form-row">
                  <label>Declaration Number</label>
                  <InputText placeholder="Enter declaration number" className="w-full" />
                </div>
                <div className="form-row">
                  <label>Notes</label>
                  <InputTextarea rows={3} placeholder="Enter notes" className="w-full" />
                </div>
                <div className="form-actions">
                  <Button label="Save" className="p-button-sm" />
                  <Button label="Cancel" className="p-button-outlined p-button-sm" />
                </div>
              </div>
            </div>
          </TabPanel>

          <TabPanel header="Migration">
            <div className="expansion-content">
              <div className="info-card">
                <h3><i className="pi pi-users"></i> Migration Information</h3>
                <p><strong>Flight:</strong> {data.flightNumber} | <strong>Status:</strong> <span className={`status-badge status-${data.migration.toLowerCase()}`}>{data.migration}</span></p>
                <div className="form-row">
                  <label>Immigration Officer</label>
                  <InputText placeholder="Enter officer name" className="w-full" />
                </div>
                <div className="form-row">
                  <label>Passenger Count</label>
                  <InputText type="number" value={data.pax} className="w-full" />
                </div>
                <div className="form-row">
                  <label>Crew Count</label>
                  <InputText type="number" value={data.crew} className="w-full" />
                </div>
                <div className="form-row">
                  <label>Special Requirements</label>
                  <InputTextarea rows={3} placeholder="Enter requirements" className="w-full" />
                </div>
                <div className="form-actions">
                  <Button label="Save" className="p-button-sm" />
                  <Button label="Cancel" className="p-button-outlined p-button-sm" />
                </div>
              </div>
            </div>
          </TabPanel>

          <TabPanel header="Permits">
            <div className="expansion-content">
              <div className="info-card">
                <h3><i className="pi pi-file"></i> Permits Information</h3>
                <p><strong>Flight:</strong> {data.flightNumber} | <strong>Status:</strong> <span className={`status-badge status-${data.permits.toLowerCase()}`}>{data.permits}</span></p>
                <div className="form-row">
                  <label>Permit Type</label>
                  <Dropdown placeholder="Select permit type" className="w-full" />
                </div>
                <div className="form-row">
                  <label>Permit Number</label>
                  <InputText placeholder="Enter permit number" className="w-full" />
                </div>
                <div className="form-row">
                  <label>Expiry Date</label>
                  <Calendar placeholder="Select date" className="w-full" />
                </div>
                <div className="form-row">
                  <label>Issuing Authority</label>
                  <InputText placeholder="Enter authority" className="w-full" />
                </div>
                <div className="form-row">
                  <label>Notes</label>
                  <InputTextarea rows={3} placeholder="Enter notes" className="w-full" />
                </div>
                <div className="form-actions">
                  <Button label="Save" className="p-button-sm" />
                  <Button label="Cancel" className="p-button-outlined p-button-sm" />
                </div>
              </div>
            </div>
          </TabPanel>
        </TabView>
      </div>
    );
  };

  const timeBodyTemplate = (rowData) => {
    return (
      <div className="time-cell">
        <div className="time-main">{rowData.time}</div>
        <div className="time-sub">{rowData.flightNumber}</div>
        <div className="time-detail">{rowData.registration}</div>
      </div>
    );
  };

  const routeBodyTemplate = (rowData) => {
    return (
      <div className="route-cell">
        <span className="route-from">{rowData.route.from}</span>
        <i className="pi pi-arrow-right" style={{ fontSize: '10px', margin: '0 4px' }}></i>
        <span className="route-to">{rowData.route.to}</span>
      </div>
    );
  };

  const fuelBodyTemplate = (rowData) => {
    return (
      <div className="fuel-cell">
        <div className="fuel-price">{rowData.fuel.price}</div>
        <div className="fuel-unit">{rowData.fuel.unit}</div>
      </div>
    );
  };

  const actionsBodyTemplate = (rowData) => {
    return (
      <div className="action-buttons">
        <Button icon="pi pi-check" className="p-button-rounded p-button-text p-button-sm" />
        <Button icon="pi pi-copy" className="p-button-rounded p-button-text p-button-sm" />
        <Button icon="pi pi-ellipsis-v" className="p-button-rounded p-button-text p-button-sm" />
      </div>
    );
  };

  // Generic clickable status template
  const createClickableStatus = (rowData, field, tabIndex, tabName) => {
    const status = rowData[field];
    const statusClass = `status-badge status-${status.toLowerCase()} clickable-status`;
    return (
      <span
        className={statusClass}
        onClick={(e) => {
          e.stopPropagation();
          handleStatusClick(rowData, tabIndex);
        }}
        style={{ cursor: 'pointer' }}
        title={`Click to view ${tabName} details`}
      >
        {status}
      </span>
    );
  };

  // Handle status column click
  const handleStatusClick = (rowData, tabIndex) => {
    setExpandedRows({ [rowData.id]: true });
    setActiveTabIndex({ ...activeTabIndex, [rowData.id]: tabIndex });
  };

  // Individual column templates
  const transportBodyTemplate = (rowData) => createClickableStatus(rowData, 'transport', 0, 'Transport');
  const hotelsBodyTemplate = (rowData) => createClickableStatus(rowData, 'hotels', 1, 'Hotels');
  const cateringBodyTemplate = (rowData) => createClickableStatus(rowData, 'catering', 2, 'Catering');
  const customsBodyTemplate = (rowData) => createClickableStatus(rowData, 'customs', 3, 'Customs');
  const migrationBodyTemplate = (rowData) => createClickableStatus(rowData, 'migration', 4, 'Migration');
  const permitsBodyTemplate = (rowData) => createClickableStatus(rowData, 'permits', 5, 'Permits');

  const header = (
    <div className="table-header">
      <div className="header-left">
        <h2>Service Orders</h2>
      </div>
      <div className="header-center">
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            placeholder="Search flights, tails, customers..."
          />
        </span>
        <Dropdown
          value={selectedDate}
          options={dateOptions}
          onChange={(e) => setSelectedDate(e.value)}
        />
        <Dropdown
          value={selectedTimezone}
          options={timezoneOptions}
          onChange={(e) => setSelectedTimezone(e.value)}
        />
        <Button icon="pi pi-filter" className="p-button-outlined" label="Filters" />
      </div>
      <div className="header-right">
        <Button icon="pi pi-plus" label="Add Flight/Service Order" />
      </div>
    </div>
  );

  // Row group header template
  const headerTemplate = (data) => {
    const dateOrders = serviceOrders.filter(o => o.date === data.date);
    const stats = getDateStatistics(dateOrders);

    return (
      <div className="date-summary-row">
        <span className="date-text">{data.date}</span>
        <span className="summary-text">
          Total {stats.total} Active {stats.active} Issues {stats.issues} Completed {stats.completed}
        </span>
        <Button icon="pi pi-cog" className="p-button-text p-button-sm" />
      </div>
    );
  };

  // Add progress bars to each row after render
  useEffect(() => {
    const addProgressBars = () => {
      serviceOrders.forEach((order) => {
        const rowElement = document.querySelector(`.row-${order.id}`);
        if (rowElement) {
          // Remove any existing progress bars first
          const existingBars = rowElement.querySelectorAll('.row-progress-bars');
          existingBars.forEach(bar => bar.remove());

          // Only add status bar if it exists and shouldShow is not explicitly false
          if (!order.statusBar || order.statusBar.shouldShow === false) return;

          // Find service columns by their headers
          // Service columns are: Transport, Hotels, Catering, Customs, Migration, Permits
          const cells = rowElement.querySelectorAll('td');

          // Column indices (0-based, accounting for expander column):
          // 0: Expander, 1: Time/Flight, 2: From, 3: To, 4: Crew, 5: Pax, 6: FAO
          // 7: Transport, 8: Hotels, 9: Catering, 10: Customs, 11: Migration, 12: Slot PPR, 13: Permits
          const transportIndex = 7;
          const permitsIndex = 13;

          const firstServiceCell = cells[transportIndex];
          const lastServiceCell = cells[permitsIndex];

          if (!firstServiceCell || !lastServiceCell) return;

          // Calculate position relative to row
          const rowRect = rowElement.getBoundingClientRect();
          const firstCellRect = firstServiceCell.getBoundingClientRect();
          const lastCellRect = lastServiceCell.getBoundingClientRect();

          const leftOffset = firstCellRect.left - rowRect.left;
          const width = lastCellRect.right - firstCellRect.left;

          // Create progress bars container that spans service columns only
          const progressContainer = document.createElement('div');
          progressContainer.className = 'row-progress-bars';
          progressContainer.style.cssText = `
            position: absolute !important;
            bottom: 0 !important;
            left: ${leftOffset}px !important;
            width: ${width}px !important;
            height: 4px !important;
            z-index: 10 !important;
            pointer-events: none !important;
            display: flex !important;
          `;

          // Determine color based on status
          const barColor = order.statusBar.color === 'green' ? '#22c55e' : '#ef4444';

          // Add single progress bar with background
          const progressBar = document.createElement('div');
          progressBar.className = 'progress-bar-container';
          progressBar.style.cssText = `
            width: 100%;
            height: 100%;
            background-color: rgba(229, 231, 235, 0.5);
            overflow: hidden;
          `;

          const progress = document.createElement('div');
          progress.className = 'progress-bar';
          progress.style.cssText = `
            width: ${order.statusBar.progress}%;
            height: 100%;
            background-color: ${barColor};
            transition: width 0.3s ease;
          `;

          progressBar.appendChild(progress);
          progressContainer.appendChild(progressBar);

          // Append to row
          rowElement.appendChild(progressContainer);
        }
      });
    };

    // Run multiple times to ensure it catches the DOM after render
    const timer1 = setTimeout(addProgressBars, 50);
    const timer2 = setTimeout(addProgressBars, 200);
    const timer3 = setTimeout(addProgressBars, 500);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, [serviceOrders, expandedRows]);

  return (
    <div className="service-orders-datatable">
      {header}

      <DataTable
        value={serviceOrders}
        expandedRows={expandedRows}
        onRowToggle={(e) => setExpandedRows(e.data)}
        rowExpansionTemplate={rowExpansionTemplate}
        dataKey="id"
        className="service-orders-table"
        rowGroupMode="subheader"
        groupRowsBy="date"
        sortMode="single"
        sortField="date"
        sortOrder={1}
        rowGroupHeaderTemplate={headerTemplate}
        rowClassName={(rowData) => `service-order-row row-${rowData.id}`}
      >
        <Column expander style={{ width: '3em' }} />
        <Column header="Time/Flight/Customer" body={timeBodyTemplate} style={{ minWidth: '150px' }} />
        <Column field="route.from" header="From" style={{ width: '80px' }} />
        <Column field="route.to" header="To" style={{ width: '80px' }} />
        <Column field="crew" header="Crew" style={{ width: '70px' }} />
        <Column field="pax" header="Pax" style={{ width: '70px' }} />
        <Column header="FAO" style={{ width: '80px' }} />
        <Column header="Transport" body={transportBodyTemplate} style={{ width: '100px' }} />
        <Column header="Hotels" body={hotelsBodyTemplate} style={{ width: '100px' }} />
        <Column header="Catering" body={cateringBodyTemplate} style={{ width: '100px' }} />
        <Column header="Customs" body={customsBodyTemplate} style={{ width: '100px' }} />
        <Column header="Migration" body={migrationBodyTemplate} style={{ width: '100px' }} />
        <Column header="Slot PPR" field="slotPPR" style={{ width: '100px' }} />
        <Column header="Permits" body={permitsBodyTemplate} style={{ width: '100px' }} />
        <Column header="FH, WB8" body={fuelBodyTemplate} style={{ width: '100px' }} />
        <Column header="Fuel" field="tasks" style={{ width: '70px' }} />
        <Column header="Tasks" field="tasks" style={{ width: '70px' }} />
        <Column body={actionsBodyTemplate} style={{ width: '120px' }} />
      </DataTable>
    </div>
  );
};

export default ServiceOrdersDataTable;

