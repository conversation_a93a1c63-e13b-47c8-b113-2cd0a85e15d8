import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import apiService from '../../mvc/services/ApiService';

export const fetchWithCache = createAsyncThunk(
  'apiCache/fetchWithCache',
  async ({ endpoint, options = {}, cacheOptions = {} }, { dispatch, getState }) => {
    const {
      method = 'GET',
      params = {},
      data = null,
      headers = {}
    } = options;

    const {
      ttl = 2 * 60 * 1000, // 2 minutes default
      staleWhileRevalidate = true,
      backgroundRefresh = false,
      tags = [],
      priority = 1
    } = cacheOptions;

    const cacheKey = generateApiCacheKey(endpoint, method, params, data);
    
    const state = getState();
    const existingEntry = state.apiCache.entries[cacheKey];
    const now = Date.now();

    if (existingEntry && existingEntry.expiresAt > now) {
      dispatch(apiCacheSlice.actions.recordHit({ key: cacheKey }));
      return {
        key: cacheKey,
        data: existingEntry.data,
        fromCache: true,
        stale: false
      };
    }

    if (existingEntry && staleWhileRevalidate) {
      dispatch(apiCacheSlice.actions.recordHit({ key: cacheKey, stale: true }));
      
      if (backgroundRefresh) {
        dispatch(backgroundRefreshData({ endpoint, options, cacheOptions, cacheKey }));
      }
      
      return {
        key: cacheKey,
        data: existingEntry.data,
        fromCache: true,
        stale: true
      };
    }

    // Fetch fresh data
    try {
      let response;
      
      switch (method.toUpperCase()) {
        case 'GET':
          response = await apiService.get(endpoint, { params, headers });
          break;
        case 'POST':
          response = await apiService.post(endpoint, data, { params, headers });
          break;
        case 'PUT':
          response = await apiService.put(endpoint, data, { params, headers });
          break;
        case 'DELETE':
          response = await apiService.delete(endpoint, { params, headers });
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }

      // Cache the response
      const entry = {
        data: response,
        createdAt: now,
        lastAccessed: now,
        expiresAt: now + ttl,
        tags,
        priority,
        endpoint,
        method,
        params,
        requestData: data,
        headers,
        size: JSON.stringify(response).length,
        version: 1
      };

      dispatch(apiCacheSlice.actions.set({ key: cacheKey, entry }));
      dispatch(apiCacheSlice.actions.recordMiss({ key: cacheKey }));

      return {
        key: cacheKey,
        data: response,
        fromCache: false,
        stale: false
      };

    } catch (error) {
      dispatch(apiCacheSlice.actions.recordError({ key: cacheKey, error: error.message }));
      throw error;
    }
  }
);

export const backgroundRefreshData = createAsyncThunk(
  'apiCache/backgroundRefresh',
  async ({ endpoint, options, cacheOptions, cacheKey }, { dispatch }) => {
    try {
      const { method = 'GET', params = {}, data = null, headers = {} } = options;
      const { ttl = 2 * 60 * 1000, tags = [], priority = 1 } = cacheOptions;

      let response;
      
      switch (method.toUpperCase()) {
        case 'GET':
          response = await apiService.get(endpoint, { params, headers });
          break;
        case 'POST':
          response = await apiService.post(endpoint, data, { params, headers });
          break;
        case 'PUT':
          response = await apiService.put(endpoint, data, { params, headers });
          break;
        case 'DELETE':
          response = await apiService.delete(endpoint, { params, headers });
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }

      const now = Date.now();
      const entry = {
        data: response,
        createdAt: now,
        lastAccessed: now,
        expiresAt: now + ttl,
        tags,
        priority,
        endpoint,
        method,
        params,
        requestData: data,
        headers,
        size: JSON.stringify(response).length,
        version: 1,
        backgroundRefresh: true
      };

      dispatch(apiCacheSlice.actions.set({ key: cacheKey, entry }));
      
      return { key: cacheKey, data: response };
    } catch (error) {
      console.error('Background refresh failed:', error);
      throw error;
    }
  }
);

export const invalidateApiCache = createAsyncThunk(
  'apiCache/invalidate',
  async ({ endpoints, tags, patterns }, { dispatch, getState }) => {
    const state = getState();
    const entries = state.apiCache.entries;
    const keysToInvalidate = [];

    if (endpoints) {
      // Invalidate by specific endpoints
      endpoints.forEach(endpoint => {
        Object.keys(entries).forEach(key => {
          const entry = entries[key];
          if (entry.endpoint === endpoint) {
            keysToInvalidate.push(key);
          }
        });
      });
    }

    if (tags) {
      // Invalidate by tags
      const tagsSet = new Set(tags);
      Object.keys(entries).forEach(key => {
        const entry = entries[key];
        if (entry.tags && entry.tags.some(tag => tagsSet.has(tag))) {
          keysToInvalidate.push(key);
        }
      });
    }

    if (patterns) {
      // Invalidate by regex patterns
      patterns.forEach(pattern => {
        const regex = new RegExp(pattern);
        Object.keys(entries).forEach(key => {
          const entry = entries[key];
          if (regex.test(entry.endpoint)) {
            keysToInvalidate.push(key);
          }
        });
      });
    }

    // Remove duplicates
    const uniqueKeys = [...new Set(keysToInvalidate)];
    
    uniqueKeys.forEach(key => {
      dispatch(apiCacheSlice.actions.delete({ key }));
    });

    return { invalidatedKeys: uniqueKeys };
  }
);

const initialState = {
  entries: {},
  analytics: {
    hits: 0,
    misses: 0,
    staleHits: 0,
    errors: 0,
    backgroundRefreshes: 0,
    hitRate: 0,
    totalRequests: 0
  },
  config: {
    maxEntries: 500,
    defaultTTL: 2 * 60 * 1000, // 2 minutes
    staleWhileRevalidate: true,
    backgroundRefresh: true,
    strategy: 'lru'
  },
  loading: {},
  errors: {}
};

const apiCacheSlice = createSlice({
  name: 'apiCache',
  initialState,
  reducers: {
    set: (state, action) => {
      const { key, entry } = action.payload;
      
      // Apply eviction strategy if needed
      if (Object.keys(state.entries).length >= state.config.maxEntries) {
        state.entries = applyApiEvictionStrategy(state.entries, state.config.strategy, state.config.maxEntries);
      }
      
      state.entries[key] = entry;
    },

    delete: (state, action) => {
      const { key } = action.payload;
      delete state.entries[key];
      delete state.loading[key];
      delete state.errors[key];
    },

    clear: (state) => {
      state.entries = {};
      state.loading = {};
      state.errors = {};
    },

    recordHit: (state, action) => {
      const { key, stale = false } = action.payload;
      
      if (stale) {
        state.analytics.staleHits++;
      } else {
        state.analytics.hits++;
      }
      
      state.analytics.totalRequests++;
      state.analytics.hitRate = ((state.analytics.hits + state.analytics.staleHits) / state.analytics.totalRequests) * 100;
      
      // Update last accessed
      if (state.entries[key]) {
        state.entries[key].lastAccessed = Date.now();
      }
    },

    recordMiss: (state, action) => {
      state.analytics.misses++;
      state.analytics.totalRequests++;
      state.analytics.hitRate = ((state.analytics.hits + state.analytics.staleHits) / state.analytics.totalRequests) * 100;
    },

    recordError: (state, action) => {
      const { key, error } = action.payload;
      state.analytics.errors++;
      state.errors[key] = error;
    },

    recordBackgroundRefresh: (state) => {
      state.analytics.backgroundRefreshes++;
    },

    cleanupExpired: (state) => {
      const now = Date.now();
      const validEntries = {};
      
      Object.entries(state.entries).forEach(([key, entry]) => {
        if (!entry.expiresAt || entry.expiresAt > now) {
          validEntries[key] = entry;
        }
      });
      
      state.entries = validEntries;
    },

    updateConfig: (state, action) => {
      state.config = { ...state.config, ...action.payload };
    },

    setLoading: (state, action) => {
      const { key, loading } = action.payload;
      if (loading) {
        state.loading[key] = true;
      } else {
        delete state.loading[key];
      }
    }
  },

  extraReducers: (builder) => {
    builder
      .addCase(fetchWithCache.pending, (state, action) => {
        const { endpoint, options = {} } = action.meta.arg;
        const cacheKey = generateApiCacheKey(endpoint, options.method || 'GET', options.params || {}, options.data);
        state.loading[cacheKey] = true;
        delete state.errors[cacheKey];
      })
      .addCase(fetchWithCache.fulfilled, (state, action) => {
        const { key } = action.payload;
        delete state.loading[key];
      })
      .addCase(fetchWithCache.rejected, (state, action) => {
        const { endpoint, options = {} } = action.meta.arg;
        const cacheKey = generateApiCacheKey(endpoint, options.method || 'GET', options.params || {}, options.data);
        delete state.loading[cacheKey];
        state.errors[cacheKey] = action.error.message;
      })
      .addCase(backgroundRefreshData.fulfilled, (state, action) => {
        state.analytics.backgroundRefreshes++;
      })
      .addCase(backgroundRefreshData.rejected, (state, action) => {
        console.error('Background refresh failed:', action.error);
      });
  }
});

function generateApiCacheKey(endpoint, method, params, data) {
  const keyData = {
    endpoint,
    method: method.toUpperCase(),
    params: params || {},
    data: data || null
  };
  
  return btoa(JSON.stringify(keyData)).replace(/[+/=]/g, '');
}

function applyApiEvictionStrategy(entries, strategy, maxEntries) {
  const entryCount = Object.keys(entries).length;
  if (entryCount <= maxEntries) return entries;

  switch (strategy) {
    case 'lru': {
      const sortedEntries = Object.entries(entries)
        .sort(([, a], [, b]) => (b.lastAccessed || 0) - (a.lastAccessed || 0));
      
      const newEntries = {};
      sortedEntries.slice(0, maxEntries).forEach(([key, entry]) => {
        newEntries[key] = entry;
      });
      
      return newEntries;
    }

    case 'endpoint-priority': {
      // Prioritize certain endpoints
      const priorityEndpoints = ['/user', '/auth', '/config'];
      const sortedEntries = Object.entries(entries)
        .sort(([, a], [, b]) => {
          const aPriority = priorityEndpoints.includes(a.endpoint) ? 10 : (a.priority || 1);
          const bPriority = priorityEndpoints.includes(b.endpoint) ? 10 : (b.priority || 1);
          return bPriority - aPriority;
        });
      
      const newEntries = {};
      sortedEntries.slice(0, maxEntries).forEach(([key, entry]) => {
        newEntries[key] = entry;
      });
      
      return newEntries;
    }

    default:
      return applyApiEvictionStrategy(entries, 'lru', maxEntries);
  }
}

// Selectors
export const selectApiCacheEntry = (state, key) => state.apiCache.entries[key];
export const selectApiCacheEntries = (state) => state.apiCache.entries;
export const selectApiCacheAnalytics = (state) => state.apiCache.analytics;
export const selectApiCacheConfig = (state) => state.apiCache.config;
export const selectApiCacheLoading = (state, key) => state.apiCache.loading[key] || false;
export const selectApiCacheError = (state, key) => state.apiCache.errors[key];
export const selectApiCacheByEndpoint = (state, endpoint) => {
  return Object.entries(state.apiCache.entries)
    .filter(([, entry]) => entry.endpoint === endpoint)
    .reduce((acc, [key, entry]) => {
      acc[key] = entry;
      return acc;
    }, {});
};

export const {
  set,
  delete: deleteEntry,
  clear,
  recordHit,
  recordMiss,
  recordError,
  recordBackgroundRefresh,
  cleanupExpired,
  updateConfig,
  setLoading
} = apiCacheSlice.actions;

export default apiCacheSlice.reducer;
