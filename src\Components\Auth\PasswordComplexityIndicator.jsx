import React from 'react';
import PropTypes from 'prop-types';
import { getPasswordRequirements, getPasswordStrength } from '../../Utils/passwordValidation';

const PasswordComplexityIndicator = ({
  password,
  username,
  showStrengthBar = true,
  showRequirements = true,
  className = ''
}) => {
  const requirements = getPasswordRequirements();
  const strength = getPasswordStrength(password);

  return (
    <div className={`password-complexity-indicator ${className}`}>
      {showStrengthBar && password && (
        <div className="password-strength-bar mb-2">
          <div
            className="progress-bar"
            style={{
              width: `${strength.percentage}%`,
              backgroundColor: strength.color,
              height: '4px',
              borderRadius: '2px',
              transition: 'all 0.3s ease'
            }}
          />
          <div className="text-sm mt-1" style={{ color: strength.color }}>
            Password Strength: {strength.strength}
          </div>
        </div>
      )}

      {showRequirements && (
        <div className="password-requirements">
          <ul className="list-none p-0 m-0">
            {requirements.map((req) => {
              const isMet = req.check(password, username);
              return (
                <li
                  key={req.id}
                  className="text-sm mb-1 flex items-center"
                  style={{
                    color: password ? (isMet ? '#2ed573' : '#ff4757') : '#666'
                  }}
                >
                  <i
                    className={`pi ${
                      password
                        ? isMet
                          ? 'pi-check-circle'
                          : 'pi-times-circle'
                        : 'pi-circle'
                    } mr-2`}
                  />
                  {req.text}
                  {req.optional && (
                    <span className="text-xs ml-1">(optional)</span>
                  )}
                </li>
              );
            })}
          </ul>
        </div>
      )}
    </div>
  );
};

PasswordComplexityIndicator.propTypes = {
  password: PropTypes.string.isRequired,
  username: PropTypes.string,
  showStrengthBar: PropTypes.bool,
  showRequirements: PropTypes.bool,
  className: PropTypes.string
};

export default PasswordComplexityIndicator;
