/* Service Orders DataTable Container */
.service-orders-datatable {
  width: 100%;
  background-color: #f5f7fa;
  min-height: 100vh;
}

/* Table Header */
.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background-color: #ffffff;
  border-bottom: 1px solid #e5e7eb;
}

.header-left h2 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #1f2937;
}

.header-center {
  display: flex;
  gap: 12px;
  align-items: center;
  flex: 1;
  justify-content: center;
}

.header-center .p-inputtext {
  width: 300px;
}

.header-right {
  display: flex;
  gap: 8px;
}

.date-summary-row {
  display: flex;
  align-items: center;
  padding: 12px 24px;
  background-color: #e8edf5 !important;
  border-bottom: 2px solid #3b5998;
  gap: 16px;
  width: 100%;
}

.date-text {
  font-weight: 600;
  color: #1f2937;
  font-size: 14px;
}

.summary-text {
  font-size: 13px;
  color: #6b7280;
  margin-left: auto;
  margin-right: 16px;
  font-weight: 500;
}

.date-summary-row .p-button {
  margin-left: auto;
}

/* Row Group Styling */
.service-orders-table .p-rowgroup-header {
  background-color: #e8edf5 !important;
  border: none !important;
  padding: 0 !important;
}

.service-orders-table .p-rowgroup-header>td {
  padding: 0 !important;
  border: none !important;
}

/* Service Orders Table */
.service-orders-table {
  background-color: #ffffff;
}

.service-orders-table .p-datatable-thead>tr>th {
  background-color: #3b5998;
  color: #ffffff;
  font-weight: 600;
  font-size: 12px;
  padding: 12px 8px;
  border: none;
  text-align: center;
}

.service-orders-table .p-datatable-tbody>tr>td {
  padding: 8px;
  border: 1px solid #e5e7eb;
  font-size: 13px;
  text-align: center;
  vertical-align: middle;
}

.service-orders-table .p-datatable-tbody>tr {
  background-color: #ffffff;
}

.service-orders-table .p-datatable-tbody>tr:hover {
  background-color: #f9fafb;
}

/* Time Cell */
.time-cell-wrapper {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
}

.time-cell {
  text-align: left;
  padding: 4px 0;
}

.time-main {
  font-weight: 600;
  font-size: 14px;
  color: #1f2937;
  margin-bottom: 2px;
}

.time-sub {
  font-size: 12px;
  color: #3b5998;
  font-weight: 500;
  margin-bottom: 2px;
}

.time-detail {
  font-size: 11px;
  color: #6b7280;
}

/* Row Progress Bars - positioned at row level */
.service-order-row {
  position: relative !important;
}

/* Ensure PrimeReact table rows have relative positioning */
.p-datatable .p-datatable-tbody>tr.service-order-row,
.p-datatable .p-datatable-tbody>tr[class*="dynamic-row-"] {
  position: relative !important;
}

.row-progress-bars {
  position: absolute !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
  width: 100% !important;
  display: flex;
  pointer-events: none;
  z-index: 10 !important;
  height: 4px;
}

.row-progress-bars .progress-bar-container {
  width: 100%;
  height: 100%;
  background-color: rgba(229, 231, 235, 0.5);
  overflow: hidden;
}

.row-progress-bars .progress-bar {
  height: 100%;
  transition: width 0.3s ease;
}

/* Route Cell */
.route-cell {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 4px;
}

.route-from,
.route-to {
  font-weight: 500;
  font-size: 12px;
  color: #1f2937;
}

/* Fuel Cell */
.fuel-cell {
  text-align: center;
}

.fuel-price {
  font-weight: 600;
  font-size: 13px;
  color: #1f2937;
}

.fuel-unit {
  font-size: 11px;
  color: #6b7280;
}

/* Status Badges */
.status-badge {
  display: inline-block;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  transition: all 0.2s ease;
}

.status-badge.clickable-status {
  cursor: pointer;
  position: relative;
}

.status-badge.clickable-status:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  filter: brightness(0.95);
}

.status-badge.clickable-status:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.status-ok {
  background-color: #d1fae5;
  color: #065f46;
}

.status-ok.clickable-status:hover {
  background-color: #a7f3d0;
}

.status-tbd {
  background-color: #fef3c7;
  color: #92400e;
}

.status-tbd.clickable-status:hover {
  background-color: #fde68a;
}

.status-nr {
  background-color: #fee2e2;
  color: #991b1b;
}

.status-nr.clickable-status:hover {
  background-color: #fecaca;
}

.status-na {
  background-color: #e5e7eb;
  color: #4b5563;
}

.status-na.clickable-status:hover {
  background-color: #d1d5db;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
  align-items: center;
}

.action-buttons .p-button {
  /* width: 28px; */
  height: 28px;
}

/* Progress Bar Container */
.progress-bar-container {
  width: 100%;
  height: 6px;
  background-color: #e5e7eb;
  border-radius: 3px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  transition: width 0.3s ease;
  border-radius: 3px;
}

/* Status Bars at Bottom */
.status-bars-container {
  position: relative;
  background-color: #ffffff;
  border-top: 2px solid #3b5998;
}

.status-bar-row {
  height: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.status-bar-group {
  display: flex;
  height: 100%;
}

.status-bar-group .progress-bar-container {
  flex: 1;
  height: 100%;
  border-radius: 0;
  background-color: transparent;
}

.status-bar-group .progress-bar-container:first-child .progress-bar {
  background-color: #22c55e;
}

.status-bar-group .progress-bar-container:last-child .progress-bar {
  background-color: #ef4444;
}

.expansion-panel {
  position: relative;
  padding: 10px;
}

.expansion-pointer {
  pointer-events: none; 
}

.expansion-panel {
  background-color: #f9fafb;
  padding: 0;
  /* border-top: 2px solid #3b5998; */
  position: relative;
  box-shadow: 0px 10px 15px -3px rgba(0, 0, 0, 0.10), 0px 4px 6px 0px rgba(0, 0, 0, 0.05);
  margin: 30px;
  border-radius: 8px;
}

.expansion-header-bar {
  position: absolute;
  top: 0px;
  right: 10px;
  z-index: 100;
}

.close-expansion-btn {
  color: #6c757d !important;
  transition: all 0.2s ease;
  /* background-color: rgba(255, 255, 255, 0.9) !important; */
}

.close-expansion-btn:hover {
  color: #dc3545 !important;
  /* background-color: #fee2e2 !important; */
  border: none;
  transform: rotate(90deg);
}

.expansion-panel .p-tabview {
  background-color: transparent;
}

.expansion-panel .p-tabview-nav {
  background-color: #e8edf5;
  border: none;
   border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  padding: 0 16px;
}

.expansion-panel .p-tabview-nav li .p-tabview-nav-link {
  background-color: transparent;
  border: none;
  color: #4b5563;
  font-weight: 500;
  font-size: 13px;
  padding: 12px 16px;
}

.expansion-panel .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  /* background-color: #ffffff; */
  color: #3b5998;
  border-bottom: 2px solid #3b5998;
}

.expansion-panel .p-tabview-panels {
  background-color: #ffffff;
  padding: 0;
  border: none;
}

/* Expansion Content */
.expansion-content {
  padding: 24px;
}

/* Info Cards */
.info-card {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  margin: 10px 0;
}

.info-card h3 {
  color: #3b5998;
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 15px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.info-card h3 i {
  font-size: 18px;
}

.info-card p {
  margin: 0 0 20px 0;
  color: #495057;
  font-size: 14px;
}

.info-card .form-row {
  margin-bottom: 15px;
}

.info-card .form-row label {
  display: block;
  margin-bottom: 5px;
  color: #495057;
  font-size: 13px;
  font-weight: 500;
}

.info-card .w-full {
  width: 100%;
}

.info-card .form-actions {
  display: flex;
  gap: 10px;
  margin-top: 20px;
}

/* Catering Panel */
.catering-panel {
  display: flex;
  gap: 24px;
  padding: 24px;
  background-color: #ffffff;
}

.catering-section {
  flex: 1;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.catering-header {
  background-color: #3b5998;
  color: #ffffff;
  padding: 12px 16px;
  font-weight: 600;
  font-size: 14px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.catering-form {
  padding: 16px;
}

.form-row {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
  align-items: center;
}

.form-row label {
  font-size: 13px;
  font-weight: 500;
  color: #4b5563;
  min-width: 80px;
}

.form-row .p-dropdown,
.form-row .p-inputtext {
  flex: 1;
}

.file-upload-area {
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  padding: 32px;
  text-align: center;
  background-color: #f9fafb;
  margin-bottom: 12px;
}

.file-upload-area p {
  margin: 0;
  font-size: 13px;
  color: #6b7280;
}

.file-upload-area a {
  color: #3b5998;
  text-decoration: underline;
  cursor: pointer;
}

.form-actions {
  display: flex;
  gap: 8px;
  margin-bottom: 16px;
}

.expenses-section {
  display: flex;
  gap: 16px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.expenses-section a {
  font-size: 13px;
  color: #3b5998;
  text-decoration: none;
  display: flex;
  align-items: center;
  gap: 4px;
}

.expenses-section a:hover {
  text-decoration: underline;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .catering-panel {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .table-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .header-center {
    flex-direction: column;
    align-items: stretch;
  }

  .header-center .p-inputtext {
    width: 100%;
  }
}