// components/common/AppButton.jsx
import React from "react";
import { Button } from "primereact/button";

export default function GlassyWhiteButton({
  label,
  icon,
  severity = "primary",
  loading = false,
  onClick,
  className = ""
}) {
  return (
    <Button
      label={label}
      icon={icon}
      severity={severity}
      loading={loading}
      onClick={onClick}
      className={`glass-btn-secondary ${className}`}
    />
  );
}
