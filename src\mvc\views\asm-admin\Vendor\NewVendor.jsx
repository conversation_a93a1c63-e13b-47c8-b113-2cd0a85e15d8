import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
// import GlassyWhiteButton from '@components/Common/Buttons/GlassyWhiteButton';
import DynamicCollectionFormGrid from '@utils/dynamicForm/DynamicFormComponentGrid';
import FormLoader from '@components/Common/Loader/FormLoader';
import { formCollections } from '@utils/dynamicForm/FormCollections';

const NewVendor = () => {
    const navigate = useNavigate();
    const [visible, setVisible] = useState(true);
     const handleClose = () => {
        navigate('/vendors');
    };

    const handleVendorFormSubmit = (formData) => {
        console.log('Vendor Form Data:', formData);
        // Handle vendor form submission here
        // You can add API call to save the data
        navigate('/vendors');
    };

    const handleLoadComplete = () => {
      setIsLoading(false);
    };
    return (
        <div className="glass-container">
            <div className="pt-0 pr-6 pb-6 pl-6 space-y-6 mt-2">
                <div className="flex justify-between items-center mb-4">
                 </div>
            
               <DynamicCollectionFormGrid
                    visible={visible}
                    setVisible={setVisible}
                    data={formCollections}
                    collectionNameToRender={'aviation_services_management_asm_employee_supplier'}
                    onSubmit={handleVendorFormSubmit}
                    onLoadComplete={handleLoadComplete}
                    // className="glass-card-global"
                />
            </div>
        </div>
    );
};

export default NewVendor;
