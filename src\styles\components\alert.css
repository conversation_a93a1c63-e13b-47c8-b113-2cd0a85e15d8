.custom-alert {
  border-radius: 8px;
  padding: 12px 16px;
  display: flex;
  align-items: center;
  gap: 12px;
  font-family: "Roboto", sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  width: 100%;
  box-sizing: border-box;
  position: relative;
}

.alert-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.alert-message {
  flex: 1;
  margin: 0;
}

.alert-close {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  background: none;
  border: none;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.alert-close:hover {
  opacity: 1;
}

/* Alert Types */
.alert-success {
  background-color: #f0fdf4;
  color: #15803D;
  border: 1px solid #BBF7D0;
}

.alert-error {
  background-color: #FECACA;
  color: #DC2626;
  border: 1px solid #FECACA;
}

.alert-warning {
  background-color: #FEF3C7;
  color: #D97706;
  border: 1px solid #FEF3C7;
}

.alert-info {
  background-color: #DBEAFE;
  color: #2563EB;
  border: 1px solid #DBEAFE;
}

/* Animation */
@keyframes fadeOut {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

.alert-fade-out {
  animation: fadeOut 0.3s ease-out forwards;
}

/* ================================
   Responsive sizing for alert text
   - Tablets: slightly smaller
   - Mobiles: smaller
   Keeps desktop default (14px)
   ================================ */
@media (max-width: 768px) {
  .custom-alert {
    font-size: 15px;
    line-height: 18px;
    padding: 10px 40px 10px 14px; /* extra right padding for close button */
    gap: 10px;
  }
  .alert-message { padding-right: 8px; }
  .alert-close { right: 8px; }
  .alert-icon {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .custom-alert {
    font-size: 13px;
    line-height: 18px;
    padding: 10px 44px 10px 12px; /* extra right padding for close button */
    gap: 8px;
  }
  .alert-message { padding-right: 10px; }
  .alert-close { right: 8px; }
  .alert-icon {
    font-size: 13px;
  }
}
