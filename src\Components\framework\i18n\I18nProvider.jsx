import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { IntlProvider, useIntl } from 'react-intl';

const I18nContext = createContext({
  currentLocale: 'en',
  locales: [],
  setLocale: () => {},
  messages: {},
  isRTL: false,
  formatMessage: () => '',
  formatDate: () => '',
  formatTime: () => '',
  formatNumber: () => ''
});

const AVAILABLE_LOCALES = [
  {
    code: 'en',
    name: 'English',
    nativeName: 'English',
    flag: '🇺🇸',
    rtl: false,
    dateFormat: 'MM/dd/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: 'USD',
      currencySymbol: '$'
    }
  },
  {
    code: 'es',
    name: 'Spanish',
    nativeName: 'Español',
    flag: '🇪🇸',
    rtl: false,
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: '.',
      currency: 'EUR',
      currencySymbol: '€'
    }
  },
  {
    code: 'fr',
    name: 'French',
    nativeName: 'Français',
    flag: '🇫🇷',
    rtl: false,
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: ' ',
      currency: 'EUR',
      currencySymbol: '€'
    }
  },
  {
    code: 'de',
    name: 'German',
    nativeName: 'Deutsch',
    flag: '🇩🇪',
    rtl: false,
    dateFormat: 'dd.MM.yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: '.',
      currency: 'EUR',
      currencySymbol: '€'
    }
  },
  {
    code: 'it',
    name: 'Italian',
    nativeName: 'Italiano',
    flag: '🇮🇹',
    rtl: false,
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: '.',
      currency: 'EUR',
      currencySymbol: '€'
    }
  },
  {
    code: 'pt',
    name: 'Portuguese',
    nativeName: 'Português',
    flag: '🇵🇹',
    rtl: false,
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: ' ',
      currency: 'EUR',
      currencySymbol: '€'
    }
  },
  {
    code: 'ru',
    name: 'Russian',
    nativeName: 'Русский',
    flag: '🇷🇺',
    rtl: false,
    dateFormat: 'dd.MM.yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: ',',
      thousands: ' ',
      currency: 'RUB',
      currencySymbol: '₽'
    }
  },
  {
    code: 'zh',
    name: 'Chinese',
    nativeName: '中文',
    flag: '🇨🇳',
    rtl: false,
    dateFormat: 'yyyy/MM/dd',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: 'CNY',
      currencySymbol: '¥'
    }
  },
  {
    code: 'ja',
    name: 'Japanese',
    nativeName: '日本語',
    flag: '🇯🇵',
    rtl: false,
    dateFormat: 'yyyy/MM/dd',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: 'JPY',
      currencySymbol: '¥'
    }
  },
  {
    code: 'ko',
    name: 'Korean',
    nativeName: '한국어',
    flag: '🇰🇷',
    rtl: false,
    dateFormat: 'yyyy.MM.dd',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: 'KRW',
      currencySymbol: '₩'
    }
  },
  {
    code: 'ar',
    name: 'Arabic',
    nativeName: 'العربية',
    flag: '🇸🇦',
    rtl: true,
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: 'SAR',
      currencySymbol: 'ر.س'
    }
  },
  {
    code: 'hi',
    name: 'Hindi',
    nativeName: 'हिन्दी',
    flag: '🇮🇳',
    rtl: false,
    dateFormat: 'dd/MM/yyyy',
    timeFormat: 'HH:mm',
    numberFormat: {
      decimal: '.',
      thousands: ',',
      currency: 'INR',
      currencySymbol: '₹'
    }
  }
];

const DEFAULT_MESSAGES = {
  en: {
    'common.loading': 'Loading...',
    'common.error': 'Error',
    'common.success': 'Success',
    'common.warning': 'Warning',
    'common.info': 'Information',
    'common.cancel': 'Cancel',
    'common.ok': 'OK',
    'common.yes': 'Yes',
    'common.no': 'No',
    'common.save': 'Save',
    'common.delete': 'Delete',
    'common.edit': 'Edit',
    'common.add': 'Add',
    'common.remove': 'Remove',
    'common.search': 'Search',
    'common.filter': 'Filter',
    'common.clear': 'Clear',
    'common.reset': 'Reset',
    'common.submit': 'Submit',
    'common.close': 'Close',
    'common.back': 'Back',
    'common.next': 'Next',
    'common.previous': 'Previous',
    'common.select': 'Select',
    'common.selectAll': 'Select All',
    'common.deselectAll': 'Deselect All',
    
    'form.required': 'This field is required',
    'form.invalid': 'Invalid value',
    'form.minLength': 'Minimum length is {min} characters',
    'form.maxLength': 'Maximum length is {max} characters',
    'form.email': 'Please enter a valid email address',
    'form.number': 'Please enter a valid number',
    'form.date': 'Please enter a valid date',
    'form.url': 'Please enter a valid URL',
    'form.phone': 'Please enter a valid phone number',
    
    'datatable.noRecords': 'No records found',
    'datatable.loading': 'Loading data...',
    'datatable.rowsPerPage': 'Rows per page',
    'datatable.of': 'of',
    'datatable.first': 'First',
    'datatable.last': 'Last',
    'datatable.next': 'Next',
    'datatable.previous': 'Previous',
    'datatable.sortAscending': 'Sort ascending',
    'datatable.sortDescending': 'Sort descending',
    'datatable.filter': 'Filter',
    'datatable.globalFilter': 'Global search',
    'datatable.export': 'Export',
    'datatable.print': 'Print',
    
    'theme.selectTheme': 'Select Theme',
    'theme.lightMode': 'Light Mode',
    'theme.darkMode': 'Dark Mode',
    'theme.customTheme': 'Custom Theme',
    'theme.applyTheme': 'Apply Theme',
    
    'language.selectLanguage': 'Select Language',
    'language.currentLanguage': 'Current Language',
    
    'nav.home': 'Home',
    'nav.dashboard': 'Dashboard',
    'nav.components': 'Components',
    'nav.forms': 'Forms',
    'nav.tables': 'Tables',
    'nav.settings': 'Settings',
    'nav.help': 'Help',
    'nav.logout': 'Logout',
    
    'demo.title': 'Dynamic Component Framework Demo',
    'demo.description': 'Explore the comprehensive component framework with theme and language support',
    'demo.componentShowcase': 'Component Showcase',
    'demo.formComponents': 'Form Components',
    'demo.layoutComponents': 'Layout Components',
    'demo.dataComponents': 'Data Components',
    'demo.messageComponents': 'Message Components'
  },
  es: {
    'common.loading': 'Cargando...',
    'common.error': 'Error',
    'common.success': 'Éxito',
    'common.warning': 'Advertencia',
    'common.info': 'Información',
    'common.cancel': 'Cancelar',
    'common.ok': 'OK',
    'common.yes': 'Sí',
    'common.no': 'No',
    'common.save': 'Guardar',
    'common.delete': 'Eliminar',
    'common.edit': 'Editar',
    'common.add': 'Agregar',
    'common.remove': 'Quitar',
    'common.search': 'Buscar',
    'common.filter': 'Filtrar',
    'common.clear': 'Limpiar',
    'common.reset': 'Restablecer',
    'common.submit': 'Enviar',
    'common.close': 'Cerrar',
    'common.back': 'Atrás',
    'common.next': 'Siguiente',
    'common.previous': 'Anterior',
    'common.select': 'Seleccionar',
    'common.selectAll': 'Seleccionar Todo',
    'common.deselectAll': 'Deseleccionar Todo',
    
    
    'form.required': 'Este campo es obligatorio',
    'form.invalid': 'Valor inválido',
    'form.minLength': 'La longitud mínima es {min} caracteres',
    'form.maxLength': 'La longitud máxima es {max} caracteres',
    'form.email': 'Por favor ingrese un email válido',
    'form.number': 'Por favor ingrese un número válido',
    'form.date': 'Por favor ingrese una fecha válida',
    'form.url': 'Por favor ingrese una URL válida',
    'form.phone': 'Por favor ingrese un teléfono válido',
    
    'theme.selectTheme': 'Seleccionar Tema',
    'theme.lightMode': 'Modo Claro',
    'theme.darkMode': 'Modo Oscuro',
    'theme.customTheme': 'Tema Personalizado',
    'theme.applyTheme': 'Aplicar Tema',
    
    'language.selectLanguage': 'Seleccionar Idioma',
    'language.currentLanguage': 'Idioma Actual',
    
    'demo.title': 'Demo del Framework de Componentes Dinámicos',
    'demo.description': 'Explora el framework completo de componentes con soporte de temas e idiomas',
    'demo.componentShowcase': 'Muestra de Componentes',
    'demo.formComponents': 'Componentes de Formulario',
    'demo.layoutComponents': 'Componentes de Diseño',
    'demo.dataComponents': 'Componentes de Datos',
    'demo.messageComponents': 'Componentes de Mensaje'
  },
  fr: {
    'common.loading': 'Chargement...',
    'common.error': 'Erreur',
    'common.success': 'Succès',
    'common.warning': 'Avertissement',
    'common.info': 'Information',
    'common.cancel': 'Annuler',
    'common.ok': 'OK',
    'common.yes': 'Oui',
    'common.no': 'Non',
    'common.save': 'Enregistrer',
    'common.delete': 'Supprimer',
    'common.edit': 'Modifier',
    'common.add': 'Ajouter',
    'common.remove': 'Retirer',
    'common.search': 'Rechercher',
    'common.filter': 'Filtrer',
    'common.clear': 'Effacer',
    'common.reset': 'Réinitialiser',
    'common.submit': 'Soumettre',
    'common.close': 'Fermer',
    'common.back': 'Retour',
    'common.next': 'Suivant',
    'common.previous': 'Précédent',
    'common.select': 'Sélectionner',
    'common.selectAll': 'Tout Sélectionner',
    'common.deselectAll': 'Tout Désélectionner',
    
    'theme.selectTheme': 'Sélectionner le Thème',
    'theme.lightMode': 'Mode Clair',
    'theme.darkMode': 'Mode Sombre',
    'theme.customTheme': 'Thème Personnalisé',
    'theme.applyTheme': 'Appliquer le Thème',
    
    'language.selectLanguage': 'Sélectionner la Langue',
    'language.currentLanguage': 'Langue Actuelle',
    
    'demo.title': 'Démo du Framework de Composants Dynamiques',
    'demo.description': 'Explorez le framework complet de composants avec support des thèmes et langues',
    'demo.componentShowcase': 'Vitrine des Composants',
    'demo.formComponents': 'Composants de Formulaire',
    'demo.layoutComponents': 'Composants de Mise en Page',
    'demo.dataComponents': 'Composants de Données',
    'demo.messageComponents': 'Composants de Message'
  }
};

const I18nProvider = ({ 
  children, 
  defaultLocale = 'en',
  messages = {},
  persistLocale = true,
  onLocaleChange,
  debug = false
}) => {
  const [currentLocale, setCurrentLocale] = useState(defaultLocale);
  const [allMessages, setAllMessages] = useState({
    ...DEFAULT_MESSAGES,
    ...messages
  });

  useEffect(() => {
    if (persistLocale) {
      const savedLocale = localStorage.getItem('app-locale');
      if (savedLocale && AVAILABLE_LOCALES.find(l => l.code === savedLocale)) {
        setCurrentLocale(savedLocale);
      }
    }
  }, [persistLocale]);

  const setLocale = useCallback((localeCode) => {
    const localeData = AVAILABLE_LOCALES.find(l => l.code === localeCode);
    if (!localeData) {
      console.warn('Locale not found:', localeCode);
      return;
    }

    setCurrentLocale(localeCode);

    document.documentElement.dir = localeData.rtl ? 'rtl' : 'ltr';
    document.documentElement.lang = localeCode;

    document.body.className = document.body.className
      .replace(/locale-\w+/g, '')
      .replace(/\s+/g, ' ')
      .trim();
    document.body.classList.add(`locale-${localeCode}`);

    if (localeData.rtl) {
      document.body.classList.add('rtl');
    } else {
      document.body.classList.remove('rtl');
    }

    if (persistLocale) {
      localStorage.setItem('app-locale', localeCode);
    }

    if (onLocaleChange) {
      onLocaleChange(localeCode, localeData);
    }

    if (debug) {
      console.log('Locale changed to:', localeCode, localeData);
    }
  }, [persistLocale, onLocaleChange, debug]);

  const getCurrentLocaleData = useCallback(() => {
    return AVAILABLE_LOCALES.find(l => l.code === currentLocale);
  }, [currentLocale]);

  const isRTL = getCurrentLocaleData()?.rtl || false;

  useEffect(() => {
    const localeData = getCurrentLocaleData();
    if (localeData) {
      document.documentElement.dir = localeData.rtl ? 'rtl' : 'ltr';
      document.documentElement.lang = currentLocale;
      document.body.classList.add(`locale-${currentLocale}`);
      
      if (localeData.rtl) {
        document.body.classList.add('rtl');
      }
    }
  }, [currentLocale, getCurrentLocaleData]);

  const contextValue = {
    currentLocale,
    locales: AVAILABLE_LOCALES,
    setLocale,
    messages: allMessages[currentLocale] || allMessages[defaultLocale] || {},
    isRTL,
    getCurrentLocaleData
  };

  return (
    <I18nContext.Provider value={contextValue}>
      <IntlProvider
        locale={currentLocale}
        messages={allMessages[currentLocale] || allMessages[defaultLocale] || {}}
        defaultLocale={defaultLocale}
      >
        {children}
      </IntlProvider>
    </I18nContext.Provider>
  );
};

I18nProvider.propTypes = {
  children: PropTypes.node.isRequired,
  defaultLocale: PropTypes.string,
  messages: PropTypes.object,
  persistLocale: PropTypes.bool,
  onLocaleChange: PropTypes.func,
  debug: PropTypes.bool
};

export const useI18n = () => {
  const context = useContext(I18nContext);
  const intl = useIntl();
  
  if (!context) {
    throw new Error('useI18n must be used within an I18nProvider');
  }

  return {
    ...context,
    formatMessage: intl.formatMessage,
    formatDate: intl.formatDate,
    formatTime: intl.formatTime,
    formatNumber: intl.formatNumber,
    formatRelativeTime: intl.formatRelativeTime
  };
};

export const addMessages = (locale, messages) => {
  if (!DEFAULT_MESSAGES[locale]) {
    DEFAULT_MESSAGES[locale] = {};
  }
  DEFAULT_MESSAGES[locale] = {
    ...DEFAULT_MESSAGES[locale],
    ...messages
  };
};

export const getMessage = (key, locale = 'en', values = {}) => {
  const messages = DEFAULT_MESSAGES[locale] || DEFAULT_MESSAGES.en;
  let message = messages[key] || key;

  Object.entries(values).forEach(([placeholder, value]) => {
    message = message.replace(new RegExp(`{${placeholder}}`, 'g'), value);
  });

  return message;
};

export { AVAILABLE_LOCALES, DEFAULT_MESSAGES };
export default I18nProvider;
