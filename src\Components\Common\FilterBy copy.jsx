import React from 'react';
import { Dropdown } from 'primereact/dropdown';
import '../../styles/global.css';

const FilterBy = ({ 
    value, 
    options, 
    onChange, 
    placeholder = "Filter By", 
    className = "",
    disabled = false,
    isDashboard = false 
}) => {
    return (
        <div className="filter-container">
            <Dropdown
                value={value}
                options={options}
                onChange={(e) => onChange(e.value)}
                placeholder={placeholder}
                className={`filter-dropdown ${className}`}
                disabled={disabled}
                filter={isDashboard}
                showClear={false}
            />
        </div>
    );
};

export default FilterBy;
