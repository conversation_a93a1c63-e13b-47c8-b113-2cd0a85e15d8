import React from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';

const KeyPersonnelOrderDataTable = ({ data, onRowSelect }) => {
  return (
    <div className="key-personnel-data-table">
      <DataTable value={data || []} selectionMode="single" onSelectionChange={onRowSelect}>
        <Column field="name" header="Name" />
        <Column field="position" header="Position" />
        <Column field="email" header="Email" />
      </DataTable>
    </div>
  );
};

export default KeyPersonnelOrderDataTable;
