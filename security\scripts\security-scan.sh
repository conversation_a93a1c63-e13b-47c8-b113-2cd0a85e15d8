
echo "🚀 Starting Comprehensive Security Scan"
echo "========================================"

echo "📦 Running dependency vulnerability scan..."
npm audit --audit-level=high
npx snyk test --severity-threshold=high

echo "🔍 Running SAST scan..."
npx semgrep scan --config=auto .
npx eslint . --config .eslint-security.js

echo "🌐 Running DAST scan..."
if curl -f http://localhost:3000 > /dev/null 2>&1; then
  npx zaproxy -cmd -quickurl http://localhost:3000 -quickout security/reports/zap-scan.json
fi

echo "🔑 Scanning for secrets..."
npx detect-secrets scan --baseline .secrets.baseline

echo "📄 Checking license compliance..."
npx license-checker --summary --production

echo "🛡️ Checking security headers..."
npx check-headers http://localhost:3000

echo "✅ Security scan completed!"
echo "📊 Reports available in security/reports/"