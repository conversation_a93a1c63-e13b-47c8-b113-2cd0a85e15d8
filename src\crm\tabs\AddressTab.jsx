import React, { useState } from 'react';
import { Card } from 'primereact/card';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import GlassyBlueButton from '../../components/common/GlassyBlueButton';
import GlassyWhiteButton from '../../components/common/GlassyWhiteButton';
/**
 * AddressTab - Displays and manages address information
 */
const AddressTab = ({ prospectId }) => {
  const [addresses, setAddresses] = useState([
    {
      id: 1,
      type: 'Registered Office',
      street: 'PO Box 686, Dubai',
      city: 'Dubai',
      state: 'Dubai',
      country: 'United Arab Emirates',
      postalCode: '00000',
      isPrimary: true
    },
    {
      id: 2,
      type: 'Business',
      street: '123 Business Street',
      city: 'Dubai',
      state: 'Dubai',
      country: 'United Arab Emirates',
      postalCode: '12345',
      isPrimary: false
    }
  ]);

  const addressTypes = [
    { label: 'Registered Office', value: 'Registered Office' },
    { label: 'Business', value: 'Business' },
    { label: 'Billing', value: 'Billing' },
    { label: 'Shipping', value: 'Shipping' }
  ];

  const countries = [
    { label: 'United Arab Emirates', value: 'United Arab Emirates' },
    { label: 'United States', value: 'United States' },
    { label: 'United Kingdom', value: 'United Kingdom' },
    { label: 'Canada', value: 'Canada' }
  ];

  return (
    <div className="address-tab">
      <div className="tab-header" style={{  display: 'flex', justifyContent: 'space-between', alignItems: 'center', color: 'black' }}>
        <h3>Address Information</h3>
        <GlassyBlueButton label="Add Address" icon="pi pi-plus" />
      </div>

      {addresses.map((address) => (
        <Card key={address.id} className="address-card" style={{ marginBottom: '1rem' }}>
          {/* 2x3 Grid Layout */}
          <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1.5rem' }}>
            {/* Row 1 - Column 1 */}
            <div className="p-field">
              <label htmlFor={`type-${address.id}`} style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>
                Address Type
              </label>
              <Dropdown
                id={`type-${address.id}`}
                value={address.type}
                options={addressTypes}
                placeholder="Select Type"
                className="w-full"
              />
            </div>

            {/* Row 1 - Column 2 */}
            <div className="p-field">
              <label htmlFor={`street-${address.id}`} style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>
                Street Address
              </label>
              <InputText 
                id={`street-${address.id}`} 
                value={address.street}
                className="w-full"
              />
            </div>

            {/* Row 2 - Column 1 */}
            <div className="p-field">
              <label htmlFor={`city-${address.id}`} style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>
                City
              </label>
              <InputText 
                id={`city-${address.id}`} 
                value={address.city}
                className="w-full"
              />
            </div>

            {/* Row 2 - Column 2 */}
            <div className="p-field">
              <label htmlFor={`state-${address.id}`} style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>
                State/Province
              </label>
              <InputText 
                id={`state-${address.id}`} 
                value={address.state}
                className="w-full"
              />
            </div>

            {/* Row 3 - Column 1 */}
            <div className="p-field">
              <label htmlFor={`country-${address.id}`} style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>
                Country
              </label>
              <Dropdown
                id={`country-${address.id}`}
                value={address.country}
                options={countries}
                placeholder="Select Country"
                className="w-full"
              />
            </div>

            {/* Row 3 - Column 2 */}
            <div className="p-field">
              <label htmlFor={`postal-${address.id}`} style={{ display: 'block', marginBottom: '0.5rem', fontWeight: 500 }}>
                Postal Code
              </label>
              <InputText 
                id={`postal-${address.id}`} 
                value={address.postalCode}
                className="w-full"
              />
            </div>
          </div>

          {/* Primary Address Checkbox */}
          <div style={{ marginTop: '1rem', marginBottom: '1rem' }}>
            <label htmlFor={`primary-${address.id}`} style={{ display: 'flex', alignItems: 'center', cursor: 'pointer' }}>
              <input 
                type="checkbox" 
                id={`primary-${address.id}`}
                checked={address.isPrimary} 
                style={{ marginRight: '0.5rem', cursor: 'pointer' }} 
              />
              <span style={{ fontWeight: 500 }}>Primary Address</span>
            </label>
          </div>

          {/* Action Buttons */}
          <div className="address-actions" style={{ marginTop: '1rem', display: 'flex', gap: '0.5rem' }}>
            <GlassyBlueButton label="Edit" icon="pi pi-pencil" />
            <GlassyWhiteButton label="Delete" icon="pi pi-trash" />
          </div>
        </Card>
      ))}
    </div>
  );
};

export default AddressTab;

