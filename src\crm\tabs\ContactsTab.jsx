import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { Avatar } from 'primereact/avatar';
import GlassyBadge from '../../components/common/GlassyBadge';
import GlassyBlueButton from '../../components/common/GlassyBlueButton';
import GlassyWhiteButton from '../../components/common/GlassyWhiteButton';

/**
 * ContactsTab - Displays contact persons for the prospect
 */
const ContactsTab = ({ prospectId }) => {
  const [contacts] = useState([
    {
      id: 1,
      name: '<PERSON>',
      designation: 'Commercial Manager',
      department: 'Merketing Manager',
      type:"Phone",
      extension:"101",
      email: '<EMAIL>',
      phone: '+971 4 214 1897',
      mobile: '+971 50 123 4567',
      isPrimary: "Yes",
      status: 'Active'
    },
    {
      id: 2,
      name: '<PERSON><PERSON>',
      designation: 'Finance Director',
      department: 'IT Support Specialist',
      type:"Mobile",
      extension:"102",
      email: '<EMAIL>',
      phone: '+971 4 214 1898',
      mobile: '+971 50 234 5678',
      isPrimary: "No",
      status: 'Active'
    },
    {
      id: 3,
      name: 'Mohammed Ali',
      designation: 'Procurement Manager',
      department: 'Sales Representative',
      type:"Phone",
      extension:"101",
      email: '<EMAIL>',
      phone: '+971 4 214 1899',
      mobile: '+971 50 345 6789',
      isPrimary: "No",
      status: 'Active'
    }
  ]);

  const nameTemplate = (rowData) => {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', gap: '0.5rem' }}>
        {/* <Avatar 
          label={rowData.name.split(' ').map(n => n[0]).join('')} 
          size="normal" 
          shape="circle"
          style={{ backgroundColor: '#2196F3', color: '#ffffff' }}
        /> */}
        <div>
          <div style={{ fontWeight: 'bold' }}>{rowData.name}</div>
          {/* <div style={{ fontSize: '0.875rem', color: '#6c757d' }}>{rowData.designation}</div> */}
        </div>
      </div>
    );
  };

  const contactTemplate = (rowData) => {
    return (
      <div>
        {/* <div><i className="pi pi-envelope" style={{ marginRight: '0.5rem' }}></i>{rowData.email}</div> */}
        <div><i className="pi pi-phone" style={{ marginRight: '0.5rem' }}></i>{rowData.phone}</div>
        {/* <div><i className="pi pi-mobile" style={{ marginRight: '0.5rem' }}></i>{rowData.mobile}</div> */}
      </div>
    );
  };

  const typeTemplate = (rowData) => {
    return (
      <div style={{ display: 'flex', justifyContent: 'center' }}>
        {rowData.isPrimary ? (
          <GlassyBadge label="Primary" className="badge-success" />
        ) : (
          <GlassyBadge label="Secondary" className="badge-info" />
        )}
      </div>
    );
  };

  const statusTemplate = (rowData) => {
    return (
      <div style={{ display: 'flex', justifyContent: 'center' }}>
        <GlassyBadge 
          label={rowData.status} 
          className={rowData.status === 'Active' ? 'badge-success' : 'badge-warning'} 
        />
      </div>
    );
  };

  const actionTemplate = (rowData) => {
    return (
      <div style={{ display: 'flex', gap: '0.5rem' }}>
        <Button
          icon="pi pi-pencil"
          className="p-button-sm p-button-text p-button-secondary"
          tooltip="Edit"
        />
        <Button
          icon="pi pi-trash"
          className="p-button-sm p-button-text p-button-danger"
          tooltip="Delete"
        />
      </div>
    );
  };

  return (
    <div className="contacts-tab">
      <div className="tab-header" style={{ marginBottom: '1.5rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center', color: 'black' }}>
        <h3>Contact Information</h3>
        <GlassyBlueButton label="Add Contact" icon="pi pi-plus" className="p-button-sm" />
      </div>

      <div className="">
        <DataTable 
          value={contacts}
          className="glass-table"
          responsiveLayout="scroll"
          stripedRows
          showGridlines
        >
          <Column field="name" header="Contact Name" body={nameTemplate} />
          <Column field="department" header="Job Position"  style={{  textAlign: 'center' }} />
          <Column field="type" header="Type" style={{  textAlign: 'center' }} />
          {/* <Column field="age" header="Age" /> */}
          <Column header="Contact Details" body={contactTemplate} style={{ minWidth: '250px',textAlign: 'center' }}  />
          {/* <Column header="Type" body={typeTemplate} /> */}
          <Column field="extension" header="Extension" style={{  textAlign: 'center' }}/>
          <Column field="isPrimary" header="Primary" style={{  textAlign: 'center' }}/>
          {/* <Column header="Status" body={statusTemplate} /> */}
          <Column header="Actions" body={actionTemplate} style={{ width: '120px',textAlign: 'center' }}    />
        </DataTable>
      </div>
    </div>
  );
};

export default ContactsTab;

