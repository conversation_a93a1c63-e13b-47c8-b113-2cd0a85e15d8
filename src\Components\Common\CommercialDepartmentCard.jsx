import React from 'react';

const CommercialDepartmentCard = ({ name, position, phone, fax, email }) => {
  return (
    <div className="glass-card-doc-management p-4">
      <h4 style={{ 
        margin: 0, 
        marginBottom: '1rem', 
        fontSize: '1rem', 
        fontWeight: '600',
        color: '#111827'
      }}>
        Commercial Department
      </h4>
      
      <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem', fontSize: '0.875rem' }}>
        <div style={{ display: 'flex', gap: '2rem' }}>
          <div style={{ flex: 1 }}>
            <div style={{ color: '#6B7280', marginBottom: '0.25rem' }}>Name:</div>
            <div style={{ color: '#111827', fontWeight: '500' }}>{name}</div>
          </div>
          <div style={{ flex: 1 }}>
            <div style={{ color: '#6B7280', marginBottom: '0.25rem' }}>Fax:</div>
            <div style={{ color: '#111827', fontWeight: '500' }}>{fax}</div>
          </div>
        </div>

        <div style={{ display: 'flex', gap: '2rem' }}>
          <div style={{ flex: 1 }}>
            <div style={{ color: '#6B7280', marginBottom: '0.25rem' }}>Position:</div>
            <div style={{ color: '#111827', fontWeight: '500' }}>{position}</div>
          </div>
          <div style={{ flex: 1 }}>
            <div style={{ color: '#6B7280', marginBottom: '0.25rem' }}>Email:</div>
            <div style={{ color: '#111827', fontWeight: '500' }}>{email}</div>
          </div>
        </div>

        <div style={{ display: 'flex', gap: '2rem' }}>
          <div style={{ flex: 1 }}>
            <div style={{ color: '#6B7280', marginBottom: '0.25rem' }}>Phone:</div>
            <div style={{ color: '#111827', fontWeight: '500' }}>{phone}</div>
          </div>
          <div style={{ flex: 1 }}></div>
        </div>
      </div>
    </div>
  );
};

export default CommercialDepartmentCard;
