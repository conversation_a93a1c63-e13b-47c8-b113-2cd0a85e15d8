import React from 'react';

const KYCVerifiedCard = ({ verificationMethod, verificationTime, verifiedBy }) => {
  return (
    <div className="glass-card-doc-management p-4" style={{ height: '100%' }}>
      <div style={{ display: 'flex', alignItems: 'flex-start', gap: '1rem' }}>
        <div style={{ 
          width: '48px', 
          height: '48px', 
          borderRadius: '50%', 
          backgroundColor: '#22c55e', 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'center',
          flexShrink: 0
        }}>
          <i className="pi pi-check" style={{ fontSize: '1.5rem', color: 'white' }}></i>
        </div>
        <div style={{ flex: 1 }}>
          <h3 style={{ margin: 0, marginBottom: '1rem', fontSize: '1.25rem', fontWeight: '600', color: 'black' }}>
            KYC Verified
          </h3>
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', fontSize: '0.875rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ color: '#6B7280' }}>Verification Method:</span>
              <span style={{ fontWeight: '500', color: '#111827' }}>{verificationMethod}</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ color: '#6B7280' }}>Verification Time:</span>
              <span style={{ fontWeight: '500', color: '#111827' }}>{verificationTime}</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ color: '#6B7280' }}>Verified by:</span>
              <span style={{ fontWeight: '500', color: '#111827' }}>{verifiedBy}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default KYCVerifiedCard;
