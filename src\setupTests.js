// Jest setup file for security tests

// Mock DOM APIs that might not be available in test environment
global.MutationObserver = class {
  constructor(callback) {
    this.callback = callback;
  }
  
  observe() {
    // Mock implementation
  }
  
  disconnect() {
    // Mock implementation
  }
};

global.ReportingObserver = class {
  constructor(callback) {
    this.callback = callback;
  }
  
  observe() {
    // Mock implementation
  }
  
  disconnect() {
    // Mock implementation
  }
};

// Mock crypto API
Object.defineProperty(window, 'crypto', {
  value: {
    getRandomValues: jest.fn((arr) => {
      for (let i = 0; i < arr.length; i++) {
        arr[i] = Math.floor(Math.random() * 256);
      }
      return arr;
    })
  }
});

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
});

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn()
};
Object.defineProperty(window, 'sessionStorage', {
  value: sessionStorageMock
});

// Mock console methods to avoid noise in tests
global.console = {
  ...console,
  warn: jest.fn(),
  error: jest.fn(),
  log: jest.fn(),
  info: jest.fn(),
  debug: jest.fn()
};

// Mock fetch globally
global.fetch = jest.fn();

// Mock XMLHttpRequest
global.XMLHttpRequest = class {
  constructor() {
    this.headers = {};
    this.readyState = 0;
    this.status = 200;
    this.statusText = 'OK';
  }
  
  open(method, url) {
    this.method = method;
    this.url = url;
  }
  
  setRequestHeader(name, value) {
    this.headers[name] = value;
  }
  
  getResponseHeader(name) {
    return this.headers[name];
  }
  
  send(data) {
    this.data = data;
    // Simulate async response
    setTimeout(() => {
      this.readyState = 4;
      if (this.onreadystatechange) {
        this.onreadystatechange();
      }
    }, 0);
  }
};

// Mock document methods
Object.defineProperty(document, 'cookie', {
  writable: true,
  value: ''
});

// Mock window.location
Object.defineProperty(window, 'location', {
  value: {
    href: 'https://localhost:3000',
    origin: 'https://localhost:3000',
    protocol: 'https:',
    hostname: 'localhost',
    port: '3000',
    pathname: '/',
    search: '',
    hash: ''
  },
  writable: true
});

// Mock window.navigator
Object.defineProperty(window, 'navigator', {
  value: {
    userAgent: 'Mozilla/5.0 (Test Environment)',
    language: 'en-US',
    languages: ['en-US', 'en']
  },
  writable: true
});

// Mock isSecureContext
Object.defineProperty(window, 'isSecureContext', {
  value: true,
  writable: true
});

// Mock CustomEvent
global.CustomEvent = class CustomEvent extends Event {
  constructor(type, options = {}) {
    super(type, options);
    this.detail = options.detail;
  }
};

// Mock addEventListener and removeEventListener
const eventListeners = {};
window.addEventListener = jest.fn((event, callback) => {
  if (!eventListeners[event]) {
    eventListeners[event] = [];
  }
  eventListeners[event].push(callback);
});

window.removeEventListener = jest.fn((event, callback) => {
  if (eventListeners[event]) {
    const index = eventListeners[event].indexOf(callback);
    if (index > -1) {
      eventListeners[event].splice(index, 1);
    }
  }
});

window.dispatchEvent = jest.fn((event) => {
  if (eventListeners[event.type]) {
    eventListeners[event.type].forEach(callback => callback(event));
  }
});

// Mock document.addEventListener
document.addEventListener = jest.fn();
document.removeEventListener = jest.fn();
document.dispatchEvent = jest.fn();

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
});

// Setup environment variables for tests
process.env.NODE_ENV = 'test';
process.env.REACT_APP_ENCRYPTION_KEY = 'test_encryption_key_for_testing_only';

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
  
  // Reset localStorage mock
  localStorageMock.getItem.mockReset();
  localStorageMock.setItem.mockReset();
  localStorageMock.removeItem.mockReset();
  localStorageMock.clear.mockReset();
  
  // Reset sessionStorage mock
  sessionStorageMock.getItem.mockReset();
  sessionStorageMock.setItem.mockReset();
  sessionStorageMock.removeItem.mockReset();
  sessionStorageMock.clear.mockReset();
  
  // Reset fetch mock
  fetch.mockReset();
  
  // Reset document.cookie
  document.cookie = '';
});
