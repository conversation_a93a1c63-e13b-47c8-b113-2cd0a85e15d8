/* ========================================
   GLASSY UI THEME - REUSABLE COMPONENTS
   ======================================== */

/* Employee Dashboard Components */
.employee-portal {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  color: var(--text-dark);
  font-family: Inter, system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

.employee-header {
  background: transparent;
  color: #fff;
  padding: 12px 20px;
}

.employee-header-inner {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.employee-logo {
  border-radius: 4px;
  background: white;
  display: flex;
  align-items: center;
  height: 40px;
  padding: 0 8px;
}

.employee-logo img {
  height: 32px;
  width: auto;
  object-fit: contain;
}

.employee-greeting {
  margin-left: 12px;
}

.employee-greeting-title {
  font-weight: 600;
  font-size: 16px;
  line-height: 24px;
  color: #fff;
  margin: 0;
}

.employee-greeting-sub {
  font-size: 13px;
  opacity: 0.9;
  line-height: 20px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.employee-company-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.employee-company-label {
  font-size: 15px;
  /* color: rgba(255, 255, 255, 0.8); */
}

.employee-modules-grid {
  max-width: 1200px;
  margin: 0 auto;
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 18px;
  padding: 28px 20px 80px;
}

.employee-module-card {
  background: var(--glass-secondary);
  border-radius: 8px;
  padding: 18px;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.employee-module-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.employee-module-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.employee-module-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background: #eef2ff;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #1e40af;
  font-size: 18px;
  flex-shrink: 0;
}

.employee-module-title {
  font-weight: 600;
  font-size: 15px;
  color: var(--text-dark);
  margin: 0;
}

.employee-module-list {
  margin: 0;
  padding: 0;
  list-style: none;
}

.employee-module-item {
  color: var(--text-light);
  font-size: 13px;
  padding: 6px 8px;
  margin: 2px 0;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.employee-module-item:hover {
  background: rgba(239, 246, 255, 0.7);
  color: #1e40af;
}

/* Responsive Styles */
@media (max-width: 1100px) {
  .employee-modules-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 800px) {
  .employee-modules-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .employee-modules-grid {
    grid-template-columns: 1fr;
  }
  
  .employee-header-inner {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
}

/* CSS Variables for consistent theming */
:root {
  --glass-primary: rgba(255, 255, 255, 0.15);
  --glass-secondary: rgba(255, 255, 255, 0.9);
  --glass-accent: rgba(255, 255, 255, 0.05);
  --glass-border: rgba(255, 255, 255, 0.2);
  --glass-blur: blur(20px);
  --glass-blur-light: blur(10px);
  --glass-blur-heavy: blur(30px);
  --primary-blue: #193276;
  --primary-blue-light: #2563eb;
  --text-dark: #374151;
  --text-light: #6b7280;
  --success-green: #10b981;
  --bord/* Glass Editor Counter */
.glass-counter {
  padding: 8px 16px;
  text-align: right;
  color: rgba(255, 255, 255, 0.4);
  font-size: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  background: rgba(255, 255, 255, 0.02);
}

/* Glass Upload Area */
.glass-upload-area  {
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(16, 1, 1, 0.17);
  border-radius: 8px;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
}

/* Glass Text Colors */
.glass-text {
  color: rgba(255, 255, 255, 0.9);
}

.glass-text-muted {
  color: rgba(255, 255, 255, 0.6);
}

.glass-text-light {
  color: rgba(255, 255, 255, 0.4);
}
}

/* ========================================
   BASE AIRPLANE BACKGROUND LAYOUT
   ======================================== */

.glassy-page {
  padding: 0;
  background: linear-gradient(135deg, rgba(173, 216, 230, 0.3) 0%, rgba(135, 206, 235, 0.4) 50%, rgba(176, 196, 222, 0.3) 100%),
              url('../../assets/Images/common/airoplane-3.png') center/cover no-repeat;
  background-attachment: fixed;
  min-height: 100vh;
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
  position: relative;
  overflow-y: auto;
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

/* Hide scrollbar for WebKit browsers */
.glassy-page::-webkit-scrollbar {
  display: none;
}

/* Glass effect overlay for entire page */
.glassy-page::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--glass-accent);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  z-index: -1;
}

/* ========================================
   GLASS CONTAINERS
   ======================================== */

/* Primary glass container - high transparency */
.glass-container {
  background: var(--glass-primary);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
              0 0 0 1px var(--glass-border);
  border: 1px solid var(--glass-border);
}

/* Secondary glass container - more opaque */
.glass-container-solid {
  background: var(--glass-secondary);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-light);
}

/* Light glass container */
.glass-container-light {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ========================================
   GLASS HEADERS
   ======================================== */

.glass-header {
  display: flex;
  width: auto;
  height: 52px;
  padding: 0;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  flex-shrink: 0;
  background: #FFFFFFCC;
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  margin: 0;
  overflow: hidden;
  box-sizing: border-box;
  position: sticky;
  top: 0;
  z-index: 100;
}

.glass-header-left {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  flex-shrink: 0;
  height: 100%;
  box-sizing: border-box;
  flex: 1;
}

.glass-header-right {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  flex-shrink: 0;
  height: 100%;
  box-sizing: border-box;
  background: #FFFFFFB2;
  border-radius: 0 0 0 12px;
}



/* ========================================
   GLASS BUTTONS
   ======================================== */

/* Primary glass button */
.glass-btn-primary {
  background: var(--primary-blue) !important;
  backdrop-filter: var(--glass-blur) !important;
  -webkit-backdrop-filter: var(--glass-blur) !important;
  border: 1px solid rgba(37, 99, 235, 0.3) !important;
  color: #FFF !important;
  text-align: center !important;
  font-family: Roboto !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  line-height: 20px !important;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.glass-btn-primary:hover {
  background: #1e40af !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(30, 64, 175, 0.3);
}

/* Secondary glass button */
.glass-btn-secondary {
  background: #FFFFFFCC !important;
  backdrop-filter: var(--glass-blur-light) !important;
  -webkit-backdrop-filter: var(--glass-blur-light) !important;
  border: 1px solid rgba(25, 50, 118, 0.3) !important;
  color: var(--primary-blue) !important;
  text-align: center !important;
  font-family: Roboto !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  line-height: 20px !important;
  padding: 8px 16px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.glass-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.9) !important;
  color: var(--primary-blue) !important;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(25, 50, 118, 0.2);
}

/* Glass icon button */
.glass-btn-icon {
  width: 32px;
  height: 32px;
  color: #6B7280 !important;
  background: rgba(255, 255, 255, 0.1) !important;
  backdrop-filter: var(--glass-blur-light) !important;
  -webkit-backdrop-filter: var(--glass-blur-light) !important;
  border: 1px solid var(--glass-border) !important;
  border-radius: 6px !important;
  transition: all 0.2s ease;
}

.glass-btn-icon:hover {
  color: #374151 !important;
  background: rgba(243, 244, 246, 0.3) !important;
  transform: translateY(-1px);
}

/* Glass text button */
.glass-btn-text {
  color: var(--primary-blue) !important;
  background: transparent !important;
  border: none !important;
  padding: 4px !important;
  transition: all 0.2s ease;
}

.glass-btn-text:hover {
  background: rgba(255, 255, 255, 0.1) !important;
  border-radius: 4px;
}

/* ========================================
   GLASS FORM INPUTS
   ======================================== */

/* Glass input field */
.glass-input {
  border-radius: 8px;
  border: 1px solid var(--border-light);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  font-size: 14px;
  padding: 9px 13px;
  color: var(--text-dark);
  transition: all 0.2s ease;
}

.glass-input:focus {
  border-color: rgba(79, 70, 229, 0.7);
  outline: none;
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.glass-input:hover {
  border-color: rgba(156, 163, 175, 0.7);
  background: rgba(249, 250, 251, 0.95);
}

/* Glass search input with icon */
.glass-search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.glass-search-icon {
  position: absolute;
  left: 12px;
  color: var(--text-light);
  z-index: 1;
  pointer-events: none;
  font-size: 14px;
}

.glass-search-input {
  width: 300px;
  height: 38px;
  padding: 9px 40px 9px 42px;
  border-radius: 8px;
  border: 1px solid var(--border-light);
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  font-size: 14px;
}

.glass-search-input::placeholder {
  color: var(--text-light);
  opacity: 0.7;
}

.search-glass-clear-btn {
  position: absolute;
  right: 12px;
  color: var(--text-light);
  cursor: pointer;
  z-index: 2;
  transition: color 0.2s ease;
}

.search-glass-clear-btn:hover {
  color: var(--text-dark);
}

@media (max-width: 992px) {
  .search-component .glass-search-input { width: 260px; }
}

@media (max-width: 768px) {
  .search-component .glass-search-input { width: 100% !important; }
  .search-component .glass-search-container { display: flex; align-items: center; gap: 8px; }
  .search-component .glass-search-btn { flex-shrink: 0; }
}

/* ========================================
   GLASS DATA TABLE
   ======================================== */


.glass-table-container {
  background: var(--glass-primary);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
              0 0 0 1px var(--glass-border);
  overflow: hidden;
  margin: 20px;
  padding: 0;
  border: 1px solid var(--glass-border);
  width: calc(100% - 40px);
  max-width: calc(100% - 40px);
  box-sizing: border-box;
}

.glass-table {
  width: 100%;
  min-width: 0;
  table-layout: fixed;
  border: none;
  border-radius: 12px;
  overflow: hidden;
  background: transparent;
}

/* Override PrimeReact table header classes with custom styling */
.glass-table .p-datatable-thead > tr > th {
  background: rgba(248, 250, 252, 0.5) !important;
  backdrop-filter: var(--glass-blur-light) !important;
  -webkit-backdrop-filter: var(--glass-blur-light) !important;
  border-bottom: 1px solid rgba(229, 231, 235, 0.4) !important;
  border-right: 1px solid rgba(229, 231, 235, 0.2) !important;
  padding: 14px 16px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #1f2937 !important;
  text-align: center !important;
  font-family: 'Roboto', sans-serif !important;
  letter-spacing: 0.3px !important;
}

.glass-table .p-datatable-thead > tr > th:last-child {
  border-right: none !important;
}

/* Remove PrimeReact sorting styles from glass-table */
.glass-table .p-datatable-thead .p-sortable-column-icon,
.glass-table .p-datatable-thead .p-sortable-column-badge {
  display: none !important;
}

.glass-table .p-datatable-thead .p-sortable-column:hover {
  background: rgba(248, 250, 252, 0.5) !important;
  cursor: default !important;
}

.glass-table .p-datatable-thead .p-column-header-content {
  justify-content: center !important;
}

.glass-table .p-datatable-thead th {
  cursor: default !important;
}

.glass-table .p-datatable-tbody > tr,
.glass-table .p-datatable-tbody > tr > td {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(243, 244, 246, 0.3);
  border-right: 1px solid rgba(229, 231, 235, 0.2);
  font-size: 14px;
  color: var(--text-dark);
  text-align: center;
  vertical-align: middle;
  background-color: rgba(255, 255, 255, 0.3) !important;
  box-shadow: none !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.glass-table .p-datatable-tbody > tr > td:last-child {
  border-right: none;
}

.glass-table .p-datatable-tbody > tr:hover {
  background-color: rgba(255, 255, 255, 0.4) !important;
}

/* ========================================
   GLASS PAGINATION
   ======================================== */

.glass-paginator {
  background: rgba(249, 250, 251, 0.3) !important;
  backdrop-filter: var(--glass-blur-light) !important;
  -webkit-backdrop-filter: var(--glass-blur-light) !important;
  border-top: 1px solid rgba(229, 231, 235, 0.3) !important;
  padding: 12px 20px !important;
  border-radius: 0 0 12px 12px !important;
  justify-content: center !important;
}

.glass-paginator .p-paginator-prev,
.glass-paginator .p-paginator-next {
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: var(--glass-blur-light) !important;
  -webkit-backdrop-filter: var(--glass-blur-light) !important;
  border: 1px solid rgba(229, 231, 235, 0.5) !important;
  color: var(--text-dark) !important;
  margin: 0 4px !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
}

.glass-paginator .p-paginator-prev:hover,
.glass-paginator .p-paginator-next:hover {
  background: rgba(243, 244, 246, 0.9) !important;
}

.glass-paginator .p-paginator-pages .p-paginator-page {
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: var(--glass-blur-light) !important;
  -webkit-backdrop-filter: var(--glass-blur-light) !important;
  border: 1px solid rgba(229, 231, 235, 0.5) !important;
  color: var(--text-dark) !important;
  margin: 0 2px !important;
  border-radius: 4px !important;
  padding: 8px 12px !important;
  min-width: 40px !important;
  text-align: center !important;
}

.glass-paginator .p-paginator-pages .p-paginator-page:hover {
  background: rgba(243, 244, 246, 0.9) !important;
}

.glass-paginator .p-paginator-pages .p-paginator-page.p-highlight {
  background: rgba(37, 99, 235, 0.9) !important;
  border-color: rgba(37, 99, 235, 0.7) !important;
  color: white !important;
}

/* ========================================
   GLASS TABS
   ======================================== */

.glass-tabview {
  background: transparent !important;
  border: none !important;
}

.glass-tabview .p-tabview-nav {
  background: transparent !important;
  border: none !important;
  border-bottom: 1px solid rgba(229, 231, 235, 0.3) !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  flex-wrap: wrap !important;
}

.glass-tabview .p-tabview-nav li {
  background: transparent !important;
  border: none !important;
  border-bottom: 2px solid transparent !important;
  border-radius: 0 !important;
  margin: 0 !important;
  margin-right: 8px !important;
  transition: none !important;
  position: relative !important;
}

.glass-tabview .p-tabview-nav li .p-tabview-nav-link {
  background: transparent !important;
  border: none !important;
  color: var(--primary-blue) !important;
  font-family: Roboto !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  padding: 12px 16px !important;
  border-radius: 0 !important;
  text-decoration: none !important;
  position: relative !important;
  transition: none !important;
  display: block !important;
  white-space: nowrap !important;
}

.glass-tabview .p-tabview-nav li:hover {
  background: transparent !important;
}

.glass-tabview .p-tabview-nav li:hover .p-tabview-nav-link {
  color: var(--primary-blue) !important;
}

.glass-tabview .p-tabview-nav li.p-highlight {
  background: #FFFFFF80 !important;
  border-bottom: 2px solid var(--primary-blue) !important;
}

  .glass-tabview .p-tabview-nav li.p-highlight .p-tabview-nav-link {
    color: var(--primary-blue) !important;
    font-weight: 500 !important;
    background: transparent !important;
  }
  /* Hide the content area of TabView */
  .glass-tabview .p-tabview-panels {
    display: none !important;
  }

  .glass-tabview .p-tabview-panels {
    /* display: none !important; */
    background: #FFFFFF80 !important;
    font-family: Roboto !important;
  }

  /* Responsive: make tab bar compact and scrollable on small screens */
  @media (max-width: 992px) {
    .glass-tabview .p-tabview-nav { gap: 4px !important; }
    .glass-tabview .p-tabview-nav li .p-tabview-nav-link {
      padding: 10px 12px !important;
      font-size: 13px !important;
    }
  }

  @media (max-width: 768px) {
    .glass-tabview .p-tabview-nav {
      overflow-x: auto !important;
      white-space: nowrap !important;
      -webkit-overflow-scrolling: touch !important;
    }
  }
  .glass-tabview .p-tabview-nav li { flex: 0 0 auto !important; }
  .glass-tabview .p-tabview-nav li .p-tabview-nav-link {
    padding: 8px 10px !important;
    font-size: 15.5px !important;
  }
  .glass-tabview .p-tabview-panels { padding: 12px !important; }

  /* Add gap between icon and text in tab headers */
  .simple-tab-header {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .simple-tab-header .tab-icon {
    display: inline-flex;
    align-items: center;
  }

  .simple-tab-header .tab-label {
    display: inline-flex;
    align-items: center;
  }

  /* Main and secondary action tabs styling */
  .main-action-tabs .p-tabview-nav,
  .secondary-action-tabs .p-tabview-nav {
    display: flex !important;
    justify-content: flex-start !important;
    flex-wrap: nowrap !important;
    border-bottom: none !important;
  }

  .main-action-tabs .p-tabview-nav li,
  .secondary-action-tabs .p-tabview-nav li {
    margin-right: 8px !important;
  }

.glass-toggle.p-togglebutton-checked {
  background: rgba(16, 185, 129, 0.9) !important;
  border-color: rgba(16, 185, 129, 0.7) !important;
}

.glass-toggle:not(.p-togglebutton-checked) {
  background: rgba(229, 231, 235, 0.8) !important;
  border-color: rgba(229, 231, 235, 0.6) !important;
}

/* ========================================
   GLASS BADGES & LABELS
   ======================================== */

.glass-badge {
  display: flex !important;
  width: 95.5px !important;
  height: 24px !important;
  padding: 4px 12px !important;
  align-items: center !important;
  flex-shrink: 0 !important;
  border-radius: 9999px !important;
  background: rgba(219, 234, 254, 0.8) !important;
  backdrop-filter: var(--glass-blur-light) !important;
  -webkit-backdrop-filter: var(--glass-blur-light) !important;
  color: #1D4ED8 !important;
  text-align: center !important;
  font-family: Roboto !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  line-height: 16px !important;
  text-decoration: none !important;
  border: 1px solid rgba(219, 234, 254, 0.3) !important;
  justify-content: center !important;
  box-sizing: border-box !important;
  min-width: 95.5px !important;
  white-space: nowrap !important;
}

.glass-badge:hover {
  background: rgba(191, 219, 254, 0.9) !important;
  color: #1E40AF !important;
  text-decoration: none !important;
}

.glass-badge-width {
  display: flex !important;
  gap: 4px !important;
  width: 130px !important;
  height: 24px !important;
  padding: 4px 12px !important;
  align-items: center !important;
  flex-shrink: 0 !important;
  border-radius: 9999px !important;
  background: rgba(219, 234, 254, 0.8) !important;
  backdrop-filter: var(--glass-blur-light) !important;
  -webkit-backdrop-filter: var(--glass-blur-light) !important;
  color: #1D4ED8 !important;
  text-align: center !important;
  font-family: Roboto !important;
  font-size: 12px !important;
  font-weight: 500 !important;
  line-height: 16px !important;
  text-decoration: none !important;
  border: 1px solid rgba(219, 234, 254, 0.3) !important;
  justify-content: center !important;
  box-sizing: border-box !important;
  min-width: 95.5px !important;
  white-space: nowrap !important;
}

.glass-badge-width:hover {
  background: rgba(191, 219, 254, 0.9) !important;
  color: #1E40AF !important;
  text-decoration: none !important;
}

/* ========================================
   GLASS CONTENT SECTIONS
   ======================================== */

.glass-content-section {
  padding: 20px;
  background: #FFFFFFB2;
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive padding for content section */
@media (max-width: 1200px) {
  .glass-content-section {
    padding: 16px;
  }
}

@media (max-width: 992px) {
  .glass-content-section {
    padding: 14px;
  }
}

@media (max-width: 768px) {
  .glass-content-section {
    padding: 12px;
  }
}

@media (max-width: 576px) {
  /* Single column buttons on small phones */
  .glass-content-section > .mb-4.flex > .flex:last-child > * {
    flex: 1 1 100%;
  }
}

/* ========================================
   GENERIC CONTROLS ROW (flex-based markup)
   Keep desktop unchanged; enhance tablet/mobile only
   ======================================== */
@media (max-width: 992px) {
  /* Turn the top controls row into a 2-col grid: controls + actions */
  .glass-content-section > .mb-4.flex {
    display: grid !important;
    grid-template-columns: 1fr auto;
    gap: 12px;
    align-items: start;
  }
  /* Inside left cluster: arrange search/filter/date in two columns */
  .glass-content-section > .mb-4.flex > .flex:first-child {
    display: grid !important;
    grid-template-columns: repeat(2, minmax(160px, 1fr));
    gap: 10px 12px;
  }
  .glass-content-section > .mb-4.flex > .flex:first-child > * {
    min-width: 0;
    width: 100% !important;
  }
  /* Actions on the right: tidy gaps and wrapping */
  .glass-content-section > .mb-4.flex > .flex:last-child {
    justify-self: end;
    display: inline-flex;
    gap: 10px;
    flex-wrap: wrap;
  }
}

@media (max-width: 768px) {
  /* One column stack for the whole row */
  .glass-content-section > .mb-4.flex {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  /* Left cluster becomes single column */
  .glass-content-section > .mb-4.flex > .flex:first-child {
    grid-template-columns: 1fr;
    gap: 10px;
  }
  /* Buttons stretch and wrap into two per row when possible */
  .glass-content-section > .mb-4.flex > .flex:last-child {
    justify-self: stretch;
    display: flex;
    gap: 10px;
  }
  .glass-content-section > .mb-4.flex > .flex:last-child > * {
    flex: 1 1 calc(50% - 10px);
  }
}

@media (max-width: 576px) {
  /* Single column buttons on small phones (already above) */
  .glass-content-section > .mb-4.flex > .flex:last-child > * {
    flex: 1 1 100%;
  }
}

.glass-page-title {
  padding: 24px 16px 16px 16px;
}

.glass-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--primary-blue);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.glass-subtitle {
  font-size: 14px;
  color: #193276;
  margin: 0;
  line-height: 1.4;
}

/* ========================================
   GLASS EDITOR COMPONENTS
   ======================================== */
/* Glass Editor Container */
.glass-editor-container {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

/* Glass Editor Content */
.glass-editor-content {
  background: rgba(255, 255, 255, 0.1);
  min-height: 300px;
  padding: 16px;
  color: #0e0202;
  font-size: 14px;
  line-height: 1.6;
}

/* Glass Editor Toolbar */
.glass-editor-toolbar {
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Glass Separator */
.glass-separator {
  width: 1px;
  height: 24px;
  background: rgba(255, 255, 255, 0.1);
  margin: 0 8px;
}

/* Glass Editor Button */
.glass-editor-btn {
  background: rgba(255, 255, 255, 0.1);
  border: none;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(0, 0, 0, 0.8);
  cursor: pointer;
  transition: all 0.2s ease;
}

.glass-editor-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* Glass Upload Area */
.glass-upload-area {
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
}

.glass-upload-preview {
  position: relative;
  width: 100%;
  height: 200px;
  border-radius: 8px;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
}

.glass-upload-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.glass-upload-remove {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border: none;
  border-radius: 50%;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  cursor: pointer;
}

/* ========================================
   GLASS MODAL/POPUP COMPONENTS
   ======================================== */

/* Glass modal overlay */
.glass-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
}

/* Glass modal container */
.glass-modal {
  background: var(--glass-primary);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
              0 0 0 1px var(--glass-border);
  border: 1px solid var(--glass-border);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  position: relative;
}

/* Glass modal header */
.glass-modal-header {
  background: rgba(248, 250, 252, 0.3);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-radius: 12px 12px 0 0;
}

/* Glass modal title */
.glass-modal-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--primary-blue);
  margin: 0;
}

/* Glass modal close button */
.glass-modal-close {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border: 1px solid var(--glass-border);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  cursor: pointer;
  transition: all 0.2s ease;
}

.glass-modal-close:hover {
  background: rgba(243, 244, 246, 0.3);
  color: #374151;
  transform: translateY(-1px);
}

/* Glass modal body */
.glass-modal-body {
  padding: 24px;
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
}

/* Glass modal footer */
.glass-modal-footer {
  background: rgba(248, 250, 252, 0.3);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-top: 1px solid rgba(229, 231, 235, 0.3);
  padding: 16px 24px;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  border-radius: 0 0 12px 12px;
}

/* ========================================
   UTILITY CLASSES
   ======================================== */

/* Glass blur variations */
.glass-blur-light { backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); }
.glass-blur-medium { backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); }
.glass-blur-heavy { backdrop-filter: blur(30px); -webkit-backdrop-filter: blur(30px); }

/* Glass opacity variations */
.glass-opacity-light { background: rgba(255, 255, 255, 0.05); }
.glass-opacity-medium { background: rgba(255, 255, 255, 0.15); }
.glass-opacity-heavy { background: rgba(255, 255, 255, 0.25); }
.glass-opacity-solid { background: rgba(255, 255, 255, 0.9); }

/* Border variations */
.glass-border-light { border: 1px solid rgba(255, 255, 255, 0.1); }
.glass-border-medium { border: 1px solid rgba(255, 255, 255, 0.2); }
.glass-border-heavy { border: 1px solid rgba(255, 255, 255, 0.3); }

/* Shadow variations */
.glass-shadow-light { box-shadow: 0 4px 16px rgba(0, 0, 0, 0.05); }
.glass-shadow-medium { box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1); }
.glass-shadow-heavy { box-shadow: 0 12px 48px rgba(0, 0, 0, 0.15); }

/* ========================================
   DOCUMENT MANAGEMENT SPECIFIC COMPONENTS
   ======================================== */

/* Document Management Header Card */
.glass-card-doc-management {
  position: sticky;
  top: 52px;
  z-index: 99;
  padding: 1rem 2rem;
  margin: 0;
  height: auto;
  min-height: 120px;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  width: 100%;
  box-sizing: border-box;
  background: rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  border: 0.81px solid;
  border-image-source: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(153, 153, 153, 0) 100%
  );
  backdrop-filter: blur(10.67px);
  box-shadow: 0px 2.7px 8.1px 0px #ffffff inset;
}

/* Document Management Table Container */
.glass-doc-table-container {
  background: var(--glass-primary);
  backdrop-filter: var(--glass-blur);
  -webkit-backdrop-filter: var(--glass-blur);
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
              0 0 0 1px var(--glass-border);
  overflow: hidden;
  margin: 0;
  padding: 0;
  border: 1px solid var(--glass-border);
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Document Management Table */
.glass-doc-table {
  width: 100%;
  min-width: 0;
  table-layout: fixed;
  border: none;
  border-radius: 12px;
  overflow: hidden;
  background: transparent;
}

/* Document Management Table Headers */
.glass-doc-table .p-datatable-thead > tr > th {
  background: rgba(248, 250, 252, 0.3);
  backdrop-filter: var(--glass-blur-light);
  -webkit-backdrop-filter: var(--glass-blur-light);
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
  border-right: 1px solid rgba(229, 231, 235, 0.2);
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-dark);
  text-align: left;
  vertical-align: middle;
}

.glass-doc-table .p-datatable-thead > tr > th:last-child {
  border-right: none;
}

/* Document Management Table Body */
.glass-doc-table .p-datatable-tbody > tr,
.glass-doc-table .p-datatable-tbody > tr > td {
  padding: 12px 16px;
  border-bottom: 1px solid rgba(243, 244, 246, 0.3);
  border-right: 1px solid rgba(229, 231, 235, 0.2);
  font-size: 14px;
  color: var(--text-dark);
  text-align: left;
  vertical-align: middle;
  background-color: rgba(255, 255, 255, 0.3) !important;
  box-shadow: none !important;
  border-color: rgba(255, 255, 255, 0.1) !important;
}

.glass-doc-table .p-datatable-tbody > tr > td:last-child {
  border-right: none;
}

.glass-doc-table .p-datatable-tbody > tr:hover {
  background-color: rgba(255, 255, 255, 0.4) !important;
}

/* ========================================
   SCROLLBAR HIDING
   ======================================== */

/* Hide scrollbar for webkit browsers */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for Firefox */
.hide-scrollbar {
  scrollbar-width: none;
}

/* Hide scrollbar for IE and Edge */
.hide-scrollbar {
  -ms-overflow-style: none;
}

/* ========================================
   RESPONSIVE DESIGN
   ======================================== */

@media (max-width: 1200px) {
  .glass-search-input {
    width: 250px;
  }
  
  .glass-table-container {
    margin: 16px;
    width: calc(100% - 32px);
    max-width: calc(100% - 32px);
  }
}

@media (max-width: 768px) {
  .glassy-page {
    padding: 16px;
  }
  
  .glass-header {
    flex-direction: column;
    gap: 16px;
    height: auto;
    padding: 16px;
  }
  
  .glass-header-left {
    justify-content: center;
    padding: 8px 16px;
  }
  
  .glass-header-right {
    border-radius: 8px;
    padding: 8px 16px;
  }
  
  .glass-search-input {
    width: 100%;
    min-width: 250px;
  }
  
  .glass-table .p-datatable-thead > tr > th,
  .glass-table .p-datatable-tbody > tr > td {
    padding: 8px;
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .glass-table-container {
    margin: 8px;
    width: calc(100% - 16px);
    max-width: calc(100% - 16px);
  }
  
  .glass-btn-primary,
  .glass-btn-secondary {
    padding: 6px 12px;
    font-size: 12px;
  }
}

.search-glass-clear-btn {
  position: absolute;
  right: 0.75rem;  /* keep it on the right side */
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: var(--surface-600);
}

/* ========================================
   INPUT WITH LEFT ICON (used in NewTicketDialog)
   ======================================== */
.input-left-icon {
  position: relative;
  /* display: inline-block; */
}

.input-left-icon .left-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #6b7280; /* gray-500 */
  z-index: 2;
  pointer-events: none;
}

/* Ensure inputs inside wrapper have enough left padding */
.input-left-icon .p-inputtext,
.input-left-icon input[type="text"],
.input-left-icon input[type="search"],
.input-left-icon input[type="email"],
.input-left-icon input[type="password"] {
  padding-left: 2.5rem !important; /* matches pl-40 */
}

.glass-container .p-accordion .p-accordion-header .p-accordion-header-link {
  background-color: #193276 !important; /* your color */
  color: white !important; /* optional for text */
  border: none !important;
}

.glass-container .p-accordion .p-accordion-content {
  background-color: transparent !important; /* your color */
}


/* .glass-input .p-component .p-dropdown-label .p-inputtext .p-placeholder.w-full{
  width: 100% !important;
} */

/* ========================================
   ADD POINT MODAL SPECIFIC STYLES
   ======================================== */

/* Add Point Modal Overlay */
.glass-modal-overlay-addpoint {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999999;
}

/* Add Point Modal Container */
.glass-card-global-addpoint {
  max-width: 80vw;
  width: 900px;
  max-height: 80vh;
  padding: 0;
  background: rgba(255, 255, 255/ 35% );
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border-radius: 20px;
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.4),
    inset 0 -1px 0 rgba(255, 255, 255, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  position: relative;
}

/* Add Point Modal Header */
.glass-modal-header-addpoint {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  padding: 12px 16px;
  min-height: 48px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Add Point Modal Body */
.glass-modal-body-addpoint {
  flex: 1;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.08);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  padding: 12px;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

/* Add Point Form Container */
.glass-form-container-addpoint {
  transform: scale(0.75);
  transform-origin: top left;
  width: 133.3%;
  height: 133.3%;
  overflow: hidden;
}

/* Add Point Modal Footer */
.glass-modal-footer-addpoint {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 0 0 20px 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  width: 100%;
  box-sizing: border-box;
  position: relative;
}