import React, { useState, useEffect } from 'react';
import { Provider } from 'react-redux';
import { PersistGate } from 'redux-persist/integration/react';
import { store, persistor } from '../store';
import { CacheProvider } from '../hooks/useCacheProvider';
import { useCache, useApiCache, useUserCache, useCacheAnalytics } from '../hooks/useCache';
import CacheDashboard from '../components/cache/CacheDashboard';
import cacheController from '../mvc/controllers/CacheController';

const mockApi = {
  fetchUserProfile: async (userId) => {
    await new Promise(resolve => setTimeout(resolve, 1000));
    return {
      id: userId,
      name: `User ${userId}`,
      email: `user${userId}@example.com`,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=${userId}`,
      lastLogin: new Date().toISOString(),
      preferences: {
        theme: 'dark',
        language: 'en',
        notifications: true
      }
    };
  },

  fetchProducts: async (category = 'all') => {
    await new Promise(resolve => setTimeout(resolve, 800));
    return [
      { id: 1, name: 'Laptop', category: 'electronics', price: 999 },
      { id: 2, name: 'Phone', category: 'electronics', price: 699 },
      { id: 3, name: 'Book', category: 'books', price: 29 },
      { id: 4, name: 'Headphones', category: 'electronics', price: 199 }
    ].filter(p => category === 'all' || p.category === category);
  },

  updateUserPreferences: async (userId, preferences) => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true, preferences };
  }
};

const UserProfile = ({ userId }) => {
  const { data: profile, isLoading, error, refetch, isStale } = useCache(
    `user-profile-${userId}`,
    () => mockApi.fetchUserProfile(userId),
    {
      ttl: 5 * 60 * 1000,
      tags: ['user', 'profile'],
      staleWhileRevalidate: true,
      onSuccess: (data, fromCache) => {
        console.log(`User profile ${fromCache ? 'loaded from cache' : 'fetched from API'}`);
      }
    }
  );

  const { data: preferences, setData: setPreferences } = useUserCache(
    userId, 
    'preferences',
    {
      ttl: 30 * 60 * 1000,
      encrypt: true
    }
  );

  const handleUpdatePreferences = async (newPrefs) => {
    try {
      await mockApi.updateUserPreferences(userId, newPrefs);
      await setPreferences(newPrefs);
      

      await cacheController.invalidateByTags(['profile']);
    } catch (error) {
      console.error('Failed to update preferences:', error);
    }
  };

  if (isLoading) return <div className="loading">Loading user profile...</div>;
  if (error) return <div className="error">Error: {error}</div>;

  return (
    <div className="user-profile">
      <div className="profile-header">
        <img src={profile.avatar} alt={profile.name} className="avatar" />
        <div className="profile-info">
          <h2>{profile.name}</h2>
          <p>{profile.email}</p>
          <p>Last login: {new Date(profile.lastLogin).toLocaleString()}</p>
          {isStale && <span className="stale-indicator">Data is stale, refreshing...</span>}
        </div>
        <button onClick={refetch} className="refresh-btn">
          Refresh Profile
        </button>
      </div>

      <div className="preferences-section">
        <h3>Preferences</h3>
        <div className="preferences-grid">
          <label>
            Theme:
            <select 
              value={preferences?.theme || profile.preferences.theme}
              onChange={(e) => handleUpdatePreferences({ 
                ...preferences, 
                theme: e.target.value 
              })}
            >
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="auto">Auto</option>
            </select>
          </label>
          
          <label>
            Language:
            <select 
              value={preferences?.language || profile.preferences.language}
              onChange={(e) => handleUpdatePreferences({ 
                ...preferences, 
                language: e.target.value 
              })}
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
            </select>
          </label>
          
          <label>
            <input
              type="checkbox"
              checked={preferences?.notifications ?? profile.preferences.notifications}
              onChange={(e) => handleUpdatePreferences({ 
                ...preferences, 
                notifications: e.target.checked 
              })}
            />
            Enable Notifications
          </label>
        </div>
      </div>
    </div>
  );
};

const ProductList = () => {
  const [category, setCategory] = useState('all');
  
  const { data: products, isLoading, error, isStale, refetch } = useApiCache(
    '/api/products',
    {
      method: 'GET',
      params: { category },
      ttl: 2 * 60 * 1000,
      staleWhileRevalidate: true,
      backgroundRefresh: true,
      tags: ['products', category],
      onSuccess: (data, fromCache, stale) => {
        console.log(`Products ${fromCache ? 'from cache' : 'from API'}${stale ? ' (stale)' : ''}`);
      }
    }
  );


  useEffect(() => {
    const fetchProducts = async () => {
      try {
        const data = await mockApi.fetchProducts(category);
        await cacheController.set('apiCache', `products-${category}`, data, {
          ttl: 2 * 60 * 1000,
          tags: ['products', category]
        });
      } catch (error) {
        console.error('Failed to fetch products:', error);
      }
    };

    fetchProducts();
  }, [category]);

  const handleClearProductCache = async () => {
    await cacheController.invalidateByTags(['products']);
    refetch();
  };

  return (
    <div className="product-list">
      <div className="product-list-header">
        <h2>Products</h2>
        <div className="product-controls">
          <select value={category} onChange={(e) => setCategory(e.target.value)}>
            <option value="all">All Categories</option>
            <option value="electronics">Electronics</option>
            <option value="books">Books</option>
          </select>
          <button onClick={refetch}>Refresh</button>
          <button onClick={handleClearProductCache}>Clear Cache</button>
        </div>
      </div>

      {isStale && <div className="stale-notice">Refreshing product data...</div>}
      
      {isLoading ? (
        <div className="loading">Loading products...</div>
      ) : error ? (
        <div className="error">Error: {error}</div>
      ) : (
        <div className="products-grid">
          {products?.map(product => (
            <div key={product.id} className="product-card">
              <h3>{product.name}</h3>
              <p>Category: {product.category}</p>
              <p>Price: ${product.price}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

const CacheAnalyticsPanel = () => {
  const { analytics, refreshAnalytics, resetAnalytics } = useCacheAnalytics();

  return (
    <div className="cache-analytics-panel">
      <h2>Cache Performance</h2>
      
      {analytics && (
        <div className="analytics-summary">
          <div className="metric">
            <label>Hit Rate:</label>
            <span className={analytics.summary.hitRate > 70 ? 'good' : 'warning'}>
              {analytics.summary.hitRate.toFixed(1)}%
            </span>
          </div>
          
          <div className="metric">
            <label>Total Requests:</label>
            <span>{analytics.summary.totalRequests}</span>
          </div>
          
          <div className="metric">
            <label>Error Rate:</label>
            <span className={analytics.summary.errorRate < 5 ? 'good' : 'error'}>
              {analytics.summary.errorRate.toFixed(1)}%
            </span>
          </div>
          
          <div className="metric">
            <label>Avg Response Time:</label>
            <span>{analytics.summary.averageResponseTime.toFixed(1)}ms</span>
          </div>
        </div>
      )}
      
      <div className="analytics-actions">
        <button onClick={refreshAnalytics}>Refresh Analytics</button>
        <button onClick={resetAnalytics}>Reset Analytics</button>
      </div>
    </div>
  );
};

const CacheExampleApp = () => {
  const [currentUserId, setCurrentUserId] = useState(1);
  const [showDashboard, setShowDashboard] = useState(false);

  return (
    <div className="cache-example-app">
      <header className="app-header">
        <h1>Redux Toolkit Cache System Demo</h1>
        <div className="header-controls">
          <label>
            User ID:
            <select 
              value={currentUserId} 
              onChange={(e) => setCurrentUserId(Number(e.target.value))}
            >
              <option value={1}>User 1</option>
              <option value={2}>User 2</option>
              <option value={3}>User 3</option>
            </select>
          </label>
          <button 
            onClick={() => setShowDashboard(!showDashboard)}
            className={showDashboard ? 'active' : ''}
          >
            {showDashboard ? 'Hide' : 'Show'} Dashboard
          </button>
        </div>
      </header>

      <main className="app-main">
        {showDashboard ? (
          <CacheDashboard />
        ) : (
          <div className="app-content">
            <div className="content-grid">
              <UserProfile userId={currentUserId} />
              <ProductList />
            </div>
            <CacheAnalyticsPanel />
          </div>
        )}
      </main>
    </div>
  );
};

const CacheIntegrationExample = () => {
  return (
    <Provider store={store}>
      <PersistGate loading={<div>Loading cache system...</div>} persistor={persistor}>
        <CacheProvider config={{
          autoCleanup: true,
          monitoring: true,
          persistence: true,
          cleanupInterval: 2 * 60 * 1000,
          monitoringInterval: 10 * 1000,
          preloadCommonData: true
        }}>
          <CacheExampleApp />
        </CacheProvider>
      </PersistGate>
    </Provider>
  );
};

export default CacheIntegrationExample;
