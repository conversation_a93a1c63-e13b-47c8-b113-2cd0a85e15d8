
class ConfigValidator {
  constructor() {
    this.schemas = new Map();
    this.customValidators = new Map();
    this.initializeDefaultValidators();
  }

  registerSchema(name, schema) {
    this.schemas.set(name, schema);
  }

  registerValidator(name, validator) {
    this.customValidators.set(name, validator);
  }

  validate(config, schema) {
    if (typeof schema === 'string') {
      schema = this.schemas.get(schema);
      if (!schema) {
        throw new Error(`Schema '${schema}' not found`);
      }
    }

    if (!schema) {
      return { isValid: true, errors: [] };
    }

    const errors = [];
    this.validateObject(config, schema, '', errors);

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  validateObject(value, schema, path, errors) {
    if (value == null) {
      if (schema.required) {
        errors.push(`${path || 'root'} is required`);
      }
      return;
    }

    if (schema.type && !this.validateType(value, schema.type)) {
      errors.push(`${path || 'root'} must be of type ${schema.type}`);
      return;
    }

    if (schema.validator) {
      const validator = this.customValidators.get(schema.validator);
      if (validator) {
        const result = validator(value, schema);
        if (!result.isValid) {
          errors.push(...result.errors.map(err => `${path || 'root'}: ${err}`));
        }
      }
    }

    if (schema.type === 'string') {
      this.validateString(value, schema, path, errors);
    }

    if (schema.type === 'number') {
      this.validateNumber(value, schema, path, errors);
    }

    if (schema.type === 'array') {
      this.validateArray(value, schema, path, errors);
    }

    if (schema.type === 'object' || schema.properties) {
      this.validateObjectProperties(value, schema, path, errors);
    }

    if (schema.enum && !schema.enum.includes(value)) {
      errors.push(`${path || 'root'} must be one of: ${schema.enum.join(', ')}`);
    }

    if (schema.pattern && typeof value === 'string') {
      const regex = new RegExp(schema.pattern);
      if (!regex.test(value)) {
        errors.push(`${path || 'root'} does not match required pattern`);
      }
    }

    if (schema.validate && typeof schema.validate === 'function') {
      try {
        const result = schema.validate(value);
        if (result !== true && typeof result === 'string') {
          errors.push(`${path || 'root'}: ${result}`);
        }
      } catch (error) {
        errors.push(`${path || 'root'}: Validation error - ${error.message}`);
      }
    }
  }

  validateType(value, type) {
    switch (type) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'function':
        return typeof value === 'function';
      case 'date':
        return value instanceof Date || !isNaN(Date.parse(value));
      default:
        return true;
    }
  }

  validateString(value, schema, path, errors) {
    if (schema.minLength && value.length < schema.minLength) {
      errors.push(`${path || 'root'} must be at least ${schema.minLength} characters long`);
    }

    if (schema.maxLength && value.length > schema.maxLength) {
      errors.push(`${path || 'root'} must be no more than ${schema.maxLength} characters long`);
    }

    if (schema.format) {
      this.validateFormat(value, schema.format, path, errors);
    }
  }

  validateNumber(value, schema, path, errors) {
    if (schema.minimum !== undefined && value < schema.minimum) {
      errors.push(`${path || 'root'} must be at least ${schema.minimum}`);
    }

    if (schema.maximum !== undefined && value > schema.maximum) {
      errors.push(`${path || 'root'} must be no more than ${schema.maximum}`);
    }

    if (schema.multipleOf && value % schema.multipleOf !== 0) {
      errors.push(`${path || 'root'} must be a multiple of ${schema.multipleOf}`);
    }
  }

  validateArray(value, schema, path, errors) {
    if (schema.minItems && value.length < schema.minItems) {
      errors.push(`${path || 'root'} must have at least ${schema.minItems} items`);
    }

    if (schema.maxItems && value.length > schema.maxItems) {
      errors.push(`${path || 'root'} must have no more than ${schema.maxItems} items`);
    }

    if (schema.uniqueItems) {
      const unique = [...new Set(value)];
      if (unique.length !== value.length) {
        errors.push(`${path || 'root'} must have unique items`);
      }
    }

    if (schema.items) {
      value.forEach((item, index) => {
        this.validateObject(item, schema.items, `${path}[${index}]`, errors);
      });
    }
  }

  validateObjectProperties(value, schema, path, errors) {
    if (schema.properties) {
      Object.entries(schema.properties).forEach(([key, propSchema]) => {
        const propPath = path ? `${path}.${key}` : key;
        this.validateObject(value[key], propSchema, propPath, errors);
      });
    }

    if (schema.required) {
      schema.required.forEach(key => {
        if (!(key in value)) {
          const propPath = path ? `${path}.${key}` : key;
          errors.push(`${propPath} is required`);
        }
      });
    }

    if (schema.additionalProperties === false) {
      const allowedKeys = Object.keys(schema.properties || {});
      const extraKeys = Object.keys(value).filter(key => !allowedKeys.includes(key));
      if (extraKeys.length > 0) {
        errors.push(`${path || 'root'} has unexpected properties: ${extraKeys.join(', ')}`);
      }
    }
  }

  validateFormat(value, format, path, errors) {
    const formats = {
      email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
      url: /^https?:\/\/.+/,
      uuid: /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i,
      date: /^\d{4}-\d{2}-\d{2}$/,
      time: /^\d{2}:\d{2}:\d{2}$/,
      'date-time': /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(\.\d{3})?Z?$/,
      color: /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/,
      phone: /^\+?[\d\s\-\(\)]+$/
    };

    const regex = formats[format];
    if (regex && !regex.test(value)) {
      errors.push(`${path || 'root'} must be a valid ${format}`);
    }
  }

  initializeDefaultValidators() {
    this.registerValidator('required', (value) => ({
      isValid: value != null && value !== '',
      errors: value == null || value === '' ? ['Value is required'] : []
    }));

    this.registerValidator('minLength', (value, schema) => ({
      isValid: !schema.minLength || (value && value.length >= schema.minLength),
      errors: value && value.length < schema.minLength ? [`Must be at least ${schema.minLength} characters`] : []
    }));

    this.registerValidator('maxLength', (value, schema) => ({
      isValid: !schema.maxLength || (value && value.length <= schema.maxLength),
      errors: value && value.length > schema.maxLength ? [`Must be no more than ${schema.maxLength} characters`] : []
    }));

    this.registerValidator('range', (value, schema) => {
      const errors = [];
      if (schema.min !== undefined && value < schema.min) {
        errors.push(`Must be at least ${schema.min}`);
      }
      if (schema.max !== undefined && value > schema.max) {
        errors.push(`Must be no more than ${schema.max}`);
      }
      return { isValid: errors.length === 0, errors };
    });

    this.registerValidator('options', (value, schema) => ({
      isValid: !schema.options || schema.options.includes(value),
      errors: schema.options && !schema.options.includes(value) ? [`Must be one of: ${schema.options.join(', ')}`] : []
    }));

    this.registerValidator('custom', (value, schema) => {
      if (!schema.customValidator || typeof schema.customValidator !== 'function') {
        return { isValid: true, errors: [] };
      }

      try {
        const result = schema.customValidator(value);
        if (typeof result === 'boolean') {
          return { isValid: result, errors: result ? [] : ['Custom validation failed'] };
        }
        if (typeof result === 'object' && result.isValid !== undefined) {
          return result;
        }
        return { isValid: true, errors: [] };
      } catch (error) {
        return { isValid: false, errors: [`Custom validation error: ${error.message}`] };
      }
    });
  }

  getSchemas() {
    return Array.from(this.schemas.keys());
  }

  getValidators() {
    return Array.from(this.customValidators.keys());
  }
}

const configValidator = new ConfigValidator();

export default configValidator;
