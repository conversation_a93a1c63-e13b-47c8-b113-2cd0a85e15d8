function simpleHash(str) {
  let hash = 0;
  if (str.length === 0) return hash.toString(16);

  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; 
  }

  return Math.abs(hash).toString(16).padStart(8, '0');
}

class CacheService {
  constructor() {
    this.store = null;
    this.strategies = new Map();
    this.listeners = new Map();
    this.analytics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      clears: 0,
      startTime: Date.now()
    };
    
    this.initializeStrategies();
  }

  initialize(store) {
    this.store = store;
    this.setupStoreListeners();
    console.log('CacheService initialized with Redux store');
  }

  initializeStrategies() {
    this.strategies.set('lru', {
      name: 'LRU',
      description: 'Least Recently Used eviction policy',
      evict: (entries, maxEntries) => {
        const sortedEntries = Object.entries(entries)
          .sort(([, a], [, b]) => (a.lastAccessed || 0) - (b.lastAccessed || 0));
        
        const toRemove = sortedEntries.slice(0, sortedEntries.length - maxEntries + 1);
        const newEntries = { ...entries };
        
        toRemove.forEach(([key]) => {
          delete newEntries[key];
        });
        
        return newEntries;
      }
    });

    this.strategies.set('fifo', {
      name: 'FIFO',
      description: 'First In, First Out eviction policy',
      evict: (entries, maxEntries) => {
        const sortedEntries = Object.entries(entries)
          .sort(([, a], [, b]) => (a.createdAt || 0) - (b.createdAt || 0));
        
        const toRemove = sortedEntries.slice(0, sortedEntries.length - maxEntries + 1);
        const newEntries = { ...entries };
        
        toRemove.forEach(([key]) => {
          delete newEntries[key];
        });
        
        return newEntries;
      }
    });

    this.strategies.set('ttl', {
      name: 'TTL',
      description: 'Time To Live based eviction',
      evict: (entries, maxEntries) => {
        const now = Date.now();
        const validEntries = {};
        
        Object.entries(entries).forEach(([key, entry]) => {
          if (!entry.expiresAt || entry.expiresAt > now) {
            validEntries[key] = entry;
          }
        });
        
        if (Object.keys(validEntries).length > maxEntries) {
          return this.strategies.get('lru').evict(validEntries, maxEntries);
        }
        
        return validEntries;
      }
    });

    this.strategies.set('size', {
      name: 'Size',
      description: 'Size-based eviction policy',
      evict: (entries, maxEntries) => {
        const entriesWithSize = Object.entries(entries).map(([key, entry]) => ({
          key,
          entry,
          size: JSON.stringify(entry.data).length
        }));
        
        entriesWithSize.sort((a, b) => b.size - a.size);
        
        const toKeep = entriesWithSize.slice(-maxEntries);
        const newEntries = {};
        
        toKeep.forEach(({ key, entry }) => {
          newEntries[key] = entry;
        });
        
        return newEntries;
      }
    });
  }

  setupStoreListeners() {
    if (!this.store) return;

    this.store.subscribe(() => {
      this.updateAnalytics();
    });
  }

  generateKey(namespace, identifier, params = {}) {
    const keyData = {
      namespace,
      identifier,
      params: typeof params === 'object' ? JSON.stringify(params) : params
    };

    const keyString = JSON.stringify(keyData);
    return simpleHash(keyString);
  }

  async set(cacheType, key, data, options = {}) {
    if (!this.store) {
      throw new Error('CacheService not initialized with store');
    }

    const {
      ttl = 5 * 60 * 1000, 
      tags = [],
      priority = 1,
      strategy = 'lru',
      metadata = {}
    } = options;

    const now = Date.now();
    const entry = {
      data,
      createdAt: now,
      lastAccessed: now,
      expiresAt: ttl > 0 ? now + ttl : null,
      tags,
      priority,
      strategy,
      metadata,
      version: 1,
      size: JSON.stringify(data).length
    };

    try {
      this.store.dispatch({
        type: `${cacheType}/set`,
        payload: { key, entry }
      });

      this.analytics.sets++;
      this.notifyListeners('set', { cacheType, key, entry });
      
      return true;
    } catch (error) {
      console.error('Failed to set cache entry:', error);
      return false;
    }
  }

  async get(cacheType, key, options = {}) {
    if (!this.store) {
      throw new Error('CacheService not initialized with store');
    }

    const { updateLastAccessed = true } = options;
    const state = this.store.getState();
    const entry = state[cacheType]?.entries?.[key];

    if (!entry) {
      this.analytics.misses++;
      this.notifyListeners('miss', { cacheType, key });
      return null;
    }

    const now = Date.now();
    if (entry.expiresAt && entry.expiresAt <= now) {
      this.store.dispatch({
        type: `${cacheType}/delete`,
        payload: { key }
      });
      
      this.analytics.misses++;
      this.notifyListeners('miss', { cacheType, key, reason: 'expired' });
      return null;
    }

    if (updateLastAccessed) {
      this.store.dispatch({
        type: `${cacheType}/updateLastAccessed`,
        payload: { key, timestamp: now }
      });
    }

    this.analytics.hits++;
    this.notifyListeners('hit', { cacheType, key, entry });
    
    return entry.data;
  }

  async has(cacheType, key) {
    if (!this.store) return false;

    const state = this.store.getState();
    const entry = state[cacheType]?.entries?.[key];

    if (!entry) return false;

    const now = Date.now();
    if (entry.expiresAt && entry.expiresAt <= now) {
      return false;
    }

    return true;
  }

  async delete(cacheType, key) {
    if (!this.store) return false;

    try {
      this.store.dispatch({
        type: `${cacheType}/delete`,
        payload: { key }
      });

      this.analytics.deletes++;
      this.notifyListeners('delete', { cacheType, key });
      
      return true;
    } catch (error) {
      console.error('Failed to delete cache entry:', error);
      return false;
    }
  }

  async clear(cacheType = null) {
    if (!this.store) return false;

    try {
      if (cacheType) {
        this.store.dispatch({
          type: `${cacheType}/clear`
        });
      } else {
        const cacheTypes = ['cache', 'userCache', 'apiCache', 'themeCache', 'i18nCache'];
        cacheTypes.forEach(type => {
          this.store.dispatch({
            type: `${type}/clear`
          });
        });
      }

      this.analytics.clears++;
      this.notifyListeners('clear', { cacheType });
      
      return true;
    } catch (error) {
      console.error('Failed to clear cache:', error);
      return false;
    }
  }

  async invalidateByTags(tags, cacheType = null) {
    if (!this.store) return false;

    const tagsArray = Array.isArray(tags) ? tags : [tags];
    
    try {
      if (cacheType) {
        this.store.dispatch({
          type: `${cacheType}/invalidateByTags`,
          payload: { tags: tagsArray }
        });
      } else {
        const cacheTypes = ['cache', 'userCache', 'apiCache', 'themeCache', 'i18nCache'];
        cacheTypes.forEach(type => {
          this.store.dispatch({
            type: `${type}/invalidateByTags`,
            payload: { tags: tagsArray }
          });
        });
      }

      this.notifyListeners('invalidate', { tags: tagsArray, cacheType });
      
      return true;
    } catch (error) {
      console.error('Failed to invalidate cache by tags:', error);
      return false;
    }
  }

  getStats(cacheType = null) {
    if (!this.store) return null;

    const state = this.store.getState();
    const now = Date.now();
    const uptime = now - this.analytics.startTime;

    if (cacheType) {
      const cacheState = state[cacheType];
      const entries = cacheState?.entries || {};
      
      return {
        type: cacheType,
        entryCount: Object.keys(entries).length,
        totalSize: Object.values(entries).reduce((sum, entry) => sum + (entry.size || 0), 0),
        oldestEntry: Math.min(...Object.values(entries).map(e => e.createdAt || now)),
        newestEntry: Math.max(...Object.values(entries).map(e => e.createdAt || 0)),
        expiredCount: Object.values(entries).filter(e => e.expiresAt && e.expiresAt <= now).length
      };
    }

    const totalRequests = this.analytics.hits + this.analytics.misses;
    const hitRate = totalRequests > 0 ? (this.analytics.hits / totalRequests) * 100 : 0;

    return {
      global: true,
      uptime,
      totalRequests,
      hits: this.analytics.hits,
      misses: this.analytics.misses,
      hitRate: Math.round(hitRate * 100) / 100,
      sets: this.analytics.sets,
      deletes: this.analytics.deletes,
      clears: this.analytics.clears,
      cacheTypes: {
        cache: this.getStats('cache'),
        userCache: this.getStats('userCache'),
        apiCache: this.getStats('apiCache'),
        themeCache: this.getStats('themeCache'),
        i18nCache: this.getStats('i18nCache')
      }
    };
  }

  updateAnalytics() {
  }

  addEventListener(event, callback) {
    if (!this.listeners.has(event)) {
      this.listeners.set(event, new Set());
    }
    
    this.listeners.get(event).add(callback);
    
    return () => {
      const eventListeners = this.listeners.get(event);
      if (eventListeners) {
        eventListeners.delete(callback);
      }
    };
  }

  notifyListeners(event, data) {
    const eventListeners = this.listeners.get(event);
    if (eventListeners) {
      eventListeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error('Cache event listener error:', error);
        }
      });
    }
  }

  getStrategies() {
    return Array.from(this.strategies.entries()).map(([key, strategy]) => ({
      key,
      name: strategy.name,
      description: strategy.description
    }));
  }

  applyStrategy(cacheType, strategyName, maxEntries) {
    if (!this.store) return false;

    const strategy = this.strategies.get(strategyName);
    if (!strategy) {
      console.error(`Unknown caching strategy: ${strategyName}`);
      return false;
    }

    try {
      this.store.dispatch({
        type: `${cacheType}/applyStrategy`,
        payload: { strategy: strategyName, maxEntries }
      });
      
      return true;
    } catch (error) {
      console.error('Failed to apply caching strategy:', error);
      return false;
    }
  }

  exportCache(cacheType = null) {
    if (!this.store) return null;

    const state = this.store.getState();
    const timestamp = Date.now();

    if (cacheType) {
      return {
        type: cacheType,
        timestamp,
        data: state[cacheType],
        stats: this.getStats(cacheType)
      };
    }

    return {
      timestamp,
      data: {
        cache: state.cache,
        userCache: state.userCache,
        apiCache: state.apiCache,
        themeCache: state.themeCache,
        i18nCache: state.i18nCache
      },
      stats: this.getStats()
    };
  }

  async importCache(cacheData, cacheType = null) {
    if (!this.store) return false;

    try {
      if (cacheType && cacheData.type === cacheType) {
        this.store.dispatch({
          type: `${cacheType}/import`,
          payload: cacheData.data
        });
      } else if (!cacheType && cacheData.data) {
        Object.entries(cacheData.data).forEach(([type, data]) => {
          this.store.dispatch({
            type: `${type}/import`,
            payload: data
          });
        });
      }

      return true;
    } catch (error) {
      console.error('Failed to import cache data:', error);
      return false;
    }
  }
}

const cacheService = new CacheService();

export default cacheService;
