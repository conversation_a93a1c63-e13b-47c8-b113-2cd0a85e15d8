import React from 'react';
import '../../styles/glassy/glassy-ui.css';
import '../../styles/glassy/global.css';

/*
  Reusable KeyData card
  - Composes glassy UI styles and global fonts
  - Mirrors the provided layout
  - Responsive: fixed desktop size target (660x582) with fluid behavior on smaller screens

  Props:
    title?: string
    person?: {
      photoUrl?: string
      fullName?: string
      countryLabel?: string
      country?: string
    }
    originalScripts?: Array<{
      code?: string   // e.g., 'CN'
      text: string    // e.g., '내의 솰IP'
      color?: 'blue' | 'green' | 'purple' | 'pink' | 'red' | 'orange' | 'teal' | 'gray'
    }>
    sanctions?: Array<{
      sourceCode?: string // e.g., 'US'
      title: string       // e.g., 'OFAC - ...'
      documents?: Array<{
        label: string     // 'Document 1'
        number: string    // 'TR-P-614166'
        country?: string  // 'USA'
        idType?: string   // 'Passport'
        onView?: () => void
        onDownload?: () => void
      }>
    }>
*/

const colorToClass = (c) => {
  switch (c) {
    case 'blue': return 'badge-blue';
    case 'green': return 'badge-green';
    case 'purple': return 'badge-purple';
    case 'pink': return 'badge-pink';
    case 'red': return 'badge-red';
    case 'orange': return 'badge-orange';
    case 'teal': return 'badge-teal';
    default: return 'badge-gray';
  }
};

export default function KeyData({
  title = 'Key Data',
  person = {
    photoUrl: '',
    fullName: 'Jane DOE',
    countryLabel: 'Country',
    country: 'North Korea',
  },
  originalScripts = [
    { code: 'CN', text: '내의 솰IP', color: 'red' },
    { code: 'RU', text: 'Джон Доу', color: 'blue' },
    { code: 'AR', text: 'جان دو', color: 'green' },
    { code: 'KO', text: '존 도우', color: 'purple' },
    { code: 'JP', text: 'ジョン・ドゥ', color: 'pink' },
    { code: 'AM', text: 'ԱՄ', color: 'orange' },
  ],
  sanctions = [
    {
      sourceCode: 'US',
      title: 'OFAC - Specially Designated Nationals - Treasury Documents',
      documents: [
        { label: 'Document 1', number: '001B850', country: 'USA', idType: 'Passport' },
        { label: 'Document 2', number: 'TR-P-614166', country: 'USA', idType: 'Passport' },
      ],
    },
  ],
}) {
  return (
    <div className="keydata-card glass-card-global glass-shadow-light flex flex-column" style={{ width: '100%', maxWidth: '100%' }}>
      {/* Header */}
      <div className="keydata-header glass-header flex align-items-center justify-content-between w-full">
        <div className="glass-header-left flex align-items-center gap-2">
          <i className="pi pi-star-fill text-yellow-500" aria-hidden></i>
          <h3 className="keydata-title m-0">{title}</h3>
        </div>
        <div className="glass-header-right" />
      </div>

      {/* Body */}
      <div className="keydata-body flex flex-column gap-3 p-3 md:p-4">
        <div className="keydata-top flex align-items-start gap-2">
          <div className="col-fixed">
            <div className="keydata-photo">
              {person?.photoUrl ? (
                <img src={person.photoUrl} alt={person.fullName || 'Profile'} />
              ) : (
                <div className="keydata-photo-fallback">{(person?.fullName || 'J D').split(' ').map(x => x[0]).join('').slice(0,2)}</div>
              )}
            </div>
          </div>
          <div className="col keydata-person flex flex-column gap-2">
            <div className="keydata-label">Name Surname</div>
            <div className="keydata-name">{person?.fullName}</div>
            <div className="keydata-label mt-3">{person?.countryLabel || 'Country'}</div>
            <div className="keydata-country">{person?.country}</div>
          </div>
        </div>

        <div className="keydata-section flex flex-column gap-2">
          <div className="keydata-section-title">Original Scripts</div>
          <div className="keydata-chips flex flex-wrap gap-2">
            {originalScripts?.map((s, idx) => (
              <div key={idx} className={`keydata-chip ${colorToClass(s.color)} flex align-items-center`}>
                {s.code && <span className="chip-code flex align-items-center justify-content-center">{s.code}</span>}
                <span className="chip-text">{s.text}</span>
              </div>
            ))}
          </div>
        </div>

        {sanctions?.map((s, i) => (
          <div className="keydata-section flex flex-column gap-2" key={`sec-${i}`}>
            <div className="keydata-sanction-header flex align-items-center gap-2">
              {s.sourceCode && <span className="source-badge">{s.sourceCode}</span>}
              <h4 className="sanction-title m-0">{s.title}</h4>
            </div>
            <div className="keydata-doc-list flex flex-column">
              {s.documents?.map((d, j) => (
                <div className="doc-row flex flex-column md:flex-row align-items-start md:align-items-center justify-content-between gap-2" key={`doc-${j}`}>
                  <div className="doc-left">
                    <div className="doc-label">{d.label}</div>
                    <div className="doc-meta flex align-items-center gap-3">
                      <span className="doc-number">{d.number}</span>
                      <span className="dot-sep" />
                      <span className="doc-country">{d.country}</span>
                      <span className="dot-sep" />
                      <span className="doc-idtype">{d.idType}</span>
                    </div>
                  </div>
                  <div className="doc-actions flex gap-2 justify-content-start md:justify-content-end">
                    <button className="icon-btn view" title="View" onClick={d.onView} aria-label="View document">
                      <i className="pi pi-eye" />
                    </button>
                    <button className="icon-btn download" title="Download" onClick={d.onDownload} aria-label="Download document">
                      <i className="pi pi-download" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
