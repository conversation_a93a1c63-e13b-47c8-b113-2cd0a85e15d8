import React from 'react';
import { Calendar } from 'primereact/calendar';
import '../../styles/glassy/global.css';

const FromToDateInput = ({ fromDate, toDate, onFromDateChange, onToDateChange, disabled = false }) => {
    return (
        <div className="date-input-container">
            <div>
                <Calendar
                    id="fromDate"
                    value={fromDate}
                    onChange={(e) => onFromDateChange(e.value)}
                    dateFormat="dd/mm/yy"
                    showIcon
                    disabled={disabled}
                    maxDate={toDate || undefined}
                    placeholder="From Date"
                    className="calendar-input"
                    
                    
                />
            </div>
            <div>
                <Calendar
                    id="toDate"
                    value={toDate}
                    onChange={(e) => onToDateChange(e.value)}
                    dateFormat="dd/mm/yy"
                    showIcon
                    disabled={disabled}
                    minDate={fromDate || undefined}
                    placeholder="To Date"
                    className="calendar-input"
                    
                />
            </div>
        </div>
    );
};

export default FromToDateInput;
