import React, { useState, useEffect, useCallback } from "react";
import "@styles/components/alert.css";

const Alert = ({ 
  type = "success", 
  message, 
  show = true, 
  icon = true,
  className = "",
  duration = 5000 // 5 seconds by default
}) => {
  const [isVisible, setIsVisible] = useState(show);
  const [isFading, setIsFading] = useState(false);

  const getIconClass = () => {
    switch (type) {
      case "success": return "pi-check-circle";
      case "error": return "pi-times-circle";
      case "warning": return "pi-exclamation-triangle";
      case "info": return "pi-info-circle";
      default: return "pi-check-circle";
    }
  };

  const hideAlert = useCallback(() => {
    setIsFading(true);
    setTimeout(() => {
      setIsVisible(false);
      setIsFading(false);
    }, 300); // Match this with CSS animation duration
  }, []);

  useEffect(() => {
    setIsVisible(show);
  }, [show]);

  useEffect(() => {
    if (isVisible && duration) {
      const timer = setTimeout(hideAlert, duration);
      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, hideAlert]);

  if (!isVisible || !message) return null;

  return (
    <div 
      className={`custom-alert alert-${type} ${className} ${isFading ? 'alert-fade-out' : ''}`}
      role="alert"
    >
      {/* {icon && (
        <i 
          className={`pi ${getIconClass()} alert-icon`}
        ></i>
      )} */}
      <span className="alert-message">
        {message}
      </span>
      <button 
        className="alert-close" 
        onClick={hideAlert}
        aria-label="Close alert"
      >
        <i className="pi pi-times"></i>
      </button>
    </div>
  );
};

export default Alert;



