import React, { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthContext } from '../../contexts/AuthContext';
import { getDefaultRoute } from '../../Utils/auth/roles';

/**
 * RoleBasedRedirect Component
 * Automatically redirects users to their role-appropriate dashboard
 */
const RoleBasedRedirect = ({ children }) => {
  const { isAuthenticated, roles, isLoading } = useAuthContext();
  const location = useLocation();

  // Show loading state while authentication is being checked
  if (isLoading) {
    return (
      <div className="flex justify-content-center align-items-center" style={{ height: '100vh' }}>
        <i className="pi pi-spinner pi-spin" style={{ fontSize: '2rem' }}></i>
      </div>
    );
  }

  // If user is authenticated and has roles, redirect to appropriate dashboard
  if (isAuthenticated && roles && roles.length > 0) {
    const defaultRoute = getDefaultRoute(roles);
    // const highestRole = getHighestRole(roles);
    
    // Only redirect if we're on the root path or login-related paths
    const shouldRedirect = ['/', '/login', '/test-cms'].includes(location.pathname);
    
    if (shouldRedirect) {
      return <Navigate to={defaultRoute} replace />;
    }
  }

  // If not authenticated or no redirect needed, render children
  return children;
};

/**
 * Dashboard Redirect Component
 * Specifically for dashboard route redirection based on role
 */
export const DashboardRedirect = () => {
  const { isAuthenticated, roles, isLoading } = useAuthContext();

  if (isLoading) {
    return (
      <div className="flex justify-content-center align-items-center" style={{ height: '100vh' }}>
        <i className="pi pi-spinner pi-spin" style={{ fontSize: '2rem' }}></i>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  if (roles && roles.length > 0) {
    const defaultRoute = getDefaultRoute(roles);
    return <Navigate to={defaultRoute} replace />;
  }

  // Fallback to login if no roles
  return <Navigate to="/login" replace />;
};

export default RoleBasedRedirect;
