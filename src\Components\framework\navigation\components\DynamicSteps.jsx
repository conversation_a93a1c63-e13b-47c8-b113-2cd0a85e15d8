import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { Steps } from 'primereact/steps';

const DynamicSteps = ({
  model = [],
  activeIndex = 0,
  readOnly = false,
  onSelect,
  className = '',
  style = {},
  id,
  onEvent,
  debug = false,
  ...props
}) => {
  const handleSelect = useCallback((e) => {
    if (debug) {
      console.log('DynamicSteps: Step selected', e);
    }

    if (onSelect) {
      onSelect(e);
    }

    if (onEvent) {
      onEvent('select', { index: e.index, value: e.value });
    }
  }, [onSelect, onEvent, debug]);

  const stepsProps = {
    model,
    activeIndex,
    readOnly,
    onSelect: handleSelect,
    className: `dynamic-steps ${className}`,
    style,
    id,
    ...props
  };

  if (debug) {
    console.log('DynamicSteps: Rendering with props', stepsProps);
  }

  return (
    <div className="dynamic-steps-wrapper">
      {debug && (
        <div className="dynamic-steps__debug">
          <small>
            Active: {activeIndex} | 
            Steps: {model.length} | 
            ReadOnly: {readOnly ? 'Yes' : 'No'}
          </small>
        </div>
      )}
      <Steps {...stepsProps} />
    </div>
  );
};

DynamicSteps.propTypes = {
  model: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string,
    icon: PropTypes.string,
    command: PropTypes.func,
    disabled: PropTypes.bool
  })),
  activeIndex: PropTypes.number,
  readOnly: PropTypes.bool,
  onSelect: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object,
  id: PropTypes.string,
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicSteps;

