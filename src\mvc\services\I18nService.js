import { createIntl, createIntlCache } from 'react-intl';
import { logger } from '../../core/logging/logger.js';

class I18nService {
  constructor() {
    this.cache = createIntlCache();
    this.currentLocale = this.getUserLocale();
    this.supportedLocales = ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ko', 'ar', 'hi'];
    this.rtlLocales = ['ar', 'he', 'fa', 'ur'];
    this.loadedMessages = new Map();
    this.intl = null;
    this.fallbackLocale = 'en';
    this.listeners = new Set();
    
    this.initializeIntl();
  }

  async initializeIntl() {
    try {
      await this.loadMessages(this.currentLocale);
      this.intl = this.createIntlInstance();
      this.updateDocumentDirection();
      this.updateDocumentLang();
      logger.info('I18n service initialized', { locale: this.currentLocale });
    } catch (error) {
      logger.error('Failed to initialize I18n service', { error: error.message });
      await this.loadMessages(this.fallbackLocale);
      this.currentLocale = this.fallbackLocale;
      this.intl = this.createIntlInstance();
    }
  }

  getUserLocale() {
    const savedLocale = localStorage.getItem('userLocale');
    if (savedLocale && this.supportedLocales.includes(savedLocale)) {
      return savedLocale;
    }

    const urlParams = new URLSearchParams(window.location.search);
    const urlLocale = urlParams.get('lang');
    if (urlLocale && this.supportedLocales.includes(urlLocale)) {
      return urlLocale;
    }

    const browserLocale = navigator.language || navigator.userLanguage;
    const baseLocale = browserLocale.split('-')[0];
    
    return this.supportedLocales.includes(baseLocale) ? baseLocale : this.fallbackLocale;
  }

  async loadMessages(locale) {
    if (this.loadedMessages.has(locale)) {
      return this.loadedMessages.get(locale);
    }

    try {
      const messages = await import(`../../locales/${locale}.json`);
      this.loadedMessages.set(locale, messages.default);
      logger.info(`Loaded messages for locale: ${locale}`);
      return messages.default;
    } catch (error) {
      logger.warn(`Failed to load messages for locale: ${locale}`, { error: error.message });
      
      if (locale !== this.fallbackLocale) {
        const fallbackMessages = await import(`../../locales/${this.fallbackLocale}.json`);
        this.loadedMessages.set(locale, fallbackMessages.default);
        return fallbackMessages.default;
      }
      
      throw error;
    }
  }

  createIntlInstance(locale = this.currentLocale) {
    const messages = this.loadedMessages.get(locale) || this.loadedMessages.get(this.fallbackLocale);
    
    return createIntl({
      locale: locale,
      messages: messages,
      defaultLocale: this.fallbackLocale,
      onError: (error) => {
        logger.warn('Intl formatting error', { error: error.message, locale });
      }
    }, this.cache);
  }

  async changeLocale(locale) {
    if (!this.supportedLocales.includes(locale)) {
      logger.warn(`Unsupported locale: ${locale}`);
      return false;
    }

    try {
      await this.loadMessages(locale);
      
      const oldLocale = this.currentLocale;
      this.currentLocale = locale;
      
      this.intl = this.createIntlInstance(locale);
      
      this.updateDocumentDirection();
      this.updateDocumentLang();
      
      localStorage.setItem('userLocale', locale);
      
      this.notifyListeners(locale, oldLocale);
      
      logger.info(`Locale changed from ${oldLocale} to ${locale}`);
      return true;
    } catch (error) {
      logger.error(`Failed to change locale to ${locale}`, { error: error.message });
      return false;
    }
  }

  updateDocumentDirection() {
    const isRTL = this.isRTL(this.currentLocale);
    document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    document.documentElement.setAttribute('data-direction', isRTL ? 'rtl' : 'ltr');
  }

  updateDocumentLang() {
    document.documentElement.lang = this.currentLocale;
  }

  isRTL(locale = this.currentLocale) {
    return this.rtlLocales.includes(locale);
  }

  getSupportedLocales() {
    return [...this.supportedLocales];
  }

  getLocaleInfo(locale = this.currentLocale) {
    const localeNames = {
      en: 'English',
      es: 'Español',
      fr: 'Français',
      de: 'Deutsch',
      it: 'Italiano',
      pt: 'Português',
      ru: 'Русский',
      zh: '中文',
      ja: '日本語',
      ko: '한국어',
      ar: 'العربية',
      hi: 'हिन्दी'
    };

    return {
      code: locale,
      name: localeNames[locale] || locale,
      isRTL: this.isRTL(locale),
      isLoaded: this.loadedMessages.has(locale)
    };
  }

  addListener(callback) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  notifyListeners(newLocale, oldLocale) {
    this.listeners.forEach(callback => {
      try {
        callback(newLocale, oldLocale);
      } catch (error) {
        logger.error('Error in locale change listener', { error: error.message });
      }
    });
  }

  t(key, values = {}) {
    if (!this.intl) {
      logger.warn('Intl not initialized, returning key', { key });
      return key;
    }

    try {
      return this.intl.formatMessage({ id: key }, values);
    } catch (error) {
      logger.warn('Translation key not found', { key, locale: this.currentLocale });
      return key;
    }
  }

  formatDate(date, options = {}) {
    if (!this.intl) return date;
    
    return this.intl.formatDate(date, {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      ...options
    });
  }

  formatTime(time, options = {}) {
    if (!this.intl) return time;
    
    return this.intl.formatTime(time, {
      hour: '2-digit',
      minute: '2-digit',
      ...options
    });
  }

  formatDateTime(datetime, options = {}) {
    if (!this.intl) return datetime;
    
    return this.intl.formatDate(datetime, {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
      ...options
    });
  }

  formatNumber(number, options = {}) {
    if (!this.intl) return number;
    
    return this.intl.formatNumber(number, {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
      ...options
    });
  }

  formatCurrency(amount, currency = 'USD', options = {}) {
    if (!this.intl) return amount;
    
    return this.intl.formatNumber(amount, {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
      ...options
    });
  }

  formatPercent(value, options = {}) {
    if (!this.intl) return value;
    
    return this.intl.formatNumber(value, {
      style: 'percent',
      minimumFractionDigits: 0,
      maximumFractionDigits: 2,
      ...options
    });
  }

  formatRelativeTime(value, unit, options = {}) {
    if (!this.intl) return value;
    
    return this.intl.formatRelativeTime(value, unit, {
      numeric: 'auto',
      ...options
    });
  }

  formatList(list, options = {}) {
    if (!this.intl) return list.join(', ');
    
    return this.intl.formatList(list, {
      type: 'conjunction',
      ...options
    });
  }

  formatPlural(count, options) {
    if (!this.intl) return count;
    
    return this.intl.formatPlural(count, options);
  }

  getCurrentLocale() {
    return this.currentLocale;
  }

  isLocaleSupported(locale) {
    return this.supportedLocales.includes(locale);
  }

  getDirection() {
    return this.isRTL() ? 'rtl' : 'ltr';
  }

  async preloadLocale(locale) {
    if (!this.supportedLocales.includes(locale)) {
      return false;
    }

    try {
      await this.loadMessages(locale);
      return true;
    } catch (error) {
      logger.error(`Failed to preload locale: ${locale}`, { error: error.message });
      return false;
    }
  }

  getBrowserLocales() {
    return navigator.languages || [navigator.language || navigator.userLanguage];
  }

  getBestMatchingLocale() {
    const browserLocales = this.getBrowserLocales();
    
    for (const browserLocale of browserLocales) {
      const baseLocale = browserLocale.split('-')[0];
      if (this.supportedLocales.includes(baseLocale)) {
        return baseLocale;
      }
    }
    
    return this.fallbackLocale;
  }

  getStats() {
    return {
      currentLocale: this.currentLocale,
      supportedLocales: this.supportedLocales.length,
      loadedLocales: this.loadedMessages.size,
      isRTL: this.isRTL(),
      listeners: this.listeners.size
    };
  }
}

const i18nService = new I18nService();

export default i18nService;
