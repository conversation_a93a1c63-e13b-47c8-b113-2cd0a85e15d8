import BaseController from '../BaseController.js';
import LeadDocumentService from '../../services/leadmanagement/LeadDocument.js';
import LeadDocumentModel from '../../models/leadmanagement/LeadDocumentModel.js';

class LeadDocumentController extends BaseController {
  constructor() {
    super();
    this.state = {
      documents: [],
      loading: false,
      error: null
    };
  }

  async fetchDocuments() {
    return this.executeAction('fetchDocuments', async () => {
      const data = await LeadDocumentService.getAll();
      const documents = Array.isArray(data) ? data.map(d => new LeadDocumentModel(d)) : [];
      this.setState({ documents });
      return documents;
    });
  }

  async saveDocument(docData) {
    return this.executeAction('saveDocument', async () => {
      const saved = await LeadDocumentService.save(docData);
      await this.fetchDocuments();
      return saved;
    });
  }

  async updateDocument(id, docData) {
    return this.executeAction('updateDocument', async () => {
      const updated = await LeadDocumentService.update(id, docData);
      await this.fetchDocuments();
      return updated;
    });
  }

  async deleteDocument(id) {
    return this.executeAction('deleteDocument', async () => {
      await LeadDocumentService.deleteById(id);
      await this.fetchDocuments();
    });
  }
}

export default new LeadDocumentController();
