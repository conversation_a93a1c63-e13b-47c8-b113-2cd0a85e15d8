

class BrowserSupport {
  static isBrowserSupported() {
    const hasES6 = typeof Symbol !== 'undefined';
    const hasPromise = typeof Promise !== 'undefined';
    const hasFetch = typeof fetch !== 'undefined';
    const hasLocalStorage = typeof localStorage !== 'undefined';
    
    return hasES6 && hasPromise && hasFetch && hasLocalStorage;
  }

  static getBrowserInfo() {
    const userAgent = navigator.userAgent;
    let browserName = 'Unknown';
    let browserVersion = 'Unknown';

    if (userAgent.indexOf('Chrome') > -1) {
      browserName = 'Chrome';
      browserVersion = userAgent.match(/Chrome\/(\d+)/)?.[1] || 'Unknown';
    }
    else if (userAgent.indexOf('Firefox') > -1) {
      browserName = 'Firefox';
      browserVersion = userAgent.match(/Firefox\/(\d+)/)?.[1] || 'Unknown';
    }
    else if (userAgent.indexOf('Safari') > -1) {
      browserName = 'Safari';
      browserVersion = userAgent.match(/Version\/(\d+)/)?.[1] || 'Unknown';
    }
    else if (userAgent.indexOf('Edge') > -1) {
      browserName = 'Edge';
      browserVersion = userAgent.match(/Edge\/(\d+)/)?.[1] || 'Unknown';
    }

    return { browserName, browserVersion };
  }

  static applyPolyfills() {
    if (!window.Promise) {
      console.warn('Promise polyfill needed');
    }
    
    if (!window.fetch) {
      console.warn('Fetch polyfill needed');
    }

    return true;
  }

  static checkRequiredFeatures() {
    const features = {
      es6: typeof Symbol !== 'undefined',
      promise: typeof Promise !== 'undefined',
      fetch: typeof fetch !== 'undefined',
      localStorage: typeof localStorage !== 'undefined',
      sessionStorage: typeof sessionStorage !== 'undefined',
      webWorkers: typeof Worker !== 'undefined',
      serviceWorkers: 'serviceWorker' in navigator,
      webGL: !!window.WebGLRenderingContext,
      canvas: !!document.createElement('canvas').getContext,
      history: !!(window.history && window.history.pushState)
    };

    return features;
  }

  static getUnsupportedFeatures() {
    const features = this.checkRequiredFeatures();
    return Object.keys(features).filter(key => !features[key]);
  }

  static displayBrowserWarning() {
    if (!this.isBrowserSupported()) {
      console.warn('Browser may not be fully supported. Please update to the latest version.');
      
      const unsupported = this.getUnsupportedFeatures();
      if (unsupported.length > 0) {
        console.warn('Missing features:', unsupported.join(', '));
      }
    }
  }
}

export default BrowserSupport;
