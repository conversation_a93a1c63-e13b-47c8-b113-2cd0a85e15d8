const securityHeaders = {
  'Content-Security-Policy': `
    default-src 'self';
    script-src 'self' 'unsafe-eval' 'unsafe-inline' https://apis.google.com https://www.googletagmanager.com;
    style-src 'self' 'unsafe-inline' https://fonts.googleapis.com;
    img-src 'self' data: https:;
    font-src 'self' https://fonts.gstatic.com;
    connect-src 'self' https://api.example.com https://sentry.io;
    frame-src 'self' https://www.youtube.com;
    media-src 'self';
    object-src 'none';
    base-uri 'self';
    form-action 'self';
    frame-ancestors 'none';
    upgrade-insecure-requests;
  `.replace(/\s+/g, ' ').trim(),

  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',

  'X-Content-Type-Options': 'nosniff',

  'X-Frame-Options': 'DENY',

  'X-XSS-Protection': '1; mode=block',

  'Referrer-Policy': 'strict-origin-when-cross-origin',

  'Permissions-Policy': [
    'camera=()',
    'microphone=()',
    'geolocation=()',
    'payment=()',
    'usb=()',
    'magnetometer=()',
    'accelerometer=()',
    'gyroscope=()',
    'fullscreen=(self)',
    'picture-in-picture=()'
  ].join(', '),

  'Cross-Origin-Embedder-Policy': 'require-corp',

  'Cross-Origin-Opener-Policy': 'same-origin',

  'Cross-Origin-Resource-Policy': 'same-origin'
};

const environmentHeaders = {
  development: {
    ...securityHeaders,
    'Content-Security-Policy': `
      default-src 'self';
      script-src 'self' 'unsafe-eval' 'unsafe-inline';
      style-src 'self' 'unsafe-inline';
      img-src 'self' data: https:;
      font-src 'self' https:;
      connect-src 'self' ws: wss:;
      frame-src 'self';
      media-src 'self';
      object-src 'none';
      base-uri 'self';
      form-action 'self';
    `.replace(/\s+/g, ' ').trim()
  },

  production: {
    ...securityHeaders
  },

  testing: {
    ...securityHeaders,
    'Strict-Transport-Security': undefined
  }
};

function validateHeaders(headers) {
  const requiredHeaders = [
    'Content-Security-Policy',
    'X-Content-Type-Options',
    'X-Frame-Options',
    'X-XSS-Protection'
  ];

  const missingHeaders = requiredHeaders.filter(header => !headers[header]);

  if (missingHeaders.length > 0) {
    console.warn('Missing required security headers:', missingHeaders);
    return false;
  }

  return true;
}

const cspReporting = {
  'report-uri': '/api/csp-report',
  'report-to': 'csp-endpoint'
};

const reportTo = {
  'Report-To': JSON.stringify({
    group: 'csp-endpoint',
    max_age: 10886400,
    endpoints: [
      { url: '/api/csp-report' }
    ]
  })
};

const expressMiddleware = {
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'", "'unsafe-eval'", "'unsafe-inline'", "https://apis.google.com"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      imgSrc: ["'self'", "data:", "https:"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      connectSrc: ["'self'", "https://api.example.com"],
      frameSrc: ["'self'"],
      objectSrc: ["'none'"],
      baseUri: ["'self'"],
      formAction: ["'self'"],
      frameAncestors: ["'none'"],
      upgradeInsecureRequests: []
    },
    reportOnly: false
  },

  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  },

  noSniff: true,
  frameguard: { action: 'deny' },
  xssFilter: true,
  referrerPolicy: { policy: 'strict-origin-when-cross-origin' }
};

const nginxConfig = `
add_header Content-Security-Policy "${securityHeaders['Content-Security-Policy']}" always;
add_header Strict-Transport-Security "${securityHeaders['Strict-Transport-Security']}" always;
add_header X-Content-Type-Options "${securityHeaders['X-Content-Type-Options']}" always;
add_header X-Frame-Options "${securityHeaders['X-Frame-Options']}" always;
add_header X-XSS-Protection "${securityHeaders['X-XSS-Protection']}" always;
add_header Referrer-Policy "${securityHeaders['Referrer-Policy']}" always;
add_header Permissions-Policy "${securityHeaders['Permissions-Policy']}" always;
add_header Cross-Origin-Embedder-Policy "${securityHeaders['Cross-Origin-Embedder-Policy']}" always;
add_header Cross-Origin-Opener-Policy "${securityHeaders['Cross-Origin-Opener-Policy']}" always;
add_header Cross-Origin-Resource-Policy "${securityHeaders['Cross-Origin-Resource-Policy']}" always;
server_tokens off;
more_clear_headers Server;
`;

const apacheConfig = `
Header always set Content-Security-Policy "${securityHeaders['Content-Security-Policy']}"
Header always set Strict-Transport-Security "${securityHeaders['Strict-Transport-Security']}"
Header always set X-Content-Type-Options "${securityHeaders['X-Content-Type-Options']}"
Header always set X-Frame-Options "${securityHeaders['X-Frame-Options']}"
Header always set X-XSS-Protection "${securityHeaders['X-XSS-Protection']}"
Header always set Referrer-Policy "${securityHeaders['Referrer-Policy']}"
Header always set Permissions-Policy "${securityHeaders['Permissions-Policy']}"
Header always set Cross-Origin-Embedder-Policy "${securityHeaders['Cross-Origin-Embedder-Policy']}"
Header always set Cross-Origin-Opener-Policy "${securityHeaders['Cross-Origin-Opener-Policy']}"
Header always set Cross-Origin-Resource-Policy "${securityHeaders['Cross-Origin-Resource-Policy']}"
ServerTokens Prod
ServerSignature Off
`;

module.exports = {
  securityHeaders,
  environmentHeaders,
  validateHeaders,
  cspReporting,
  reportTo,
  expressMiddleware,
  nginxConfig,
  apacheConfig
};
