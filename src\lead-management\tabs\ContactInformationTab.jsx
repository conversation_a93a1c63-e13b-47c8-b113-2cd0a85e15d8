import React, { useState, useCallback, useMemo } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { <PERSON><PERSON> } from 'primereact/button';
import { Card } from 'primereact/card';
import { Tag } from 'primereact/tag';
import { Avatar } from 'primereact/avatar';

const ContactInformationTab = ({ leadId }) => {
  const [selectedContacts, setSelectedContacts] = useState([]);

  const contacts = useMemo(() => [
    {
      id: 1,
      name: '<PERSON>',
      jobTitle: 'CEO',
      email: '<EMAIL>',
      phone: '+****************',
      mobile: '+****************',
      type: 'Primary',
      status: 'Active'
    },
    {
      id: 2,
      name: '<PERSON>',
      jobTitle: 'CFO',
      email: '<EMAIL>',
      phone: '+****************',
      mobile: '+****************',
      type: 'Secondary',
      status: 'Active'
    },
    {
      id: 3,
      name: '<PERSON>',
      jobTitle: 'VP Operations',
      email: 'micha<PERSON>.<EMAIL>',
      phone: '+****************',
      mobile: '+****************',
      type: 'Secondary',
      status: 'Active'
    },
    {
      id: 4,
      name: 'Emily Davis',
      jobTitle: 'Procurement Manager',
      email: '<EMAIL>',
      phone: '+****************',
      mobile: '+****************',
      type: 'Secondary',
      status: 'Inactive'
    }
  ], []);

  const nameTemplate = useCallback((rowData) => {
    return (
      <div className="contact-name-cell flex align-items-center gap-2">
        <Avatar
          label={rowData.name.split(' ').map(n => n[0]).join('')}
          size="normal"
          shape="circle"
          style={{ backgroundColor: '#2196F3', color: '#ffffff' }}
        />
        <div>
          <div className="contact-name font-bold">{rowData.name}</div>
          <div className="contact-title text-sm text-secondary">{rowData.jobTitle}</div>
        </div>
      </div>
    );
  }, []);

  const contactTemplate = useCallback((rowData) => {
    return (
      <div className="contact-info text-sm">
        <div><i className="pi pi-envelope mr-1"></i>{rowData.email}</div>
        <div><i className="pi pi-phone mr-1"></i>{rowData.phone}</div>
        <div><i className="pi pi-mobile mr-1"></i>{rowData.mobile}</div>
      </div>
    );
  }, []);

  const typeTemplate = useCallback((rowData) => {
    const severity = rowData.type === 'Primary' ? 'success' : 'info';
    return <Tag value={rowData.type} severity={severity} />;
  }, []);

  const statusTemplate = useCallback((rowData) => {
    const severity = rowData.status === 'Active' ? 'success' : 'warning';
    return <Tag value={rowData.status} severity={severity} />;
  }, []);

  const actionTemplate = useCallback(() => {
    return (
      <div className="flex gap-2">
        <Button icon="pi pi-pencil" className="p-button-rounded p-button-text p-button-sm" />
        <Button icon="pi pi-envelope" className="p-button-rounded p-button-text p-button-sm" />
        <Button icon="pi pi-phone" className="p-button-rounded p-button-text p-button-sm" />
        <Button icon="pi pi-trash" className="p-button-rounded p-button-text p-button-danger p-button-sm" />
      </div>
    );
  }, []);

  const header = (
    <div className="table-header flex justify-content-between align-items-center">
      <h3 className="m-0">Contact Information</h3>
      <Button
        label="Add Contact"
        icon="pi pi-plus"
        className="p-button-sm p-button-success"
      />
    </div>
  );

  return (
    <Card className="contact-information-tab">
      <DataTable
        value={contacts}
        selection={selectedContacts}
        onSelectionChange={(e) => setSelectedContacts(e.value)}
        dataKey="id"
        header={header}
        emptyMessage="No contacts found"
        className="contacts-datatable"
        scrollable
        scrollHeight="400px"
        virtualScrollerOptions={{ itemSize: 60 }}
      >
        <Column selectionMode="multiple" headerStyle={{ width: '3rem' }} />
        <Column field="name" header="Name" body={nameTemplate} sortable />
        <Column field="contact" header="Contact Details" body={contactTemplate} />
        <Column field="type" header="Type" body={typeTemplate} sortable />
        <Column field="status" header="Status" body={statusTemplate} sortable />
        <Column body={actionTemplate} headerStyle={{ width: '12rem' }} />
      </DataTable>
    </Card>
  );
};

export default ContactInformationTab;
