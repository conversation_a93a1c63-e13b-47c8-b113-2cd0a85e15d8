import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { InputText } from 'primereact/inputtext';

const PasswordInput = ({
  value,
  onChange,
  onValidationChange,
  username = "",
  disabled = false,
  required = true,
  placeholder = "Enter your password",
  className = ""
}) => {
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const validatePassword = (password) => {
    // Reset error
    setError('');

    // Check for empty value if required
    if (required && (!password || !password.trim())) {
      setError('Please enter a valid Password');
      return false;
    }

    // Check length (10-20 characters)
    if (password.length < 10) {
      setError('Password must be at least 10 characters long');
      return false;
    }
    if (password.length > 20) {
      setError('Password must not exceed 20 characters');
      return false;
    }

    // Check for spaces
    if (password.includes(' ')) {
      setError('Password must not contain spaces');
      return false;
    }

    // Check for emojis
    const emojiRegex = /[\u{1F300}-\u{1F9FF}]/u;
    if (emojiRegex.test(password)) {
      setError('Password must not contain emojis');
      return false;
    }

    // Check if password contains username (if username is more than 4 characters)
    if (username && username.length > 4) {
      const usernameLower = username.toLowerCase();
      const passwordLower = password.toLowerCase();

      // Check for full username
      if (passwordLower.includes(usernameLower)) {
        setError('Password must not contain your username');
        return false;
      }

      // Check for username parts (4+ characters)
      for (let i = 0; i <= usernameLower.length - 4; i++) {
        const usernamePart = usernameLower.substring(i, i + 4);
        if (passwordLower.includes(usernamePart)) {
          setError('Password must not contain parts of your username (4+ characters)');
          return false;
        }
      }
    }

    // Additional password requirements
    const hasUpperCase = /[A-Z]/.test(password);
    const hasLowerCase = /[a-z]/.test(password);
    const hasNumbers = /\d/.test(password);
    const hasSpecialChar = /[!@#$%^&*]/.test(password);

    if (!hasUpperCase) {
      setError('Password must contain at least one uppercase letter');
      return false;
    }
    if (!hasLowerCase) {
      setError('Password must contain at least one lowercase letter');
      return false;
    }
    if (!hasNumbers) {
      setError('Password must contain at least one number');
      return false;
    }
    if (!hasSpecialChar) {
      setError('Password must contain at least one special character (!@#$%^&*)');
      return false;
    }

    return true;
  };

  const handleChange = (e) => {
    const rawValue = e?.target?.value || '';
    // Remove all whitespace characters to prevent spaces in password
    const newValue = rawValue.replace(/\s/g, '');
    const isValid = validatePassword(newValue);
    // Call onChange with an event-like object so parent handlers get the sanitized value
    if (onChange) onChange({ target: { value: newValue } });
    if (onValidationChange) {
      onValidationChange(isValid);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="password-input-container">
      <div className="input-wrapper">
        <InputText
          type={showPassword ? "text" : "password"}
          value={value}
          onChange={handleChange}
          className={`input-with-icon ${className} ${error ? 'p-invalid' : ''}`}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
        />
        <i
          className={`pi ${showPassword ? "pi-eye-slash" : "pi-eye"} input-icon input-icon-clickable`}
          onClick={togglePasswordVisibility}
        />
      </div>
      {error && (
        <small className="p-error block mt-1">
          {error}
        </small>
      )}
    </div>
  );
};

PasswordInput.propTypes = {
  value: PropTypes.string.isRequired,
  onChange: PropTypes.func.isRequired,
  onValidationChange: PropTypes.func,
  username: PropTypes.string,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  placeholder: PropTypes.string,
  className: PropTypes.string
};

export default PasswordInput;
