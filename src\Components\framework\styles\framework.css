
:root {
  --framework-primary: #3B82F6;
  --framework-secondary: #6B7280;
  --framework-success: #10B981;
  --framework-info: #3B82F6;
  --framework-warning: #F59E0B;
  --framework-danger: #EF4444;
  --framework-surface: #FFFFFF;
  --framework-text: #1F2937;
  --framework-border: #E5E7EB;
  --framework-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);

  --framework-spacing-xs: 0.25rem;
  --framework-spacing-sm: 0.5rem;
  --framework-spacing-md: 1rem;
  --framework-spacing-lg: 1.5rem;
  --framework-spacing-xl: 2rem;

  --framework-border-radius: 6px;
  --framework-border-radius-sm: 4px;
  --framework-border-radius-lg: 8px;

  --framework-transition: all 0.2s ease-in-out;
}

.theme-dark {
  --framework-surface: #1F2937;
  --framework-text: #F9FAFB;
  --framework-border: #374151;
  --framework-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3);
}

body {
  background: url('/src/assets/airoplane-1.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  min-height: 100vh;
}

.rtl {
  direction: rtl;
}

.rtl .pi {
  transform: scaleX(-1);
}

.rtl .pi-chevron-left::before {
  content: "\e900";
}

.rtl .pi-chevron-right::before {
  content: "\e901";
}

.dynamic-component {
  position: relative;
}

.dynamic-component--loading {
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.dynamic-component__loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--text-color-secondary);
}

.dynamic-component--error {
  border: 1px solid var(--red-500);
  border-radius: 6px;
  padding: 1rem;
  background: var(--red-50);
}

.dynamic-component__error {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--red-700);
}

.dynamic-component__error h4 {
  margin: 0;
  color: var(--red-800);
}

.dynamic-component--not-found {
  border: 1px solid var(--orange-500);
  border-radius: 6px;
  padding: 1rem;
  background: var(--orange-50);
}

.dynamic-component__not-found {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  color: var(--orange-700);
}

.dynamic-component__debug {
  position: absolute;
  top: -20px;
  right: 0;
  background: var(--blue-100);
  color: var(--blue-800);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 10px;
  z-index: 1000;
}

.dynamic-form {
  width: 100%;
}

.dynamic-form--error {
  border: 1px solid var(--red-500);
  border-radius: 6px;
  padding: 1rem;
  background: var(--red-50);
}

.form-config-error {
  margin-bottom: 1rem;
}

.form-config-errors {
  list-style: none;
  padding: 0;
  margin: 0;
}

.form-config-errors li {
  color: var(--red-700);
  font-size: 0.875rem;
  margin-bottom: 0.25rem;
}

.form-progress {
  margin-bottom: 1.5rem;
}

.form-progress__bar {
  margin-bottom: 0.5rem;
}

.form-progress__text {
  color: var(--text-color-secondary);
  font-weight: 500;
}

.form-fields {
  display: grid;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.form-fields--1-column {
  grid-template-columns: 1fr;
}

.form-fields--2-column {
  grid-template-columns: repeat(2, 1fr);
}

.form-fields--3-column {
  grid-template-columns: repeat(3, 1fr);
}

.form-fields--4-column {
  grid-template-columns: repeat(4, 1fr);
}

@media (max-width: 768px) {
  .form-fields--2-column,
  .form-fields--3-column,
  .form-fields--4-column {
    grid-template-columns: 1fr;
  }
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 600;
  color: var(--text-color);
  font-size: 0.875rem;
}

.form-label--required {
  color: var(--text-color);
}

.form-label__required {
  color: var(--red-500);
  margin-left: 0.25rem;
}

.form-field-wrapper {
  position: relative;
}

.form-field {
  width: 100%;
}

.form-field--error {
  border-color: var(--red-500) !important;
}

.form-error {
  margin-top: 0.25rem;
}

.form-error__message {
  margin-bottom: 0.25rem;
}

.form-help {
  margin-top: 0.25rem;
}

.form-help small {
  color: var(--text-color-secondary);
  font-size: 0.75rem;
}

.form-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
  padding-top: 1rem;
  border-top: 1px solid var(--surface-border);
}

.form-submit-button {
  min-width: 120px;
}

.dynamic-input-text-wrapper {
  position: relative;
}

.dynamic-input-text__debug {
  position: absolute;
  top: -18px;
  right: 0;
  background: var(--blue-100);
  color: var(--blue-800);
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
  z-index: 1000;
}

.dynamic-input-text--small .p-inputtext {
  padding: 0.375rem 0.5rem;
  font-size: 0.875rem;
}

.dynamic-input-text--large .p-inputtext {
  padding: 0.75rem 1rem;
  font-size: 1.125rem;
}

.dynamic-dropdown-wrapper {
  position: relative;
}

.dynamic-dropdown__debug {
  position: absolute;
  top: -18px;
  right: 0;
  background: var(--blue-100);
  color: var(--blue-800);
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
  z-index: 1000;
}

.dropdown-item {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.dropdown-item__description {
  color: var(--text-color-secondary);
  font-size: 0.75rem;
}

.dropdown-value {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.dropdown-placeholder {
  color: var(--text-color-secondary);
}

.dynamic-datatable-wrapper {
  position: relative;
}

.dynamic-datatable__debug {
  position: absolute;
  top: -18px;
  right: 0;
  background: var(--blue-100);
  color: var(--blue-800);
  padding: 1px 4px;
  border-radius: 2px;
  font-size: 9px;
  z-index: 1000;
}

.datatable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background: var(--surface-card);
  border: 1px solid var(--surface-border);
  border-radius: 6px;
}

.datatable-global-filter {
  flex: 1;
  max-width: 300px;
}

.datatable-actions {
  display: flex;
  gap: 0.5rem;
}

.table-actions {
  display: flex;
  gap: 0.25rem;
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
}

.status-active {
  background: var(--green-100);
  color: var(--green-800);
}

.status-inactive {
  background: var(--red-100);
  color: var(--red-800);
}

.status-pending {
  background: var(--orange-100);
  color: var(--orange-800);
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

.app-content {

 flex: 1;
  padding: 0;
}

.demo-app {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-header {
  text-align: center;
  margin-bottom: 2rem;
}

.demo-header h1 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.demo-header p {
  color: var(--text-color-secondary);
  font-size: 1.125rem;
  max-width: 600px;
  margin: 0 auto 1.5rem;
}

.demo-stats {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-bottom: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: var(--primary-color);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-color-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.demo-card {
  margin-bottom: 1rem;
}

.demo-description {
  color: var(--text-color-secondary);
  margin-bottom: 1.5rem;
  font-style: italic;
}

.registry-info {
  margin-top: 1rem;
}

.component-list {
  display: grid;
  gap: 1rem;
  margin-top: 1rem;
}

.component-item {
  border: 1px solid var(--surface-border);
  border-radius: 6px;
  padding: 1rem;
  background: var(--surface-card);
}

.component-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.component-category {
  background: var(--primary-100);
  color: var(--primary-800);
  padding: 0.125rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
  text-transform: uppercase;
  margin-left: auto;
}

.component-description {
  color: var(--text-color-secondary);
  margin: 0.5rem 0;
  font-size: 0.875rem;
}

.component-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.tag {
  background: var(--surface-100);
  color: var(--text-color-secondary);
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  font-size: 0.75rem;
}

.config-examples {
  margin-top: 1rem;
}

.config-examples h3 {
  margin-top: 2rem;
  margin-bottom: 1rem;
  color: var(--primary-color);
}

.config-code {
  background: var(--surface-100);
  border: 1px solid var(--surface-border);
  border-radius: 6px;
  padding: 1rem;
  font-family: 'Courier New', monospace;
  font-size: 0.875rem;
  overflow-x: auto;
  white-space: pre-wrap;
  color: var(--text-color);
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.settings-card {
  height: fit-content;
}

.setting-info {
  margin-bottom: 1rem;
  padding: 1rem;
  background: var(--surface-ground);
  border-radius: var(--framework-border-radius);
  border: 1px solid var(--surface-border);
}

.setting-info p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
}

.setting-info strong {
  color: var(--text-color);
}

.theme-selectors,
.language-selectors {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.theme-selectors h4,
.language-selectors h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: var(--text-color);
  border-bottom: 1px solid var(--surface-border);
  padding-bottom: 0.25rem;
}

.localized-demo {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.localized-demo ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.localized-demo li {
  padding: 0.25rem 0;
  color: var(--text-color-secondary);
}

.component-showcase {
  display: flex;
  flex-direction: column;
  gap: 2rem;
  margin-top: 2rem;
}

.dynamic-navbar {
  background: var(--surface-card);
  border-bottom: 1px solid var(--surface-border);
  box-shadow: var(--framework-shadow);
  transition: var(--framework-transition);
}

.dynamic-navbar--fixed {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
}

.dynamic-navbar--sticky {
  position: sticky;
  top: 0;
  z-index: 1000;
}

.dynamic-navbar--compact .navbar-menubar {
  padding: 0.5rem 1rem;
}

.dynamic-navbar--minimal {
  border-bottom: none;
  box-shadow: none;
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: var(--framework-spacing-sm);
  cursor: pointer;
  padding: var(--framework-spacing-sm);
  border-radius: var(--framework-border-radius);
  transition: var(--framework-transition);
}

.navbar-brand:hover {
  background: var(--surface-hover);
}

.navbar-brand__icon {
  font-size: 1.5rem;
  color: var(--primary-color);
}

.navbar-brand__text {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color);
}

.navbar-end {
  display: flex;
  align-items: center;
  gap: var(--framework-spacing-sm);
}

.navbar-controls {
  display: flex;
  align-items: center;
  gap: var(--framework-spacing-sm);
}

.navbar-control {
  display: flex;
  align-items: center;
}

.navbar-desktop-controls {
  display: flex;
  align-items: center;
  gap: var(--framework-spacing-sm);
}

.navbar-mobile-controls {
  display: none;
}

/* Theme Selector Styles */
.theme-selector {
  display: flex;
  align-items: center;
  gap: var(--framework-spacing-sm);
}

.theme-selector__label {
  display: flex;
  align-items: center;
  gap: var(--framework-spacing-xs);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color-secondary);
}

.theme-selector__controls {
  display: flex;
  align-items: center;
  gap: var(--framework-spacing-xs);
}

.theme-dropdown {
  min-width: 150px;
}

.theme-option {
  display: flex;
  align-items: center;
  gap: var(--framework-spacing-sm);
  padding: var(--framework-spacing-xs);
}

.theme-color-preview {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 1px solid var(--surface-border);
}

.theme-toggle-btn,
.custom-theme-btn {
  width: 2.5rem;
  height: 2.5rem;
}

/* Language Selector Styles */
.language-selector {
  display: flex;
  align-items: center;
  gap: var(--framework-spacing-sm);
}

.language-selector__label {
  display: flex;
  align-items: center;
  gap: var(--framework-spacing-xs);
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text-color-secondary);
}

.language-dropdown {
  min-width: 140px;
}

.language-option {
  display: flex;
  align-items: center;
  gap: var(--framework-spacing-sm);
  padding: var(--framework-spacing-xs);
}

.language-flag {
  font-size: 1.2rem;
}

.language-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.language-name {
  font-weight: 500;
}

.language-native {
  font-size: 0.75rem;
  color: var(--text-color-secondary);
}

.language-rtl-indicator {
  color: var(--text-color-secondary);
  font-size: 0.75rem;
}

/* User Menu Styles */
.navbar-user__trigger {
  display: flex;
  align-items: center;
  gap: var(--framework-spacing-sm);
  padding: var(--framework-spacing-sm);
}

.navbar-user__name {
  font-size: 0.875rem;
  font-weight: 500;
}

.navbar-user-sidebar .p-sidebar-content {
  padding: 0;
}

.user-menu {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.user-menu__header {
  display: flex;
  align-items: center;
  gap: var(--framework-spacing-md);
  padding: var(--framework-spacing-lg);
  background: var(--surface-ground);
}

.user-info h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
}

.user-info p {
  margin: var(--framework-spacing-xs) 0 0 0;
  font-size: 0.875rem;
  color: var(--text-color-secondary);
}

.user-info small {
  color: var(--text-color-secondary);
}

.user-menu__actions {
  flex: 1;
  padding: var(--framework-spacing-md);
}

.user-menu-item {
  width: 100%;
  justify-content: flex-start;
  margin-bottom: var(--framework-spacing-xs);
}

/* Notifications */
.navbar-notifications__trigger {
  position: relative;
  width: 2.5rem;
  height: 2.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .demo-app {
    padding: 1rem;
  }

  .demo-stats {
    flex-direction: column;
    gap: 1rem;
  }

  .datatable-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .datatable-global-filter {
    max-width: none;
  }

  .navbar-desktop-controls {
    display: none;
  }

  .navbar-mobile-controls {
    display: flex;
  }

  .navbar-mobile-menu-btn {
    width: 2.5rem;
    height: 2.5rem;
  }

  .navbar-mobile-sidebar .p-sidebar-content {
    padding: 0;
  }

  .mobile-menu {
    height: 100%;
    display: flex;
    flex-direction: column;
  }

  .mobile-menu__header {
    padding: var(--framework-spacing-lg);
    background: var(--surface-ground);
  }

  .mobile-menu__items {
    flex: 1;
    padding: var(--framework-spacing-md);
  }

  .mobile-menu-item {
    margin-bottom: var(--framework-spacing-sm);
  }

  .mobile-menu-item__button {
    width: 100%;
    justify-content: flex-start;
  }

  .mobile-submenu {
    margin-left: var(--framework-spacing-lg);
    margin-top: var(--framework-spacing-xs);
  }

  .mobile-submenu-item {
    width: 100%;
    justify-content: flex-start;
    margin-bottom: var(--framework-spacing-xs);
  }

  .mobile-menu__controls {
    padding: var(--framework-spacing-md);
    border-top: 1px solid var(--surface-border);
  }

  .mobile-language-selector,
  .mobile-theme-selector {
    margin-bottom: var(--framework-spacing-md);
  }
}
