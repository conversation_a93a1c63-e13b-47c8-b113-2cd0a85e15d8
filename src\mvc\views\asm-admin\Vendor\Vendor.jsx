import React, { useState, useEffect } from "react";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { Checkbox } from "primereact/checkbox";
import { useNavigate } from "react-router-dom";
import SearchInput from "@components/Common/Input/SearchInput";
import FilterBy from "@components/Common/FilterBy";
import GlassyBlueButton from "@components/Common/Buttons/GlassyBlueButton";
import GlassyWhiteButton from "@components/Common/Buttons/GlassyWhiteButton";
import Paginator from "@components/Common/Paginator/Paginator";
import ActionButton from "@components/Common/Buttons/ActionButton";
import GlassBadge from "@components/Common/Badge/GlassBadge";
import vendorController from "@controllers/vendor/vendorController";
import { exportTableToCSV } from "@utils/csvExport";

function Vendor() {
  const navigate = useNavigate();
  const [searchValue, setSearchValue] = useState("");
  const [dateRange, setDateRange] = useState({ from: null, to: null });
  const [selectedVendors, setSelectedVendors] = useState([]);
  const [paginatedVendors, setPaginatedVendors] = useState([]);
  const itemsPerPage = 10;
  const [selectedFilter, setSelectedFilter] = useState(null);
  const [vendorData, setVendorData] = useState([]);
  const [loading, setLoading] = useState(false);

  const filterOptions = [
    { label: "All Vendors", value: "all" },
    { label: "On Hold - Yes", value: "on_hold_yes" },
    { label: "On Hold - No", value: "on_hold_no" },
    { label: "Recent", value: "recent" },
  ];

  // Fetch vendor details from API using controller
  const fetchVendorDetails = async () => {
    setLoading(true);
    try {
      const result = await vendorController.getVendorDetails();
      if (result.success) {
        setVendorData(result.data);
      } else {
        console.error("Error fetching vendor details:", result.error);
        setVendorData([]);
      }
    } catch (error) {
      console.error("Error fetching vendor details:", error);
      setVendorData([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle search functionality
  const handleSearch = async (searchTerm) => {
    if (!searchTerm || searchTerm.trim() === "") {
      fetchVendorDetails();
      return;
    }

    setLoading(true);
    try {
      const result = await vendorController.searchVendors(searchTerm);
      if (result.success) {
        setVendorData(result.data);
      } else {
        console.error("Error searching vendors:", result.error);
        setVendorData([]);
      }
    } catch (error) {
      console.error("Error searching vendors:", error);
      setVendorData([]);
    } finally {
      setLoading(false);
    }
  };

  // Handle filter functionality
  const handleFilter = async (filterValue) => {
    setLoading(true);
    try {
      const result = await vendorController.filterVendorsByHoldStatus(filterValue);
      if (result.success) {
        setVendorData(result.data);
      } else {
        console.error("Error filtering vendors:", result.error);
        setVendorData([]);
      }
    } catch (error) {
      console.error("Error filtering vendors:", error);
      setVendorData([]);
    } finally {
      setLoading(false);
    }
  };

  // Fetch data on component mount
  useEffect(() => {
    fetchVendorDetails();
  }, []);

  // CSV Export functionality
  const handleExportCSV = () => {
    const columns = [
      { field: 'supplierName', header: 'Supplier Name' },
      { field: 'contactNumber', header: 'Contact Number' },
      { field: 'email', header: 'Email' },
      { field: 'location', header: 'Location' },
      { field: 'supplierOnHold', header: 'Supplier On Hold' },
      { field: 'lastInvoice', header: 'Last Invoice' },
      { field: 'totalDue', header: 'Total Due' },
      { field: 'supplierGroup', header: 'Supplier Group' },
      { field: 'serviceCategory', header: 'Service Category' },
      { field: 'firstName', header: 'First Name' },
      { field: 'lastName', header: 'Last Name' },
      { field: 'companyAddress', header: 'Company Address' },
      { field: 'taxId', header: 'Tax ID' },
      { field: 'websiteUrl', header: 'Website URL' },
      { field: 'createdDate', header: 'Created Date' }
    ];
    
    exportTableToCSV(vendorData, columns, 'vendors');
  };

  // Handle pagination change - receive paginated data from Paginator
  const onPageChange = (page, paginatedData) => {
    console.log("Page changed to:", page, "Data:", paginatedData);
    setPaginatedVendors(paginatedData);
  };

  // Template functions
  const checkboxTemplate = (rowData) => {
    return (
      <Checkbox
        checked={selectedVendors.includes(rowData)}
        onChange={(e) => {
          if (e.checked) {
            setSelectedVendors([...selectedVendors, rowData]);
          } else {
            setSelectedVendors(selectedVendors.filter(vendor => vendor.id !== rowData.id));
          }
        }}
      />
    );
  };

  const supplierOnHoldTemplate = (rowData) => {
    const severity = rowData.supplierOnHold === "Yes" ? "danger" : "success";
    return (
      <div className="flex justify-content-center">
        <GlassBadge 
          label={rowData.supplierOnHold} 
          severity={severity} 
        />
      </div>
    );
  };

  const emailTemplate = (rowData) => {
    return (
      <span className="cursor-pointer blue-text">
        {rowData.email}
      </span>
    );
  };

  const actionTemplate = (rowData) => {
    return (
      <div >
        <ActionButton action="update" />
      </div>
    );
  };

  return (
    <div className="glassy-page">
      {/* Content Above Table */}
      <div className="glass-content-section">
        {/* Page Title and Controls */}
        <div
       className="vendor-custom-header"
        >
          <div
           className="flex items-center gap-4 flex-wrap"
          >
            <h2 className="vendor-custom-title">
              Vendors
            </h2>
            <GlassyBlueButton
              label="New"
              icon="pi pi-plus"
              onClick={() => navigate("/vendors/new-vendor")}
            />
          </div>
          
          <div
           className="flex items-center gap-4 flex-wrap"
          >
            <FilterBy
              value={selectedFilter}
              options={filterOptions}
              onChange={(value) => {
                setSelectedFilter(value);
                handleFilter(value);
              }}
              placeholder="Filter by"
            />
            <SearchInput
              value={searchValue}
              onChange={(value) => {
                setSearchValue(value);
                handleSearch(value);
              }}
              placeholder="Search"
            />
            <div className="flex gap-2">
              <GlassyWhiteButton 
                label="" 
                icon="pi pi-download" 
                onClick={handleExportCSV}
              />
              <GlassyWhiteButton label="" icon="pi pi-file-export" />
              <GlassyWhiteButton label="" icon="pi pi-upload" />
              <GlassyWhiteButton label="" icon="pi pi-trash" />
            </div>
          </div>
        </div>
      </div>

      {/* Table Section */}
      <div className="glass-table-container">
        <DataTable
          value={paginatedVendors}
          selection={selectedVendors}
          onSelectionChange={(e) => setSelectedVendors(e.value)}
          className="glass-table"
          emptyMessage={loading ? "Loading vendors..." : "No vendors found"}
          loading={loading}
        >
          <Column body={checkboxTemplate} className="w-3rem" />
          <Column field="supplierName" header="Supplier Name" sortable />
          <Column field="contactNumber" header="Contact Number" sortable />
          <Column field="email" header="Email" body={emailTemplate} sortable />
          <Column field="location" header="Location" sortable />
          <Column 
            header="Supplier On Hold" 
            body={supplierOnHoldTemplate} 
            bodyStyle={{ textAlign: 'center' }}
          />
          <Column field="lastInvoice" header="Last Invoice" sortable />
          <Column field="totalDue" header="Total Due" sortable />
          <Column field="supplierGroup" header="Supplier Group" sortable />
          <Column field="serviceCategory" header="Service Category" sortable />
          <Column header="Action" body={actionTemplate} />
        </DataTable>

        {/* Custom Pagination */}
        <Paginator
          data={vendorData}
          itemsPerPage={itemsPerPage}
          onPageChange={onPageChange}
        />
      </div>
    </div>
  );
}

export default Vendor;
