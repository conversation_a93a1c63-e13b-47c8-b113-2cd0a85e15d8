import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Menubar } from 'primereact/menubar';
import { But<PERSON> } from 'primereact/button';
import { Avatar } from 'primereact/avatar';
import { Badge } from 'primereact/badge';
import { Sidebar } from 'primereact/sidebar';
import { Divider } from 'primereact/divider';
import ThemeSelector from '../theme/ThemeSelector';
import LanguageSelector from '../i18n/LanguageSelector';
import { useTheme } from '../theme/ThemeProvider';
import { useI18n } from '../i18n/I18nProvider';

const DynamicNavbar = ({
  brand = null,
  brandText = 'Dynamic Framework',
  brandIcon = 'pi pi-bolt',
  brandUrl = '/',
  menuItems = [],
  showThemeSelector = true,
  showLanguageSelector = true,
  showUserMenu = true,
  user = null,
  notifications = [],
  variant = 'default', 
  position = 'static', 
  className = '',
  style = {},
  onMenuItemClick,
  onBrandClick,
  onUserMenuClick,
  onNotificationClick,
  debug = false
}) => {
  const { isDarkMode } = useTheme();
  const { formatMessage, isRTL } = useI18n();
  const [sidebarVisible, setSidebarVisible] = useState(false);
  const [userMenuVisible, setUserMenuVisible] = useState(false);

  const defaultMenuItems = [
    {
      label: formatMessage({ id: 'nav.home', defaultMessage: 'Home' }),
      icon: 'pi pi-home',
      command: () => handleMenuItemClick('home')
    },
    {
      label: formatMessage({ id: 'nav.components', defaultMessage: 'Components' }),
      icon: 'pi pi-th-large',
      items: [
        {
          label: formatMessage({ id: 'nav.forms', defaultMessage: 'Forms' }),
          icon: 'pi pi-file-edit',
          command: () => handleMenuItemClick('forms')
        },
        {
          label: formatMessage({ id: 'nav.tables', defaultMessage: 'Tables' }),
          icon: 'pi pi-table',
          command: () => handleMenuItemClick('tables')
        }
      ]
    },
    {
      label: formatMessage({ id: 'nav.settings', defaultMessage: 'Settings' }),
      icon: 'pi pi-cog',
      command: () => handleMenuItemClick('settings')
    }
  ];

  const processedMenuItems = menuItems.length > 0 ? menuItems : defaultMenuItems;

  const handleMenuItemClick = useCallback((itemKey, item = null) => {
    if (debug) {
      console.log('Navbar menu item clicked:', itemKey, item);
    }

    if (onMenuItemClick) {
      onMenuItemClick(itemKey, item);
    }
  }, [onMenuItemClick, debug]);

  const handleBrandClick = useCallback((e) => {
    e.preventDefault();
    
    if (debug) {
      console.log('Navbar brand clicked');
    }

    if (onBrandClick) {
      onBrandClick();
    } else if (brandUrl) {
      window.location.href = brandUrl;
    }
  }, [onBrandClick, brandUrl, debug]);

  const handleUserMenuClick = useCallback((action) => {
    if (debug) {
      console.log('User menu action:', action);
    }

    if (onUserMenuClick) {
      onUserMenuClick(action);
    }

    setUserMenuVisible(false);
  }, [onUserMenuClick, debug]);

  const handleNotificationClick = useCallback((notification) => {
    if (debug) {
      console.log('Notification clicked:', notification);
    }

    if (onNotificationClick) {
      onNotificationClick(notification);
    }
  }, [onNotificationClick, debug]);

  const navbarClasses = [
    'dynamic-navbar',
    `dynamic-navbar--${variant}`,
    `dynamic-navbar--${position}`,
    isDarkMode ? 'dynamic-navbar--dark' : 'dynamic-navbar--light',
    isRTL ? 'dynamic-navbar--rtl' : 'dynamic-navbar--ltr',
    className
  ].filter(Boolean).join(' ');

  const renderBrand = () => {
    if (brand) {
      return brand;
    }

    return (
      <div className="navbar-brand" onClick={handleBrandClick}>
        {brandIcon && <i className={`navbar-brand__icon ${brandIcon}`} />}
        {brandText && <span className="navbar-brand__text">{brandText}</span>}
      </div>
    );
  };

  const renderUserMenu = () => {
    if (!showUserMenu) return null;

    const userInfo = user || {
      name: 'John Doe',
      email: '<EMAIL>',
      avatar: null,
      role: 'User'
    };

    return (
      <div className="navbar-user">
        <Button
          className="navbar-user__trigger p-button-text"
          onClick={() => setUserMenuVisible(true)}
        >
          <Avatar
            image={userInfo.avatar}
            icon={!userInfo.avatar ? 'pi pi-user' : null}
            label={!userInfo.avatar && !userInfo.image ? userInfo.name?.charAt(0) : null}
            size="normal"
            shape="circle"
          />
          <span className="navbar-user__name">{userInfo.name}</span>
          <i className="pi pi-chevron-down" />
        </Button>

        <Sidebar
          visible={userMenuVisible}
          onHide={() => setUserMenuVisible(false)}
          position={isRTL ? 'left' : 'right'}
          className="navbar-user-sidebar"
          style={{ width: '300px'}}
        >
          <div className="user-menu">
            <div className="user-menu__header">
              <Avatar
                image={userInfo.avatar}
                icon={!userInfo.avatar ? 'pi pi-user' : null}
                label={!userInfo.avatar && !userInfo.image ? userInfo.name?.charAt(0) : null}
                size="large"
                shape="circle"
              />
              <div className="user-info">
                <h4>{userInfo.name}</h4>
                <p>{userInfo.email}</p>
                <small>{userInfo.role}</small>
              </div>
            </div>

            <Divider />

            <div className="user-menu__actions">
              <Button
                label={formatMessage({ id: 'nav.settings', defaultMessage: 'Settings' })}
                icon="pi pi-cog"
                className="p-button-text user-menu-item"
                onClick={() => handleUserMenuClick('settings')}
              />
              <Button
                label={formatMessage({ id: 'nav.help', defaultMessage: 'Help' })}
                icon="pi pi-question-circle"
                className="p-button-text user-menu-item"
                onClick={() => handleUserMenuClick('help')}
              />
              <Button
                label={formatMessage({ id: 'nav.logout', defaultMessage: 'Logout' })}
                icon="pi pi-sign-out"
                className="p-button-text user-menu-item"
                onClick={() => handleUserMenuClick('logout')}
              />
            </div>
          </div>
        </Sidebar>
      </div>
    );
  };

  const renderNotifications = () => {
    if (!notifications || notifications.length === 0) return null;

    return (
      <div className="navbar-notifications">
        <Button
          icon="pi pi-bell"
          className="p-button-text navbar-notifications__trigger"
          onClick={() => handleNotificationClick(null)}
        >
          {notifications.length > 0 && (
            <Badge value={notifications.length} severity="danger" />
          )}
        </Button>
      </div>
    );
  };

  const renderControls = () => (
    <div className="navbar-controls">
      {showLanguageSelector && (
        <div className="navbar-control">
          <LanguageSelector
            variant="dropdown"
            showLabel={false}
            size="small"
            className="navbar-language-selector"
          />
        </div>
      )}

      {showThemeSelector && (
        <div className="navbar-control">
          <ThemeSelector
            variant="dropdown"
            showLabel={false}
            showDarkModeToggle={true}
            showCustomThemeButton={false}
            size="small"
            className="navbar-theme-selector"
          />
        </div>
      )}

      {renderNotifications()}
      {renderUserMenu()}
    </div>
  );

  const renderMobileMenuButton = () => (
    <Button
      icon="pi pi-bars"
      className="p-button-text navbar-mobile-menu-btn"
      onClick={() => setSidebarVisible(true)}
    />
  );

  const renderMobileSidebar = () => (
    <Sidebar
      visible={sidebarVisible}
      onHide={() => setSidebarVisible(false)}
      position={isRTL ? 'right' : 'left'}
      className="navbar-mobile-sidebar"
    >
      <div className="mobile-menu">
        <div className="mobile-menu__header">
          {renderBrand()}
        </div>

        <Divider />

        <div className="mobile-menu__items">
          {processedMenuItems.map((item, index) => (
            <div key={index} className="mobile-menu-item">
              <Button
                label={item.label}
                icon={item.icon}
                className="p-button-text mobile-menu-item__button"
                onClick={() => {
                  if (item.command) item.command();
                  setSidebarVisible(false);
                }}
              />
              {item.items && (
                <div className="mobile-submenu">
                  {item.items.map((subItem, subIndex) => (
                    <Button
                      key={subIndex}
                      label={subItem.label}
                      icon={subItem.icon}
                      className="p-button-text mobile-submenu-item"
                      onClick={() => {
                        if (subItem.command) subItem.command();
                        setSidebarVisible(false);
                      }}
                    />
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>

        <Divider />

        <div className="mobile-menu__controls">
          {showLanguageSelector && (
            <LanguageSelector
              variant="dropdown"
              showLabel={true}
              className="mobile-language-selector"
            />
          )}

          {showThemeSelector && (
            <ThemeSelector
              variant="dropdown"
              showLabel={true}
              showDarkModeToggle={true}
              className="mobile-theme-selector"
            />
          )}
        </div>
      </div>
    </Sidebar>
  );

  const endTemplate = () => (
    <div className="navbar-end">
      <div className="navbar-desktop-controls">
        {renderControls()}
      </div>
      <div className="navbar-mobile-controls">
        {renderMobileMenuButton()}
      </div>
    </div>
  );

  return (
    <>
      {debug && (
        <div className="dynamic-navbar__debug">
          <small>
            Variant: {variant} | 
            Position: {position} | 
            Theme: {isDarkMode ? 'Dark' : 'Light'} | 
            RTL: {isRTL ? 'Yes' : 'No'} |
            Menu Items: {processedMenuItems.length}
          </small>
        </div>
      )}
      
      <div className={navbarClasses} style={style}>
        <Menubar
          model={processedMenuItems}
          start={renderBrand()}
          end={endTemplate()}
          className="navbar-menubar"
        />
      </div>

      {renderMobileSidebar()}
    </>
  );
};

DynamicNavbar.propTypes = {
  brand: PropTypes.node,
  brandText: PropTypes.string,
  brandIcon: PropTypes.string,
  brandUrl: PropTypes.string,
  menuItems: PropTypes.array,
  showThemeSelector: PropTypes.bool,
  showLanguageSelector: PropTypes.bool,
  showUserMenu: PropTypes.bool,
  user: PropTypes.shape({
    name: PropTypes.string,
    email: PropTypes.string,
    avatar: PropTypes.string,
    role: PropTypes.string
  }),
  notifications: PropTypes.array,
  variant: PropTypes.oneOf(['default', 'compact', 'minimal']),
  position: PropTypes.oneOf(['static', 'fixed', 'sticky']),
  className: PropTypes.string,
  style: PropTypes.object,
  onMenuItemClick: PropTypes.func,
  onBrandClick: PropTypes.func,
  onUserMenuClick: PropTypes.func,
  onNotificationClick: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicNavbar;
