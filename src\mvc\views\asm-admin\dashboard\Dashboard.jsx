import React, { useState, useEffect, useMemo } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { InputText } from 'primereact/inputtext';
import { Button } from 'primereact/button';
import { Dropdown } from 'primereact/dropdown';
import { Chart } from 'primereact/chart';
import 'chart.js/auto';
import FromToDateInput from '@components/common/FromToDateInput';
import FilterBy from '@components/common/FilterBy';
import GlassBadge from '@components/common/GlassyBadge';
import { useAuthContext } from '@contexts/AuthContext';
import { fetchTickets, fetchDashboardStats, fetchTicketsByFilter, searchTickets } from '@temp-data/dashboardService';
import { exportTableToCSV } from '@utils/csvExport';
import GlassyBlueButton from '@components/common/GlassyBlueButton';

const Dashboard = () => {
  const { user } = useAuthContext();
  const [tickets, setTickets] = useState([]);
  const [filteredTickets, setFilteredTickets] = useState([]);
  const [selectedFilter, setSelectedFilter] = useState(null);
  const [searchValue, setSearchValue] = useState('');
  const [loading, setLoading] = useState(true);
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);

  // Dashboard statistics state
  const [dashboardStats, setDashboardStats] = useState({
    todayActiveUsers: 90,
    monthlyActiveUsers: 169,
    totalRoles: 8,
    overallActiveUsers: 250,
    projectConversation: 65,
    todayTickets: 315,
    overdueTickets: 5,
    criticalTickets: 3,
    allOpenTickets: 3
  });

  // Fetch initial data
  useEffect(() => {
    fetchInitialData();
  }, []);

  const fetchInitialData = async () => {
    try {
      setLoading(true);

      // Fetch dashboard statistics and tickets in parallel
      const [statsResponse, ticketsResponse] = await Promise.all([
        fetchDashboardStats(),
        fetchTickets()
      ]);

      // Batch state updates together using React 18's automatic batching
      if (statsResponse.success && ticketsResponse.success) {
        setDashboardStats(statsResponse.data);
        setTickets(ticketsResponse.data);
        setFilteredTickets(ticketsResponse.data);
        setLoading(false);
      } else {
        if (statsResponse.success) {
          setDashboardStats(statsResponse.data);
        }
        if (ticketsResponse.success) {
          setTickets(ticketsResponse.data);
          setFilteredTickets(ticketsResponse.data);
        }
        setLoading(false);
      }
    } catch (error) {
      console.error('Error fetching initial data:', error);
      setLoading(false);
    }
  };

  // Filter tickets based on selected filter
  const filterTickets = async (filterType) => {
    try {
      setLoading(true);
      setSelectedFilter(filterType);

      const response = await fetchTicketsByFilter(filterType);

      if (response.success) {
        setFilteredTickets(response.data);
      }
    } catch (error) {
      console.error('Error filtering tickets:', error);
      // Fallback to local filtering if API fails
      let filtered = [...tickets];

      switch (filterType) {
        case 'today':
          filtered = tickets.filter(ticket => ticket.category === 'today');
          break;
        case 'overdue':
          filtered = tickets.filter(ticket => ticket.category === 'overdue');
          break;
        case 'critical':
          filtered = tickets.filter(ticket => ticket.category === 'critical');
          break;
        case 'open':
          filtered = tickets.filter(ticket => ticket.status === 'Pending');
          break;
        default:
          filtered = tickets;
      }

      setFilteredTickets(filtered);
    } finally {
      setLoading(false);
    }
  };

  // Search functionality
  const onGlobalFilterChange = async (e) => {
    const value = e.target.value;
    setSearchValue(value);

    if (value.trim() === '') {
      setFilteredTickets(tickets);
    } else {
      try {
        const response = await searchTickets(value);

        if (response.success) {
          setFilteredTickets(response.data);
        }
      } catch (error) {
        console.error('Error searching tickets:', error);
        // Fallback to local search if API fails
        const filtered = tickets.filter(ticket =>
          Object.values(ticket).some(field =>
            field.toString().toLowerCase().includes(value.toLowerCase())
          )
        );
        setFilteredTickets(filtered);
      }
    }
  };

  // Chart data for Active User Summary (memoized)
  const chartData = useMemo(() => ({
    labels: ['Active Users', 'Inactive Users'],
    datasets: [
      {
        data: [75, 25],
        backgroundColor: ['#3B82F6', '#E5E7EB'],
        borderWidth: 0
      }
    ]
  }), []);

  const chartOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false
      }
    }
  }), []);

  // Project Conversation Doughnut: Won, Lost, In Progress
  const wonPct = Number.isFinite(dashboardStats.won)
    ? dashboardStats.won
    : Number.isFinite(dashboardStats.projectConversation)
      ? dashboardStats.projectConversation
      : 0;
  const lostPct = Number.isFinite(dashboardStats.lost)
    ? dashboardStats.lost
    : Math.max(0, Math.round((100 - wonPct) * 0.6));
  const inProgressPct = Number.isFinite(dashboardStats.inProgress)
    ? dashboardStats.inProgress
    : Math.max(0, 100 - wonPct - lostPct);

  // const projectDoughnutData = {
  //   labels: ['Won', 'Lost', 'In Progress'],
  //   datasets: [
  //     {
  //       data: [wonPct, lostPct, inProgressPct],
  //       backgroundColor: ['rgb(52, 239, 130)', 'rgb(241, 139, 126)', 'rgb(255, 205, 86)'],
  //       borderWidth: 0,
  //       hoverOffset: 4
  //     }
  //   ]
  // };


  const projectDoughnutData = useMemo(() => ({
    labels: ['Won', 'Lost', 'In Progress'],
    datasets: [
      {
        data: [wonPct, lostPct, inProgressPct],
        backgroundColor: ['rgb(52, 239, 130)', 'rgb(241, 139, 126)', 'rgb(255, 205, 86)'],
        borderWidth: 0,
        hoverOffset: 4
      }
    ]
  }), [wonPct, lostPct, inProgressPct]);

  // Memoize doughnut chart options
  const projectDoughnutOptions = useMemo(() => ({
    responsive: true,
    maintainAspectRatio: false,
    cutout: '60%',
    plugins: {
      legend: {
        display: true,
        position: 'bottom',
        labels: { usePointStyle: true, boxWidth: 8 }
      },
      tooltip: { enabled: true }
    }
  }), []);

  // Priority badge template
  const priorityBodyTemplate = (rowData) => {
    const priorityClass = rowData.priority.toLowerCase();
    return (
      <GlassBadge 
        label={rowData.priority}
        className={`priority-badge ${priorityClass}`}
      />
    );
  };

  // Status badge template
  const statusBodyTemplate = (rowData) => {
    const statusClass = rowData.status.toLowerCase().replace(/\s+/g, '-');
    return (
      <GlassBadge 
        label={rowData.status}
        className={`status-badge ${statusClass}`}
      />
    );
  };

  // Ticket ID template - all ticket IDs are clickable links
  const ticketIdBodyTemplate = (rowData) => {
    return (
      <span
        className="ticket-id-link"
        onClick={() => handleTicketClick(rowData.id)}
      >
        {rowData.id}
      </span>
    );
  };

  // Handle individual ticket click
  const handleTicketClick = (ticketId) => {
    console.log('Ticket clicked:', ticketId);
    // You can implement navigation to ticket details here
    // navigate(`/tickets/${ticketId}`);
  };

  // Handle view all functionality
  const handleViewAll = () => {
    console.log('View all tickets for filter:', selectedFilter);
    // You can implement navigation to a full tickets page here
    // navigate('/tickets', { state: { filter: selectedFilter } });
  };

  // Handle CSV download using reusable utility
  const downloadCSV = () => {
    const columns = [
      { field: 'id', header: 'Ticket ID' },
      { field: 'subject', header: 'Subject' },
      { field: 'ticketType', header: 'Ticket Type' },
      { field: 'priority', header: 'Ticket Priority' },
      { field: 'raisedBy', header: 'Raised By' },
      { field: 'createdOn', header: 'Created On' },
      { field: 'resolvedOn', header: 'Resolved On' },
      { field: 'status', header: 'Status' }
    ];
    
    exportTableToCSV(filteredTickets, columns, 'tickets');
  };

  // Action buttons template
  const actionBodyTemplate = (rowData) => {
    return (
      <Button
        icon="pi pi-eye"
        className="p-button-rounded p-button-text"
        onClick={() => console.log('View ticket:', rowData.id)}
      />
    );
  };
  console.log('whole Project render')
  return (
    <div className="dashboard-container mx-auto">
      
      {/* Dashboard Header */}
      <div className="dashboard-header">
        <div className="welcome-section">
          <h1>Hi, Welcome Back !!</h1>
          <p>Please view the summary of the tasks performed by the teams !!</p>
        </div>
        <div className="header-actions">
          <div className="company-section">
            <span className="company-label">Company</span>
            <Dropdown
              value="Aviation Services Management, Dubai"
              options={[{label: 'Aviation Services Management, Dubai', value: 'Aviation Services Management, Dubai'}]}
              placeholder="Company"
              className="company-dropdown"
            />
          </div>
          <Button icon="pi pi-bell" className="p-button-rounded p-button-text notification-btn" />
        </div>
      </div>

      {/* Statistics Cards Row */}
      <div className="stats-row">

        
        {/* Active User Summary Card */}
        <div className="glass-card ">
          <h3>Active User Summary</h3>
          <div className="chart-container">
            <Chart type="bar" data={chartData} options={chartOptions} />
            <div className="chart-legend h-auto">
              <div className="legend-item">
                <span className="legend-color active "></span>
                <span>Active Users</span>
              </div>
              <div className="legend-item">
                <span className="legend-color inactive"></span>
                <span>Inactive Users</span>
              </div>
            </div>
          </div>
        </div>

        {/* Project Conversation Card */}
        {console.log('Project Conversation Card rendered')}
        <div className="glass-card">
          <h3 className="text-center mb-2">PROJECT CONVERSATION</h3>
          <div className="circular-progress" style={{ height: '200px', width: '100%', position: 'relative' }}>
            <Chart
              type="doughnut"
              data={projectDoughnutData}
              options={projectDoughnutOptions}
              style={{ width: '100%', height: '100%' }}
            />
          
          </div>
               {/* Legend handled by Chart.js (below) */}
        </div>

        {/* User Statistics Cards */}
        <div className="user-stats-grid">
          <div className="user-stat-card glass-card-global">
            <div className="stat-label">Today's Active Users</div>
            <div className="stat-value blue">{dashboardStats.todayActiveUsers}</div>
          </div>
          <div className="user-stat-card glass-card-global">
            <div className="stat-label">Monthly Active Users</div>
            <div className="stat-value green">{dashboardStats.monthlyActiveUsers}</div>
          </div>
          <div className="user-stat-card glass-card-global">
            <div className="stat-label">Total Roles</div>
            <div className="stat-value purple">{dashboardStats.totalRoles}</div>
          </div>
          <div className="user-stat-card glass-card-global">
            <div className="stat-label">Overall Active Users</div>
            <div className="stat-value orange">{dashboardStats.overallActiveUsers}</div>
          </div>
        </div>
      </div>

      {/* Tickets Section */}
      <div  className="tickets-section glass-card-global">
        <div className="tickets-header">
          <h2>Tickets</h2>
           <div className="flex gap-2">
            <GlassyBlueButton
            label="Add Ticket"
            icon="pi pi-plus"
            onClick={() => navigate('/add-ticket')}
            />
            
            <Button 
            icon="pi pi-arrow-down" 
            className="p-button-rounded add-ticket-btn"
            onClick={downloadCSV}
          /></div>
          
        </div>

        {/* Ticket Filter Cards */}
        <div className="ticket-filters">
          <div 
            className={`filter-card ${selectedFilter === 'today' ? 'active' : ''}`}
            onClick={() => filterTickets('today')}
          >
            <div className="filter-label">Today's Tickets</div>
            <div className="filter-value blue">{dashboardStats.todayTickets}</div>
          </div>
          <div 
            className={`filter-card ${selectedFilter === 'overdue' ? 'active' : ''}`}
            onClick={() => filterTickets('overdue')}
          >
            <div className="filter-label">Overdue Tickets</div>
            <div className="filter-value orange">{dashboardStats.overdueTickets}</div>
          </div>
          <div 
            className={`filter-card ${selectedFilter === 'critical' ? 'active' : ''}`}
            onClick={() => filterTickets('critical')}
          >
            <div className="filter-label">Critical Tickets</div>
            <div className="filter-value red">{dashboardStats.criticalTickets}</div>
          </div>
          <div 
            className={`filter-card ${selectedFilter === 'open' ? 'active' : ''}`}
            onClick={() => filterTickets('open')}
          >
            <div className="filter-label">All Open Tickets</div>
            <div className="filter-value green">{dashboardStats.allOpenTickets}</div>
          </div>
        </div>

        {/* Search and Filter Controls */}
        <div className="table-controls">
          <div className="search-container">
            <i className="pi pi-search search-icon"></i>
            <InputText
              value={searchValue}
              onChange={onGlobalFilterChange}
              placeholder="Search"
              className="search-input"
            />
          </div>
          <div className="filter-controls">
            <FilterBy
              value={selectedFilter}
              options={[
                {label: 'All', value: 'all'},
                {label: 'High', value: 'high'},
                {label: 'Medium', value: 'medium'},
                {label: 'Low', value: 'low'}
              ]}
              onChange={(value) => {
                setSelectedFilter(value);
                if (value === 'all') {
                  setFilteredTickets(tickets);
                } else {
                  const priorityFiltered = tickets.filter(ticket => 
                    ticket.priority.toLowerCase() === value.toLowerCase()
                  );
                  setFilteredTickets(priorityFiltered);
                }
              }}
              placeholder="Filter by"
            />
            <FromToDateInput
              fromDate={fromDate}
              toDate={toDate}
              onFromDateChange={setFromDate}
              onToDateChange={setToDate}
              dateFormat="dd/mm/yy"
            />
          </div>
        </div>

        {/* Tickets Data Table */}
        <div className="">
          <DataTable
            value={filteredTickets.slice(0, 5)}
            loading={loading}
            className="glass-table"
          >
            <Column field="id" header="Ticket ID" body={ticketIdBodyTemplate} sortable />
            <Column field="subject" header="Subject" sortable />
            <Column field="ticketType" header="Ticket Type" sortable />
            <Column field="priority" header="Ticket Priority" body={priorityBodyTemplate} sortable   />
            <Column field="raisedBy" header="Raised By" sortable />
            <Column field="createdOn" header="Created On" sortable />
            <Column field="resolvedOn" header="Resolved On" sortable />
            <Column field="status" header="Status" body={statusBodyTemplate} sortable />
            <Column header="Action" body={actionBodyTemplate} />
          </DataTable>
        </div>

        {/* View All Link below table */}
        {filteredTickets.length > 5 && (
          <div className="view-all-below-table">
            {/* <span
              className="view-all-link"
              onClick={() => handleViewAll()}
            >
              View all
            </span> */}
          </div>
        )}

      </div>
    </div>
  );
};

export default React.memo(Dashboard);