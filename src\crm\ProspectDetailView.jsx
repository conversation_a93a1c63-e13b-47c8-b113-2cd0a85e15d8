import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Card } from 'primereact/card';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { TabView, TabPanel } from 'primereact/tabview';
import { Tag } from 'primereact/tag';
import { Divider } from 'primereact/divider';

const ProspectDetailView = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const [activeIndex, setActiveIndex] = useState(0);

  const [prospect, setProspect] = useState({
    id: 'ASMK-000012',
    account: 'Emirates Airlines',
    name: 'Emirates Airlines',
    email: '<EMAIL>',
    phone: '+971 4 214 4444',
    mobileNumber: '+971 4 214 4444',
    website: 'https://www.emirates.com',
    tradeLicense: '************',
    language: 'English',
    tags: ['COP28', 'Event2025'],
    vat: 'Local 18%',
    vatNo: '**********',
    customerGroup: 'Active',
    openingTime: '',
    closingTime: '',
    businessType: '',
    salesperson: 'Sarah Johnson',
    lineOfBusiness: 'Aviation Services',
    segment: 'Commercial Aviation',
    subSegment: 'Fuel Supply',
    iata: '************',
    icao: '************',
    iataNo: '01-123456',
    blacklistProspect: 'No',
    reason: '',
    status: 'New Lead',
    stage: 1
  });

  const stages = [
    { number: 1, label: 'New Lead', sublabel: 'Initial contact' },
    { number: 2, label: 'Opportunity', sublabel: 'Qualification' },
    { number: 3, label: 'Won / Lost', sublabel: 'Closed' }
  ];

  const languageOptions = [
    { label: 'English', value: 'English' },
    { label: 'Arabic', value: 'Arabic' },
    { label: 'French', value: 'French' }
  ];

  const vatOptions = [
    { label: 'Local 18%', value: 'Local 18%' },
    { label: 'Local 5%', value: 'Local 5%' },
    { label: 'Exempt', value: 'Exempt' }
  ];

  const customerGroupOptions = [
    { label: 'Active', value: 'Active' },
    { label: 'Inactive', value: 'Inactive' },
    { label: 'VIP', value: 'VIP' }
  ];

  const blacklistOptions = [
    { label: 'No', value: 'No' },
    { label: 'Yes', value: 'Yes' }
  ];

  const handleSave = () => {
    console.log('Saving prospect:', prospect);
  };

  const handleEnrichData = () => {
    console.log('Enriching data for prospect:', prospect);
  };

  const handleCreateOpportunity = () => {
    navigate(`/opportunity/new?prospectId=${prospect.id}`);
  };

  const handleMarkAsWon = () => {
    console.log('Marking as won:', prospect);
  };

  const handleMarkAsLost = () => {
    console.log('Marking as lost:', prospect);
  };

  const handleCreateCustomer = () => {
    navigate(`/customer/new?prospectId=${prospect.id}`);
  };

  const handleCreateVendor = () => {
    navigate(`/vendor/new?prospectId=${prospect.id}`);
  };

  return (
    <div className="prospect-detail-view">
      <div className="breadcrumb-nav">
        <Button
          icon="pi pi-arrow-left"
          className="p-button-text p-button-sm"
          onClick={() => navigate('/prospects')}
        />
        <span className="breadcrumb-separator">/</span>
        <span className="breadcrumb-link" onClick={() => navigate('/prospects')}>Pipeline</span>
        <span className="breadcrumb-separator">/</span>
        <span>{prospect.name} - {prospect.id}</span>
      </div>

      <div className="pipeline-stages">
        {stages.map((stage) => (
          <div
            key={stage.number}
            className={`pipeline-stage ${prospect.stage >= stage.number ? 'active' : ''}`}
          >
            <div className="stage-number">{stage.number}</div>
            <div className="stage-info">
              <div className="stage-label">{stage.label}</div>
              <div className="stage-sublabel">{stage.sublabel}</div>
            </div>
          </div>
        ))}
      </div>

      <div className="action-buttons-bar">
        <Button label="Save" icon="pi pi-save" className="p-button-primary" onClick={handleSave} />
        <Button label="Enrich Data" icon="pi pi-refresh" className="p-button-outlined" onClick={handleEnrichData} />
        <Button label="Create Opportunity" icon="pi pi-plus" className="p-button-outlined" onClick={handleCreateOpportunity} />
        <Button label="Mark as Won" icon="pi pi-check" className="p-button-success" onClick={handleMarkAsWon} />
        <Button label="Mark as Lost" icon="pi pi-times" className="p-button-danger" onClick={handleMarkAsLost} />
        <Button label="Create Customer" icon="pi pi-user-plus" className="p-button-success" onClick={handleCreateCustomer} />
        <Button label="Create Vendor" icon="pi pi-briefcase" className="p-button-outlined" onClick={handleCreateVendor} />
      </div>

      <Card className="prospect-detail-card">
        <div className="detail-section">
          <h3>General</h3>
          <div className="form-grid">
            <div className="form-field">
              <label>Account</label>
              <InputText
                value={prospect.account}
                onChange={(e) => setProspect({ ...prospect, account: e.target.value })}
              />
            </div>

            <div className="form-field">
              <label>Language</label>
              <Dropdown
                value={prospect.language}
                options={languageOptions}
                onChange={(e) => setProspect({ ...prospect, language: e.value })}
              />
            </div>

            <div className="form-field">
              <label>Tags</label>
              <div className="tags-input">
                {prospect.tags.map((tag, index) => (
                  <Tag key={index} value={tag} className="mr-1" />
                ))}
              </div>
            </div>

            <div className="form-field">
              <label>Name</label>
              <InputText
                value={prospect.name}
                onChange={(e) => setProspect({ ...prospect, name: e.target.value })}
              />
            </div>

            <div className="form-field">
              <label>Email</label>
              <InputText
                value={prospect.email}
                onChange={(e) => setProspect({ ...prospect, email: e.target.value })}
              />
            </div>

            <div className="form-field">
              <label>VAT</label>
              <Dropdown
                value={prospect.vat}
                options={vatOptions}
                onChange={(e) => setProspect({ ...prospect, vat: e.value })}
              />
            </div>

            <div className="form-field">
              <label>Phone</label>
              <InputText
                value={prospect.phone}
                onChange={(e) => setProspect({ ...prospect, phone: e.target.value })}
              />
            </div>

            <div className="form-field">
              <label>Website</label>
              <InputText
                value={prospect.website}
                onChange={(e) => setProspect({ ...prospect, website: e.target.value })}
              />
            </div>

            <div className="form-field">
              <label>VAT No.</label>
              <InputText
                value={prospect.vatNo}
                onChange={(e) => setProspect({ ...prospect, vatNo: e.target.value })}
              />
            </div>

            <div className="form-field">
              <label>Mobile Number</label>
              <InputText
                value={prospect.mobileNumber}
                onChange={(e) => setProspect({ ...prospect, mobileNumber: e.target.value })}
              />
            </div>

            <div className="form-field">
              <label>Trade License</label>
              <InputText
                value={prospect.tradeLicense}
                onChange={(e) => setProspect({ ...prospect, tradeLicense: e.target.value })}
              />
            </div>
          </div>
        </div>

        <Divider />

        <div className="detail-section">
          <h3 className="section-title-blue">Other Information</h3>
          <div className="form-grid">
            <div className="form-field">
              <label>Customer Group</label>
              <Dropdown
                value={prospect.customerGroup}
                options={customerGroupOptions}
                onChange={(e) => setProspect({ ...prospect, customerGroup: e.value })}
              />
            </div>

            <div className="form-field">
              <label>Opening Time</label>
              <InputText
                value={prospect.openingTime}
                onChange={(e) => setProspect({ ...prospect, openingTime: e.target.value })}
                placeholder="Enter opening time"
              />
            </div>

            <div className="form-field">
              <label>Closing Time</label>
              <InputText
                value={prospect.closingTime}
                onChange={(e) => setProspect({ ...prospect, closingTime: e.target.value })}
                placeholder="Enter closing time"
              />
            </div>

            <div className="form-field">
              <label>Business Type</label>
              <InputText
                value={prospect.businessType}
                onChange={(e) => setProspect({ ...prospect, businessType: e.target.value })}
                placeholder="Enter business type"
              />
            </div>
          </div>
        </div>

        <Divider />

        <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
          <TabPanel header="Address">
            <div className="tab-content">
              <p>Address information will be displayed here</p>
            </div>
          </TabPanel>
          <TabPanel header="Contacts">
            <div className="tab-content">
              <p>Contact information will be displayed here</p>
            </div>
          </TabPanel>
          <TabPanel header="Compliance">
            <div className="tab-content">
              <p>Compliance information will be displayed here</p>
            </div>
          </TabPanel>
          <TabPanel header="KYC">
            <div className="tab-content">
              <p>KYC information will be displayed here</p>
            </div>
          </TabPanel>
          <TabPanel header="Financials">
            <div className="tab-content">
              <p>Financial information will be displayed here</p>
            </div>
          </TabPanel>
          <TabPanel header="Notes & Messages">
            <div className="tab-content">
              <p>Notes and messages will be displayed here</p>
            </div>
          </TabPanel>
          <TabPanel header="Logs">
            <div className="tab-content">
              <p>Activity logs will be displayed here</p>
            </div>
          </TabPanel>
          <TabPanel header="Documentation">
            <div className="tab-content">
              <p>Documentation will be displayed here</p>
            </div>
          </TabPanel>
        </TabView>
      </Card>
    </div>
  );
};

export default ProspectDetailView;

