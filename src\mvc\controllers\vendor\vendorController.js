import VendorService from "@services/vendor/vendorService";

/**
 * Get all vendor details with transformation
 * @returns {Promise} - Promise resolving to transformed vendor data
 */
const getVendorDetails = async () => {
  try {
    const result = await VendorService.getVendorDetails();
    console.log("getVendorDetails result:", result);
    if (result.success) {
      const transformedVendors = transformVendorsData(result.data);
      result.data = transformedVendors;
    }
    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

/**
 * Transform vendor API data to match the expected table format
 * @param {Array} apiData - Raw API response data
 * @returns {Array} - Transformed vendor data
 */
const transformVendorsData = (apiData) => {
  if (!Array.isArray(apiData)) {
    return [];
  }
  return apiData.map((vendor) => {
    return {
      id: vendor.id,
      supplierName: vendor.companyName || "-",
      contactNumber: vendor.phone || "-",
      email: vendor.email || "-",
      location: vendor.companyAddress || "-",
      supplierOnHold: vendor.statusId === 1 ? "No" : "Yes", // Assuming statusId 1 means active (not on hold)
      lastInvoice: "-", // Not available in API data
      totalDue: "-", // Not available in API data
      supplierGroup: "-", // Not available in API data
      serviceCategory: "-", // Not available in API data
      firstName: vendor.firstname || "-",
      lastName: vendor.lastname || "-",
      companyName: vendor.companyName || "-",
      industryId: vendor.industryId || "-",
      stateId: vendor.stateId || "-",
      countryId: vendor.countryId || "-",
      companyAddress: vendor.companyAddress || "-",
      taxId: vendor.taxId || "-",
      websiteUrl: vendor.websiteUrl || "-",
      statusId: vendor.statusId,
      displayName: vendor.displayName || "-",
      createdDate: vendor.createdDate ? new Date(vendor.createdDate).toLocaleDateString() : "-",
      lastModifiedDate: vendor.lastModifiedDate ? new Date(vendor.lastModifiedDate).toLocaleDateString() : "-"
    };
  });
};

/**
 * Search vendors by term
 * @param {string} searchTerm - Search term
 * @returns {Promise} - Promise resolving to filtered vendor data
 */
const searchVendors = async (searchTerm) => {
  try {
    if (!searchTerm || searchTerm.trim() === "") {
      // If no search term, return all vendors
      return await getVendorDetails();
    }

    const result = await VendorService.searchVendors(searchTerm.trim());
    
    if (result.success) {
      const transformedVendors = transformVendorsData(result.data);
      result.data = transformedVendors;
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

/**
 * Get vendor by ID
 * @param {number} vendorId - Vendor ID
 * @returns {Promise} - Promise resolving to vendor data
 */
const getVendorById = async (vendorId) => {
  try {
    const result = await VendorService.getVendorById(vendorId);
    if (result.success && result.data) {
      // Transform single vendor data
      const transformedVendor = transformVendorsData([result.data])[0];
      result.data = transformedVendor;
    }
    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: null,
    };
  }
};

/**
 * Create new vendor
 * @param {Object} vendorData - Vendor data to create
 * @returns {Promise} - Promise resolving to creation result
 */
const createVendor = async (vendorData) => {
  try {
    const result = await VendorService.createVendor(vendorData);
    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Update vendor
 * @param {number} vendorId - Vendor ID
 * @param {Object} vendorData - Updated vendor data
 * @returns {Promise} - Promise resolving to update result
 */
const updateVendor = async (vendorId, vendorData) => {
  try {
    const result = await VendorService.updateVendor(vendorId, vendorData);
    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Delete vendor
 * @param {number} vendorId - Vendor ID
 * @returns {Promise} - Promise resolving to deletion result
 */
const deleteVendor = async (vendorId) => {
  try {
    const result = await VendorService.deleteVendor(vendorId);
    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Update vendor status
 * @param {number} vendorId - Vendor ID
 * @param {number} statusId - New status ID
 * @returns {Promise} - Promise resolving to update result
 */
const updateVendorStatus = async (vendorId, statusId) => {
  try {
    const result = await VendorService.updateVendorStatus(vendorId, statusId);
    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message,
    };
  }
};

/**
 * Filter vendors by on hold status
 * @param {string} holdStatus - Hold status ('on_hold_yes', 'on_hold_no', 'all')
 * @returns {Promise} - Promise resolving to filtered vendor data
 */
const filterVendorsByHoldStatus = async (holdStatus) => {
  try {
    const result = await getVendorDetails();
    
    if (!result.success) {
      return result;
    }

    if (holdStatus === 'all') {
      return result;
    }

    const filteredVendors = result.data.filter((vendor) => {
      if (holdStatus === 'on_hold_yes') {
        return vendor.supplierOnHold === 'Yes';
      } else if (holdStatus === 'on_hold_no') {
        return vendor.supplierOnHold === 'No';
      }
      return true;
    });

    return {
      success: true,
      data: filteredVendors,
      total: filteredVendors.length,
      statusCode: result.statusCode,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

const vendorController = {
  getVendorDetails,
  searchVendors,
  getVendorById,
  createVendor,
  updateVendor,
  deleteVendor,
  updateVendorStatus,
  filterVendorsByHoldStatus,
};

export default vendorController;
