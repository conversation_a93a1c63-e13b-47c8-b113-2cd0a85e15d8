// components/common/ActionButton.jsx
import React from "react";
import { Button } from "primereact/button";

export default function ActionButton({
  label,
  icon,
  action = "default", // view | update | delete | cancel | default
  onClick,
  className = "",
  tooltip,
  ...props
}) {
  const baseStyle = "glass-btn-icon";

  const actionStyles = {
    default: "bg-gray-200 text-gray-800 hover:bg-gray-300",
    view: "bg-green-600 text-white hover:bg-green-700",
    delete: "bg-red-600 text-white hover:bg-red-700",
    cancel: "bg-gray-100 text-gray-600 hover:bg-gray-200",
    confirm: "bg-blue-600 text-white hover:bg-blue-700",
  };

  const actionIcons = {
    view: "pi pi-eye",
    update: "pi pi-pencil",
    delete: "pi pi-trash",
    cancel: "pi pi-times",
    confirm: "pi pi-check-circle",
    default: "pi pi-ellipsis-v",
  };
  // tooltip for view, update, delete, cancel, confirm
  const actionTooltips = {
    view: "View",
    update: "Update",
    delete: "Delete",
    cancel: "Cancel",
    confirm: "Confirm",
    default: "More",
  };

  return (
    <Button
      label={label}
      icon={icon || actionIcons[action]}
      onClick={onClick}
      className={`${baseStyle} ${actionStyles[action]} ${className}`}
      tooltip={tooltip || actionTooltips[action]}
      {...props}
    />
  );
}
