import BaseController from '../BaseController.js';
import LeadService from '../../services/leadmanagement/LeadService.js';

class LeadController extends BaseController {
    constructor() {
        super();
        this.state = {
            leads: [],
            selectedLead: null,
            loading: false,
            error: null
        };
    }
    async fetchLeadAddress(id) {
        return this.executeAction('fetchLeadAddress', async () => {
            const LeadAddressService = (await import('../../services/leadmanagement/LeadAddress.js')).default;
            const address = await LeadAddressService.getById(id);
            this.setState({ address });
            return address;
        });
    }

    async fetchLeadLog(id) {
        return this.executeAction('fetchLeadLog', async () => {
            const LeadLogService = (await import('../../services/leadmanagement/LeadLog.js')).default;
            const log = await LeadLogService.getById(id);
            this.setState({ log });
            return log;
        });
    }

    async fetchLeadDocument(id) {
        return this.executeAction('fetchLeadDocument', async () => {
            const LeadDocumentService = (await import('../../services/leadmanagement/LeadDocument.js')).default;
            const document = await LeadDocumentService.getById(id);
            this.setState({ document });
            return document;
        });
    }


    async fetchLeads() {
        return this.executeAction('fetchLeads', async () => {
            const leads = await LeadService.getAll();
            this.setState({ leads });
            return leads;
        });
    }

    async fetchLeadById(id) {
        return this.executeAction('fetchLeadById', async () => {
            const lead = await LeadService.getById(id);
            this.setState({ selectedLead: lead });
            return lead;
        });
    }

    async saveLead(lead) {
        return this.executeAction('saveLead', async () => {
            const saved = await LeadService.save(lead);
            await this.fetchLeads();
            return saved;
        });
    }

    async updateLead(id, lead) {
        return this.executeAction('updateLead', async () => {
            const updated = await LeadService.update(id, lead);
            await this.fetchLeads();
            return updated;
        });
    }

    async updatePartialLead(id, lead) {
        return this.executeAction('updatePartialLead', async () => {
            const updated = await LeadService.updatePartial(id, lead);
            await this.fetchLeads();
            return updated;
        });
    }

    async deleteLead(id) {
        return this.executeAction('deleteLead', async () => {
            await LeadService.deleteById(id);
            await this.fetchLeads();
        });
    }

    async sortLeads(sortBy, sortOrder) {
        return this.executeAction('sortLeads', async () => {
            const leads = await LeadService.sort(sortBy, sortOrder);
            this.setState({ leads });
            return leads;
        });
    }

    async searchLeads(query) {
        return this.executeAction('searchLeads', async () => {
            const leads = await LeadService.executeQuery(query);
            this.setState({ leads });
            return leads;
        });
    }
}

export default new LeadController();
