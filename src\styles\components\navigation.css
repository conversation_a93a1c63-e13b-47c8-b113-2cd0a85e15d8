.nav {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: var(--color-surface);
  border-bottom: 1px solid var(--color-border);
}

.nav-brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text);
  text-decoration: none;
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-left: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 1rem;
  color: var(--color-text-secondary);
  text-decoration: none;
  border-radius: var(--border-radius);
  transition: all 0.2s ease-in-out;
}

.nav-item:hover {
  background-color: var(--color-surface-hover);
  color: var(--color-text);
}
