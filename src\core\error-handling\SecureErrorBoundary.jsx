import React from 'react';
import { logger } from '../logging/logger';

class SecureErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    const sanitizedError = this.sanitizeError(error);
    const sanitizedErrorInfo = this.sanitizeErrorInfo(errorInfo);

    logger.error('React Error Boundary caught error:', {
      error: sanitizedError,
      errorInfo: sanitizedErrorInfo,
      timestamp: new Date().toISOString()
    });

    this.reportError(sanitizedError);
  }

  sanitizeError = (error) => {
    const safeError = {
      message: error.message,
      name: error.name,
      stack: process.env.NODE_ENV === 'production' 
        ? 'Stack trace hidden in production' 
        : error.stack
    };

    safeError.message = safeError.message.replace(
      /(password|token|key|secret)=[^&\s]*/gi,
      '$1=***'
    );

    return safeError;
  };

  sanitizeErrorInfo = (errorInfo) => {
    return process.env.NODE_ENV === 'production'
      ? { componentStack: 'Stack trace hidden in production' }
      : errorInfo;
  };

  reportError = (error) => {
    fetch('/api/error-report', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(error)
    }).catch(() => {
    });
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-boundary">
          <h2>Something went wrong</h2>
          <p>We've been notified and are working on a fix.</p>
          {process.env.NODE_ENV === 'development' && (
            <details>
              <summary>Error Details (Development only)</summary>
              <pre>{this.state.error?.toString()}</pre>
            </details>
          )}
        </div>
      );
    }

    return this.props.children;
  }
}

export default SecureErrorBoundary;