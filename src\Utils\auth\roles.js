// Simple Role Constants
export const ROLES = {
  ASM_ADMIN: 'asm_admin',
  ASM_EMPLOYEE: 'asm_employee', 
  CLIENT: 'client',
  CLIENT_EMPLOYEE: 'client_employee'
};

// Default routes for each role
export const DEFAULT_ROUTES = {
  [ROLES.ASM_ADMIN]: '/dashboard',
  [ROLES.ASM_EMPLOYEE]: '/asm-employee-dashboard',
  [ROLES.CLIENT]: '/client-dashboard',
  [ROLES.CLIENT_EMPLOYEE]: '/client-dashboard'
};

// Secure function to check if user has required role
export const hasRole = (userRoles, requiredRole) => {
  if (!userRoles || !Array.isArray(userRoles) || !requiredRole) return false;
  // Validate role exists in our system
  if (!Object.values(ROLES).includes(requiredRole)) return false;
  return userRoles.includes(requiredRole);
};

// Secure function to check if user has any of the required roles
export const hasAnyRole = (userRoles, requiredRoles) => {
  if (!userRoles || !Array.isArray(userRoles) || !Array.isArray(requiredRoles)) return false;
  // Validate all required roles exist in our system
  const validRequiredRoles = requiredRoles.filter(role => Object.values(ROLES).includes(role));
  if (validRequiredRoles.length === 0) return false;
  return validRequiredRoles.some(role => userRoles.includes(role));
};

// Get highest priority role for user
export const getHighestRole = (userRoles) => {
  if (!userRoles || userRoles.length === 0) return null;
  
  // Priority order: asm_admin > asm_employee > client > client_employee
  if (userRoles.includes(ROLES.ASM_ADMIN)) return ROLES.ASM_ADMIN;
  if (userRoles.includes(ROLES.ASM_EMPLOYEE)) return ROLES.ASM_EMPLOYEE;
  if (userRoles.includes(ROLES.CLIENT)) return ROLES.CLIENT;
  if (userRoles.includes(ROLES.CLIENT_EMPLOYEE)) return ROLES.CLIENT_EMPLOYEE;
  
  return null;
};

// Get default route for user
export const getDefaultRoute = (userRoles) => {
  if (!userRoles || userRoles.length === 0) return '/login';
  
  // Priority order: asm_admin > asm_employee > client > client_employee
  if (userRoles.includes(ROLES.ASM_ADMIN)) return DEFAULT_ROUTES[ROLES.ASM_ADMIN];
  if (userRoles.includes(ROLES.ASM_EMPLOYEE)) return DEFAULT_ROUTES[ROLES.ASM_EMPLOYEE];
  if (userRoles.includes(ROLES.CLIENT)) return DEFAULT_ROUTES[ROLES.CLIENT];
  if (userRoles.includes(ROLES.CLIENT_EMPLOYEE)) return DEFAULT_ROUTES[ROLES.CLIENT_EMPLOYEE];
  
  return '/login';
};
