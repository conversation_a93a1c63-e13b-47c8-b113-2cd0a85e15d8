import { configureStore, combineReducers } from '@reduxjs/toolkit';
import {
  persistStore,
  persistReducer,
  FLUSH,
  REHYDRATE,
  PAUSE,
  PERSIST,
  PURGE,
  REGISTER
} from 'redux-persist';
import storage from 'redux-persist/lib/storage';

import cacheSlice from './slices/cacheSlice';
import userCacheSlice from './slices/userCacheSlice';
import apiCacheSlice from './slices/apiCacheSlice';
import themeCacheSlice from './slices/themeCacheSlice';
import i18nCacheSlice from './slices/i18nCacheSlice';

import cacheMiddleware from './middleware/cacheMiddleware';
import analyticsMiddleware from './middleware/analyticsMiddleware';
import persistenceMiddleware from './middleware/persistenceMiddleware';
import tableReducer from "./tableSlice";

import CacheService from '../mvc/services/CacheService';

// Combine all slices
const rootReducer = combineReducers({
  cache: cacheSlice,
  userCache: userCacheSlice,
  apiCache: apiCacheSlice,
  themeCache: themeCacheSlice,
  i18nCache: i18nCacheSlice,
  table: tableReducer,
});

// Persist config
const persistConfig = {
  key: 'root',
  storage,
  whitelist: ['cache', 'userCache', 'themeCache', 'i18nCache'],
  blacklist: ['apiCache'],
  version: 1,
  migrate: (state) => {
    if (state && state._persist && state._persist.version !== 1) {
      return Promise.resolve(undefined);
    }
    return Promise.resolve(state);
  },
  transforms: [
    {
      in: (inboundState, key) => {
        if (key === 'cache' && inboundState) {
          const now = Date.now();
          const cleanedEntries = {};

          Object.entries(inboundState.entries || {}).forEach(([cacheKey, entry]) => {
            if (!entry.expiresAt || entry.expiresAt > now) {
              cleanedEntries[cacheKey] = entry;
            }
          });

          return {
            ...inboundState,
            entries: cleanedEntries
          };
        }
        return inboundState;
      },
      out: (outboundState, key) => {
        if (key === 'cache' && outboundState) {
          const now = Date.now();
          const cleanedEntries = {};

          Object.entries(outboundState.entries || {}).forEach(([cacheKey, entry]) => {
            if (!entry.expiresAt || entry.expiresAt > now) {
              cleanedEntries[cacheKey] = entry;
            }
          });

          return {
            ...outboundState,
            entries: cleanedEntries
          };
        }
        return outboundState;
      }
    }
  ]
};

const persistedReducer = persistReducer(persistConfig, rootReducer);

// Configure store
export const store = configureStore({
  reducer: persistedReducer,
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: [FLUSH, REHYDRATE, PAUSE, PERSIST, PURGE, REGISTER],
        ignoredActionsPaths: ['meta.arg', 'payload.timestamp'],
        ignoredPaths: ['cache.entries', 'userCache.entries']
      },
      immutableCheck: {
        ignoredPaths: ['cache.entries', 'userCache.entries']
      }
    })
      .concat(cacheMiddleware)
      .concat(analyticsMiddleware)
      .concat(persistenceMiddleware),
  devTools:
    import.meta.env?.MODE !== 'production' && {
      name: 'MVC Cache Store',
      trace: true,
      traceLimit: 25,
      actionSanitizer: (action) => ({
        ...action,
        payload:
          action.payload && typeof action.payload === 'object' && action.payload.data
            ? { ...action.payload, data: '[Large Data Object]' }
            : action.payload
      }),
      stateSanitizer: (state) => ({
        ...state,
        cache: state.cache
          ? {
            ...state.cache,
            entries: Object.keys(state.cache.entries || {}).reduce((acc, key) => {
              acc[key] = '[Cache Entry]';
              return acc;
            }, {})
          }
          : state.cache
      })
    }
});

// Persistor setup
export const persistor = persistStore(store, null, () => {
  console.log('Cache store rehydrated');
  CacheService.initialize(store);
  store.dispatch({ type: 'cache/cleanupExpired' });
});

// Store configuration
export const storeConfig = {
  cache: {
    defaultTTL: 5 * 60 * 1000,
    maxEntries: 1000,
    cleanupInterval: 60 * 1000,
    persistenceEnabled: true,
    analyticsEnabled: true
  },
  apiCache: {
    defaultTTL: 2 * 60 * 1000,
    maxEntries: 500,
    staleWhileRevalidate: true,
    backgroundRefresh: true
  },
  userCache: {
    defaultTTL: 30 * 60 * 1000,
    maxEntries: 100,
    persistenceEnabled: true
  },
  themeCache: {
    defaultTTL: 24 * 60 * 60 * 1000,
    maxEntries: 50,
    persistenceEnabled: true
  },
  i18nCache: {
    defaultTTL: 60 * 60 * 1000,
    maxEntries: 200,
    persistenceEnabled: true
  }
};

// Cache store utility functions
export const cacheStoreUtils = {
  getStats: () => {
    const state = store.getState();
    return {
      totalEntries: Object.keys(state.cache?.entries || {}).length,
      userCacheEntries: Object.keys(state.userCache?.entries || {}).length,
      apiCacheEntries: Object.keys(state.apiCache?.entries || {}).length,
      themeCacheEntries: Object.keys(state.themeCache?.entries || {}).length,
      i18nCacheEntries: Object.keys(state.i18nCache?.entries || {}).length,
      memoryUsage: JSON.stringify(state).length,
      lastCleanup: state.cache?.lastCleanup,
      hitRate: state.cache?.analytics?.hitRate || 0,
      missRate: state.cache?.analytics?.missRate || 0
    };
  },

  clearAll: () => {
    store.dispatch({ type: 'cache/clear' });
    store.dispatch({ type: 'userCache/clear' });
    store.dispatch({ type: 'apiCache/clear' });
    store.dispatch({ type: 'themeCache/clear' });
    store.dispatch({ type: 'i18nCache/clear' });
  },

  cleanupExpired: () => {
    store.dispatch({ type: 'cache/cleanupExpired' });
    store.dispatch({ type: 'userCache/cleanupExpired' });
    store.dispatch({ type: 'apiCache/cleanupExpired' });
    store.dispatch({ type: 'themeCache/cleanupExpired' });
    store.dispatch({ type: 'i18nCache/cleanupExpired' });
  },

  getEntry: (cacheType, key) => {
    const state = store.getState();
    return state[cacheType]?.entries?.[key];
  },

  isValid: (cacheType, key) => {
    const entry = cacheStoreUtils.getEntry(cacheType, key);
    if (!entry) return false;

    const now = Date.now();
    return !entry.expiresAt || entry.expiresAt > now;
  },

  exportCache: () => {
    const state = store.getState();
    return {
      timestamp: Date.now(),
      version: persistConfig.version,
      data: {
        cache: state.cache,
        userCache: state.userCache,
        apiCache: state.apiCache,
        themeCache: state.themeCache,
        i18nCache: state.i18nCache
      },
      stats: cacheStoreUtils.getStats()
    };
  },

  importCache: (cacheData) => {
    if (cacheData.version !== persistConfig.version) {
      console.warn('Cache version mismatch, skipping import');
      return false;
    }

    try {
      Object.entries(cacheData.data).forEach(([cacheType, data]) => {
        if (data && data.entries) {
          store.dispatch({
            type: `${cacheType}/importEntries`,
            payload: data.entries
          });
        }
      });
      return true;
    } catch (error) {
      console.error('Failed to import cache data:', error);
      return false;
    }
  }
};

// Cleanup interval
let cleanupInterval;

export const startCleanupInterval = () => {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
  }

  cleanupInterval = setInterval(() => {
    cacheStoreUtils.cleanupExpired();
  }, storeConfig.cache.cleanupInterval);
};

export const stopCleanupInterval = () => {
  if (cleanupInterval) {
    clearInterval(cleanupInterval);
    cleanupInterval = null;
  }
};

// Auto start cleanup interval
if (typeof window !== 'undefined') {
  startCleanupInterval();

  window.addEventListener('beforeunload', () => {
    stopCleanupInterval();
  });
}

export default store;
