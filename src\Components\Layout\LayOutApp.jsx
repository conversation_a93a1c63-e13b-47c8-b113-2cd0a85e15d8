import React, { useContext, useEffect } from "react";
import { Outlet } from "react-router-dom";
import Navbar from "./Navbar";
import { useAuthContext } from "@contexts/AuthContext";

const LayoutApp = () => {

    const { isAuthenticated } = useAuthContext();
    useEffect(() => {
        if (!isAuthenticated) {
          window.location.href = '/login';
        }
      }, [isAuthenticated]);
  
  return (
    <div className="content-container">
      <Navbar />
      <div className="webapp-content-container">
        <div className="grid">
          <div className="col-12 col-md-12 col-lg-10 col-xl-11">
            <div className="main-layout-sub">{<Outlet />} </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default LayoutApp;
