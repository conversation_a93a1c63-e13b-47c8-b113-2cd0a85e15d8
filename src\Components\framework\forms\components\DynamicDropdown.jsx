import React, { useState, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { Dropdown } from 'primereact/dropdown';
import FloatLabel from './FloatLabel';

const DynamicDropdown = ({
  value,
  onChange,
  onBlur,
  onFocus,
  onShow,
  onHide,
  options = [],
  optionLabel = 'label',
  optionValue = 'value',
  optionDisabled = 'disabled',
  optionGroupLabel = 'label',
  optionGroupChildren = 'items',
  placeholder = 'Select an option',
  disabled = false,
  required = false,
  editable = false,
  filter = false,
  filterBy = 'label',
  filterPlaceholder = 'Search...',
  filterMatchMode = 'contains',
  showClear = false,
  autoFocus = false,
  className = '',
  style = {},
  panelClassName = '',
  panelStyle = {},
  scrollHeight = '200px',
  id,
  name,
  label,
  floatLabel = false,
  emptyMessage = 'No options available',
  emptyFilterMessage = 'No results found',
  virtualScrollerOptions,
  tooltip,
  size = 'normal', // small, normal, large
  variant = 'outlined', // outlined, filled
  loading = false,
  loadingIcon = 'pi pi-spinner pi-spin',
  itemTemplate,
  valueTemplate,
  panelFooterTemplate,
  onEvent,
  debug = false,
  ...props
}) => {
  const [focused, setFocused] = useState(false);
  const [panelVisible, setPanelVisible] = useState(false);

  // Process options to ensure consistent format
  const processedOptions = useMemo(() => {
    if (!Array.isArray(options)) {
      return [];
    }

    return options.map(option => {
      // Handle string options
      if (typeof option === 'string' || typeof option === 'number') {
        return { label: String(option), value: option };
      }

      // Handle object options
      if (typeof option === 'object' && option !== null) {
        return {
          ...option,
          label: option[optionLabel] || option.label || String(option.value || option),
          value: option[optionValue] !== undefined ? option[optionValue] : option.value,
          disabled: option[optionDisabled] || option.disabled || false
        };
      }

      return { label: String(option), value: option };
    });
  }, [options, optionLabel, optionValue, optionDisabled]);

  const handleChange = useCallback((e) => {
    const newValue = e.value;
    
    if (debug) {
      console.log('DynamicDropdown change:', { name, value: newValue, option: e.target.selectedOption });
    }

    if (onChange) {
      onChange(newValue, e);
    }

    if (onEvent) {
      onEvent('change', { 
        value: newValue, 
        name, 
        option: e.target.selectedOption,
        event: e 
      });
    }
  }, [onChange, onEvent, name, debug]);

  const handleBlur = useCallback((e) => {
    setFocused(false);
    
    if (onBlur) {
      onBlur(e);
    }

    if (onEvent) {
      onEvent('blur', { value, name, event: e });
    }
  }, [onBlur, onEvent, value, name]);

  const handleFocus = useCallback((e) => {
    setFocused(true);
    
    if (onFocus) {
      onFocus(e);
    }

    if (onEvent) {
      onEvent('focus', { value, name, event: e });
    }
  }, [onFocus, onEvent, value, name]);

  const handleShow = useCallback((e) => {
    setPanelVisible(true);
    
    if (onShow) {
      onShow(e);
    }

    if (onEvent) {
      onEvent('show', { value, name, event: e });
    }
  }, [onShow, onEvent, value, name]);

  const handleHide = useCallback((e) => {
    setPanelVisible(false);
    
    if (onHide) {
      onHide(e);
    }

    if (onEvent) {
      onEvent('hide', { value, name, event: e });
    }
  }, [onHide, onEvent, value, name]);

  // Build CSS classes
  const dropdownClasses = [
    'dynamic-dropdown',
    `dynamic-dropdown--${size}`,
    `dynamic-dropdown--${variant}`,
    focused ? 'dynamic-dropdown--focused' : '',
    panelVisible ? 'dynamic-dropdown--open' : '',
    disabled ? 'dynamic-dropdown--disabled' : '',
    required ? 'dynamic-dropdown--required' : '',
    loading ? 'dynamic-dropdown--loading' : '',
    className
  ].filter(Boolean).join(' ');

  // Find selected option for display
  const selectedOption = useMemo(() => {
    return processedOptions.find(option => option.value === value);
  }, [processedOptions, value]);

  // Custom item template
  const defaultItemTemplate = useCallback((option) => {
    if (itemTemplate && typeof itemTemplate === 'function') {
      return itemTemplate(option);
    }

    return (
      <div className="dropdown-item">
        <span className="dropdown-item__label">{option.label}</span>
        {option.description && (
          <small className="dropdown-item__description">{option.description}</small>
        )}
      </div>
    );
  }, [itemTemplate]);

  // Custom value template
  const defaultValueTemplate = useCallback((option, props) => {
    if (valueTemplate && typeof valueTemplate === 'function') {
      return valueTemplate(option, props);
    }

    if (!option) {
      return <span className="dropdown-placeholder">{placeholder}</span>;
    }

    return (
      <div className="dropdown-value">
        <span className="dropdown-value__label">{option.label}</span>
      </div>
    );
  }, [valueTemplate, placeholder]);

  // Dropdown props
  const dropdownProps = {
    id: id || name,
    name,
    value,
    options: processedOptions,
    onChange: handleChange,
    onBlur: handleBlur,
    onFocus: handleFocus,
    onShow: handleShow,
    onHide: handleHide,
    placeholder,
    disabled,
    required,
    editable,
    filter,
    filterBy,
    filterPlaceholder,
    filterMatchMode,
    showClear,
    autoFocus,
    className: dropdownClasses,
    style,
    panelClassName,
    panelStyle,
    scrollHeight,
    emptyMessage,
    emptyFilterMessage,
    virtualScrollerOptions,
    tooltip,
    itemTemplate: defaultItemTemplate,
    valueTemplate: defaultValueTemplate,
    panelFooterTemplate,
    'aria-label': label || placeholder,
    'aria-required': required,
    'data-component-type': 'dropdown',
    'data-field-name': name,
    ...props
  };

  // Remove undefined props
  Object.keys(dropdownProps).forEach(key => {
    if (dropdownProps[key] === undefined) {
      delete dropdownProps[key];
    }
  });

  const renderDropdown = () => <Dropdown {...dropdownProps} />;

  const renderWithFloatLabel = (dropdown) => {
    if (!floatLabel || !label) {
      return dropdown;
    }

    return (
      <FloatLabel>
        {dropdown}
        <label htmlFor={id || name}>{label}</label>
      </FloatLabel>
    );
  };

  let finalDropdown = renderDropdown();
  finalDropdown = renderWithFloatLabel(finalDropdown);

  return (
    <div className="dynamic-dropdown-wrapper">
      {debug && (
        <div className="dynamic-dropdown__debug">
          <small>
            Value: {JSON.stringify(value)} | 
            Options: {processedOptions.length} | 
            Selected: {selectedOption?.label || 'None'} |
            Panel: {panelVisible ? 'Open' : 'Closed'}
          </small>
        </div>
      )}
      {finalDropdown}
    </div>
  );
};

DynamicDropdown.propTypes = {
  value: PropTypes.any,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onFocus: PropTypes.func,
  onShow: PropTypes.func,
  onHide: PropTypes.func,
  options: PropTypes.array,
  optionLabel: PropTypes.string,
  optionValue: PropTypes.string,
  optionDisabled: PropTypes.string,
  optionGroupLabel: PropTypes.string,
  optionGroupChildren: PropTypes.string,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  editable: PropTypes.bool,
  filter: PropTypes.bool,
  filterBy: PropTypes.string,
  filterPlaceholder: PropTypes.string,
  filterMatchMode: PropTypes.string,
  showClear: PropTypes.bool,
  autoFocus: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object,
  panelClassName: PropTypes.string,
  panelStyle: PropTypes.object,
  scrollHeight: PropTypes.string,
  id: PropTypes.string,
  name: PropTypes.string,
  label: PropTypes.string,
  floatLabel: PropTypes.bool,
  emptyMessage: PropTypes.string,
  emptyFilterMessage: PropTypes.string,
  virtualScrollerOptions: PropTypes.object,
  tooltip: PropTypes.string,
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  variant: PropTypes.oneOf(['outlined', 'filled']),
  loading: PropTypes.bool,
  loadingIcon: PropTypes.string,
  itemTemplate: PropTypes.func,
  valueTemplate: PropTypes.func,
  panelFooterTemplate: PropTypes.func,
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicDropdown;
