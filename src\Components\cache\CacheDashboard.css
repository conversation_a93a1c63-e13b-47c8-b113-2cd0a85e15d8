
.cache-dashboard {
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-6);
  background: var(--theme-bg-primary);
  color: var(--theme-text-primary);
}

.cache-dashboard--loading,
.cache-dashboard--error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.cache-dashboard__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
  padding-bottom: var(--spacing-4);
  border-bottom: 1px solid var(--theme-border-primary);
}

.cache-dashboard__title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--theme-text-primary);
  margin: 0;
}

.cache-dashboard__actions {
  display: flex;
  gap: var(--spacing-3);
}

/* Health Status */
.cache-health-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-4);
  padding: var(--spacing-4);
  border-radius: var(--border-radius-lg);
  margin-bottom: var(--spacing-6);
  border: 2px solid;
}

.cache-health-status--healthy {
  background: var(--color-success-50);
  border-color: var(--color-success-200);
  color: var(--color-success-800);
}

.cache-health-status--warning {
  background: var(--color-warning-50);
  border-color: var(--color-warning-200);
  color: var(--color-warning-800);
}

.cache-health-status--error {
  background: var(--color-error-50);
  border-color: var(--color-error-200);
  color: var(--color-error-800);
}

.cache-health-status--unknown {
  background: var(--color-gray-50);
  border-color: var(--color-gray-200);
  color: var(--color-gray-800);
}

.cache-health-status__icon {
  font-size: var(--font-size-2xl);
}

.cache-health-status__info h3 {
  margin: 0 0 var(--spacing-2) 0;
  font-size: var(--font-size-lg);
}

.cache-health-issues {
  list-style: none;
  padding: 0;
  margin: 0;
}

.cache-health-issue {
  padding: var(--spacing-1) 0;
  font-size: var(--font-size-sm);
}

.cache-health-issue--error {
  color: var(--color-error-700);
}

.cache-health-issue--warning {
  color: var(--color-warning-700);
}

/* Tabs */
.cache-dashboard__tabs {
  display: flex;
  border-bottom: 1px solid var(--theme-border-primary);
  margin-bottom: var(--spacing-6);
}

.cache-tab {
  background: none;
  border: none;
  padding: var(--spacing-3) var(--spacing-6);
  cursor: pointer;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-secondary);
  border-bottom: 2px solid transparent;
  transition: var(--transition-colors);
}

.cache-tab:hover {
  color: var(--theme-text-primary);
  background: var(--theme-bg-secondary);
}

.cache-tab--active {
  color: var(--theme-primary);
  border-bottom-color: var(--theme-primary);
}

/* Content */
.cache-dashboard__content {
  min-height: 400px;
}

/* Overview */
.cache-metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-8);
}

.cache-metric-card {
  background: var(--theme-bg-card);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  text-align: center;
  transition: var(--transition-all);
}

.cache-metric-card:hover {
  box-shadow: var(--theme-card-hover-shadow);
  transform: translateY(-2px);
}

.cache-metric-card h3 {
  margin: 0 0 var(--spacing-3) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-secondary);
}

.cache-metric-value {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--theme-text-primary);
  margin-bottom: var(--spacing-2);
}

.cache-metric-status {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-1) var(--spacing-3);
  border-radius: var(--border-radius-full);
}

.cache-metric-status.good {
  background: var(--color-success-100);
  color: var(--color-success-800);
}

.cache-metric-status.warning {
  background: var(--color-warning-100);
  color: var(--color-warning-800);
}

.cache-metric-status.error {
  background: var(--color-error-100);
  color: var(--color-error-800);
}

/* Cache Types Overview */
.cache-types-overview h3 {
  margin-bottom: var(--spacing-4);
  color: var(--theme-text-primary);
}

.cache-types-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-3);
}

.cache-type-card {
  background: var(--theme-bg-card);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--border-radius-base);
  padding: var(--spacing-4);
}

.cache-type-card h4 {
  margin: 0 0 var(--spacing-2) 0;
  font-size: var(--font-size-base);
  color: var(--theme-text-primary);
}

.cache-type-stats {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

.cache-type-stats div {
  margin-bottom: var(--spacing-1);
}

/* Analytics */
.cache-analytics__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-6);
}

.analytics-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.analytics-stat {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-3);
  background: var(--theme-bg-card);
  border-radius: var(--border-radius-base);
  border: 1px solid var(--theme-border-primary);
}

.analytics-stat label {
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-secondary);
}

.analytics-stat span {
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
}

.analytics-operations h4,
.analytics-patterns h4,
.analytics-recommendations h4 {
  margin-bottom: var(--spacing-4);
  color: var(--theme-text-primary);
}

.operations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.operation-stat {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-2) var(--spacing-3);
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-base);
}

.patterns-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--border-radius-base);
}

.pattern-item {
  padding: var(--spacing-3);
  border-bottom: 1px solid var(--theme-border-primary);
}

.pattern-item:last-child {
  border-bottom: none;
}

.pattern-key {
  font-family: var(--font-mono);
  font-size: var(--font-size-sm);
  color: var(--theme-text-primary);
  margin-bottom: var(--spacing-2);
}

.pattern-stats {
  display: flex;
  gap: var(--spacing-4);
  font-size: var(--font-size-xs);
  color: var(--theme-text-secondary);
}

.recommendations-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
}

.recommendation {
  padding: var(--spacing-4);
  border-radius: var(--border-radius-base);
  border-left: 4px solid;
}

.recommendation--high {
  background: var(--color-error-50);
  border-left-color: var(--color-error-500);
  color: var(--color-error-800);
}

.recommendation--medium {
  background: var(--color-warning-50);
  border-left-color: var(--color-warning-500);
  color: var(--color-warning-800);
}

.recommendation--low {
  background: var(--color-blue-50);
  border-left-color: var(--color-blue-500);
  color: var(--color-blue-800);
}

.recommendation-type {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  text-transform: uppercase;
  margin-bottom: var(--spacing-1);
}

/* Storage */
.storage-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.storage-stat {
  display: flex;
  justify-content: space-between;
  padding: var(--spacing-4);
  background: var(--theme-bg-card);
  border-radius: var(--border-radius-base);
  border: 1px solid var(--theme-border-primary);
}

.storage-breakdown h4 {
  margin-bottom: var(--spacing-4);
  color: var(--theme-text-primary);
}

.storage-types {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-3);
  margin-bottom: var(--spacing-6);
}

.storage-type {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-3);
  background: var(--theme-bg-card);
  border-radius: var(--border-radius-base);
  border: 1px solid var(--theme-border-primary);
}

.storage-type-header {
  display: flex;
  gap: var(--spacing-4);
  align-items: center;
}

.storage-type-name {
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
}

.storage-type-size {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
}

/* Management */
.management-section {
  margin-bottom: var(--spacing-8);
}

.management-section h4 {
  margin-bottom: var(--spacing-4);
  color: var(--theme-text-primary);
}

.management-actions {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

.import-export-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-4);
}

.export-section,
.import-section {
  display: flex;
  gap: var(--spacing-3);
  align-items: center;
  flex-wrap: wrap;
}

.file-input {
  padding: var(--spacing-2);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--border-radius-base);
  background: var(--theme-input-bg);
  color: var(--theme-input-text);
}

.config-info {
  padding: var(--spacing-4);
  background: var(--theme-bg-secondary);
  border-radius: var(--border-radius-base);
  color: var(--theme-text-secondary);
}

.config-info p {
  margin: 0 0 var(--spacing-2) 0;
}

.config-info p:last-child {
  margin-bottom: 0;
}

/* Buttons */
.btn {
  background: var(--theme-button-primary-bg);
  border: none;
  border-radius: var(--border-radius-base);
  padding: var(--spacing-2) var(--spacing-4);
  color: var(--theme-button-primary-text);
  cursor: pointer;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-colors);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
}

.btn:hover {
  background: var(--theme-button-primary-hover);
}

.btn:disabled {
  background: var(--theme-button-primary-disabled);
  cursor: not-allowed;
  opacity: 0.6;
}

.btn--secondary {
  background: var(--theme-button-secondary-bg);
  border: 1px solid var(--theme-button-secondary-border);
  color: var(--theme-button-secondary-text);
}

.btn--secondary:hover {
  background: var(--theme-button-secondary-hover);
}

.btn--warning {
  background: var(--color-warning-500);
  color: white;
}

.btn--warning:hover {
  background: var(--color-warning-600);
}

.btn--small {
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
}

/* Loading Spinner */
.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid var(--theme-border-primary);
  border-top-color: var(--theme-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-4);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .cache-dashboard {
    padding: var(--spacing-4);
  }
  
  .cache-dashboard__header {
    flex-direction: column;
    gap: var(--spacing-4);
    align-items: stretch;
  }
  
  .cache-dashboard__actions {
    justify-content: center;
  }
  
  .cache-metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .analytics-summary {
    grid-template-columns: 1fr;
  }
  
  .storage-overview {
    grid-template-columns: 1fr;
  }
  
  .management-actions {
    flex-direction: column;
  }
  
  .export-section,
  .import-section {
    flex-direction: column;
    align-items: stretch;
  }
}
