import React, { useState } from 'react';
import { Tab<PERSON>iew, TabPanel } from 'primereact/tabview';
import { Card } from 'primereact/card';
import { But<PERSON> } from 'primereact/button';
import LeadManagement from './LeadManagement';
import LeadManagementDynamic from './LeadManagementDynamic';
import './LeadManagementDemo.css';

/**
 * LeadManagementDemo - Comparison page showing both implementations
 */
const LeadManagementDemo = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const [showComparison, setShowComparison] = useState(true);

  return (
    <div className="lead-management-demo">
      {showComparison && (
        <Card className="demo-info-card">
          <div className="demo-header">
            <h1>Lead Management Page - Implementation Comparison</h1>
            <Button 
              icon="pi pi-times" 
              className="p-button-text p-button-rounded"
              onClick={() => setShowComparison(false)}
              tooltip="Hide comparison info"
            />
          </div>
          
          <div className="demo-description">
            <p>
              This demo showcases two different approaches to building the same Lead Management page:
            </p>
            
            <div className="comparison-grid">
              <div className="comparison-item">
                <div className="comparison-header">
                  <i className="pi pi-code" style={{ fontSize: '2rem', color: '#2196F3' }}></i>
                  <h3>Traditional React</h3>
                </div>
                <ul>
                  <li>Direct PrimeReact component usage</li>
                  <li>Explicit state management with useState</li>
                  <li>Traditional form handling</li>
                  <li>Full control over component behavior</li>
                  <li>Best for custom, one-off implementations</li>
                </ul>
              </div>

              <div className="comparison-item">
                <div className="comparison-header">
                  <i className="pi pi-cog" style={{ fontSize: '2rem', color: '#4CAF50' }}></i>
                  <h3>Dynamic Framework</h3>
                </div>
                <ul>
                  <li>JSON-driven form configuration</li>
                  <li>Reusable component patterns</li>
                  <li>Built-in validation support</li>
                  <li>Less boilerplate code</li>
                  <li>Best for scalable, maintainable apps</li>
                </ul>
              </div>
            </div>

            <div className="features-section">
              <h3>Page Features Demonstrated:</h3>
              <div className="features-grid">
                <div className="feature-item">
                  <i className="pi pi-check-circle"></i>
                  <span>Wizard/Steps Component</span>
                </div>
                <div className="feature-item">
                  <i className="pi pi-check-circle"></i>
                  <span>Multi-column Forms</span>
                </div>
                <div className="feature-item">
                  <i className="pi pi-check-circle"></i>
                  <span>TabView Organization</span>
                </div>
                <div className="feature-item">
                  <i className="pi pi-check-circle"></i>
                  <span>Rating Component</span>
                </div>
                <div className="feature-item">
                  <i className="pi pi-check-circle"></i>
                  <span>Dropdown Selectors</span>
                </div>
                <div className="feature-item">
                  <i className="pi pi-check-circle"></i>
                  <span>Action Buttons</span>
                </div>
                <div className="feature-item">
                  <i className="pi pi-check-circle"></i>
                  <span>Toast Notifications</span>
                </div>
                <div className="feature-item">
                  <i className="pi pi-check-circle"></i>
                  <span>Responsive Design</span>
                </div>
              </div>
            </div>

            <div className="code-comparison">
              <h3>Code Comparison Example:</h3>
              <div className="code-grid">
                <div className="code-block">
                  <h4>Traditional Approach</h4>
                  <pre>{`<div className="field">
  <label htmlFor="company">Company</label>
  <InputText 
    id="company" 
    value={formData.company}
    onChange={(e) => 
      handleInputChange('company', e.target.value)
    }
  />
  <small className="field-hint">
    (Helper text here...)
  </small>
</div>`}</pre>
                </div>

                <div className="code-block">
                  <h4>Dynamic Framework Approach</h4>
                  <pre>{`{
  name: 'company',
  type: 'input-text',
  label: 'Company',
  required: false,
  props: {
    placeholder: 'Enter company name'
  },
  helpText: '(Helper text here...)'
}`}</pre>
                </div>
              </div>
            </div>
          </div>
        </Card>
      )}

      <div className="demo-tabs">
        <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
          <TabPanel 
            header="Traditional React Implementation" 
            leftIcon="pi pi-code"
          >
            <div className="implementation-wrapper">
              <div className="implementation-badge traditional">
                <i className="pi pi-code"></i>
                <span>Traditional React with PrimeReact</span>
              </div>
              <LeadManagement />
            </div>
          </TabPanel>

          <TabPanel 
            header="Dynamic Framework Implementation" 
            leftIcon="pi pi-cog"
          >
            <div className="implementation-wrapper">
              <div className="implementation-badge dynamic">
                <i className="pi pi-cog"></i>
                <span>Dynamic Component Framework</span>
              </div>
              <LeadManagementDynamic />
            </div>
          </TabPanel>

          <TabPanel 
            header="Documentation" 
            leftIcon="pi pi-book"
          >
            <Card title="Implementation Guide">
              <div className="documentation-content">
                <h2>Getting Started</h2>
                <p>
                  Both implementations produce identical UI and functionality. Choose the approach
                  that best fits your project requirements.
                </p>

                <h3>When to Use Traditional React</h3>
                <ul>
                  <li>Building unique, one-off pages</li>
                  <li>Need maximum control over component behavior</li>
                  <li>Team is more comfortable with traditional React patterns</li>
                  <li>Prototyping or proof-of-concept work</li>
                </ul>

                <h3>When to Use Dynamic Framework</h3>
                <ul>
                  <li>Building multiple similar forms across the application</li>
                  <li>Need to generate forms from backend configuration</li>
                  <li>Want consistent validation and error handling</li>
                  <li>Building a form builder or admin panel</li>
                  <li>Need to version and A/B test form layouts</li>
                </ul>

                <h3>File Structure</h3>
                <pre className="file-structure">{`src/pages/
├── LeadManagement.jsx          # Traditional implementation
├── LeadManagementDynamic.jsx   # Dynamic framework implementation
├── LeadManagement.css          # Shared styling
├── LeadManagementDemo.jsx      # This demo page
└── README.md                   # Detailed documentation`}</pre>

                <h3>Key Components</h3>
                <div className="components-list">
                  <div className="component-card">
                    <h4>Steps (Wizard)</h4>
                    <p>Multi-step process visualization for lead lifecycle</p>
                  </div>
                  <div className="component-card">
                    <h4>TabView</h4>
                    <p>Organized content sections (Address, Contacts, etc.)</p>
                  </div>
                  <div className="component-card">
                    <h4>Form Components</h4>
                    <p>Input fields, dropdowns, textareas, rating</p>
                  </div>
                  <div className="component-card">
                    <h4>Action Buttons</h4>
                    <p>Edit, Create, Mark as Won/Lost operations</p>
                  </div>
                </div>

                <h3>Customization</h3>
                <p>
                  Both implementations can be easily customized:
                </p>
                <ul>
                  <li>Modify <code>LeadManagement.css</code> for styling changes</li>
                  <li>Add new fields by updating form configuration</li>
                  <li>Extend with additional tabs for more content sections</li>
                  <li>Integrate with backend APIs for data persistence</li>
                  <li>Add validation rules and error handling</li>
                </ul>

                <h3>Next Steps</h3>
                <ol>
                  <li>Review the source code of both implementations</li>
                  <li>Try modifying the form fields and see the changes</li>
                  <li>Integrate with your backend API</li>
                  <li>Add data tables for address and contact lists</li>
                  <li>Implement full CRUD operations</li>
                </ol>
              </div>
            </Card>
          </TabPanel>
        </TabView>
      </div>
    </div>
  );
};

export default LeadManagementDemo;
