import BaseController from '../BaseController.js';
import LeadLogService from '../../services/leadmanagement/LeadLog.js';
import LeadLogModel from '../../models/leadmanagement/LeadLogModel.js';

class LeadLogController extends BaseController {
  constructor() {
    super();
    this.state = {
      logs: [],
      loading: false,
      error: null
    };
  }

  async fetchLogs() {
    return this.executeAction('fetchLogs', async () => {
      const data = await LeadLogService.getAll();
      const logs = Array.isArray(data) ? data.map(l => new LeadLogModel(l)) : [];
      this.setState({ logs });
      return logs;
    });
  }

  async saveLog(logData) {
    return this.executeAction('saveLog', async () => {
      const saved = await LeadLogService.save(logData);
      await this.fetchLogs();
      return saved;
    });
  }

  async updateLog(id, logData) {
    return this.executeAction('updateLog', async () => {
      const updated = await LeadLogService.update(id, logData);
      await this.fetchLogs();
      return updated;
    });
  }

  async deleteLog(id) {
    return this.executeAction('deleteLog', async () => {
      await LeadLogService.deleteById(id);
      await this.fetchLogs();
    });
  }
}

export default new LeadLogController();
