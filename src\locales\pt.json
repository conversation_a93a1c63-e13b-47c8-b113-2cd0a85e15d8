{"nav": {"home": "Início", "dashboard": "<PERSON><PERSON>", "components": "Componentes", "forms": "Formulários", "tables": "<PERSON><PERSON><PERSON>", "settings": "Configurações", "help": "<PERSON><PERSON><PERSON>", "logout": "<PERSON><PERSON>"}, "common": {"loading": "Carregando...", "error": "Erro", "success": "Sucesso", "warning": "Aviso", "info": "Informação", "cancel": "<PERSON><PERSON><PERSON>", "ok": "OK", "yes": "<PERSON>m", "no": "Não", "save": "<PERSON><PERSON>", "delete": "Excluir", "edit": "<PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Remover", "search": "<PERSON><PERSON><PERSON><PERSON>", "filter": "Filtrar", "clear": "Limpar", "reset": "Redefinir", "submit": "Enviar", "close": "<PERSON><PERSON><PERSON>", "back": "Voltar", "next": "Próximo", "previous": "Anterior", "select": "Selecionar", "selectAll": "Selecionar tudo", "deselectAll": "<PERSON><PERSON><PERSON> tudo"}, "navigation": {"home": "Início", "dashboard": "<PERSON><PERSON>", "profile": "Perfil", "settings": "Configurações", "users": "Usuários", "admin": "Admin", "help": "<PERSON><PERSON><PERSON>", "about": "Sobre", "contact": "Contato", "logout": "<PERSON><PERSON>", "login": "Entrar", "register": "Registrar"}, "theme": {"selectTheme": "Selecionar tema", "lightMode": "<PERSON>do claro", "darkMode": "<PERSON><PERSON> es<PERSON>ro", "customTheme": "<PERSON><PERSON>", "applyTheme": "Aplicar <PERSON>"}, "language": {"selectLanguage": "Selecionar idioma", "currentLanguage": "Idioma atual"}, "user": {"profile": "Perfil do usuário", "account": "Conta", "personalInfo": "Informações pessoais", "contactInfo": "Informações de contato", "preferences": "Preferências", "security": "Segurança", "privacy": "Privacidade", "notifications": "Notificações", "firstName": "Nome", "lastName": "Sobrenome", "fullName": "Nome completo", "displayName": "Nome de exibição", "bio": "Biografia", "avatar": "Avatar", "role": "Função", "permissions": "Permissões", "lastLogin": "<PERSON><PERSON>imo login", "joinDate": "Data de cadastro", "emailVerified": "<PERSON>ail verificado", "phoneVerified": "Telefone verificado", "twoFactorEnabled": "Autenticação de dois fatores habilitada"}, "form": {"required": "Este campo é obrigatório", "invalid": "Valor <PERSON>", "minLength": "O comprimento mínimo é {min} caracteres", "maxLength": "O comprimento máximo é {max} caracteres", "email": "Digite um endereço de email válido", "number": "Digite um número válido", "date": "Digite uma data válida", "url": "Digite uma URL válida", "phone": "Digite um número de telefone válido"}, "datatable": {"noRecords": "Nenhum registro encontrado", "loading": "Carregando dados...", "rowsPerPage": "<PERSON><PERSON> por página", "of": "de", "first": "<PERSON><PERSON>", "last": "Última", "next": "Próxima", "previous": "Anterior", "sortAscending": "Ordenar crescente", "sortDescending": "Ordenar decrescente", "filter": "Filtrar", "globalFilter": "Busca global", "export": "Exportar", "print": "Imprimir"}, "demo": {"title": "Demo do Framework de Componentes Dinâmicos", "description": "Explore o framework completo de componentes com suporte a temas e idiomas", "componentShowcase": "Vitrine de Componentes", "formComponents": "Componentes de Formulário", "layoutComponents": "Componentes de Layout", "dataComponents": "Componentes de Dados", "messageComponents": "Componentes de Mensagem"}, "forms": {"validation": {"required": "Este campo é obrigatório", "email": "Digite um endereço de email válido", "password": "A senha deve ter pelo menos 8 caracteres", "passwordMatch": "As senhas não coincidem", "minLength": "<PERSON>e ter pelo menos {min} caracteres", "maxLength": "<PERSON>ão deve ter mais de {max} caracteres", "numeric": "Deve ser um número", "alphanumeric": "Deve conter apenas letras e números", "phone": "Digite um número de telefone válido", "url": "Digite uma URL válida", "date": "Digite uma data válida", "time": "Digite um horário válido", "fileSize": "O tamanho do arquivo deve ser menor que {size}MB", "fileType": "Tipo de arquivo inválido. Tipos permitidos: {types}", "unique": "Este valor já existe", "min": "O valor deve ser pelo menos {min}", "max": "O valor não deve ser maior que {max}", "pattern": "Formato inválido", "custom": "Valor <PERSON>"}, "placeholders": {"enterName": "Digite seu nome", "enterEmail": "Digite seu email", "enterPassword": "Digite sua senha", "confirmPassword": "Confirme sua senha", "enterPhone": "Digite seu telefone", "enterAddress": "Digite seu endereço", "selectOption": "Selecione uma opção", "searchHere": "Pesquisar aqui...", "typeMessage": "Digite sua mensagem...", "selectDate": "Selecionar data", "selectTime": "Selecionar horário", "uploadFile": "Enviar arquivo", "dragDropFile": "Arraste e solte o arquivo aqui"}}}