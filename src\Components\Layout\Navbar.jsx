import React, { useState, useRef, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Avatar } from 'primereact/avatar';
import { Badge } from 'primereact/badge';
import { Menu } from 'primereact/menu';
import { Button } from 'primereact/button';
import LanguageSwitcher from '@components/common/LanguageSwitcher';
import ThemeSelector from '@components/common/ThemeSelector';
import '../../styles/components/theme-selector.css';
import { useAuthContext } from '@contexts/AuthContext';

const Navbar = ({ cartItemCount = 0 }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const menuRef = useRef(null);
  const [activeItem, setActiveItem] = useState(location.pathname);
  const { t } = useTranslation();
  const { logout } = useAuthContext();

  const menuItems = [
    { label: t('navigation.dashboard'), icon: 'pi pi-th-large', path: '/dashboard' },
    { label: t('navigation.orders'), icon: 'pi pi-shopping-cart', path: '/orders' },
    { label: t('navigation.companies'), icon: 'pi pi-building', path: '/companies' },
    { label: t('navigation.vendors'), icon: 'pi pi-users', path: '/vendors' },
    { label: t('navigation.clients'), icon: 'pi pi-user', path: '/clients' },
    { label: t('navigation.sales'), icon: 'pi pi-dollar', path: '/sales' },
    { label: t('navigation.subscription'), icon: 'pi pi-chart-bar', path: '/subscription' },
    { label: t('navigation.masterSetting'), icon: 'pi pi-cog', path: '/master-setting' },

  ];


  useEffect(() => {
    setActiveItem(location.pathname);
  }, [location.pathname]);


  const handleLogout = () => {
    logout();
    navigate('/login');
  };

  const companyMenuItems = [
    { label: t('navigation.profile'), icon: 'pi pi-user', command: () => window.location.href = '/profile' },
    { label: t('navigation.settings'), icon: 'pi pi-cog' },
    { label: t('auth.logout'), icon: 'pi pi-sign-out', command: handleLogout }
  ];

  const handleMenuClick = (path) => {
    setActiveItem(path);
  };


  return (
    <div className="navbar-container">
      {/* Logo Section */}
      <div className="navbar-logo">
        <Link to="/">
          <img src="/images/orglogo.png" alt="R-Filings Logo" />
        </Link>
      </div>

      {/* Menu Section */}
      <div className="navbar-menu">
        {menuItems.map((item) => (
          <Link
            key={item.path}
            to={item.path}
            className={`menu-item ${location.pathname.startsWith(item.path) ? 'active' : ''}`}
            onClick={() => handleMenuClick(item.path)}
          >
            <i className={item.icon}></i>
            <span>{item.label}</span>
          </Link>
        ))}
      </div>


      {/* Profile Section */}
      <div className="navbar-profile">
        <ThemeSelector />
        <LanguageSwitcher />
        <Button
          className="company-selector-btn"
          onClick={(e) => menuRef.current.toggle(e)}
        >
          <Avatar className="company-avatar" label="RF" />
          <span>Choose Company</span>
          <i className="pi pi-chevron-down"></i>
        </Button>
        <Menu
          model={companyMenuItems}
          popup
          ref={menuRef}
          className="company-menu"
        />
        <Link to="/dashboard" className="cart-icon cart-badge">
          <i className="pi pi-shopping-cart"></i>
          {cartItemCount > 0 && (
            <Badge value={cartItemCount} severity="danger" />
          )}
        </Link>
      </div>
    </div>
  );
};

export default Navbar;
