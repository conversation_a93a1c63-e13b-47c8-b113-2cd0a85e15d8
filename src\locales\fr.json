{"nav": {"home": "Accueil", "dashboard": "Tableau de bord", "components": "Composants", "forms": "Formulaires", "tables": "Tableaux", "settings": "Paramètres", "help": "Aide", "logout": "Déconnexion"}, "common": {"loading": "Chargement...", "error": "<PERSON><PERSON><PERSON>", "success": "Su<PERSON>ès", "warning": "Avertissement", "info": "Information", "cancel": "Annuler", "ok": "OK", "yes": "O<PERSON>", "no": "Non", "save": "Enregistrer", "delete": "<PERSON><PERSON><PERSON><PERSON>", "edit": "Modifier", "add": "Ajouter", "remove": "<PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON><PERSON>", "reset": "Réinitialiser", "submit": "So<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "back": "Retour", "next": "Suivant", "previous": "Précédent", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "selectAll": "<PERSON><PERSON>", "deselectAll": "<PERSON><PERSON>"}, "navigation": {"home": "Accueil", "dashboard": "Tableau de bord", "profile": "Profil", "settings": "Paramètres", "users": "Utilisateurs", "admin": "Admin", "help": "Aide", "about": "À propos", "contact": "Contact", "logout": "Déconnexion", "login": "Connexion", "register": "S'inscrire"}, "theme": {"selectTheme": "Sé<PERSON><PERSON>ner un thème", "lightMode": "Mode clair", "darkMode": "Mode sombre", "customTheme": "<PERSON><PERSON><PERSON>", "applyTheme": "App<PERSON>r le Thème"}, "language": {"selectLanguage": "Sélectionner la Langue", "currentLanguage": "<PERSON>ue Actuelle"}, "user": {"profile": "Profil utilisateur", "account": "<PERSON><PERSON><PERSON>", "personalInfo": "Informations personnelles", "contactInfo": "Informations de contact", "preferences": "Préférences", "security": "Sécurité", "privacy": "Confidentialité", "notifications": "Notifications", "firstName": "Prénom", "lastName": "Nom de famille", "fullName": "Nom complet", "displayName": "Nom d'affichage", "bio": "Biographie", "avatar": "Avatar", "role": "R<PERSON><PERSON>", "permissions": "Permissions", "lastLogin": "Dernière connexion", "joinDate": "Date d'inscription", "emailVerified": "<PERSON>ail v<PERSON>", "phoneVerified": "Téléphone vérifié", "twoFactorEnabled": "Authentification à deux facteurs activée"}, "form": {"required": "Ce champ est requis", "invalid": "<PERSON><PERSON> invalide", "minLength": "La longueur minimale est de {min} caractères", "maxLength": "La longueur maximale est de {max} caractères", "email": "Veuillez saisir une adresse email valide", "number": "Veuillez saisir un nombre valide", "date": "Veuillez saisir une date valide", "url": "Veuillez saisir une URL valide", "phone": "Veuillez saisir un numéro de téléphone valide"}, "datatable": {"noRecords": "Aucun enregistrement trouvé", "loading": "Chargement des données...", "rowsPerPage": "Lignes par page", "of": "de", "first": "Premier", "last": "<PERSON><PERSON>", "next": "Suivant", "previous": "Précédent", "sortAscending": "Tri croissant", "sortDescending": "Tri décrois<PERSON>t", "filter": "<PERSON><PERSON><PERSON>", "globalFilter": "Recherche globale", "export": "Exporter", "print": "<PERSON><PERSON><PERSON><PERSON>"}, "demo": {"title": "Démo du Framework de Composants Dynamiques", "description": "Explorez le framework complet de composants avec support des thèmes et langues", "componentShowcase": "Vitrine des composants", "formComponents": "Composants de Formulaire", "layoutComponents": "Composants de Mise en Page", "dataComponents": "Composants de Données", "messageComponents": "Composants de Message"}, "forms": {"validation": {"required": "Ce champ est requis", "email": "Veuillez saisir une adresse email valide", "password": "Le mot de passe doit contenir au moins 8 caractères", "passwordMatch": "Les mots de passe ne correspondent pas", "minLength": "Doit contenir au moins {min} caractères", "maxLength": "Ne doit pas dépasser {max} caractères", "numeric": "Doit être un nombre", "alphanumeric": "Doit contenir uniquement des lettres et des chiffres", "phone": "Veuillez saisir un numéro de téléphone valide", "url": "Veuillez saisir une URL valide", "date": "Veuillez saisir une date valide", "time": "Veuillez saisir une heure valide", "fileSize": "La taille du fichier doit être inférieure à {size}MB", "fileType": "Type de fichier invalide. Types autorisés : {types}", "unique": "Cette valeur existe déjà", "min": "La valeur doit être au moins {min}", "max": "La valeur ne doit pas dépasser {max}", "pattern": "Format invalide", "custom": "<PERSON><PERSON> invalide"}, "placeholders": {"enterName": "Saisissez votre nom", "enterEmail": "Saisissez votre email", "enterPassword": "Saisissez votre mot de passe", "confirmPassword": "Confirmez votre mot de passe", "enterPhone": "Saisissez votre numéro de téléphone", "enterAddress": "Saisissez votre adresse", "selectOption": "Sélectionnez une option", "searchHere": "Rechercher ici...", "typeMessage": "Tapez votre message...", "selectDate": "Sélectionner une date", "selectTime": "Sélectionner une heure", "uploadFile": "Télécharger un fichier", "dragDropFile": "Glissez<PERSON><PERSON><PERSON><PERSON><PERSON> le fichier ici"}}, "messages": {"success": {"saved": "Enregistré avec succès", "updated": "Mis à jour avec succès", "deleted": "Supprimé avec succès", "created": "<PERSON><PERSON><PERSON> avec succès", "sent": "<PERSON><PERSON><PERSON> a<PERSON> su<PERSON>", "uploaded": "Téléchargé avec succès", "downloaded": "Téléchargé avec succès", "copied": "<PERSON><PERSON><PERSON> avec succès", "imported": "Importé avec succès", "exported": "Exporté avec succès"}, "error": {"general": "Une erreur s'est produite. Veuillez réessayer.", "network": "<PERSON><PERSON><PERSON> réseau. Vérifiez votre connexion.", "server": "Erreur serveur. Veuillez réessayer plus tard.", "notFound": "La ressource demandée n'a pas été trouvée.", "unauthorized": "Vous n'êtes pas autorisé à effectuer cette action.", "forbidden": "<PERSON><PERSON>ès refusé.", "validation": "<PERSON><PERSON><PERSON><PERSON> corriger les erreurs et réessayer.", "timeout": "<PERSON><PERSON><PERSON> d'attente dépassé. Veuillez réessayer.", "fileUpload": "Échec du téléchargement. Veuillez réessayer.", "fileTooLarge": "Le fichier est trop volumineux. Taille maximale : {size}MB.", "invalidFileType": "Type de fichier invalide. Types autorisés : {types}", "sessionExpired": "Votre session a expiré. Veuillez vous reconnecter.", "maintenance": "Le système est en maintenance. Veuillez réessayer plus tard."}, "warning": {"unsavedChanges": "Vous avez des modifications non enregistrées. Êtes-vous sûr de vouloir quitter ?", "deleteConfirm": "Êtes-vous sûr de vouloir supprimer cet élément ?", "irreversible": "Cette action ne peut pas être annulée.", "dataLoss": "Cette action peut entraîner une perte de données.", "slowConnection": "Votre connexion semble lente. Cela peut prendre du temps.", "browserSupport": "Votre navigateur peut ne pas supporter toutes les fonctionnalités.", "cookiesDisabled": "Les cookies sont désactivés. Certaines fonctionnalités peuvent ne pas fonctionner.", "javascriptDisabled": "JavaScript est désactivé. Veuillez l'activer pour une fonctionnalité complète."}, "info": {"noData": "<PERSON><PERSON><PERSON> donnée disponible", "noResults": "Aucun résultat trouvé", "emptyList": "La liste est vide", "processing": "Traitement de votre demande...", "uploading": "Téléchargement du fichier...", "downloading": "Téléchargement du fichier...", "connecting": "Connexion...", "syncing": "Synchronisation des données...", "offline": "Vous êtes actuellement hors ligne", "online": "Vous êtes de nouveau en ligne", "newVersion": "Une nouvelle version est disponible", "updateRequired": "Veuillez mettre à jour pour continuer"}}}