import React, { useState, useRef } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Tag } from 'primereact/tag';
import { TabView, TabPanel } from 'primereact/tabview';
import { FileUpload } from 'primereact/fileupload';
import './ServiceOrdersTable.css';

const ServiceOrdersTable = () => {
  const [expandedRows, setExpandedRows] = useState(null);
  const [globalFilter, setGlobalFilter] = useState('');
  const dt = useRef(null);

  // Sample data matching the image
  const serviceOrders = [
    {
      id: 1,
      time: '14:00',
      arrivalTime: '16:30',
      flightNumber: 'VG573',
      registration: 'UK-73',
      route: { from: 'DMDW', to: 'DRSV' },
      crew: 4,
      pax: 150,
      transport: 'NA',
      hotels: 'NR',
      catering: 'TBD',
      customs: 'Ok',
      migration: 'Ok',
      slotPPR: '14:00z',
      permits: 'NR',
      fuel: { price: 3.33, unit: 'USD/USG' },
      tasks: 'N',
      progress: { catering: 75, migration: 25 },
      status: 'active',
      cateringDetails: {
        departure: { service: 'TBD', for: 'Pax', order: '-', remarks: '-', confirmation: '-' },
        arrival: { service: 'TBD', for: 'Pax', order: '-', remarks: '-', confirmation: '-' }
      }
    },
    {
      id: 2,
      time: '18:45',
      arrivalTime: '21:15',
      flightNumber: 'AN1JH',
      registration: 'ASH-88',
      route: { from: 'ORSV', to: 'DRSV' },
      crew: 6,
      pax: 89,
      transport: 'NR',
      hotels: 'NR',
      catering: 'TBD',
      customs: 'Ok',
      migration: 'Ok',
      slotPPR: '14:00z',
      permits: 'N',
      fuel: { price: 2.85, unit: 'USD/USG' },
      tasks: 'N',
      progress: { catering: 45, migration: 80 },
      status: 'pending',
      cateringDetails: {
        departure: { service: 'TBD', for: 'Pax', order: '-', remarks: '-', confirmation: '-' },
        arrival: { service: 'TBD', for: 'Pax', order: '-', remarks: '-', confirmation: '-' }
      }
    }
  ];

  // Column templates
  const timeBodyTemplate = (rowData) => {
    return (
      <div className="time-cell">
        <div className="departure-time">{rowData.time}</div>
        <div className="arrival-time">{rowData.arrivalTime}</div>
      </div>
    );
  };

  const flightBodyTemplate = (rowData) => {
    return (
      <div className="flight-cell">
        <div className="flight-number">{rowData.flightNumber}</div>
        <div className="registration">{rowData.registration}</div>
      </div>
    );
  };

  const routeBodyTemplate = (rowData) => {
    return (
      <div className="route-cell">
        <span className="route-from">{rowData.route.from}</span>
        <i className="pi pi-arrow-right route-arrow"></i>
        <span className="route-to">{rowData.route.to}</span>
      </div>
    );
  };

  const statusBodyTemplate = (value, field) => (rowData) => {
    const val = rowData[field];
    let severity = 'info';
    
    if (val === 'Ok') severity = 'success';
    else if (val === 'NR') severity = 'danger';
    else if (val === 'TBD') severity = 'warning';
    else if (val === 'NA') severity = 'secondary';
    
    return <Tag value={val} severity={severity} className="status-tag" />;
  };

  const fuelBodyTemplate = (rowData) => {
    return (
      <div className="fuel-cell">
        <div className="fuel-price">{rowData.fuel.price}</div>
        <div className="fuel-unit">{rowData.fuel.unit}</div>
      </div>
    );
  };

  const tasksBodyTemplate = (rowData) => {
    return (
      <div className="tasks-cell">
        <span className={`task-indicator ${rowData.tasks === 'Y' ? 'has-tasks' : ''}`}>
          {rowData.tasks}
        </span>
      </div>
    );
  };

  const actionsBodyTemplate = (rowData) => {
    return (
      <div className="actions-cell">
        <Button icon="pi pi-check-circle" className="p-button-rounded p-button-text" />
        <Button icon="pi pi-copy" className="p-button-rounded p-button-text" />
        <Button icon="pi pi-ellipsis-v" className="p-button-rounded p-button-text" />
      </div>
    );
  };

  // Progress bar template
  const progressBarTemplate = (rowData) => {
    return (
      <div className="progress-bar-container">
        <div className="progress-bar">
          <div 
            className="progress-catering" 
            style={{ width: `${rowData.progress.catering}%` }}
          ></div>
          <div 
            className="progress-migration" 
            style={{ width: `${rowData.progress.migration}%` }}
          ></div>
        </div>
      </div>
    );
  };

  // Row expansion template
  const rowExpansionTemplate = (data) => {
    return (
      <div className="expanded-row-content">
        <TabView>
          <TabPanel header="Hotels">
            <div className="expansion-section">
              <p>Hotels information for {data.flightNumber}</p>
            </div>
          </TabPanel>
          
          <TabPanel header="Catering" leftIcon="pi pi-arrow-right-arrow-left">
            <div className="catering-expansion">
              <div className="catering-grid">
                {/* Departure Catering */}
                <div className="catering-card">
                  <div className="catering-header">
                    <span className="catering-title">Catering</span>
                    <i className="pi pi-arrow-right"></i>
                    <span className="catering-location">{data.route.from}</span>
                  </div>
                  <div className="catering-form">
                    <div className="form-row">
                      <label>NR</label>
                      <Dropdown value="Aviation Service I" options={[{ label: 'Aviation Service I', value: 'Aviation Service I' }]} />
                    </div>
                    <div className="form-row">
                      <label>Service</label>
                      <Dropdown value="TBD" options={[{ label: 'TBD', value: 'TBD' }]} />
                      <label>For</label>
                      <Dropdown value="Pax" options={[{ label: 'Pax', value: 'Pax' }]} />
                    </div>
                    <div className="form-row">
                      <label>Order</label>
                      <InputText placeholder="-" />
                    </div>
                    <div className="form-row">
                      <label>Pax Remarks</label>
                      <InputText placeholder="-" />
                    </div>
                    <div className="form-row">
                      <label>Confirmation</label>
                      <div className="file-upload-section">
                        <span>Drop your file(s) here or browse</span>
                      </div>
                    </div>
                    <div className="form-actions">
                      <Button label="Send" className="p-button-primary" />
                      <Button label="Cancel" className="p-button-secondary" />
                      <label className="send-quote-only">
                        <input type="checkbox" /> Send Quote Only
                      </label>
                    </div>
                    <div className="expenses-section">
                      <Button label="+ Add Expense" className="p-button-text" />
                      <span className="expenses-count">Expenses (0)</span>
                    </div>
                  </div>
                </div>

                {/* Arrival Catering */}
                <div className="catering-card">
                  <div className="catering-header">
                    <span className="catering-title">Catering</span>
                    <i className="pi pi-arrow-right"></i>
                    <span className="catering-location">{data.route.to}</span>
                  </div>
                  <div className="catering-form">
                    <div className="form-row">
                      <label>NR</label>
                      <Dropdown value="Aviation Service I" options={[{ label: 'Aviation Service I', value: 'Aviation Service I' }]} />
                    </div>
                    <div className="form-row">
                      <label>Service</label>
                      <Dropdown value="TBD" options={[{ label: 'TBD', value: 'TBD' }]} />
                      <label>For</label>
                      <Dropdown value="Pax" options={[{ label: 'Pax', value: 'Pax' }]} />
                    </div>
                    <div className="form-row">
                      <label>Order</label>
                      <InputText placeholder="-" />
                    </div>
                    <div className="form-row">
                      <label>Pax Remarks</label>
                      <InputText placeholder="-" />
                    </div>
                    <div className="form-row">
                      <label>Confirmation</label>
                      <div className="file-upload-section">
                        <span>Drop your file(s) here or browse</span>
                      </div>
                    </div>
                    <div className="form-actions">
                      <Button label="Send" className="p-button-primary" />
                      <Button label="Cancel" className="p-button-secondary" />
                      <label className="send-quote-only">
                        <input type="checkbox" /> Send Quote Only
                      </label>
                    </div>
                    <div className="expenses-section">
                      <Button label="+ Add Expense" className="p-button-text" />
                      <span className="expenses-count">Expenses (0)</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </TabPanel>

          <TabPanel header="Customs / Migration">
            <div className="expansion-section">
              <p>Customs and Migration information</p>
            </div>
          </TabPanel>
        </TabView>
      </div>
    );
  };

  // Header
  const header = (
    <div className="table-header">
      <div className="header-left">
        <h2>Service Orders</h2>
      </div>
      <div className="header-right">
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText 
            type="search" 
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)} 
            placeholder="Search flights, tail, customers..." 
          />
        </span>
        <Dropdown value="Today" options={[{ label: 'Today', value: 'Today' }]} />
        <Dropdown value="HTC" options={[{ label: 'HTC', value: 'HTC' }]} />
        <Button icon="pi pi-filter" label="Filters" className="p-button-outlined" />
        <Button icon="pi pi-plus" label="Add Flight/Service Order" className="p-button-primary" />
      </div>
    </div>
  );

  return (
    <div className="service-orders-container">
      <DataTable 
        ref={dt}
        value={serviceOrders} 
        expandedRows={expandedRows}
        onRowToggle={(e) => setExpandedRows(e.data)}
        rowExpansionTemplate={rowExpansionTemplate}
        dataKey="id"
        header={header}
        globalFilter={globalFilter}
        className="service-orders-table"
      >
        <Column expander style={{ width: '3em' }} />
        <Column field="time" header="Time" body={timeBodyTemplate} />
        <Column field="flightNumber" header="Flight" body={flightBodyTemplate} />
        <Column field="route" header="Route" body={routeBodyTemplate} />
        <Column field="crew" header="Crew" />
        <Column field="pax" header="Pax" />
        <Column field="transport" header="Transport" body={statusBodyTemplate('transport', 'transport')} />
        <Column field="hotels" header="Hotels" body={statusBodyTemplate('hotels', 'hotels')} />
        <Column field="catering" header="Catering" body={statusBodyTemplate('catering', 'catering')} />
        <Column field="customs" header="Customs" body={statusBodyTemplate('customs', 'customs')} />
        <Column field="migration" header="Migration" body={statusBodyTemplate('migration', 'migration')} />
        <Column field="slotPPR" header="Slot/PPR" />
        <Column field="permits" header="Permits" body={statusBodyTemplate('permits', 'permits')} />
        <Column field="fuel" header="Fuel" body={fuelBodyTemplate} />
        <Column field="tasks" header="Tasks" body={tasksBodyTemplate} />
        <Column body={actionsBodyTemplate} style={{ width: '8em' }} />
      </DataTable>
      
      {/* Progress bars below each row */}
      <div className="progress-bars-container">
        {serviceOrders.map(order => (
          <div key={order.id} className="row-progress">
            {progressBarTemplate(order)}
          </div>
        ))}
      </div>
    </div>
  );
};

export default ServiceOrdersTable;

