import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { Checkbox } from 'primereact/checkbox';

const DynamicCheckbox = ({
  checked = false,
  onChange,
  onBlur,
  onFocus,
  disabled = false,
  required = false,
  readOnly = false,
  className = '',
  style = {},
  inputStyle = {},
  id,
  name,
  value,
  label,
  labelPosition = 'right', // left, right
  tooltip,
  size = 'normal',
  variant = 'outlined',
  onEvent,
  debug = false,
  ...props
}) => {
  const handleChange = useCallback((e) => {
    const newChecked = e.checked;
    
    if (debug) {
      console.log('DynamicCheckbox change:', { name, checked: newChecked, value });
    }

    if (onChange) {
      onChange(newChecked, e);
    }

    if (onEvent) {
      onEvent('change', { checked: newChecked, value, name, event: e });
    }
  }, [onChange, onEvent, name, value, debug]);

  const handleBlur = useCallback((e) => {
    if (onBlur) {
      onBlur(e);
    }

    if (onEvent) {
      onEvent('blur', { checked, value, name, event: e });
    }
  }, [onBlur, onEvent, checked, value, name]);

  const handleFocus = useCallback((e) => {
    if (onFocus) {
      onFocus(e);
    }

    if (onEvent) {
      onEvent('focus', { checked, value, name, event: e });
    }
  }, [onFocus, onEvent, checked, value, name]);

  // Build CSS classes
  const checkboxClasses = [
    'dynamic-checkbox',
    `dynamic-checkbox--${size}`,
    `dynamic-checkbox--${variant}`,
    `dynamic-checkbox--label-${labelPosition}`,
    disabled ? 'dynamic-checkbox--disabled' : '',
    required ? 'dynamic-checkbox--required' : '',
    readOnly ? 'dynamic-checkbox--readonly' : '',
    className
  ].filter(Boolean).join(' ');

  // Checkbox props
  const checkboxProps = {
    inputId: id || name,
    name,
    value,
    checked,
    onChange: handleChange,
    onBlur: handleBlur,
    onFocus: handleFocus,
    disabled,
    required,
    readOnly,
    className: checkboxClasses,
    style,
    inputStyle,
    tooltip,
    'aria-label': label,
    'aria-required': required,
    'data-component-type': 'checkbox',
    'data-field-name': name,
    ...props
  };

  // Remove undefined props
  Object.keys(checkboxProps).forEach(key => {
    if (checkboxProps[key] === undefined) {
      delete checkboxProps[key];
    }
  });

  const renderCheckbox = () => (
    <div className="checkbox-container">
      {label && labelPosition === 'left' && (
        <label htmlFor={id || name} className="checkbox-label checkbox-label--left">
          {label}
          {required && <span className="checkbox-label__required">*</span>}
        </label>
      )}
      
      <Checkbox {...checkboxProps} />
      
      {label && labelPosition === 'right' && (
        <label htmlFor={id || name} className="checkbox-label checkbox-label--right">
          {label}
          {required && <span className="checkbox-label__required">*</span>}
        </label>
      )}
    </div>
  );

  return (
    <div className="dynamic-checkbox-wrapper">
      {debug && (
        <div className="dynamic-checkbox__debug">
          <small>
            Checked: {checked ? 'Yes' : 'No'} | 
            Value: {JSON.stringify(value)} | 
            Label: {labelPosition}
          </small>
        </div>
      )}
      {renderCheckbox()}
    </div>
  );
};

DynamicCheckbox.propTypes = {
  checked: PropTypes.bool,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onFocus: PropTypes.func,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  readOnly: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object,
  inputStyle: PropTypes.object,
  id: PropTypes.string,
  name: PropTypes.string,
  value: PropTypes.any,
  label: PropTypes.string,
  labelPosition: PropTypes.oneOf(['left', 'right']),
  tooltip: PropTypes.string,
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  variant: PropTypes.oneOf(['outlined', 'filled']),
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicCheckbox;
