import React from 'react';
import PropTypes from 'prop-types';
import './FloatLabel.css';

/**
 * FloatLabel - Custom wrapper for PrimeReact v9 compatibility
 * Provides float label functionality for form inputs
 */
const FloatLabel = ({ children }) => {
  return (
    <span className="p-float-label">
      {children}
    </span>
  );
};

FloatLabel.propTypes = {
  children: PropTypes.node.isRequired
};

export default FloatLabel;
