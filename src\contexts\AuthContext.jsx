import { createContext, useContext, useMemo } from 'react';
import { ROLES, hasRole, hasAnyRole, getDefaultRoute } from '@utils/auth/roles.js';
import useAuth from '@hooks/useAuth';

const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const auth = useAuth();

  // Memoize auth context to prevent unnecessary re-renders
  const authWithRoles = useMemo(() => {
    // Ensure roles is always an array
    const userRoles = Array.isArray(auth.roles) ? auth.roles : [];
    
    return {
      ...auth,
      roles: userRoles, // Ensure consistent roles format
      
      // Simple role checking methods
      hasRole: (role) => hasRole(userRoles, role),
      hasAnyRole: (rolesList) => hasAnyRole(userRoles, rolesList),
      getDefaultRoute: () => getDefaultRoute(userRoles),
      
      // Easy role checks (memoized for performance)
      isAsmAdmin: () => hasRole(userRoles, ROLES.ASM_ADMIN),
      isAsmEmployee: () => hasRole(userRoles, ROLES.ASM_EMPLOYEE),
      isClient: () => hasRole(userRoles, ROLES.CLIENT),
      isClientEmployee: () => hasRole(userRoles, ROLES.CLIENT_EMPLOYEE)
    };
  }, [
    auth.roles, 
    auth.isAuthenticated, 
    auth.user?.id, // Only track user.id instead of entire user object
    auth.isLoading
  ]); // Only re-compute when these change

  return (
    <AuthContext.Provider value={authWithRoles}>
      {children}
    </AuthContext.Provider>
  );
};

/**
 * Custom hook to use authentication context
 * @returns {Object} Authentication state and methods with role-based utilities
 */
export const useAuthContext = () => {
  const context = useContext(AuthContext);
  
  if (!context) {
    throw new Error('useAuthContext must be used within an AuthProvider');
  }
  
  return context;
};

export default AuthContext;
