import React, { useState, useEffect, useRef } from 'react';
import { useForm, Controller, useFieldArray, FormProvider } from 'react-hook-form';
import { InputText } from 'primereact/inputtext';
import { InputNumber } from 'primereact/inputnumber';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { Checkbox } from 'primereact/checkbox';
import { InputMask } from 'primereact/inputmask';
import { InputTextarea } from 'primereact/inputtextarea';
import { FileUpload } from 'primereact/fileupload';
import { Password } from 'primereact/password';
import { Card } from 'primereact/card';
import { Toast } from 'primereact/toast';
import { Tooltip } from 'primereact/tooltip';
// import { ProgressBar } from 'primereact/progressbar';
import { Divider } from 'primereact/divider';
import { Accordion, AccordionTab } from 'primereact/accordion';

import GlassyBlueButton from '@components/Common/Buttons/GlassyBlueButton';
import GlassyWhiteButton from '@components/Common/Buttons/GlassyWhiteButton';
import KeyPersonalFormComponent from './KeyPersonalFormComponent';
import KeyPersonnelOrderDataTable from './KeyPersonnelOrderDataTable';
import { tempdata } from './getAllDefaultTable';
// import MasterService from '../Service/MasterService';
import RCMSService from '@services/RCMSService';
import FormLoader from '@components/Common/Loader/FormLoader';
import useTableData from '@store/Reducers/useTableData';
export default function DynamicFormComponentGrid({ data, collectionNameToRender, strapiData, onSubmit: externalSubmitHandler, onProgress: handleFormProgress, onLoadComplete }) {
  const [isLoading, setIsLoading] = useState(true);
  const methods = useForm({
    defaultValues: {
      // components: {},
      // fields: {}
    },
    mode: 'onChange'
  });



  const {
    data: fetchedStateData,
    isLoading: stateLoading,
    isError: isStateError,
    error: stateError,
    refetch: reftechStateData,
  } = useTableData("state");

  const {
    data: fetchedCountyData,
    isLoading: countyLoading,
    isError: isCountyError,
    error: countyError,
    refetch: reftechCountyData,
  } = useTableData("countyMaster");

  const {
    data: fetchedCountryData,
    isLoading: countryLoading,
    isError: isCountryError,
    error: countryError,
    refetch: reftechCountryData,
  } = useTableData("countryMaster");

  const {
    data: fetchedNaicsSubCodeData,
    isLoading: naicsSubCodeLoading,
    isError: isNaicsSubCodeError,
    error: naicsSubCodeError,
    refetch: reftechNaicsSubCodeData,
  } = useTableData("naicsSubCode");

  const {
    data: fetchedNaicsCodeData,
    isLoading: naicsCodeLoading,
    isError: isNaicsCodeError,
    error: naicsCodeError,
    refetch: reftechNaicsCodeData,
  } = useTableData("naicsCode");

  const {
    data: fetchedEntityTypeData,
    isLoading: entityTypeLoading,
    isError: isEntityTypeError,
    error: entityTypeError,
    refetch: reftechEntityTypeData,
  } = useTableData("entityTypeMaster");

  const {
    data: fetchedAddressTypeData,
    isLoading: addressTypeLoading,
    isError: isAddressTypeError,
    error: addressTypeError,
    refetch: reftechAddressTypeData,
  } = useTableData("addressType");

  const {
    data: fetchedProductStrapiData,
    isLoading: productStrapiLoading,
    isError: isProductStrapiError,
    error: productStrapiError,
    refetch: reftechProductStrapiData,
  } = useTableData("productStrapi");

  const {
    data: fetchedApplicantData,
    isLoading: applicantTypeLoading,
    isError: isApplicantError,
    error: applicantTypeError,
    refetch: reftechApplicantData,
  } = useTableData("applicantType");

  const {
    data: fetchedKeyPersonnelTypeData,
    isLoading: keyPersonnelTypeLoading,
    isError: isKeyPersonnelTypeError,
    error: keyPersonnelTypeError,
    refetch: reftechKeyPersonnelTypeData,
  } = useTableData("keyPersonnelType");

  const { control, handleSubmit, formState, watch, setValue, getValues } = methods;
  const { errors } = formState;

  const [formProgress, setFormProgress] = useState(0);
  const [componentProgress, setComponentProgress] = useState({});
  const [activeIndex, setActiveIndex] = useState(0);
  const toast = useRef(null);
  const [allMemberOrManager, setAllMemberOrManager] = useState([]);
  const [keyPersonnelValue, setKeyPersonnelValue] = useState(null);
  const [formCounts, setFormCounts] = useState({});
  const [optionsMap, setOptionsMap] = useState({});
  const [keyPersonnelKey, setKeyPersonnelKey] = useState(null)
  



  const [collection, setCollection] = useState(null)

  const [isDataAvailable,setIsDataAvailable]=useState(true)

  useEffect(() => {
    setIsLoading(true)

    RCMSService.getAllCollectionByAPIId(collectionNameToRender)
      .then((res) => {
        console.log(res)
        if (res.data) {
          setCollection(res.data)
        }
      })
      .catch((err) => {
        console.log(err)
        setIsDataAvailable(false)
      })
      .finally(() => {
        setIsLoading(false)
      })
  }, [collectionNameToRender])

  // Find the collection to render based on name if provided
  // console.log(data, "collection name")
  // const collection = collectionNameToRender
  //   ? (Array.isArray(data)
  //     ? data.find(c => c?.collectionApiId === collectionNameToRender)
  //     : (data?.collectionApiId === collectionNameToRender ? data : null))
  //   : data;

  console.log(collection, "saumya")

  // Use watch with a callback to avoid constant re-renders
  const formValues = watch();

  console.log(formValues, "form values")

  // Calculate form completion progress
  useEffect(() => {
    // // Skip if collection is not available
    // if (!collection) return;

    // console.log(formValues, "form values")

    // Debounce function to prevent too many calculations
    const calculateProgress = () => {
      let totalFields = 0;
      let filledFields = 0;
      const componentProgressData = {};

      // Count component fields
      collection?.components?.forEach(comp => {
        if (comp.isActive && comp.component?.isActive) {
          const componentKey = `${comp?.component?.componentApiId}`;
          const componentFields = comp.component.fields || [];
          let compTotalFields = 0;
          let compFilledFields = 0;

          componentFields.forEach(field => {
            if (field.configs?.validations?.required) {
              totalFields++;
              compTotalFields++;
              const fieldName = field.configs?.properties?.name || field.fieldType?.displayName || `field_${field.id}`;
              const fieldValue = formValues?.[componentKey]?.[fieldName];
              if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
                filledFields++;
                compFilledFields++;
              }
            }
          });

          // Calculate component percentage
          const compPercentage = compTotalFields > 0 ? Math.round((compFilledFields / compTotalFields) * 100) : 100;
          componentProgressData[componentKey] = {
            percentage: compPercentage,
            filledFields: compFilledFields,
            totalFields: compTotalFields,
            isValid: compPercentage === 100 && !errors.components?.[componentKey]
          };
        }
      });

      // Count direct fields
      let directFieldsProgress = { percentage: 0, filledFields: 0, totalFields: 0, isValid: true };
      if (collection?.fields && collection?.fields.length > 0) {
        let directTotalFields = 0;
        let directFilledFields = 0;

        collection?.fields.forEach(field => {
          if (field.configs?.validations?.required) {
            totalFields++;
            directTotalFields++;
            const fieldName = field.configs?.properties?.name || field.fieldType?.displayName || `field_${field.id}`;
            const fieldValue = formValues.fields?.[fieldName];

            if (fieldValue !== undefined && fieldValue !== null && fieldValue !== '') {
              filledFields++;
              directFilledFields++;
            }
          }
        });

        // Calculate direct fields percentage
        const directPercentage = directTotalFields > 0 ? Math.round((directFilledFields / directTotalFields) * 100) : 100;
        directFieldsProgress = {
          percentage: directPercentage,
          filledFields: directFilledFields,
          totalFields: directTotalFields,
          isValid: directPercentage === 100 && !errors.fields
        };
        componentProgressData['direct_fields'] = directFieldsProgress;
      }

      // Calculate overall percentage
      const percentage = totalFields > 0 ? Math.round((filledFields / totalFields) * 100) : 100;
      setFormProgress(percentage);
      setComponentProgress(componentProgressData);
    };

    // Call the calculation function
    calculateProgress();

  }, [collection, JSON.stringify(formValues), JSON.stringify(errors)]);
  function flattenObject(obj) {
    let result = {};
    let localFormCount;

    function recurse(current, path = []) {
      for (let key in current) {
        if (!current.hasOwnProperty(key) || key === "undefined") continue;

        const newPath = [...path, key];

        if (Array.isArray(current[key])) {
          localFormCount = current[key].length;

          current[key].forEach((item, index) => {
            if (item && typeof item === "object") {
              if (item.hasOwnProperty("__temp_key__")) {
                const tempKey = item["__temp_key__"];
                recurse(item, [...newPath, tempKey]);
              } else {
                recurse(item, [...newPath, index]);
              }
            }
          });
        } else if (typeof current[key] === "object" && current[key] !== null) {
          recurse(current[key], newPath);
          localFormCount = 1;
        } else {
          const flatKey = newPath.join(".");
          result[flatKey] = current[key];
          localFormCount = 1;
        }
      }
    }

    recurse(obj);

    return { flattenedData: result, localFormCount };
  }

  function flattenOneLevel(obj) {
  const result = {};

  for (const parentKey in obj) {
    if (!obj.hasOwnProperty(parentKey)) continue;

    const child = obj[parentKey];
    if (typeof child === 'object' && child !== null && !Array.isArray(child)) {
      for (const key in child) {
        if (!child.hasOwnProperty(key)) continue;
        const fullKey = `${parentKey}.${key}`;
        result[fullKey] = child[key]; 
      }
    } else {
      result[parentKey] = child;
    }
  }

  return result;
}


  console.log(strapiData)
  useEffect(() => {
    if (strapiData) {
      const flattenedData = flattenOneLevel(strapiData);
      console.log(flattenedData);
      if (typeof flattenedData === "object") {
        Object.keys(flattenedData).forEach((key) => {
          setValue(key, flattenedData[key]);  //--> autofill happens here
        });
      }
    }
  }, [strapiData]);



  // useEffect(() => {
  //   const uniqueUrls = {};
  //   tempdata.forEach((matchedItem) => {
  //     if (matchedItem?.getAllUrl) {  
  //       if (!uniqueUrls[matchedItem.getAllUrl]) {
  //         uniqueUrls[matchedItem.getAllUrl] = [];
  //       }
  //       uniqueUrls[matchedItem.getAllUrl].push(matchedItem.item);
  //     }
  //   });

  //   Object.entries(uniqueUrls).forEach(([url, items]) => {
  //     if (url === "/core/api/StateMaster/list" && fetchedStateData) {
  //       const newOptions = fetchedStateData;
  //       setOptionsMap((prev) => {
  //         const updatedMap = { ...prev };
  //         items.forEach((item) => {
  //           updatedMap[item] = newOptions;
  //         });
  //         return updatedMap;
  //       });
  //     } else if (
  //       url === "/core/api/countryMaster/getAll" &&
  //       fetchedCountryData
  //     ) {
  //       const newOptions = fetchedCountryData;
  //       setOptionsMap((prev) => {
  //         const updatedMap = { ...prev };
  //         items.forEach((item) => {
  //           updatedMap[item] = newOptions;
  //         });
  //         return updatedMap;
  //       });
  //     } else if (url === "/core/api/countyMaster/getAll" && fetchedCountyData) {
  //       const newOptions = fetchedCountyData;
  //       setOptionsMap((prev) => {
  //         const updatedMap = { ...prev };
  //         items.forEach((item) => {
  //           updatedMap[item] = newOptions;
  //         });
  //         return updatedMap;
  //       });
  //     } else if (
  //       url === "/core/api/naicsSubCodes/getAll" &&
  //       fetchedNaicsSubCodeData
  //     ) {
  //       const newOptions = fetchedNaicsSubCodeData;
  //       setOptionsMap((prev) => {
  //         const updatedMap = { ...prev };
  //         items.forEach((item) => {
  //           updatedMap[item] = newOptions;
  //         });
  //         return updatedMap;
  //       });
  //     } else if (
  //       url === "/core/api/stateWiseNaicsCodes/getAll" &&
  //       fetchedNaicsCodeData
  //     ) {
  //       const newOptions = fetchedNaicsCodeData;
  //       setOptionsMap((prev) => {
  //         const updatedMap = { ...prev };
  //         items.forEach((item) => {
  //           updatedMap[item] = newOptions;
  //         });
  //         return updatedMap;
  //       });
  //     } else {
  //       MasterService.getAllData(url)
  //         .then((fetchedData) => {
  //           if (Array.isArray(fetchedData.data)) {
  //             const newOptions = fetchedData.data;
  //             setOptionsMap((prev) => {
  //               const updatedMap = { ...prev };
  //               items.forEach((item) => {
  //                 updatedMap[item] = newOptions;
  //               });
  //               return updatedMap;
  //             });
  //           }
  //         })
  //         .catch((err) => console.log(err));
  //     }
  //   });
  // }, [
  //   fetchedCountyData,
  //   fetchedCountyData,
  //   fetchedStateData,
  //   fetchedNaicsCodeData,
  //   fetchedNaicsSubCodeData,
  // ]);

  const onSubmit = (formData) => {
    console.log('Submitted Data:', formData);
    // Call external submit handler if provided
    if (externalSubmitHandler) {
      externalSubmitHandler(formData, formProgress);
    }

    if (handleFormProgress) {
      handleFormProgress(formProgress)
    }

    toast.current.show({
      severity: 'success',
      summary: 'Form Submitted',
      detail: 'Form data has been successfully submitted',
      life: 3000
    });
  };

  const getValidationRules = (field) => {
    const rules = {};

    // Required validation
    if (field.configs?.validations?.required) {
      rules.required = 'This field is required';
    }

    // Min/Max length validation
    if (field.configs?.attributes?.minLength) {
      rules.minLength = {
        value: parseInt(field.configs.attributes.minLength, 10),
        message: `Minimum length is ${field.configs.attributes.minLength} characters`
      };
    }

    if (field.configs?.attributes?.maxLength) {
      rules.maxLength = {
        value: parseInt(field.configs.attributes.maxLength, 10),
        message: `Maximum length is ${field.configs.attributes.maxLength} characters`
      };
    }

    // Min/Max value validation for numbers
    if (field.configs?.validations?.minValue) {
      rules.min = {
        value: parseInt(field.configs.validations.minValue, 10),
        message: `Minimum value is ${field.configs.validations.minValue}`
      };
    }

    if (field.configs?.validations?.maxValue) {
      rules.max = {
        value: parseInt(field.configs.validations.maxValue, 10),
        message: `Maximum value is ${field.configs.validations.maxValue}`
      };
    }

    // Pattern/Regex validation
    if (field.configs?.validations?.regex && field.configs.validations.regex !== '#2874632474#') {
      try {
        const pattern = new RegExp(field.configs.validations.regex);
        rules.pattern = {
          value: pattern,
          message: 'Invalid format'
        };
      } catch (e) {
        console.warn('Invalid regex pattern:', field.configs.validations.regex);
      }
    }

    // Special case for  IP code pattern
    if (field.fieldType?.fieldTypeName === 'text' &&
      field.configs?.properties?.['display-name']?.toLowerCase().includes('zip')) {
      rules.pattern = {
        value: /^\d{5}(-\d{4})?$/,
        message: 'Please enter a valid ZIP code (e.g., 12345 or 12345-6789)'
      };
    }

    // Special case for email pattern
    if (field.fieldType?.fieldTypeName === 'text' &&
      field.configs?.properties?.['display-name']?.toLowerCase().includes('email')) {
      rules.pattern = {
        value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
        message: 'Please enter a valid email address'
      };
    }

    return rules;
  };

  const renderValidationErrors = (fieldState) => {
    return fieldState.error ? (
      <small className="p-error block mt-1">{fieldState.error.message}</small>
    ) : null;
  };

  function convertName(originalString) {
    if (typeof originalString === "string") {
      if (!originalString.includes("_")) {
        return originalString;
      }

      const parts = originalString.split("_");

      const relevantParts = parts.slice(1);

      const formattedString = relevantParts
        .map((part) => part.charAt(0).toUpperCase() + part.slice(1))
        .join(" ");

      return formattedString;
    }
  }

  console.log(keyPersonnelKey, "key personnel key")

  function flattenObject(obj) {
    const result = {};

    for (const parentKey in obj) {
      if (!obj.hasOwnProperty(parentKey)) continue;

      const child = obj[parentKey];
      if (typeof child === 'object' && child !== null && !Array.isArray(child)) {
        for (const key in child) {
          if (!child.hasOwnProperty(key)) continue;
          const fullKey = `${key}`;
          result[fullKey] = child[key];
        }
      } else {
        result[parentKey] = child;
      }
    }

    return result;
  }

  useEffect(() => {
    if (collection) {
      const matchedComponent = collection?.components
        ?.filter((c) => c.isActive && c.component?.isActive)
        .find((c) => c.component?.componentApiId === keyPersonnelKey);
      const keyValue = flattenObject(keyPersonnelValue)
      if (matchedComponent) {
        const fields = matchedComponent.component.fields || [];
        const childComponents = matchedComponent.component?.childComponents || [];
        fields.forEach((field) => {
          const displayName = field.configs?.properties?.name;
          if (displayName) {
            const key = `${keyPersonnelKey}.${displayName
              .replace(/\s+/g, "_")}`;

            console.log(keyValue, "key default value")


            // const keyPersonnelValue = {
            //   [key]: "",
            // };

            setValue(key, keyValue?.[displayName]);
          }
        });

        childComponents.forEach((child) => {
          const childKey = child.childComponent?.componentApiId || toCamel(child.childComponent?.componentName || "child");

          child.childComponent?.fields?.forEach((field) => {
            const displayName = field.configs?.properties?.name;
            console.log(displayName, "display defaault")
            if (displayName) {
              const key = `${keyPersonnelKey}.${(displayName)}`;
              setValue(key, keyValue?.[displayName]);
            }
          });
        }
        )
      }
    }
  }, [collection, keyPersonnelKey, keyPersonnelValue]);


  const renderField = (field, fieldNamePrefix, index, isHalfWidth = true) => {

    const fieldName = field.configs?.properties?.['display-name'] || field.fieldType?.displayName || `field_${field.id}`;
    const name = field.configs?.properties?.name
    const tempval = convertName(fieldName);
    const options = optionsMap?.[tempval] || [];
    const inputName = `${fieldNamePrefix}.${name}`;
    const placeholder = field.configs?.attributes?.placeholder || field.configs?.properties?.placeholder || '';
    const helpText = field.configs?.attributes?.helpText || field.fieldType?.helpText || '';
    const isRequired = field.configs?.validations?.required === true;
    const isDisabled = field.configs?.attributes?.disabled === true;

    const maxLength = field.configs?.attributes?.maxLength;
    const minValue = field.configs?.validations?.minValue;
    const maxValue = field.configs?.validations?.maxValue;
    const showButtons = field.configs?.attributes?.showButtons === true;
    const mask = field.configs?.attributes?.mask || '';
    const currency = field.configs?.attributes?.currency
    const locale = field.configs?.attributes?.locale
    const mode = field.configs?.attributes?.mode

    const colClass = isHalfWidth ? 'col-12 md:col-3' : 'col-12';

    switch (field?.fieldType?.fieldTypeName) {
      case 'text':
        return (
          <div key={`${inputName}-${index}`} className={colClass}>
            <div className="">
              <Controller
                name={inputName}
                control={control}
                defaultValue=""
                rules={getValidationRules(field)}
                render={({ field: formField, fieldState }) => (
                  <div className="content-section">
                    <label htmlFor={formField.name} className="block mb-2" style={{color: 'black'}}>
                      {fieldName} {isRequired && <span className="text-red-500">*</span>}
                    </label>
                    <InputText
                      id={formField.name}
                      {...formField}
                      placeholder={placeholder}
                      className={`  w-full ${fieldState.invalid ? 'p-invalid' : ''}`}
                      disabled={isDisabled}
                      maxLength={maxLength}
                    />
                    {helpText && <small className="text-color-secondary block mt-1">{helpText}</small>}
                    {renderValidationErrors(fieldState)}
                  </div>
                )}
              />
            </div>
          </div>
        );

      case 'number':
        return (
          <div key={`${inputName}-${index}`} className={colClass}>
            <div className="">
              <Controller
                name={inputName}
                control={control}
                defaultValue={null}
                rules={getValidationRules(field)}
                render={({ field: formField, fieldState }) => (
                  <div className="content-section">
                    <label htmlFor={formField.name} className="block mb-2" style={{color: 'black'}}>
                      {fieldName} {isRequired && <span className="text-red-500">*</span>}
                    </label>
                    <InputNumber
                      id={formField.name}
                      value={formField.value}
                      onValueChange={(e) => formField.onChange(e.value)}
                      placeholder={placeholder}
                      className={`  w-full ${fieldState.invalid ? 'p-invalid' : ''}`}
                      // min={minValue}
                      // max={maxValue}
                      mode={mode}
                      currency={currency}
                      locale={locale}
                      showButtons={showButtons}
                      disabled={isDisabled}
                      useGrouping={false}
                    />
                    {helpText && <small className="text-color-secondary block mt-1">{helpText}</small>}
                    {renderValidationErrors(fieldState)}
                  </div>
                )}
              />
            </div>
          </div>
        );

      case 'dropdown':
        // Prepare options for dropdown
        const matchedItem = tempdata.defaultTableData.find(
          (item) => item.item === tempval
        );


        const filter = field.configs?.attributes?.filter === true;
        const showClear = field.configs?.attributes?.showClear === true;

        return (
          <div key={`${inputName}-${index}`} className={colClass}>
            <div className="">
              <Controller
                name={inputName}
                control={control}
                defaultValue={null}
                rules={getValidationRules(field)}
                render={({ field: formField, fieldState }) => {
                  const defaultValue =
                    matchedItem !== undefined
                      ? matchedItem.label === "countyName" &&
                        allCounty.length > 0
                        ? allCounty
                        : options
                      : options;
                  return (
                    <div className="content-section">
                      <label htmlFor={formField.name} className="block mb-2" style={{color: 'black'}}>
                        {fieldName} {isRequired && <span className="text-red-500">*</span>}
                      </label>
                      <Dropdown
                        id={formField.name}
                        value={formField.value}
                        options={defaultValue}
                        onChange={(e) => formField.onChange(e.value)}
                        optionValue={
                          matchedItem !== undefined
                            ? matchedItem.optionValue
                            : "name"
                        }
                        optionLabel={
                          matchedItem !== undefined
                            ? matchedItem.label
                            : "name"
                        }
                        placeholder={placeholder}
                        className={`  w-full ${fieldState.invalid ? 'p-invalid' : ''}`}
                        filter={filter}
                        showClear={showClear}
                        disabled={isDisabled}
                      />
                      {helpText && <small className="text-color-secondary block mt-1">{helpText}</small>}
                      {renderValidationErrors(fieldState)}
                    </div>
                  )
                }}
              />
            </div>
          </div>
        );

      case 'date':
        const showIcon = field.configs?.attributes?.showIcon === true;
        const dateFormat = field.configs?.attributes?.dateFormat || 'mm/dd/yy';
        const minDate = field.configs?.attributes?.minDate ? new Date(field.configs.attributes.minDate) : undefined;
        const maxDate = field.configs?.attributes?.maxDate ? new Date(field.configs.attributes.maxDate) : undefined;

        return (
          <div key={`${inputName}-${index}`} className={colClass}>
            <div className="">
              <Controller
                name={inputName}
                control={control}
                defaultValue={null}
                rules={getValidationRules(field)}
                render={({ field: formField, fieldState }) => (
                  <div className="content-section">
                    <label htmlFor={formField.name} className="block mb-2" style={{color: 'black'}}>
                      {fieldName} {isRequired && <span className="text-red-500">*</span>}
                    </label>
                    <Calendar
                      id={formField.name}
                      value={formField.value}
                      onChange={(e) => formField.onChange(e.value)}
                      placeholder={placeholder}
                      className={` glass-input w-full ${fieldState.invalid ? 'p-invalid' : ''}`}
                      showIcon={showIcon}
                      dateFormat={dateFormat}
                      minDate={minDate}
                      maxDate={maxDate}
                      disabled={isDisabled}
                    />
                    {helpText && <small className="text-color-secondary block mt-1">{helpText}</small>}
                    {renderValidationErrors(fieldState)}
                  </div>
                )}
              />
            </div>
          </div>
        );

      case 'checkbox':
        return (
          <div key={`${inputName}-${index}`} className={colClass}>
            <div className="">
              <Controller
                name={inputName}
                control={control}
                defaultValue={false}
                rules={getValidationRules(field)}
                render={({ field: formField, fieldState }) => (
                  <div className="content-section">
                    <div className="flex align-items-center">
                      <Checkbox
                        id={formField.name}
                        checked={formField.value}
                        onChange={(e) => formField.onChange(e.checked)}
                        className={fieldState.invalid ? 'p-invalid' : ''}
                        disabled={isDisabled}
                      />
                      <label htmlFor={formField.name} className="ml-2" style={{color: 'black'}}>
                        {fieldName} {isRequired && <span className="text-red-500">*</span>}
                      </label>
                    </div>
                    {helpText && <small className="text-color-secondary block mt-1">{helpText}</small>}
                    {renderValidationErrors(fieldState)}
                  </div>
                )}
              />
            </div>
          </div>
        );

      case 'mask':
        // let maskPattern = mask;
        // if (mask === 'SSN') {
        //   maskPattern = '***********';
        // } else if (mask === 'Phone') {
        //   maskPattern = '(*************';
        // } else if (mask === 'EIN') {
        //   maskPattern = '99-9999999';
        // }

        return (
          <div key={`${inputName}-${index}`} className={colClass}>
            <div className="glass-card-global">
              <Controller
                name={inputName}
                control={control}
                defaultValue=""
                rules={getValidationRules(field)}
                render={({ field: formField, fieldState }) => (
                  <div className="content-section">
                    <label htmlFor={formField.name} className="block mb-2" style={{color: 'black'}}>
                      {fieldName} {isRequired && <span className="text-red-500">*</span>}
                    </label>
                    <InputMask
                      id={formField.name}
                      value={formField.value}
                      onChange={(e) => formField.onChange(e.value)}
                      mask={mask}
                      placeholder={placeholder}
                      className={` glass-input w-full ${fieldState.invalid ? 'p-invalid' : ''}`}
                      disabled={isDisabled}
                    />
                    {helpText && <small className="text-color-secondary block mt-1">{helpText}</small>}
                    {renderValidationErrors(fieldState)}
                  </div>
                )}
              />
            </div>
          </div>
        );

      case 'textarea':
        const rows = field.configs?.attributes?.rows || 4;

        return (
          <div key={`${inputName}-${index}`} className="col-12">
            <div className="">
              <Controller
                name={inputName}
                control={control}
                defaultValue=""
                rules={getValidationRules(field)}
                render={({ field: formField, fieldState }) => (
                  <div className="content-section">
                    <label htmlFor={formField.name} className="block mb-2" style={{color: 'black'}}>
                      {fieldName} {isRequired && <span className="text-red-500">*</span>}
                    </label>
                    <InputTextarea
                      id={formField.name}
                      {...formField}
                      rows={rows}
                      placeholder={placeholder}
                      className={` glass-input w-full ${fieldState.invalid ? 'p-invalid' : ''}`}
                      disabled={isDisabled}
                    />
                    {helpText && <small className="text-color-secondary block mt-1">{helpText}</small>}
                    {renderValidationErrors(fieldState)}
                  </div>
                )}
              />
            </div>
          </div>
        );

      case 'file':
        const multiple = field.configs?.attributes?.multiple === true;
        const maxFileSize = field.configs?.attributes?.maxFileSize ? parseInt(field.configs.attributes.maxFileSize, 10) * 1024 * 1024 : undefined;
        const maxFileSizeMB = field.configs?.attributes?.maxFileSize ? parseInt(field.configs.attributes.maxFileSize, 10) : 10;

        const handleDownloadTemplate = (event) => {
          event.preventDefault();
          event.stopPropagation();
          // Create a sample CSV template
          const csvContent = "data:text/csv;charset=utf-8,Name,Value,Description\nSample1,Value1,Description1\nSample2,Value2,Description2";
          const encodedUri = encodeURI(csvContent);
          const link = document.createElement("a");
          link.setAttribute("href", encodedUri);
          link.setAttribute("download", "template.csv");
          document.body.appendChild(link);
          link.click();
          document.body.removeChild(link);
        };

        const handleUploadClick = (event, formField) => {
          event.preventDefault();
          event.stopPropagation();
          // Find the PrimeReact FileUpload input
          const fileUploadElement = document.getElementById(formField.name);
          if (fileUploadElement) {
            const fileInput = fileUploadElement.querySelector('input[type="file"]');
            if (fileInput) {
              fileInput.click();
            }
          }
        };

        return (
          <div key={`${inputName}-${index}`} className="col-12">
            <div className="">
              <Controller
                name={inputName}
                control={control}
                defaultValue={null}
                rules={getValidationRules(field)}
                render={({ field: formField, fieldState }) => (
                  <div className="content-section">
                    <label htmlFor={formField.name} className="block mb-2" style={{color: 'black'}}>
                      {fieldName} {isRequired && <span className="text-red-500">*</span>}
                    </label>
                    <div className="glass-upload-area" style={{position: 'relative'}}>
                      <FileUpload
                        id={formField.name}
                        name={formField.name}
                        onSelect={(e) => {
                          if (e.files && e.files.length > 0) {
                            formField.onChange(e.files[0]);
                          }
                        }}
                        onDrop={(e) => {
                          if (e.files && e.files.length > 0) {
                            formField.onChange(e.files[0]);
                          }
                        }}
                        multiple={multiple}
                        accept=".csv,text/csv"
                        maxFileSize={maxFileSize}
                        itemTemplate={(file) => (
                          <div>
                            <i className="pi pi-file" style={{ marginRight: '0.5rem', color: '#10B981' }}></i>
                            <span>{file.name}</span>
                          </div>
                        )}

                        emptyTemplate={
                          <div className="p-fileupload-empty">
                            <div 
                              style={{  
                                padding: '2rem !important',
                                textAlign: 'center !important',
                                cursor: 'pointer !important',
                                minHeight: '120px !important',
                                display: 'flex !important',
                                flexDirection: 'column !important',
                                justifyContent: 'center !important',
                                alignItems: 'center !important',
                                borderRadius: '8px !important',
                                transition: 'all 0.3s ease !important',
                                width: '100% !important',
                                height: '100% !important',
                                position: 'relative !important',
                                zIndex: '999 !important'
                              }}
                              onClick={(e) => {
                                e.preventDefault();
                                handleUploadClick(e, formField);
                              }}
                            >
                              {formField.value && formField.value.name ? (
                                <p style={{
                                  fontSize: '16px !important', 
                                  fontWeight: '500 !important', 
                                  color: '#374151 !important',
                                  userSelect: 'none !important',
                                  margin: '0 !important',
                                  display: 'block !important',
                                  visibility: 'visible !important'
                                }}>
                                  {formField.value.name}
                                </p>
                              ) : (
                                <div style={{
                                  display: 'flex !important',
                                  flexDirection: 'column !important',
                                  alignItems: 'center !important',
                                  justifyContent: 'center !important',
                                  width: '100% !important'
                                }}>
                                  <i className="pi pi-upload" style={{
                                    fontSize: '2.5rem !important',
                                    color: '#6B7280 !important',
                                    marginBottom: '1rem !important',
                                    display: 'block !important',
                                    visibility: 'visible !important'
                                  }}></i>
                                  <p style={{
                                    fontSize: '14px', 
                                    fontWeight: '400', 
                                    color: 'var(--text-color, #6b7280)',
                                    margin: '0 0 8px 0',
                                    userSelect: 'none',
                                    display: 'block',
                                    visibility: 'visible',
                                    lineHeight: '1.5'
                                  }}>Drag and drop CSV file here, or click to browse</p>
                                  <p style={{
                                    fontSize: '13px', 
                                    color: 'var(--text-secondary, #9ca3af)',
                                    userSelect: 'none',
                                    margin: '0',
                                    display: 'block',
                                    visibility: 'visible',
                                    lineHeight: '1.5'
                                  }}>Maximum file size: {maxFileSizeMB}MB</p>
                                </div>
                              )}
                            </div>
                          </div>
                        }
                        className={`cursor-pointer ${fieldState.invalid ? 'p-invalid' : ''}`}
                        disabled={isDisabled}
                        mode="advanced"
                        auto={false}
                        chooseOptions={{style: {display: 'none !important'}}}
                        uploadOptions={{style: {display: 'none !important'}}}
                        cancelOptions={{style: {display: 'none !important'}}}
                        style={{
                          '.p-fileupload-content': {
                            display: 'block !important',
                            visibility: 'visible !important'
                          }
                        }}
                      />
                    </div>
                    <div className="flex justify-center gap-3 mt-4 mb-3">
                      <GlassyBlueButton
                        label="Upload CSV"
                        icon="pi pi-upload"
                        className="glass-btn-primary cursor-pointer"
                        onClick={(e) => handleUploadClick(e, formField)}
                        disabled={isDisabled}
                        type="button"
                      />
                      <GlassyBlueButton
                        label="Import Template.csv"
                        icon="pi pi-download"
                        className="glass-btn-secondary"
                        onClick={handleDownloadTemplate}
                        type="button"
                      />
                    </div>
                    {helpText && <small className="text-color-secondary block mt-1">{helpText}</small>}
                    {renderValidationErrors(fieldState)}
                  </div>
                )}
              />
            </div>
          </div>
        );

      case 'password':
        const toggleMask = true;
        const feedback = false;

        return (
          <div key={`${inputName}-${index}`} className={colClass}>
            <div className="glass-card-global">
              <Controller
                name={inputName}
                control={control}
                defaultValue=""
                rules={getValidationRules(field)}
                render={({ field: formField, fieldState }) => (
                  <div className="content-section">
                    <label htmlFor={formField.name} className="block mb-2" style={{color: 'black'}}>
                      {fieldName} {isRequired && <span className="text-red-500">*</span>}
                    </label>
                    <Password
                      id={formField.name}
                      {...formField}
                      placeholder={placeholder}
                      toggleMask={toggleMask}
                      feedback={feedback}
                      className={` glass-input w-full ${fieldState.invalid ? 'p-invalid' : ''}`}
                      disabled={isDisabled}
                    />
                    {helpText && <small className="text-color-secondary block mt-1">{helpText}</small>}
                    {renderValidationErrors(fieldState)}
                  </div>
                )}
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  const renderComponentFields = (comp) => {
    const componentKey = `${comp?.component?.componentApiId}`;
    console.log(comp,"component")
    const componentDisplayName = `${comp?.displayName}`;
    const componentName = comp.component.componentName || `Component ${comp.id}`;
    const progress = componentProgress[componentKey] || { percentage: 0, isValid: false };

    const headerTemplate = (
      <div className="flex align-items-center justify-content-between w-full">
        <div className="flex align-items-center">
          <span className="font-bold">{componentDisplayName}</
          span>
          {progress.isValid && (
            <i className="pi pi-check-circle ml-2 text-green-500"></i>
          )}
        </div>
       </div>
    );

    const dropdownCompArray = ["Organizer Information"]

    return (
      <div key={componentKey} className="form-section mb-6">
        <div className="section-header mb-4">
          {headerTemplate}
        </div>
        {
          dropdownCompArray.includes(componentDisplayName) && <KeyPersonalFormComponent keys={componentName} name={componentKey} setKeyPersonnelKey={setKeyPersonnelKey} setKeyPersonnelValue={setKeyPersonnelValue} />
        }

        <div className="grid">
          {comp.component.fields
            ?.sort((a, b) => (a.displayPreference || 0) - (b.displayPreference || 0))
            .map((field, fieldIndex) => {
              const isFullWidth =
                field.fieldType?.fieldTypeName === 'textarea' ||
                field.fieldType?.fieldTypeName === 'file' ||
                (field.configs?.properties?.['display-name']?.toLowerCase().includes('address') &&
                  !field.configs?.properties?.['display-name']?.toLowerCase().includes('line'));

              return renderField(field, `${componentKey}`, fieldIndex, !isFullWidth);
            })}
        </div>
        {
          comp?.component?.childComponents
            ?.sort((a, b) => (a.displayPreference || 0) - (b.displayPreference || 0))
            .map((val, fieldIndex) => {
              return (
                <div key={fieldIndex} className="vstate-card px-4">
                  <h3>{val?.childComponent?.componentName}</h3>
                  <div className="grid">
                    {
                      val?.childComponent?.fields
                        ?.sort((a, b) => (a.displayPreference || 0) - (b.displayPreference || 0))
                        .map((field, innerFieldIndex) => {
                          const isFullWidth =
                            field.fieldType?.fieldTypeName === 'textarea' ||
                            field.fieldType?.fieldTypeName === 'file' ||
                            (
                              field.configs?.properties?.['display-name']?.toLowerCase().includes('address') &&
                              !field.configs?.properties?.['display-name']?.toLowerCase().includes('line')
                            );

                          return renderField(
                            field,
                            `${componentKey}.${val?.childComponent?.componentName}`,
                            innerFieldIndex,
                            !isFullWidth
                          );
                        })
                    }
                  </div>
                </div>
              );
            })
        }

        {comp?.isRepeatable && (
          <>
            <div className="flex justify-content-end">
              <Button
                type="button"
                icon="pi pi-plus"
                style={{
                  fontSize: "14px", width: "300px"
                }}
                label="Add Member Or Manager"
                className=" vstate-button font-fam-for-all text-center mt-4 mb-3"
              // onClick={() =>
              //   handleKeyPersonal(
              //     "MEMBER_OR_MANAGER",
              //     `member_or_manger_details-${formCounts[key] + 1 || 1
              //     }`
              //   )
              // }
              ></Button>
            </div>
            <KeyPersonnelOrderDataTable
              data={allMemberOrManager}
              setterFunction={
                setAllMemberOrManager
              }
              responseValue={keyPersonnelValue}
              keyValue={componentName}
              formCounts={formCounts}
              setFormCounts={setFormCounts}
            />
          </>
        )}


      </div>
    );
  };

  const renderComponentFieldsFlat = (comp, index) => {
    return renderComponentFields(comp, index);
  };

  const renderDirectFields = () => {
    console.log(collection, "collection")
    if (!collection?.fields || collection?.fields.length === 0) {
      return null;
    }

    const progress = componentProgress['direct_fields'] || { percentage: 0, isValid: false };

    // Create header template with progress indicator
    const headerTemplate = (
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-3">
          <span className="font-medium text-gray-800">Direct Fields</span>
        </div>
        {/* <div className="flex items-center gap-2">
          <ProgressBar
            value={progress.percentage}
            showValue={false}
            style={{ height: '8px' }}
            className={progress.isValid ? 'bg-green-500' : ''}
          />
          <small className="text-xs">{progress.percentage}% complete</small>
        </div> */}
      </div>
    );

    return (
      <div key="direct_fields" className="form-section mb-6">
        <div className="section-header mb-4">
          {headerTemplate}
        </div>
        <div className="grid">
          {collection?.fields
            ?.sort((a, b) => (a.displayPreference || 0) - (b.displayPreference || 0))
            .map((field, fieldIndex) => {
              const isFullWidth =
                field?.fieldType?.fieldTypeName === 'textarea' ||
                field?.fieldType?.fieldTypeName === 'file';

              return renderField(field, 'fields', fieldIndex, !isFullWidth);
            })}
        </div>
      </div>
    );
  };

  const renderDirectFieldsFlat = () => {
    return renderDirectFields();
  };

  if (!isDataAvailable) {
    return <div>No collection data available</div>;
  }

  return (
    <FormProvider {...methods}>
      <div className="">
        <Toast ref={toast} />
        
        <div className="pt-0 pr-6 pb-6 pl-6 space-y-6 mt-2">
          <div className="flex justify-content-between align-items-center mb-3">
            <div>
              <h2 className="text-xl font-semibold" style={{ color: "#193276" }}>{collection?.collectionName}</h2>
              {collection?.collectionDesc && (
                <p className="text-color-secondary mt-2 mb-0">{collection?.collectionDesc}</p>
              )}
            </div>

            <div className="flex align-items-center">
              <div className="mr-3">
                {/* <ProgressBar
                  value={formProgress}
                  showValue={true}
                  style={{ height: '12px', width: '150px' }}
                  className={formProgress === 100 ? 'bg-green-500' : ''}
                /> */}
              </div>
            </div>
          </div>

          <Divider className="mt-0 mb-4" />

          {
            isLoading ? (
              <FormLoader />
            ):(
<form onSubmit={handleSubmit(onSubmit)} className="p-fluid">
            <div className="form-sections">
              {collection?.components
                ?.filter((c) => c.isActive && c.component?.isActive)
                .sort((a, b) => (a.displayPreference || 0) - (b.displayPreference || 0))
                .map((comp, index) => (
                  <div key={`comp-${index}`} className="glass-card-global mb-4" style={{ padding: '24px' }}>
                    {renderComponentFieldsFlat(comp, index)}
                  </div>
                ))}

              {collection?.fields && collection?.fields.length > 0 && (
                <div className="glass-card-global" style={{ padding: '24px' }}>
                  {renderDirectFieldsFlat()}
                </div>
              )}
            </div>
          </form>
            )
          }

          
        </div>
      </div>
    </FormProvider>
  );
}
