import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { ProgressBar } from 'primereact/progressbar';

const DynamicProgressBar = ({
  value = 0,
  showValue = true,
  unit = '%',
  mode = 'determinate', 
  color,
  className = '',
  style = {},
  id,
  size = 'normal',
  variant = 'default',
  animated = false,
  striped = false,
  label,
  displayValueTemplate,
  onEvent,
  debug = false,
  ...props
}) => {
  const renderDisplayValue = useCallback((value) => {
    if (displayValueTemplate && typeof displayValueTemplate === 'function') {
      return displayValueTemplate(value);
    }

    if (!showValue) {
      return null;
    }

    if (mode === 'indeterminate') {
      return label || 'Loading...';
    }

    return `${value}${unit}`;
  }, [displayValueTemplate, showValue, mode, label, unit]);

  const progressClasses = [
    'dynamic-progressbar',
    `dynamic-progressbar--${size}`,
    `dynamic-progressbar--${variant}`,
    `dynamic-progressbar--${mode}`,
    animated ? 'dynamic-progressbar--animated' : '',
    striped ? 'dynamic-progressbar--striped' : '',
    className
  ].filter(Boolean).join(' ');

  const getProgressStyle = () => {
    const baseStyle = { ...style };
    
    if (color) {
      baseStyle['--progress-color'] = color;
    }

    return baseStyle;
  };

  const handleComplete = useCallback(() => {
    if (debug) {
      console.log('DynamicProgressBar complete:', { value });
    }

    if (onEvent) {
      onEvent('complete', { value });
    }
  }, [onEvent, value, debug]);

  React.useEffect(() => {
    if (mode === 'determinate' && value >= 100) {
      handleComplete();
    }
  }, [value, mode, handleComplete]);

  const progressProps = {
    id,
    value: mode === 'indeterminate' ? null : Math.max(0, Math.min(100, value)),
    showValue: false, 
    mode,
    className: progressClasses,
    style: getProgressStyle(),
    displayValueTemplate: renderDisplayValue,
    'data-component-type': 'progressbar',
    'data-progress-size': size,
    'data-progress-variant': variant,
    'data-progress-mode': mode,
    ...props
  };

  Object.keys(progressProps).forEach(key => {
    if (progressProps[key] === undefined) {
      delete progressProps[key];
    }
  });

  return (
    <div className="dynamic-progressbar-wrapper">
      {debug && (
        <div className="dynamic-progressbar__debug">
          <small>
            Value: {mode === 'indeterminate' ? 'N/A' : `${value}${unit}`} | 
            Mode: {mode} | 
            Size: {size} | 
            Variant: {variant}
          </small>
        </div>
      )}
      
      {label && (
        <div className="progressbar-label">
          <span>{label}</span>
          {showValue && mode === 'determinate' && (
            <span className="progressbar-value">{value}{unit}</span>
          )}
        </div>
      )}
      
      <ProgressBar {...progressProps} />
      
      {showValue && !label && (
        <div className="progressbar-display-value">
          {renderDisplayValue(value)}
        </div>
      )}
    </div>
  );
};

DynamicProgressBar.propTypes = {
  value: PropTypes.number,
  showValue: PropTypes.bool,
  unit: PropTypes.string,
  mode: PropTypes.oneOf(['determinate', 'indeterminate']),
  color: PropTypes.string,
  className: PropTypes.string,
  style: PropTypes.object,
  id: PropTypes.string,
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  variant: PropTypes.oneOf(['default', 'primary', 'secondary', 'success', 'info', 'warning', 'danger']),
  animated: PropTypes.bool,
  striped: PropTypes.bool,
  label: PropTypes.string,
  displayValueTemplate: PropTypes.func,
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicProgressBar;
