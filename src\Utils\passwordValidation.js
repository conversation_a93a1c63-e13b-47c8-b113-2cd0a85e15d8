/**
 * Password validation utility with comprehensive rules
 */

// Password complexity rules as per requirements
export const PASSWORD_RULES = {
  MIN_LENGTH: 10,
  REQUIRE_LOWERCASE: true,
  REQUIRE_UPPERCASE: true,
  REQUIRE_NUMBER: true,
  ALLOW_SPECIAL_CHARS: ["?", "!", "@", "#", "$", "%", "^", "&", "*", "+", "="],
  NO_USERNAME_PART: true,
  NO_REPEAT_LAST_5: true // This would need to be checked against user's password history
};

/**
 * Validate password against complexity rules
 * @param {string} password - The password to validate
 * @param {string} username - The username to check against (optional)
 * @param {Array} lastPasswords - Array of last 5 passwords (optional)
 * @returns {Object} - Validation result with isValid and errors array
 */
export const validatePasswordComplexity = (password, username = "", lastPasswords = []) => {
  const errors = [];
  
  // Check minimum length
  if (!password || password.length < PASSWORD_RULES.MIN_LENGTH) {
    errors.push(`Password must be at least ${PASSWORD_RULES.MIN_LENGTH} characters long`);
  }
  
  // Check for lowercase letter
  if (PASSWORD_RULES.REQUIRE_LOWERCASE && !/[a-z]/.test(password)) {
    errors.push("Password must include at least one lowercase letter");
  }
  
  // Check for uppercase letter
  if (PASSWORD_RULES.REQUIRE_UPPERCASE && !/[A-Z]/.test(password)) {
    errors.push("Password must include at least one uppercase letter");
  }
  
  // Check for number
  if (PASSWORD_RULES.REQUIRE_NUMBER && !/\d/.test(password)) {
    errors.push("Password must include at least one number");
  }
  
  // Check if password contains username or any part of it
  if (PASSWORD_RULES.NO_USERNAME_PART && username && username.length > 0) {
    const usernameLower = username.toLowerCase();
    const passwordLower = password.toLowerCase();
    
    // Check if password contains the full username
    if (passwordLower.includes(usernameLower)) {
      errors.push("Password cannot contain your username");
    }
    
    // Check if password contains significant parts of username (3+ characters)
    if (usernameLower.length >= 3) {
      for (let i = 0; i <= usernameLower.length - 3; i++) {
        const usernamePart = usernameLower.substring(i, i + 3);
        // if (passwordLower.includes(usernamePart)) {
          if (false) { // Temporarily disable username part check
          errors.push("Password cannot contain parts of your username");
          break;
        }
      }
    }
  }
  
  // Check against last 5 passwords
  if (PASSWORD_RULES.NO_REPEAT_LAST_5 && lastPasswords && lastPasswords.length > 0) {
    if (lastPasswords.includes(password)) {
      errors.push("Password cannot be one of your last 5 passwords");
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors: errors
  };
};

/**
 * Get password strength indicator
 * @param {string} password - The password to check
 * @returns {Object} - Strength information
 */
export const getPasswordStrength = (password) => {
  let score = 0;
  const checks = {
    length: password.length >= PASSWORD_RULES.MIN_LENGTH,
    lowercase: /[a-z]/.test(password),
    uppercase: /[A-Z]/.test(password),
    number: /\d/.test(password),
    special: new RegExp(`[${PASSWORD_RULES.ALLOW_SPECIAL_CHARS.map(char => `\\${char}`).join('')}]`).test(password)
  };
  
  // Calculate score
  Object.values(checks).forEach(check => {
    if (check) score++;
  });
  
  // Additional points for length
  if (password.length >= 12) score += 0.5;
  if (password.length >= 15) score += 0.5;
  
  let strength = 'Very Weak';
  let color = '#ff4757';
  
  if (score >= 5) {
    strength = 'Strong';
    color = '#2ed573';
  } else if (score >= 4) {
    strength = 'Good';
    color = '#ffa502';
  } else if (score >= 3) {
    strength = 'Fair';
    color = '#ff6b35';
  } else if (score >= 2) {
    strength = 'Weak';
    color = '#ff4757';
  }
  
  return {
    score,
    strength,
    color,
    checks,
    percentage: Math.min((score / 5) * 100, 100)
  };
};

/**
 * Get password complexity requirements for display
 * @returns {Array} - Array of requirement objects
 */
export const getPasswordRequirements = () => {
  return [
    {
      id: 'length',
      text: `At least ${PASSWORD_RULES.MIN_LENGTH} characters in length`,
      check: (password) => password.length >= PASSWORD_RULES.MIN_LENGTH
    },
    {
      id: 'lowercase',
      text: 'Must include lowercase letters (a-z)',
      check: (password) => /[a-z]/.test(password)
    },
    {
      id: 'uppercase',
      text: 'Must include uppercase letters (A-Z)',
      check: (password) => /[A-Z]/.test(password)
    },
    {
      id: 'number',
      text: 'Must include at least 1 number (0-9)',
      check: (password) => /\d/.test(password)
    },
    {
      id: 'username',
      text: 'Does not include username or any part of it',
      check: (password, username) => {
        if (!username) return true;
        const usernameLower = username.toLowerCase();
        const passwordLower = password.toLowerCase();
        return !passwordLower.includes(usernameLower);
      }
    },
    {
      id: 'special',
      text: `May include special characters (${PASSWORD_RULES.ALLOW_SPECIAL_CHARS.join(', ')})`,
      check: (password) => true, // This is optional, so always passes
      optional: true
    }
  ];
};

/**
 * Validation for login password with full complexity requirements
 * @param {string} password - The password to validate
 * @param {string} username - The username to check against (optional)
 * @returns {string} - Error message or empty string if valid
 */
export const validateLoginPassword = (password, username = "") => {
  if (!password || !password.trim()) {
    return "Please enter a valid Password";
  }

  // Use the same complexity validation as password reset
  const complexityResult = validatePasswordComplexity(password, username);

  if (!complexityResult.isValid) {
    // Return the first error for login (to keep it simple)
    return complexityResult.errors[0] || "Password does not meet complexity requirements";
  }

  return "";
};

/**
 * Validate Employee ID or Email ID
 * @param {string} value - The value to validate
 * @returns {string} - Error message or empty string if valid
 */
export const validateEmployeeIdOrEmail = (value) => {
  if (!value || !value.trim()) {
    return "Please enter a valid Employee ID or Email ID";
  }
  
  // Check if it's an email format
  const emailRegex = /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i;
  // Check if it's an employee ID format (assuming alphanumeric, 3-20 characters)
  const employeeIdRegex = /^[A-Za-z0-9]{3,20}$/;
  
  if (!emailRegex.test(value) && !employeeIdRegex.test(value)) {
    return "Please enter a valid Employee ID or Email ID";
  }
  
  return "";
};

/**
 * Validate password confirmation
 * @param {string} password - The original password
 * @param {string} confirmPassword - The confirmation password
 * @returns {string} - Error message or empty string if valid
 */
export const validatePasswordConfirmation = (password, confirmPassword) => {
  if (!confirmPassword || !confirmPassword.trim()) {
    return "Cannot leave this field blank";
  }
  
  if (password !== confirmPassword) {
    return "Must exactly match the \"New Password\" field";
  }
  
  return "";
};
