.lead-management-demo {
  padding: 2rem;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* Demo Info Card */
.demo-info-card {
  margin-bottom: 2rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-info-card .p-card-body {
  padding: 2rem;
}

.demo-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.demo-header h1 {
  margin: 0;
  color: #212529;
  font-size: 1.75rem;
}

.demo-description {
  color: #495057;
  line-height: 1.6;
}

.demo-description > p {
  font-size: 1.1rem;
  margin-bottom: 1.5rem;
}

/* Comparison Grid */
.comparison-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 2rem;
  margin: 2rem 0;
}

.comparison-item {
  padding: 1.5rem;
  background-color: #fff;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.comparison-item:hover {
  border-color: #2196F3;
  box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);
  transform: translateY(-2px);
}

.comparison-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1rem;
}

.comparison-header h3 {
  margin: 0;
  color: #212529;
  font-size: 1.25rem;
}

.comparison-item ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.comparison-item li {
  padding: 0.5rem 0;
  padding-left: 1.5rem;
  position: relative;
  color: #495057;
}

.comparison-item li::before {
  content: '✓';
  position: absolute;
  left: 0;
  color: #4CAF50;
  font-weight: bold;
}

/* Features Section */
.features-section {
  margin: 2rem 0;
  padding: 1.5rem;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.features-section h3 {
  margin-top: 0;
  margin-bottom: 1rem;
  color: #212529;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem;
  background-color: #fff;
  border-radius: 6px;
  font-size: 0.9rem;
  color: #495057;
}

.feature-item i {
  color: #4CAF50;
  font-size: 1.2rem;
}

/* Code Comparison */
.code-comparison {
  margin: 2rem 0;
}

.code-comparison h3 {
  margin-bottom: 1rem;
  color: #212529;
}

.code-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

.code-block {
  background-color: #1e1e1e;
  border-radius: 8px;
  overflow: hidden;
}

.code-block h4 {
  margin: 0;
  padding: 1rem;
  background-color: #2d2d2d;
  color: #fff;
  font-size: 0.95rem;
  font-weight: 600;
}

.code-block pre {
  margin: 0;
  padding: 1.5rem;
  color: #d4d4d4;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.85rem;
  line-height: 1.5;
  overflow-x: auto;
}

/* Demo Tabs */
.demo-tabs {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.demo-tabs .p-tabview-nav {
  background-color: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
}

.demo-tabs .p-tabview-nav li .p-tabview-nav-link {
  padding: 1rem 1.5rem;
  font-weight: 600;
}

.demo-tabs .p-tabview-panels {
  padding: 0;
  background-color: transparent;
}

/* Implementation Wrapper */
.implementation-wrapper {
  position: relative;
}

.implementation-badge {
  position: sticky;
  top: 0;
  z-index: 100;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  font-weight: 600;
  font-size: 0.95rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.implementation-badge.traditional {
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  color: white;
}

.implementation-badge.dynamic {
  background: linear-gradient(135deg, #4CAF50 0%, #388E3C 100%);
  color: white;
}

.implementation-badge i {
  font-size: 1.2rem;
}

/* Documentation Content */
.documentation-content {
  padding: 1.5rem;
}

.documentation-content h2 {
  color: #212529;
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.5rem;
}

.documentation-content h3 {
  color: #495057;
  margin-top: 2rem;
  margin-bottom: 1rem;
  font-size: 1.25rem;
}

.documentation-content p {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.documentation-content ul,
.documentation-content ol {
  color: #6c757d;
  line-height: 1.8;
  padding-left: 1.5rem;
}

.documentation-content li {
  margin-bottom: 0.5rem;
}

.documentation-content code {
  background-color: #f8f9fa;
  padding: 0.2rem 0.4rem;
  border-radius: 3px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9em;
  color: #e83e8c;
}

.file-structure {
  background-color: #1e1e1e;
  color: #d4d4d4;
  padding: 1.5rem;
  border-radius: 6px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.6;
  overflow-x: auto;
}

.components-list {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
  margin: 1rem 0;
}

.component-card {
  padding: 1rem;
  background-color: #f8f9fa;
  border-left: 4px solid #2196F3;
  border-radius: 4px;
}

.component-card h4 {
  margin: 0 0 0.5rem 0;
  color: #212529;
  font-size: 1rem;
}

.component-card p {
  margin: 0;
  color: #6c757d;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .comparison-grid,
  .code-grid {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .components-list {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .lead-management-demo {
    padding: 1rem;
  }

  .demo-info-card .p-card-body {
    padding: 1rem;
  }

  .demo-header h1 {
    font-size: 1.25rem;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .implementation-badge {
    font-size: 0.85rem;
    padding: 0.75rem 1rem;
  }
}

