import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import GlassyBadge from '../../components/common/GlassyBadge';
import GlassyBlueButton from '../../components/common/GlassyBlueButton';

/**
 * TransactionLogsTab - Displays transaction history and logs
 */
const TransactionLogsTab = ({ prospectId }) => {
  const [dateRange, setDateRange] = useState(null);
  const [transactionType, setTransactionType] = useState(null);

  const transactionTypes = [
    { label: 'All', value: null },
    { label: 'Quote', value: 'Quote' },
    { label: 'Order', value: 'Order' },
    { label: 'Invoice', value: 'Invoice' },
    { label: 'Payment', value: 'Payment' }
  ];

  const [transactions] = useState([
    {
      id: 1,
      date: '2024-01-20',
      type: 'Quote',
      reference: 'QT-2024-001',
      description: 'Initial service quotation',
      amount: '$15,000',
      status: 'Sent',
      createdBy: '<PERSON>'
    },
    {
      id: 2,
      date: '2024-01-21',
      type: 'Quote',
      reference: 'QT-2024-002',
      description: 'Revised quotation with discounts',
      amount: '$13,500',
      status: 'Accepted',
      createdBy: 'Ahmed Hassan'
    },
    {
      id: 3,
      date: '2024-01-22',
      type: 'Order',
      reference: 'SO-2024-001',
      description: 'Service order confirmation',
      amount: '$13,500',
      status: 'Confirmed',
      createdBy: 'Fatima Al Zahra'
    },
    {
      id: 4,
      date: '2024-01-23',
      type: 'Invoice',
      reference: 'INV-2024-001',
      description: 'Service invoice',
      amount: '$13,500',
      status: 'Pending',
      createdBy: 'Fatima Al Zahra'
    },
    {
      id: 5,
      date: '2024-01-24',
      type: 'Payment',
      reference: 'PAY-2024-001',
      description: 'Advance payment received',
      amount: '$6,750',
      status: 'Received',
      createdBy: 'System'
    }
  ]);

  const typeTemplate = (rowData) => {
    const colorMap = {
      'Quote': 'info',
      'Order': 'warning',
      'Invoice': 'primary',
      'Payment': 'success'
    };
    return (
      <div style={{ display: 'flex', justifyContent: 'center' }}>
        <GlassyBadge label={rowData.type} className={`badge-${colorMap[rowData.type]}`} />
      </div>
    );
  };

  const statusTemplate = (rowData) => {
    const severityMap = {
      'Sent': 'info',
      'Accepted': 'success',
      'Confirmed': 'success',
      'Pending': 'warning',
      'Received': 'success',
      'Rejected': 'danger'
    };
    return (
      <div style={{ display: 'flex', justifyContent: 'center' }}>
        <GlassyBadge label={rowData.status} className={`badge-${severityMap[rowData.status]}`} />
      </div>
    );
  };

  const actionTemplate = (rowData) => {
    return (
      <div style={{ display: 'flex', gap: '0.5rem' }}>
        <Button
          icon="pi pi-eye"
          className="p-button-sm p-button-text p-button-secondary"
          tooltip="View"
        />
        <Button
          icon="pi pi-download"
          className="p-button-sm p-button-text p-button-secondary"
          tooltip="Download"
        />
        <Button
          icon="pi pi-print"
          className="p-button-sm p-button-text p-button-secondary"
          tooltip="Print"
        />
      </div>
    );
  };

  return (
    <div className="transaction-logs-tab">
      <div className="tab-header" style={{ marginBottom: '1.5rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center', color: 'black' }}>
        <h3>Transaction Logs</h3>
        <GlassyBlueButton label="Export" icon="pi pi-download" className="p-button-sm" />
      </div>

      {/* Transactions Table */}
      <div className="">
        <DataTable 
          value={transactions} 
          className="glass-table"
          responsiveLayout="scroll"
          stripedRows
          showGridlines
        >
          <Column field="date" header="Date" sortable style={{ textAlign: 'center' }} />
          <Column field="type" header="Type" body={typeTemplate} sortable style={{ textAlign: 'center' }} />
          <Column field="reference" header="Reference" sortable style={{ textAlign: 'center' }} />
          <Column field="description" header="Description" sortable style={{ textAlign: 'center' }} />
          <Column field="amount" header="Amount" sortable style={{ textAlign: 'center' }} />
          <Column field="status" header="Status" body={statusTemplate} sortable style={{ textAlign: 'center' }} />
          <Column field="createdBy" header="Created By" sortable style={{ textAlign: 'center' }} />
          <Column header="Actions" body={actionTemplate} style={{ width: '120px', textAlign: 'center' }} />
        </DataTable>
      </div>
    </div>
  );
};

export default TransactionLogsTab;

