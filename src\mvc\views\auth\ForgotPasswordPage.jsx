import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import { InputText } from "primereact/inputtext";
import { Button } from "primereact/button";
import Alert from "@components/common/Alert";
import AuthHeader from "@components/auth/AuthHeader";
import EmailInput from "@components/auth/EmailInput";
import PasswordInput from "@components/auth/PasswordInput";
import {
  validatePasswordComplexity,
  validatePasswordConfirmation
} from "@utils/passwordValidation.js";
import {
  handleForgotPassword,
  handleVerifyOtp,
  handleResetPassword
} from "@controllers/auth/authController";

const ForgotPasswordPage = () => {
  const [currentStep, setCurrentStep] = useState(1); // 1: Email, 2: Verification, 3: Reset Password, 4: Success
  const [email, setEmail] = useState("");
  const [verificationCode, setVerificationCode] = useState("");
  const [formData, setFormData] = useState({
    newPassword: "",
    confirmPassword: "",
  });
  const [showPasswords, setShowPasswords] = useState({
    newPassword: false,
    confirmPassword: false,
  });
  const [validationErrors, setValidationErrors] = useState({
    newPassword: "",
    confirmPassword: ""
  });
  const [isEmailValid, setIsEmailValid] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [error, setError] = useState("");
  const [testOtp, setTestOtp] = useState(null)

  // Countdown timer states
  const [countdown, setCountdown] = useState(0);
  const [canResend, setCanResend] = useState(true);
  const countdownIntervalRef = useRef(null);

  const navigate = useNavigate();

  // Countdown timer effect
  useEffect(() => {
    if (countdown > 0) {
      countdownIntervalRef.current = setInterval(() => {
        setCountdown((prev) => {
          if (prev <= 1) {
            setCanResend(true);
            clearInterval(countdownIntervalRef.current);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    }

    return () => {
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
    };
  }, [countdown]);

  // Start countdown timer (2 minutes = 120 seconds)
  const startCountdown = (seconds = 120) => {
    setCountdown(seconds);
    setCanResend(false);
  };

  // Clear countdown when leaving step 2
  useEffect(() => {
    if (currentStep !== 2) {
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
      setCountdown(0);
      setCanResend(true);
    }
  }, [currentStep]);

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
    };
  }, []);

  // Step 1: Send Email
  const handleEmailSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setShowSuccessMessage(false);

    // Email validation is now handled by EmailInput component

    try {
      // Call forgot password API through controller
      const response = await handleForgotPassword(email);

      if (response.isSuccess) {
        // Show success message and move to verification step
        setSuccessMessage(response.message);
        setShowSuccessMessage(true);

        // Move to verification step after brief delay
        setTimeout(() => {
          setCurrentStep(2);
          setShowSuccessMessage(false);
          startCountdown(120); // Start 2-minute countdown

          setTestOtp(response.data?.otp);
        }, 1500);
      } else {
        setError(response.errorMessage);
      }
    } catch (error) {
      const errorMessage = error.message || "Failed to send reset code. Please try again.";
      setError(errorMessage);
      console.error("Forgot password failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Step 2: Verify Code
  const handleVerificationSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setShowSuccessMessage(false);

    try {
      // Call verify OTP API through controller
      const response = await handleVerifyOtp(email, verificationCode);

      if (response.isSuccess) {
        // Show success message briefly
        setSuccessMessage(response.message);
        setShowSuccessMessage(true);

        // Move to reset password step
        setTimeout(() => {
          setShowSuccessMessage(false);
          setCurrentStep(3);
        }, 1500);
      } else {
        setError(response.errorMessage);
      }
    } catch (error) {
      const errorMessage = error.message || "Invalid verification code. Please try again.";
      setError(errorMessage);
      console.error("Verification failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Step 3: Reset Password
  const handlePasswordResetSubmit = async (e) => {
    e.preventDefault();

    if (!validatePasswords()) {
      return;
    }

    setIsLoading(true);
    setError("");

    try {
      // Call reset password API through controller
      const response = await handleResetPassword(
        email,
        formData.newPassword,
        formData.confirmPassword
      );

      if (response.isSuccess) {
        // Move to success step
        setCurrentStep(4);
      } else {
        setError(response.errorMessage);
      }
    } catch (error) {
      const errorMessage = error.message || "Failed to reset password. Please try again.";
      setError(errorMessage);
      console.error("Password reset failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Helper functions
  const handleInputChange = (field, value) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setError(""); // Clear error when user types

    // Real-time validation for password fields
    if (field === "newPassword") {
      const passwordValidation = validatePasswordComplexity(value, email);
      setValidationErrors(prev => ({
        ...prev,
        newPassword: passwordValidation.errors.join(". "),
        confirmPassword: formData.confirmPassword ? validatePasswordConfirmation(value, formData.confirmPassword) : ""
      }));
    } else if (field === "confirmPassword") {
      const confirmError = validatePasswordConfirmation(formData.newPassword, value);
      setValidationErrors(prev => ({
        ...prev,
        confirmPassword: confirmError
      }));
    }
  };

  // Email validation is now handled by EmailInput component

  const togglePasswordVisibility = (field) => {
    setShowPasswords((prev) => ({ ...prev, [field]: !prev[field] }));
  };

  const validatePasswords = () => {
    // Validate new password complexity
    const passwordValidation = validatePasswordComplexity(formData.newPassword, email);
    const confirmPasswordError = validatePasswordConfirmation(formData.newPassword, formData.confirmPassword);

    setValidationErrors({
      email: "",
      newPassword: passwordValidation.errors.join(". "),
      confirmPassword: confirmPasswordError
    });

    if (!passwordValidation.isValid) {
      setError("Please fix the password requirements below");
      return false;
    }

    if (confirmPasswordError) {
      setError("Please fix the password confirmation");
      return false;
    }

    return true;
  };

  const handleResendCode = async () => {
    setIsLoading(true);
    setError("");
    setShowSuccessMessage(false);

    try {
      // Call forgot password API again to resend code through controller
      const response = await handleForgotPassword(email);

      if (response.isSuccess) {
        console.log(response);
        setSuccessMessage(response.message);
        setShowSuccessMessage(true);
        startCountdown(120); // Start 2-minute countdown again after resending
        setTestOtp(response.data?.otp);
        setTimeout(() => setShowSuccessMessage(false), 3000);
      } else {
        setError(response.errorMessage);
      }
    } catch (error) {
      const errorMessage = error.message || "Failed to resend code. Please try again.";
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  const handleBackToLogin = () => {
    navigate("/login");
  };

  const handleBack = () => {
    if (currentStep === 2) {
      setCurrentStep(1);
    } else if (currentStep === 3) {
      setCurrentStep(2);
    }
  };

  // Step 1: Email Input
  if (currentStep === 1) {
    return (
      <div className="auth-page-layout">
        <div className="glass-card">
          <AuthHeader
            showLogo={true}
            title="Forgot Password?"
            className="mb-5 md:mb-2 "
            subtitle="Enter your email address and we'll send you a link to reset your password."
            icon={
              <div className="icon-container icon-bg-blue mb-3">
                <i className="pi pi-lock text-2xl"></i>
              </div>
            }
          />

          {/* Success Message */}
          {showSuccessMessage && (
            <div className="text-xs md:text-sm mb-4">
              <Alert
                type="success"
                message={successMessage}
                show={showSuccessMessage}
              />
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="text-xs md:text-sm mb-4">
              <Alert type="error" message={error} show={!!error} />
            </div>
          )}

          <form onSubmit={handleEmailSubmit} className="auth-form-layout">
            <div className="form-group">
              <label htmlFor="email" className="form-label">
                Employee ID / Email ID <span className="required">*</span>
              </label>
              <EmailInput
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  setError('');
                }}
                onValidationChange={setIsEmailValid}
                disabled={isLoading}
                placeholder="Enter Employee ID or Email ID"
              />
            </div>

            <Button
              type="submit"
              label={isLoading ? "Sending..." : "Request OTP"}
              className="btn-base btn-primary mt-4"
              loading={isLoading}
              disabled={isLoading || !email.trim() || !isEmailValid}
            />

            <div className="text-center mt-2">
              <button
                type="button"
                onClick={handleBackToLogin}
                className="btn-link"
                disabled={isLoading}
              >
                Back to Login
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  // Step 2: Verification Code
  if (currentStep === 2) {
    return (
      <div className="auth-page-layout">
        <div className="glass-card">
          <AuthHeader
            showLogo={true}
            showBackButton={true}
            onBackClick={handleBack}
            backButtonText="Reset Password"
            subtitle={`We've sent a verification code to ${email}`}
            className="mb-2"
          />

          {testOtp && <p className="my-2 text-center">OTP: {testOtp}</p>}

          {/* Success Message */}
          {showSuccessMessage && (
            <div className="mb-4">
              <Alert
                type="success"
                message={successMessage}
                show={showSuccessMessage}
              />
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-4">
              <Alert type="error" message={error} show={!!error} />
            </div>
          )}

          <form
            onSubmit={handleVerificationSubmit}
            className="auth-form-layout"
          >
            <div className="form-group">
              <label htmlFor="verificationCode" className="form-label">
                Verification Code
              </label>
              <div className="input-wrapper">
                <InputText
                  id="verificationCode"
                  value={verificationCode}
                  onChange={(e) => {
                    const value = e.target.value.replace(/[^0-9]/g, ''); // This removes any non-numeric characters
                    if (value.length <= 6) {
                      setVerificationCode(value);
                    }
                  }}
                  placeholder="Enter Verification Code"
                  className="input-base input-no-icon text-center"
                  required
                  disabled={isLoading}
                  maxLength={6}
                  keyfilter="int"  // This restricts input to only numbers
                  style={{ letterSpacing: "0.2em", fontSize: "16px" }}
                />
              </div>
            </div>

            <Button
              type="submit"
              label={isLoading ? "Verifying..." : "Verify Code"}
              className="btn-base btn-primary mt-4"
              loading={isLoading}
              disabled={isLoading || !verificationCode.trim() || verificationCode.length !== 6}  // This condition ensures 6 digits
            />
            <div className="text-center mt-2">
              <button
                type="button"
                onClick={handleBackToLogin}
                className="btn-link"
                disabled={isLoading}
              >
                Back to Login
              </button>
            </div>

            <div className="text-center mt-2">
              <p className="text-sm text-gray-500 mb-2">
                Didn't receive the code?
              </p>
              {!canResend && countdown > 0 ? (
                <div className="text-center">
                  <p className="text-sm text-gray-400 mb-2">
                    Resend code in {countdown} seconds
                  </p>
                  <button
                    type="button"
                    className="btn-link opacity-50 cursor-not-allowed"
                    disabled={true}
                  >
                    Resend Code
                  </button>
                </div>
              ) : (
                <button
                  type="button"
                  onClick={handleResendCode}
                  className="btn-link mt-2"
                  disabled={isLoading}
                >
                  Resend Code
                </button>
              )}
            </div>
          </form>
        </div>
      </div>
    );
  }

  // Step 3: Reset Password Form
  if (currentStep === 3) {
    return (
      <div className="auth-page-layout">
        <div className="glass-card">
          <AuthHeader
            showLogo={true}
            showBackButton={true}
            onBackClick={handleBack}
            backButtonText="Reset Password"
          />

          {/* Error Message */}
          {error && (
            <div className="mb-4">
              <Alert type="error" message={error} show={!!error} />
            </div>
          )}

          <form
            onSubmit={handlePasswordResetSubmit}
            className="auth-form-layout"
          >
            {/* New Password */}
            <div className="form-group">
              <label htmlFor="newPassword" className="form-label">
                New Password <span className="required">*</span>
              </label>
              <div className="input-wrapper">
                <PasswordInput
                  value={formData.newPassword}
                  onChange={(e) => handleInputChange("newPassword", e.target.value)}
                  onValidationChange={(isValid) => {
                    // Update validationErrors similarly to previous behavior
                    const passwordValidation = validatePasswordComplexity(formData.newPassword, email);
                    setValidationErrors(prev => ({
                      ...prev,
                      newPassword: passwordValidation.errors.join('. ')
                    }));
                  }}
                  username={email}
                  disabled={isLoading}
                  required={true}
                  placeholder="Enter New Password"
                  className="input-with-icon"
                />
              </div>
            </div>

            {/* Password Complexity Indicator */}
            {/* Password Complexity Indicator Section - Temporarily disabled
             * This section shows password requirements and complexity validation
             * Features:
             * - Shows password requirements
             * - Changes background color based on password input
             * - Validates against username
             */}
            {/* <div
              className="form-group"
              style={{
                transition: "all 0.4s cubic-bezier(0.4, 0, 0.2, 1)",
                overflow: "hidden",
                backgroundColor: formData.newPassword
                  ? "rgba(239, 68, 68, 0.05)"
                  : "transparent",
                borderRadius: formData.newPassword ? "8px" : "0px",
                padding: formData.newPassword ? "2px 6px" : "0px",
                border: formData.newPassword
                  ? "1px solid rgba(239, 68, 68, 0.2)"
                  : "1px solid transparent",
              }}
            >
              <PasswordComplexityIndicator
                password={formData.newPassword}
                username={email}
                showStrengthBar={false}
                showRequirements={true}
                className="mb-3"
              />
            </div> */}

            {/* Confirm Password */}
            <div className="form-group">
              <label htmlFor="confirmPassword" className="form-label">
                Confirm Password <span className="required">*</span>
              </label>
              <div className="input-wrapper">
                <InputText
                  id="confirmPassword"
                  type={showPasswords.confirmPassword ? "text" : "password"}
                  value={formData.confirmPassword}
                  onChange={(e) =>
                    handleInputChange("confirmPassword", (e.target.value || '').replace(/\s/g, ''))
                  }
                  placeholder="Enter Confirm Password"
                  className={`input-with-icon ${validationErrors.confirmPassword ? 'p-invalid' : ''}`}
                  required
                  disabled={isLoading}
                />
                <i
                  className={`pi ${showPasswords.confirmPassword ? "pi-eye-slash" : "pi-eye"
                    } input-icon input-icon-clickable`}
                  onClick={() => togglePasswordVisibility("confirmPassword")}
                ></i>
              </div>
              {validationErrors.confirmPassword && (
                <small className="p-error block mt-1">{validationErrors.confirmPassword}</small>
              )}
            </div>

            <Button
              type="submit"
              label={isLoading ? "Updating..." : "Update Password"}
              className="btn-base btn-primary mt-4"
              loading={isLoading}
              disabled={
                isLoading ||
                !formData.newPassword ||
                !formData.confirmPassword ||
                validationErrors.confirmPassword
              }
            />

            <div className="text-center mt-2">
              <button
                type="button"
                onClick={handleBackToLogin}
                className="btn-link"
                disabled={isLoading}
              >
                Back to Login
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  // Step 4: Success
  if (currentStep === 4) {
    return (
      <div className="auth-page-layout">
        <div className="glass-card">
          <AuthHeader
            showLogo={true}
            title="Password reset"
            className="mb-5"
            subtitle="Your password has been successfully reset. Click below to log in."
          />

          {/* Success State */}
          <div className="flex flex-col items-center text-center">
            {/* Success Icon */}
            {/* <div className="icon-container icon-bg-green mb-3">
              <i className="pi pi-check text-2xl"></i>
            </div> */}

            <div className="w-full">
              <Button
                type="button"
                label="Login"
                className="btn-base btn-primary w-full"
                onClick={handleBackToLogin}
              />
            </div>
          </div>
        </div>
      </div>
    );
  }

  return null;
};

export default ForgotPasswordPage;
