import { useState, useEffect, useCallback } from 'react';
import logger from '../utils/logger';

// Create a logger instance for auth operations
const authLogger = logger.createLogger('AuthHook');

/**
 * Custom hook for authentication management
 * Replaces Redux-based authentication with direct service calls
 */
const useAuth = () => {
  const [authState, setAuthState] = useState({
    user: null,
    accessToken: null,
    refreshToken: null,
    expiresAt: null,
    permissions: [],
    roles: [],
    azureGroups: [],
    isAuthenticated: false,
    isLoading: false,
    error: null,
  });

  /**
   * Initialize authentication from localStorage
   */
  const initializeAuth = useCallback(() => {
    try {
      // Prefer persistent login; fall back to session login
      const persistent = localStorage.getItem('user');
      const session = !persistent ? sessionStorage.getItem('user') : null;
      const raw = persistent || session;
      if (raw) {
        const userData = JSON.parse(raw);
        if (userData && userData.accessToken) {
          setAuthState(prevState => ({
            ...prevState,
            user: userData.user,
            accessToken: userData.accessToken,
            refreshToken: userData.refreshToken,
            expiresAt: userData.expiresAt,
            permissions: userData.permissions || [],
            roles: userData.roles || [],
            azureGroups: userData.azureGroups || [],
            isAuthenticated: true,
            error: null,
          }));
          authLogger.info('Auth initialized from storage', {
            userId: userData.user?.id,
            email: userData.user?.email
          });
        }
      }
    } catch (error) {
      authLogger.error('Error initializing auth from localStorage:', error);
      setAuthState(prevState => ({
        ...prevState,
        error: 'Failed to initialize authentication',
      }));
    }
  }, []);

  /**
   * Login user with email and password
   */
const login = useCallback(async (credentials) => {
  authLogger.info('Login attempt', { email: credentials.email });

  try {
    const result = credentials; // Replace this with real API call in production

    const userDataToStore = {
      user: result.user,
      accessToken: result.accessToken,
      refreshToken: result.refreshToken,
      expiresAt: result.expiresAt,
      permissions: result.permissions || [],
      roles: result.roles || [],
      azureGroups: result.azureGroups || [],
    };

    // ✅ Save user to chosen storage based on rememberMe
    const remember = !!result.rememberMe;
    const storage = remember ? localStorage : sessionStorage;
    storage.setItem('user', JSON.stringify(userDataToStore));
    // Clean up from the other storage to avoid stale data
    try {
      (remember ? sessionStorage : localStorage).removeItem('user');
    } catch {}

    // ✅ Update state
    setAuthState(prevState => ({
      ...prevState,
      isLoading: false,
      isAuthenticated: true,
      ...userDataToStore,
      error: null,
    }));

    authLogger.info('Login successful', {
      userId: result.user?.id,
      email: result.user?.email
    });

    return result;
  } catch (error) {
    const errorMessage = error.message || 'Login failed';

    setAuthState(prevState => ({
      ...prevState,
      isLoading: false,
      isAuthenticated: false,
      user: null,
      accessToken: null,
      refreshToken: null,
      expiresAt: null,
      permissions: [],
      roles: [],
      azureGroups: [],
      error: errorMessage,
    }));

    authLogger.error('Login failed', {
      error: errorMessage,
      email: credentials.email
    });

    throw new Error(errorMessage);
  }
}, []);


  /**
   * Logout user
   */
  const logout = useCallback(() => {
    setAuthState({
      user: null,
      accessToken: null,
      refreshToken: null,
      expiresAt: null,
      permissions: [],
      roles: [],
      azureGroups: [],
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });

    try { localStorage.removeItem('user'); } catch {}
    try { sessionStorage.removeItem('user'); } catch {}
    try { localStorage.removeItem('userToken'); } catch {} // Keep for backward compatibility

    authLogger.info('User logged out');
  }, []);

  /**
   * Clear authentication error
   */
  const clearError = useCallback(() => {
    setAuthState(prevState => ({
      ...prevState,
      error: null,
    }));
  }, []);

  /**
   * Check if user has specific permission
   */
  const hasPermission = useCallback((permission) => {
    return authState.permissions.includes(permission);
  }, [authState.permissions]);

  /**
   * Check if user has specific role
   */
  const hasRole = useCallback((role) => {
    return authState.roles.includes(role);
  }, [authState.roles]);

  /**
   * Get current user data
   */
  const getCurrentUser = useCallback(() => {
    return authState.user;
  }, [authState.user]);

  /**
   * Get access token
   */
  const getAccessToken = useCallback(() => {
    return authState.accessToken;
  }, [authState.accessToken]);

  // Initialize auth on hook mount (only once)
  useEffect(() => {
    initializeAuth();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []); // Empty dependency array - only run once on mount

  return {
    // State
    ...authState,
    
    // Actions
    login,
    logout,
    clearError,
    initializeAuth,
    
    // Utility functions
    hasPermission,
    hasRole,
    getCurrentUser,
    getAccessToken,
  };
};

export default useAuth;
