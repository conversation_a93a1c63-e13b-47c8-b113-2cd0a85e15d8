import React, { useRef, useCallback, useImperativeHandle, forwardRef } from 'react';
import PropTypes from 'prop-types';
import { Toast } from 'primereact/toast';

const DynamicToast = forwardRef(({
  position = 'top-right', 
  baseZIndex = 0,
  autoZIndex = true,
  className = '',
  style = {},
  onShow,
  onHide,
  onRemove,
  onClick,
  onEvent,
  debug = false,
  ...props
}, ref) => {
  const toastRef = useRef(null);

  useImperativeHandle(ref, () => ({
    show: (message) => {
      if (debug) {
        console.log('DynamicToast show:', message);
      }
      
      toastRef.current?.show(message);
      
      if (onEvent) {
        onEvent('show', { message });
      }
    },
    clear: () => {
      if (debug) {
        console.log('DynamicToast clear');
      }
      
      toastRef.current?.clear();
      
      if (onEvent) {
        onEvent('clear', {});
      }
    },
    replace: (message) => {
      if (debug) {
        console.log('DynamicToast replace:', message);
      }
      
      toastRef.current?.replace(message);
      
      if (onEvent) {
        onEvent('replace', { message });
      }
    },
    remove: (message) => {
      if (debug) {
        console.log('DynamicToast remove:', message);
      }
      
      toastRef.current?.remove(message);
      
      if (onEvent) {
        onEvent('remove', { message });
      }
    }
  }), [debug, onEvent]);

  const handleShow = useCallback((e) => {
    if (debug) {
      console.log('DynamicToast onShow:', e.message);
    }

    if (onShow) {
      onShow(e);
    }

    if (onEvent) {
      onEvent('messageShow', { message: e.message, event: e });
    }
  }, [onShow, onEvent, debug]);

  const handleHide = useCallback((e) => {
    if (debug) {
      console.log('DynamicToast onHide:', e.message);
    }

    if (onHide) {
      onHide(e);
    }

    if (onEvent) {
      onEvent('messageHide', { message: e.message, event: e });
    }
  }, [onHide, onEvent, debug]);

  const handleRemove = useCallback((e) => {
    if (debug) {
      console.log('DynamicToast onRemove:', e.message);
    }

    if (onRemove) {
      onRemove(e);
    }

    if (onEvent) {
      onEvent('messageRemove', { message: e.message, event: e });
    }
  }, [onRemove, onEvent, debug]);

  const handleClick = useCallback((e) => {
    if (debug) {
      console.log('DynamicToast onClick:', e.message);
    }

    if (onClick) {
      onClick(e);
    }

    if (onEvent) {
      onEvent('messageClick', { message: e.message, event: e });
    }
  }, [onClick, onEvent, debug]);

  const toastClasses = [
    'dynamic-toast',
    `dynamic-toast--${position}`,
    className
  ].filter(Boolean).join(' ');

  const toastProps = {
    ref: toastRef,
    position,
    baseZIndex,
    autoZIndex,
    className: toastClasses,
    style,
    onShow: handleShow,
    onHide: handleHide,
    onRemove: handleRemove,
    onClick: handleClick,
    'data-component-type': 'toast',
    'data-toast-position': position,
    ...props
  };

  Object.keys(toastProps).forEach(key => {
    if (toastProps[key] === undefined) {
      delete toastProps[key];
    }
  });

  return (
    <div className="dynamic-toast-wrapper">
      {debug && (
        <div className="dynamic-toast__debug" style={{ 
          position: 'fixed', 
          top: '10px', 
          left: '10px', 
          zIndex: 9999,
          background: 'rgba(0,0,0,0.8)', 
          color: 'white', 
          padding: '4px 8px', 
          borderRadius: '4px',
          fontSize: '10px'
        }}>
          Toast Position: {position}
        </div>
      )}
      <Toast {...toastProps} />
    </div>
  );
});

DynamicToast.displayName = 'DynamicToast';

DynamicToast.propTypes = {
  position: PropTypes.oneOf(['top-left', 'top-center', 'top-right', 'bottom-left', 'bottom-center', 'bottom-right']),
  baseZIndex: PropTypes.number,
  autoZIndex: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object,
  onShow: PropTypes.func,
  onHide: PropTypes.func,
  onRemove: PropTypes.func,
  onClick: PropTypes.func,
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export const createToastMessage = (severity, summary, detail, options = {}) => ({
  severity,
  summary,
  detail,
  life: options.life || 3000,
  closable: options.closable !== false,
  sticky: options.sticky || false,
  ...options
});

export const createSuccessToast = (summary, detail, options) => 
  createToastMessage('success', summary, detail, options);

export const createInfoToast = (summary, detail, options) => 
  createToastMessage('info', summary, detail, options);

export const createWarnToast = (summary, detail, options) => 
  createToastMessage('warn', summary, detail, options);

export const createErrorToast = (summary, detail, options) => 
  createToastMessage('error', summary, detail, options);

export default DynamicToast;
