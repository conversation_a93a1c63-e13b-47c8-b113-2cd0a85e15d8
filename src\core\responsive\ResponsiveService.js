
class ResponsiveService {
  constructor() {
    this.breakpoints = {
      xs: 0,
      sm: 576,
      md: 768,
      lg: 992,
      xl: 1200,
      xxl: 1400
    };
    
    this.currentBreakpoint = this.getCurrentBreakpoint();
    this.listeners = [];
    
    this.init();
  }

  init() {
    if (typeof window !== 'undefined') {
      window.addEventListener('resize', this.handleResize.bind(this));
      this.handleResize(); 
    }
  }

  handleResize() {
    const newBreakpoint = this.getCurrentBreakpoint();
    if (newBreakpoint !== this.currentBreakpoint) {
      const oldBreakpoint = this.currentBreakpoint;
      this.currentBreakpoint = newBreakpoint;
      
      this.listeners.forEach(callback => {
        callback({
          current: newBreakpoint,
          previous: oldBreakpoint,
          width: window.innerWidth,
          height: window.innerHeight
        });
      });

      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('breakpointchange', {
          detail: {
            current: newBreakpoint,
            previous: oldBreakpoint,
            width: window.innerWidth,
            height: window.innerHeight
          }
        }));
      }
    }
  }

  getCurrentBreakpoint() {
    if (typeof window === 'undefined') return 'lg';
    
    const width = window.innerWidth;
    
    if (width >= this.breakpoints.xxl) return 'xxl';
    if (width >= this.breakpoints.xl) return 'xl';
    if (width >= this.breakpoints.lg) return 'lg';
    if (width >= this.breakpoints.md) return 'md';
    if (width >= this.breakpoints.sm) return 'sm';
    return 'xs';
  }

  isBreakpoint(breakpoint) {
    return this.currentBreakpoint === breakpoint;
  }

  isBreakpointUp(breakpoint) {
    const breakpointValues = Object.keys(this.breakpoints);
    const currentIndex = breakpointValues.indexOf(this.currentBreakpoint);
    const targetIndex = breakpointValues.indexOf(breakpoint);
    return currentIndex >= targetIndex;
  }

  isBreakpointDown(breakpoint) {
    const breakpointValues = Object.keys(this.breakpoints);
    const currentIndex = breakpointValues.indexOf(this.currentBreakpoint);
    const targetIndex = breakpointValues.indexOf(breakpoint);
    return currentIndex <= targetIndex;
  }

  isMobile() {
    return this.isBreakpointDown('sm');
  }

  isTablet() {
    return this.isBreakpoint('md') || this.isBreakpoint('lg');
  }

  isDesktop() {
    return this.isBreakpointUp('xl');
  }

  getScreenSize() {
    if (typeof window === 'undefined') {
      return { width: 1200, height: 800 };
    }
    
    return {
      width: window.innerWidth,
      height: window.innerHeight
    };
  }

  addListener(callback) {
    this.listeners.push(callback);
    
    return () => {
      const index = this.listeners.indexOf(callback);
      if (index > -1) {
        this.listeners.splice(index, 1);
      }
    };
  }

  removeListener(callback) {
    const index = this.listeners.indexOf(callback);
    if (index > -1) {
      this.listeners.splice(index, 1);
    }
  }

  destroy() {
    if (typeof window !== 'undefined') {
      window.removeEventListener('resize', this.handleResize.bind(this));
    }
    this.listeners = [];
  }
}

const responsiveService = new ResponsiveService();

export default responsiveService;
