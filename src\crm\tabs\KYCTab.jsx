import React, { useState } from 'react';
import { But<PERSON> } from 'primereact/button';
import KYCVerifiedCard from '../../components/common/KYCVerifiedCard';
import KYCPendingCard from '../../components/common/KYCPendingCard';
import CompanyDetailsCard from '../../components/common/CompanyDetailsCard';
import CommercialDepartmentCard from '../../components/common/CommercialDepartmentCard';
import AccountsFinanceDepartmentCard from '../../components/common/AccountsFinanceDepartmentCard';
import KYCDocumentationTable from '../../components/common/KYCDocumentationTable';
import GlassyBlueButton from '../../components/common/GlassyBlueButton';
import GlassyWhiteButton from '../../components/common/GlassyWhiteButton';

/**
 * KYCTab - Displays KYC status and documentation
 */
const KYCTab = ({ prospectId }) => {
  const [language, setLanguage] = useState('English');

  const languages = [
    { label: 'English', value: 'English' },
    { label: 'Arabic', value: 'Arabic' },
    { label: 'French', value: 'French' },
    { label: 'Spanish', value: 'Spanish' }
  ];

  const kycDocuments = [
    {
      id: 1,
      documentType: 'Trade License',
      status: 'Verified',
      uploadDate: '15/01/2024',
      verifiedBy: 'Ahmed Hassan'
    },
    {
      id: 2,
      documentType: 'Emirates ID',
      status: 'Verified',
      uploadDate: '15/01/2024',
      verifiedBy: 'Ahmed Hassan'
    },
    {
      id: 3,
      documentType: 'Bank Statement',
      status: 'Pending',
      uploadDate: '16/01/2024',
      verifiedBy: '-'
    },
    {
      id: 4,
      documentType: 'VAT Certificate',
      status: 'Verified',
      uploadDate: '14/01/2024',
      verifiedBy: 'Ahmed Hassan'
    }
  ];

  return (
    <div className="kyc-tab">
      {/* Header */}
      <div style={{ 
        marginBottom: '1.5rem', 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: '1rem'
      }}>
        <h3 style={{ margin: 0, fontSize: '1.5rem', fontWeight: '600',color: 'black' }}>KYC Status</h3>
        <div style={{ display: 'flex', gap: '0.5rem' }}>
          <GlassyBlueButton 
            label="Add Manual KYC" 
            icon="pi pi-plus" 
            className="p-button-sm"
            style={{ backgroundColor: '#3B82F6', border: 'none' }}
          />
          <GlassyWhiteButton 
            label="Obtain Link" 
            icon="pi pi-link" 
            className="p-button-sm p-button-success"
          />
        </div>
      </div>

      {/* KYC Status Cards Row */}
      <div style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', 
        gap: '1rem',
        marginBottom: '1.5rem'
      }}>
        <KYCVerifiedCard
          verificationMethod="Manual/Online"
          verificationTime="1/2/2024 11:40 AM"
          verifiedBy="Ahmed Hassan"
        />
        <KYCPendingCard
          verificationMethod="Manual/Online"
          verificationTime="1/2/2024 11:40 AM"
          verifiedBy="Ahmed Hassan"
        />
      </div>

      {/* Company Details Card */}
      <div style={{ marginBottom: '1.5rem' }}>
        <CompanyDetailsCard
          customerName="Emirates Airlines"
          registeredOfficeAddress="PO Box 686, Dubai, United Arab Emirates"
          operationalBase="Dubai International Airport, Terminal 3"
          language={language}
          languages={languages}
          onLanguageChange={(e) => setLanguage(e.value)}
        />
      </div>

      {/* Commercial Department Card */}
      <div style={{ marginBottom: '1.5rem' }}>
        <CommercialDepartmentCard
          name="Ahmed Al Rashid"
          position="Commercial Manager"
          phone="+971 4 214 5000"
          fax="+971 4 214 5001"
          email="<EMAIL>"
        />
      </div>

      {/* Accounts/Finance Department Card */}
      <div style={{ marginBottom: '1.5rem' }}>
        <AccountsFinanceDepartmentCard
          name="Fatima Al Zahra"
          position="Finance Director"
          phone="+971 4 214 5100"
          email="<EMAIL>"
          bankName="Emirates NBD"
          accountNumber="**********"
          swiftCode="EBILAEAD"
          iban="************************"
        />
      </div>

      {/* KYC Documentation Table */}
      <KYCDocumentationTable documents={kycDocuments} />
    </div>
  );
};

export default KYCTab;

