/**
 * Lightweight helpers for opening/closing MFA/OAuth popup windows.
 * These helpers intentionally avoid touching React state so they remain reusable
 * across components. They accept callbacks for popup opened/closed events.
 */
export function buildModifiedOauthUrl(oAuth2Url) {
  const callbackUrl = `${window.location.origin}/oauth2-callback.html`;

  let modifiedUrl = oAuth2Url;

  if (oAuth2Url.includes('redirect_uri=')) {
    modifiedUrl = oAuth2Url.replace(/redirect_uri=[^&]*/, `redirect_uri=${encodeURIComponent(callbackUrl)}`);
  } else {
    const separator = oAuth2Url.includes('?') ? '&' : '?';
    modifiedUrl = `${oAuth2Url}${separator}redirect_uri=${encodeURIComponent(callbackUrl)}`;
  }

  const additionalParams = [
    'prompt=login',
    'max_age=0',
    'login_hint=',
  ];

  const sep = modifiedUrl.includes('?') ? '&' : '?';
  modifiedUrl = `${modifiedUrl}${sep}${additionalParams.join('&')}`;

  return modifiedUrl;
}

/**
 * Open an MFA/OAuth popup and monitor its lifecycle.
 * @param {string} oAuth2Url - original OAuth2 URL
 * @param {{onOpened?: (popup: Window) => void, onClosed?: () => void}} callbacks
 * @returns {{popup: Window|null, stop: () => void, error?: string}}
 */
export function openMfaWindow(oAuth2Url, { onOpened, onClosed } = {}) {
  try {
    const width = 500;
    const height = 600;
    const left = window.screenX + (window.outerWidth - width) / 2;
    const top = window.screenY + (window.outerHeight - height) / 2;

    const modifiedUrl = buildModifiedOauthUrl(oAuth2Url);

    const popup = window.open(
      modifiedUrl,
      'mfa-authentication',
      `width=${width},height=${height},left=${left},top=${top},scrollbars=yes,resizable=yes`
    );

    if (!popup) {
      return { popup: null, error: 'Failed to open authentication window. Please disable popup blocker and try again.' };
    }

    // Try to clear storage on same-origin popup (best-effort)
    setTimeout(() => {
      try {
        if (popup.location.hostname === window.location.hostname) {
          popup.sessionStorage?.clear();
          popup.localStorage?.clear();
        }
      } catch (e) {
        // cross-origin is expected, ignore
      }
    }, 100);

    if (typeof onOpened === 'function') onOpened(popup);

    const interval = setInterval(() => {
      try {
        if (popup.closed) {
          clearInterval(interval);
          if (typeof onClosed === 'function') onClosed();
        }
      } catch (e) {
        // ignore cross-origin access errors while polling
      }
    }, 1000);

    return {
      popup,
      stop: () => clearInterval(interval),
    };
  } catch (error) {
    return { popup: null, error: error.message || 'Failed to open popup' };
  }
}

export function closeMfaWindow(popup) {
  try {
    if (popup && !popup.closed) popup.close();
  } catch (e) {
    // ignore
  }
}
