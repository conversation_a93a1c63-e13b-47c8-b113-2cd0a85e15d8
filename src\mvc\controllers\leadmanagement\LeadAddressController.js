import BaseController from '../BaseController.js';
import LeadAddressService from '../../services/leadmanagement/LeadAddress.js';
import LeadAddressModel from '../../models/leadmanagement/LeadAddressModel.js';

class LeadAddressController extends BaseController {
  constructor() {
    super();
    this.state = {
      addresses: [],
      loading: false,
      error: null
    };
  }

  async fetchAddresses() {
    return this.executeAction('fetchAddresses', async () => {
      const data = await LeadAddressService.getAll();
      const addresses = Array.isArray(data) ? data.map(a => new LeadAddressModel(a)) : [];
      this.setState({ addresses });
      return addresses;
    });
  }

  async saveAddress(addressData) {
    return this.executeAction('saveAddress', async () => {
      const saved = await LeadAddressService.save(addressData);
      await this.fetchAddresses();
      return saved;
    });
  }

  async updateAddress(id, addressData) {
    return this.executeAction('updateAddress', async () => {
      const updated = await LeadAddressService.update(id, addressData);
      await this.fetchAddresses();
      return updated;
    });
  }

  async deleteAddress(id) {
    return this.executeAction('deleteAddress', async () => {
      await LeadAddressService.deleteById(id);
      await this.fetchAddresses();
    });
  }
}

export default new LeadAddressController();
