import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { useAuthContext } from '../../contexts/AuthContext';
import { ROLES } from '../../Utils/auth/roles';
import { MdKeyboardDoubleArrowLeft, MdKeyboardDoubleArrowRight } from 'react-icons/md';

// Props:
// - isCollapsed, setIsCollapsed: control collapse
// - customGroupedItems: optional object { [groupName]: Array<{ id, masterName, displayName } > }
// - activeCustomItemId: optional id to highlight active custom item
// - onCustomItemClick: optional handler(product)
// - showSearch, searchQuery, onSearchChange: optional search controls for custom mode
// - initialExpandedGroup: optional initial expanded group name (custom mode)
const Sidebar = ({
  isCollapsed,
  setIsCollapsed,
  customGroupedItems,
  activeCustomItemId,
  onCustomItemClick,
  showSearch,
  searchQuery,
  onSearchChange,
  initialExpandedGroup
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { roles, isAsmAdmin, isAsmEmployee, isClient, isClientEmployee } = useAuthContext();
  const [expandedGroup, setExpandedGroup] = useState(initialExpandedGroup || null);

  useEffect(() => {
    setExpandedGroup(initialExpandedGroup || null);
  }, [initialExpandedGroup]);

  // Simple menu items based on roles
  const getMenuItems = () => {
    const items = [];

    // Dashboard for each role
    if (isAsmAdmin()) {
      items.push({ label: 'Dashboard', icon: 'pi pi-th-large', path: '/dashboard' });
      items.push({ label: 'Manage Users', icon: 'pi pi-users', path: '/manage-users' });
      items.push({ label: 'Customer', icon: 'pi pi-user', path: '/customer' });
      items.push({ label: 'Vendor', icon: 'pi pi-user-plus', path: '/vendors' });
      items.push({ label: 'Role Management', icon: 'pi pi-user-edit', path: '/role-management' });
      items.push({ label: 'Manage Static Content', icon: 'pi pi-file', path: '/manage-static-content' });
     items.push({ label: 'Master Management', icon: 'pi pi-list', path: '/master-management' });
     items.push({ label: 'Manage Tickets', icon: 'pi pi-ticket', path: '/manage-tickets' });
     items.push({ label: 'Manage Approval', icon: 'pi pi-check', path: '/manage-approval' });
     items.push({ label: 'Manage Offers', icon: 'pi pi-tag', path: '/manage-offers' });
     items.push({ label: 'Document Management', icon: 'pi pi-folder', path: '/document-management' });
     items.push({ label: 'Reports', icon: 'pi pi-chart-bar', path: '/reports' });
     items.push({ label: 'Dynamic Form Management', icon: 'pi pi-cog', path: '/dynamic-form-management' });
     
     items.push({ label: 'Leads', icon: 'pi pi-chart-line', path: '/leadlist' });
     items.push({ label: 'Prospects', icon: 'pi pi-users', path: '/prospects' });
     items.push({ label: 'Customers', icon: 'pi pi-briefcase', path: '/customers' });
// items.push({ label: 'Lead Details', icon: 'pi pi-search', path: '/lead/:leadId' });
// items.push({ label: 'Prospect Details', icon: 'pi pi-user-edit', path: '/prospect/:id' });
// items.push({ label: 'Custom Data Table', icon: 'pi pi-table', path: '/custom-datatable' });
// items.push({ label: 'Service Orders', icon: 'pi pi-send', path: '/service-orders' });
// items.push({ label: 'Service Orders (Dynamic)', icon: 'pi pi-cog', path: '/service-orders-dynamic' });
// items.push({ label: 'Demo App', icon: 'pi pi-cog', path: '/demo' });

      
    }

    if (isAsmEmployee()) {
      items.push({ label: 'Dashboard', icon: 'pi pi-briefcase', path: '/asm-employee-dashboard' });
    //   items.push({ label: 'Companies', icon: 'pi pi-building', path: '/companies' });
    //   items.push({ label: 'Vendors', icon: 'pi pi-truck', path: '/vendors' });
    //   items.push({ label: 'Clients', icon: 'pi pi-users', path: '/clients' });
    //   items.push({ label: 'Orders', icon: 'pi pi-shopping-cart', path: '/orders' });
    //   items.push({ label: 'Sales', icon: 'pi pi-chart-line', path: '/sales' });
    }

    if (isClient()) {
      items.push({ label: 'Dashboard', icon: 'pi pi-user', path: '/client-dashboard' });
    //   items.push({ label: 'Orders', icon: 'pi pi-shopping-cart', path: '/orders' });
    //   items.push({ label: 'Subscription', icon: 'pi pi-credit-card', path: '/subscription' });
    }

    if (isClientEmployee()) {
      items.push({ label: 'Dashboard', icon: 'pi pi-user', path: '/client-dashboard' });
    //   items.push({ label: 'Orders', icon: 'pi pi-shopping-cart', path: '/orders' });
    }

    return items;
  };

  const menuItems = getMenuItems();



  const isActiveRoute = (path) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  // Get role display name
  const getRoleDisplayName = () => {
    if (isAsmAdmin()) return 'ASM Admin';
    if (isAsmEmployee()) return 'ASM Employee'; 
    if (isClient()) return 'Client';
    if (isClientEmployee()) return 'Client Employee';
    return 'User';
  };

  const renderDefaultMenu = () => (
    <nav className="menu-nav">
      {menuItems.map((item, index) => (
        <Link
          key={index}
          to={item.path}
          className={`menu-item ${isActiveRoute(item.path) ? 'active' : ''}`}
          title={isCollapsed ? item.label : ''}
        >
          <i className={item.icon}></i>
          {!isCollapsed && <span className="menu-label">{item.label}</span>}
        </Link>
      ))}
    </nav>
  );

  const renderCustomMenu = () => {
    const groupNames = Object.keys(customGroupedItems || {});
    return (
      <>
        {showSearch && !isCollapsed && (
          <div className="p-2">
            <InputText
              value={searchQuery || ''}
              onChange={(e) => onSearchChange && onSearchChange(e.target.value)}
              placeholder="Search menus or submenus..."
              className="w-full"
            />
          </div>
        )}
        <nav className="menu-nav">
          {groupNames.map((groupName) => (
            <div key={groupName}>
              <div
                className={`menu-item ${expandedGroup === groupName ? 'active' : ''}`}
                onClick={() => setExpandedGroup((prev) => (prev === groupName ? null : groupName))}
                title={groupName}
              >
                <i className="pi pi-folder"></i>
                {!isCollapsed && <span className="menu-label cursor-pointer">{groupName}</span>}
              </div>
              {expandedGroup === groupName && (
                <div className="pl-3" style={{ maxHeight: '400px', overflowY: 'auto' }}>
                  {(customGroupedItems[groupName] || []).map((product) => (
                    <div
                      key={product.id}
                      onClick={() => onCustomItemClick && onCustomItemClick(product)}
                      className={`menu-item ${activeCustomItemId === product.id ? 'active' : ''}`}
                      title={product.masterName || product.displayName}
                    >
                      <i className="pi pi-file"></i>
                      {!isCollapsed && (
                        <span className="menu-label cursor-pointer">
                          {product.masterName || product.displayName}
                        </span>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </div>
          ))}
        </nav>
      </>
    );
  };

  return (
    <div className={`sidebar ${isCollapsed ? 'collapsed' : 'expanded'}`}>
      {/* Header */}
      {!isCollapsed && (
        <div className="sidebar-admin-header">
          <div className="admin-avatar">
            <i className="pi pi-user admin-icon"></i>
          </div>
          <div className="admin-info">
            <h3 className="admin-title">{getRoleDisplayName()}</h3>
            <p className="admin-subtitle">{getRoleDisplayName()} Access</p>
          </div>
        </div>
      )}

      {/* Collapsed Header */}
      {isCollapsed && (
        <div className="sidebar-collapsed-header">
          <div className="admin-avatar">
            <i className="pi pi-user admin-icon"></i>
          </div>
        </div>
      )}

      {/* Menu */}
      <div className="sidebar-menu">
        {customGroupedItems ? renderCustomMenu() : renderDefaultMenu()}
      </div>

      {/* Middle Toggle Button */}
      <div className="sidebar-middle-toggle">
        <Button
          className="p-button-text sidebar-toggle-btn-middle"
          onClick={() => setIsCollapsed(!isCollapsed)}
          tooltip={isCollapsed ? "Show Sidebar" : "Hide Sidebar"}
        >
          {isCollapsed ? <MdKeyboardDoubleArrowRight size={24} /> : <MdKeyboardDoubleArrowLeft size={24} />}
        </Button>
      </div>

      {/* Footer */}
      {!isCollapsed && (
        <div className="sidebar-footer">
          <div className="system-status">
            <div className="status-indicator">
              <div className="status-dot online"></div>
              <span className="status-text">System Status: Online</span>
            </div>
            <p className="status-subtitle">All services operational</p>
          </div>
        </div>
      )}
    </div>
  );
}

export default Sidebar;
