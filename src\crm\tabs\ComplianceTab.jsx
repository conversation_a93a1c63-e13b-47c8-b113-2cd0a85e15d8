import React, { useState } from 'react';
import { Card } from 'primereact/card';
import { Tag } from 'primereact/tag';
import { Button } from 'primereact/button';
import { Dropdown } from 'primereact/dropdown';

/**
 * ComplianceTab - Displays compliance and regulatory information
 */
const ComplianceTab = ({ prospectId }) => {
  const [language, setLanguage] = useState('English');

  const languages = [
    { label: 'English', value: 'English' },
    { label: 'Arabic', value: 'Arabic' },
    { label: 'French', value: 'French' },
    { label: 'Spanish', value: 'Spanish' }
  ];

  const complianceData = {
    kycStatus: 'Verified',
    kycVerificationTime: '1/2/2024 11:40 AM',
    kycVerifiedBy: '<PERSON>',
    kycManualOnline: 'Manual/Online',
    registeredOfficeAddress: 'PO Box 686, Dubai, United Arab Emirates',
    identificationName: 'Dubai International Airport, Terminal 3',
    companyDetails: {
      customerName: 'Emirates Airlines',
      registeredOffice: 'PO Box 686, Dubai, United Arab Emirates',
      identificationName: 'Dubai International Airport, Terminal 3'
    },
    commercialDepartment: {
      name: '<PERSON>',
      position: 'Commercial Manager',
      age: 42,
      email: '<EMAIL>',
      phone: '+971 4 214 1897'
    },
    financeDepartment: {
      name: 'Fatima Al Zahra',
      position: 'Finance Director',
      email: '<EMAIL>',
      phone: '+971 4 214 1898',
      bankingDetails: {
        bankName: 'Emirates NBD',
        accountNumber: '**********',
        swiftCode: 'EBILAEAD',
        iban: 'AE07 0331 2345 6789 0123 456'
      }
    }
  };

  return (
    <div className="compliance-tab">
      <div className="tab-header" style={{ marginBottom: '1.5rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h3>Compliance & KYC Information</h3>
        <div style={{ display: 'flex', gap: '0.5rem' }}>
          <Button label="Add Manual KYC" icon="pi pi-plus" className="p-button-sm" />
          <Button label="Obtain Link" icon="pi pi-link" className="p-button-sm p-button-success" />
        </div>
      </div>

      {/* KYC Status Card */}
      <div title="KYC Status" className="kyc-status-card glass-card-doc-management" style={{ marginBottom: '1.5rem', position:'relative', zIndex: 4 }}>
        <div className="p-grid">
          <div className="p-col-12 p-md-6">
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
              <i className="pi pi-check-circle" style={{ fontSize: '2rem', color: '#22c55e' }}></i>
              <div>
                <div style={{ fontWeight: 'bold', fontSize: '1.25rem',  color:'gray'}}>KYC Verified</div>
                <div style={{ color: '#6c757d' }}>Verification Time: {complianceData.kycVerificationTime}</div>
                <div style={{ color: '#6c757d' }}>Verified by: {complianceData.kycVerifiedBy}</div>
              </div>
            </div>
            <div style={{ marginTop: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.5rem', color:'gray' }}>Language</label>
              <Dropdown
                value={language}
                options={languages}
                onChange={(e) => setLanguage(e.value)}
                style={{ width: '100%' }}
              />
            </div>
          </div>
          <div className="p-col-12 p-md-6 mt-3">
            <div style={{ display: 'flex', alignItems: 'center', gap: '1rem', marginBottom: '1rem' }}>
              <i className="pi pi-clock" style={{ fontSize: '2rem', color: '#f59e0b' }}></i>
              <div>
                <div style={{ fontWeight: 'bold', fontSize: '1.25rem',  color:'gray'}}>KYC Pending</div>
                <div style={{ color: '#6c757d' }}>Verification Time: 1/2/2024 11:40 AM</div>
                <div style={{ color: '#6c757d' }}>Verified by: Ahmed Hassan</div>
              </div>
            </div>
            <div style={{ marginTop: '1rem' }}>
              <Tag value="Follow up to obtain their KYC is pending" severity="warning" style={{ width: '100%' }} />
            </div>
          </div>
        </div>
      </div>

      {/* Company Details Card */}
      <div title="Company Details" className='glass-card-doc-management' style={{ marginBottom: '1.5rem', color:'gray', position: 'relative', zIndex: 3 }}>
        <div className="p-grid">
          <div className="p-col-12 p-md-6">
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Customer Name:</label>
              <div>{complianceData.companyDetails.customerName}</div>
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Registered Office Address:</label>
              <div>{complianceData.companyDetails.registeredOffice}</div>
            </div>
          </div>
          <div className="p-col-12 p-md-6">
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Identification Name:</label>
              <div>{complianceData.companyDetails.identificationName}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Commercial Department Card */}
      <div title="Commercial Department" className='glass-card-doc-management' style={{ marginBottom: '1.5rem', color:'gray', position: 'relative', zIndex: 2 }}>
        <div className="p-grid">
          <div className="p-col-12 p-md-6">
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Name:</label>
              <div>{complianceData.commercialDepartment.name}</div>
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Position:</label>
              <div>{complianceData.commercialDepartment.position}</div>
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Age:</label>
              <div>{complianceData.commercialDepartment.age}</div>
            </div>
          </div>
          <div className="p-col-12 p-md-6">
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Email:</label>
              <div>{complianceData.commercialDepartment.email}</div>
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Phone:</label>
              <div>{complianceData.commercialDepartment.phone}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Accounts/Finance Department Card */}
      <div title="Accounts/Finance Department" className='glass-card-doc-management' style={{ marginBottom: '1.5rem', color:'gray', position: 'relative', zIndex: 1 }}>
        <div className="p-grid">
          <div className="p-col-12 p-md-6">
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Name:</label>
              <div>{complianceData.financeDepartment.name}</div>
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Position:</label>
              <div>{complianceData.financeDepartment.position}</div>
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Email:</label>
              <div>{complianceData.financeDepartment.email}</div>
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Phone:</label>
              <div>{complianceData.financeDepartment.phone}</div>
            </div>
          </div>
          <div className="p-col-12 p-md-6">
            <h4>Banking Details</h4>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Bank Name:</label>
              <div>{complianceData.financeDepartment.bankingDetails.bankName}</div>
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>Account Number:</label>
              <div>{complianceData.financeDepartment.bankingDetails.accountNumber}</div>
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>SWIFT Code:</label>
              <div>{complianceData.financeDepartment.bankingDetails.swiftCode}</div>
            </div>
            <div style={{ marginBottom: '1rem' }}>
              <label style={{ fontWeight: 'bold', display: 'block', marginBottom: '0.25rem' }}>IBAN:</label>
              <div>{complianceData.financeDepartment.bankingDetails.iban}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ComplianceTab;

