import React from 'react';

const AccountsFinanceDepartmentCard = ({ 
  name, 
  position, 
  phone, 
  email,
  bankName,
  accountNumber,
  swiftCode,
  iban
}) => {
  return (
    <div className="glass-card-doc-management p-4">
      <h4 style={{ 
        margin: 0, 
        marginBottom: '1rem', 
        fontSize: '1rem', 
        fontWeight: '600',
        color: '#111827'
      }}>
        Accounts/Finance Department
      </h4>
      
      <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr', gap: '1.5rem' }}>
        {/* Left Column - Contact Details */}
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem', fontSize: '0.875rem' }}>
          <div>
            <div style={{ color: '#6B7280', marginBottom: '0.25rem' }}>Name:</div>
            <div style={{ color: '#111827', fontWeight: '500' }}>{name}</div>
          </div>

          <div>
            <div style={{ color: '#6B7280', marginBottom: '0.25rem' }}>Position:</div>
            <div style={{ color: '#111827', fontWeight: '500' }}>{position}</div>
          </div>

          <div>
            <div style={{ color: '#6B7280', marginBottom: '0.25rem' }}>Phone:</div>
            <div style={{ color: '#111827', fontWeight: '500' }}>{phone}</div>
          </div>

          <div>
            <div style={{ color: '#6B7280', marginBottom: '0.25rem' }}>Email:</div>
            <div style={{ color: '#111827', fontWeight: '500' }}>{email}</div>
          </div>
        </div>

        {/* Right Column - Banking Details */}
        <div style={{ 
          backgroundColor: '#F9FAFB', 
          padding: '1rem', 
          borderRadius: '0.5rem',
          border: '1px solid #E5E7EB'
        }}>
          <h5 style={{ 
            margin: 0, 
            marginBottom: '0.75rem', 
            fontSize: '0.875rem', 
            fontWeight: '600',
            color: '#111827'
          }}>
            Banking Details
          </h5>
          
          <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', fontSize: '0.875rem' }}>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ color: '#6B7280' }}>Bank Name:</span>
              <span style={{ color: '#111827', fontWeight: '500' }}>{bankName}</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ color: '#6B7280' }}>Account Number:</span>
              <span style={{ color: '#111827', fontWeight: '500' }}>{accountNumber}</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ color: '#6B7280' }}>SWIFT Code:</span>
              <span style={{ color: '#111827', fontWeight: '500' }}>{swiftCode}</span>
            </div>
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <span style={{ color: '#6B7280' }}>IBAN:</span>
              <span style={{ color: '#111827', fontWeight: '500' }}>{iban}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AccountsFinanceDepartmentCard;
