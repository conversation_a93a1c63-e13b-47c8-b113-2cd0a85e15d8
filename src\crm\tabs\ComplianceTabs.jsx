import React, { useState } from 'react';
import { Button } from 'primereact/button';
import KeyData from '../../components/common/KeyData.jsx';
import KeyDataDocVerify from '../../components/common/KeyDataDocVerify.jsx';
import PdfReport from '../../components/common/PdfReport.jsx';
import '../../styles/glassy/glassy-ui.css';
import '../../styles/glassy/global.css';
import GlassyBlueButton from '../../components/common/GlassyBlueButton';
import GlassyWhiteButton from '../../components/common/GlassyWhiteButton';

/**
 * ComplianceTabs - Displays compliance information using glassy UI cards
 * Uses KeyData, KeyDataDocVerify, and PdfReport components
 */
const ComplianceTabs = ({ prospectId }) => {
  const [activeTab, setActiveTab] = useState(0); // 0 = Key Data, 1 = Documents

  // Sample data for KeyData component
  const keyDataPerson = {
    photoUrl: '',
    fullName: 'Jane DOE',
    countryLabel: 'Country',
    country: 'North Korea',
  };

  const originalScripts = [
    { code: 'CN', text: '내의 솰IP', color: 'red' },
    { code: 'RU', text: 'Джон Доу', color: 'blue' },
    { code: 'AR', text: 'جان دو', color: 'green' },
    { code: 'KO', text: '존 도우', color: 'purple' },
    { code: 'JP', text: 'ジョン・ドゥ', color: 'pink' },
    { code: 'AM', text: 'ԱՄ', color: 'orange' },
  ];

  const sanctions = [
    {
      sourceCode: 'US',
      title: 'OFAC - Specially Designated Nationals- Treasury Documents',
      documents: [
        { 
          label: 'Document 1', 
          number: '001B850', 
          country: 'USA', 
          idType: 'Passport',
          onView: () => console.log('View Document 1'),
          onDownload: () => console.log('Download Document 1')
        },
        { 
          label: 'Document 2', 
          number: 'TR-P-614166', 
          country: 'USA', 
          idType: 'Passport',
          onView: () => console.log('View Document 2'),
          onDownload: () => console.log('Download Document 2')
        },
      ],
    },
  ];

  // Handlers for PdfReport
  const handleDownloadPdf = () => {
    console.log('Downloading PDF report...');
  };

  const handlePreviewPdf = () => {
    console.log('Previewing PDF report...');
  };

  // Handler for document upload
  const handleUploadDocuments = () => {
    console.log('Upload documents clicked...');
  };

  // Stats data
  const statsData = [
    {
      icon: 'pi-shield',
      iconBg: '#d1fae5',
      iconColor: '#10b981',
      label: 'Match Status',
      value: '100%',
      valueColor: '#10b981'
    },
    {
      icon: 'pi-flag',
      iconBg: '#dbeafe',
      iconColor: '#3b82f6',
      label: 'Country',
      value: 'Verified',
      valueColor: '#3b82f6'
    },
    {
      icon: 'pi-search',
      iconBg: '#fed7aa',
      iconColor: '#f97316',
      label: 'Matches Found',
      value: '3',
      valueColor: '#f97316'
    },
    {
      icon: 'pi-exclamation-triangle',
      iconBg: '#fecaca',
      iconColor: '#ef4444',
      label: 'Risk Level',
      value: 'Medium',
      valueColor: '#ef4444'
    }
  ];

  return (
    <div className="compliance-tabs-container" style={{ padding: '1.5rem' }}>
      {/* Header */}
      <div className="tab-header" style={{ marginBottom: '1.5rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2 className="text-900" style={{ margin: 0, fontWeight: 600, fontSize: '1.25rem' }}>Compliance Information</h2>
        <div style={{ display: 'flex', gap: '0.75rem' }}>
          <GlassyBlueButton 
            label="Run Compliance Check" 
            className="p-button-sm" 
            severity="info"
            style={{ fontWeight: 500 }}
          />
          <GlassyWhiteButton 
            label="Generate Report" 
            className="p-button-sm" 
            severity="success"
            style={{ fontWeight: 500 }}
          />
        </div>
      </div>

      {/* Stats Cards Row */}
      <div className="stats-cards-row" style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(200px, 1fr))', 
        gap: '1rem',
        marginBottom: '1.5rem'
      }}>
        {statsData.map((stat, index) => (
          <div 
            key={index}
            className="glass-card-global"
            style={{
              padding: '1.5rem',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              textAlign: 'center',
              gap: '0.75rem'
            }}
          >
            <div 
              style={{
                width: 48,
                height: 48,
                borderRadius: '50%',
                backgroundColor: stat.iconBg,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              <i className={`pi ${stat.icon}`} style={{ fontSize: '1.25rem', color: stat.iconColor }} />
            </div>
            <div>
              <div style={{ fontSize: '0.875rem', color: '#6b7280', marginBottom: '0.25rem' }}>
                {stat.label}
              </div>
              <div style={{ fontSize: '1.25rem', fontWeight: 600, color: stat.valueColor }}>
                {stat.value}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content: Two Column Layout */}
      <div className="compliance-main-content" style={{ 
        display: 'grid', 
        gridTemplateColumns: '1fr 1fr',
        gap: '5rem',
        alignItems: 'start'
      }}>
        
        {/* Left Column: Key Data with Tabs */}
        <div className="left-column">
          <KeyData
            title="Key Data"
            person={keyDataPerson}
            originalScripts={originalScripts}
            sanctions={sanctions}
          />
        </div>

        {/* Right Column: Document Verification and PDF Report stacked */}
        <div className="right-column" style={{ display: 'flex', flexDirection: 'column', gap: '5rem' }}>
          
          {/* Document Verification Card */}
          <KeyDataDocVerify onUpload={handleUploadDocuments} />

          {/* PDF Report Card */}
          <PdfReport
            title="Compliance Report"
            subtitle="Detailed compliance verification report with all checks and validations"
            onDownload={handleDownloadPdf}
            onPreview={handlePreviewPdf}
          />

        </div>

      </div>
    </div>
  );
};

export default ComplianceTabs;
