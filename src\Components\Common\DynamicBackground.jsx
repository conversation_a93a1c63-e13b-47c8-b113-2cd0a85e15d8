import React, { useState, useEffect } from 'react';
import bgImage1 from '../../assets/Images/BG_IMAGE.png';
import bgImage2 from '../../assets/Images/BG_IMAGE1.png';
// import bgImage3 from '../assets/Images/Blue Technology Images - Free Download on Freepik.png';
// import bgImage4 from '../assets/Images/2,667,500+ Technology Background Stock Illustrations, Royalty-Free Vector  Graphics & Clip Art - iStock _ Innovation, Technology abstract, Technology  icons.png';

const DynamicBackground = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  
  const backgroundImages = [
    bgImage1,
    bgImage2,
    // bgImage3,
    // bgImage4
  ];
  
  useEffect(() => {
    const intervalId = setInterval(() => {
      setCurrentImageIndex(prevIndex => 
        (prevIndex + 1) % backgroundImages.length
      );
    }, 60000);
    
    return () => clearInterval(intervalId);
  }, []);
  
  useEffect(() => {
    if (document.body.classList.contains('theme-dynamic')) {
      document.documentElement.style.setProperty(
        '--dynamic-background-image', 
        `url(${backgroundImages[currentImageIndex]})`
      );
    }
    
    return () => {
      if (document.body.classList.contains('theme-dynamic')) {
        document.documentElement.style.removeProperty('--dynamic-background-image');
      }
    };
  }, [currentImageIndex]);
  
  return null;
};

export default DynamicBackground;
