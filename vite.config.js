import { defineConfig, loadEnv } from "vite";
import react from "@vitejs/plugin-react";
import path from 'path';

export default defineConfig(({ mode }) => {
  // Load env variables based on the current mode (development, production, etc.)
  const env = loadEnv(mode, process.cwd(), "");

  return {
    plugins: [react()],
     resolve: {
      alias: {
        '@components': path.resolve(__dirname, './src/components'),
        '@assets': path.resolve(__dirname, './src/assets'),
        '@styles': path.resolve(__dirname, './src/styles'),
        '@hooks': path.resolve(__dirname, './src/hooks'),
        '@utils': path.resolve(__dirname, './src/utils'),
        '@store': path.resolve(__dirname, './src/store'),
        // '@tempData': path.resolve(__dirname, './src/tempData'),
        '@contexts': path.resolve(__dirname, './src/contexts'),
        '@views': path.resolve(__dirname, './src/mvc/views'),
        '@controllers': path.resolve(__dirname, './src/mvc/controllers'),
        '@services': path.resolve(__dirname, './src/mvc/services'),
        '@temp-data': path.resolve(__dirname, './src/temp-data'),
       
      }
    },
    server: {
      proxy: {
        "/api": {
          target: env.VITE_API_URL, // Access the variable here
          changeOrigin: true,
          secure: false,
          configure: (proxy, _options) => {
          proxy.on('error', (err, _req, _res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, _res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, _res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        },
        }
      },
    },
  };
});