
import axios from 'axios'

let axiosInstance = null;
let isRefreshing = false;
let failedQueue = [];

const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  failedQueue = [];
};

const refreshAccessToken = async () => {
  try {
    const user = JSON.parse(localStorage.getItem('user'));
    if (!user?.refreshToken) {
      throw new Error('No refresh token available');
    }

    const response = await axios.post(
      `${import.meta.env.VITE_API_BASE_URL}/api/aviation-auth/refresh`,
      { refreshToken: user.refreshToken }
    );

    if (response.data?.accessToken) {
      // Update the stored tokens
      const updatedUser = { ...user, accessToken: response.data.accessToken };
      localStorage.setItem('user', JSON.stringify(updatedUser));
      
      // Update axios default headers
      axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${response.data.accessToken}`;
      
      return response.data.accessToken;
    }
    throw new Error('No access token in response');
  } catch (error) {
    // Clear user data and redirect to login on refresh token failure
    localStorage.removeItem('user');
    window.location.href = '/login';
    throw error;
  }
};

export const configureAxios = (setLoading) => {
  axiosInstance = axios.create();

  let activeRequests = 0;
  const user = JSON.parse(localStorage.getItem('user'));
  
  // Set default authorization header if token exists
  // if (user?.accessToken) {
  //   axiosInstance.defaults.headers.common['Authorization'] = `Bearer ${user.accessToken}`;
  // }

  // Request interceptor
  axiosInstance.interceptors.request.use(
    (config) => {
      if (user?.accessToken) {
        config.headers['Authorization'] = `Bearer ${user.accessToken}`;
      }
      activeRequests++;
      setLoading(true);
      return config;
    },
    (error) => {
      activeRequests--;
      if (activeRequests === 0) setLoading(false);
      return Promise.reject(error);
    }
  );

  // Response interceptor
  axiosInstance.interceptors.response.use(
    (response) => {
      activeRequests--;
      if (activeRequests === 0) setLoading(false);
      return response;
    },
    async (error) => {
      activeRequests--;
      const originalRequest = error.config;

      // If error is not 401 or if it's a retry request, reject immediately
      if (error.response?.status !== 401 || originalRequest._retry) {
        if (activeRequests === 0) setLoading(false);
        return Promise.reject(error);
      }

      // If we're already refreshing the token, add the request to the queue
      if (isRefreshing) {
        return new Promise((resolve, reject) => {
          failedQueue.push({ resolve, reject });
        })
        .then(token => {
          originalRequest.headers['Authorization'] = 'Bearer ' + token;
          return axiosInstance(originalRequest);
        })
        .catch(err => {
          return Promise.reject(err);
        });
      }

      // Set refresh token flag and refresh the token
      originalRequest._retry = true;
      isRefreshing = true;

      try {
        const newToken = await refreshAccessToken();
        
        // Update the original request with the new token
        originalRequest.headers['Authorization'] = 'Bearer ' + newToken;
        
        // Process the queue of failed requests
        processQueue(null, newToken);
        
        // Retry the original request
        return axiosInstance(originalRequest);
      } catch (error) {
        processQueue(error, null);
        if (activeRequests === 0) setLoading(false);
        return Promise.reject(error);
      } finally {
        isRefreshing = false;
      }
    }
  );
};

export const getAxiosInstance = () => {
  if (!axiosInstance) throw new Error('Axios instance not configured');
  return axiosInstance;
};
