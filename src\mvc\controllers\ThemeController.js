import BaseController from './BaseController.js';
import themeService from '../services/ThemeService.js';
import { logger } from '../../core/logging/logger.js';

class ThemeController extends BaseController {
  constructor() {
    super();
    
    this.state = {
      currentTheme: 'light',
      availableThemes: [],
      customThemes: new Map(),
      systemDarkMode: false,
      themePreferences: {}
    };
    
    this.themeChangeListener = null;
  }

  async onInitialize() {
    this.setState({
      currentTheme: themeService.getCurrentTheme(),
      availableThemes: themeService.getAvailableThemes(),
      customThemes: themeService.getCustomThemes(),
      systemDarkMode: themeService.isSystemDarkMode()
    });

    this.themeChangeListener = themeService.addListener((newTheme, oldTheme, resolvedTheme) => {
      this.setState({
        currentTheme: newTheme,
        resolvedTheme: resolvedTheme
      });
      
      this.notifyListeners('themeChanged', {
        newTheme,
        oldTheme,
        resolvedTheme
      });
    });

    await this.loadThemePreferences();
  }

  async changeTheme(themeName) {
    return this.executeAction('changeTheme', async () => {
      const success = themeService.changeTheme(themeName);
      
      if (success) {
        this.setState({
          currentTheme: themeName,
          resolvedTheme: themeService.getResolvedTheme()
        });
        
        await this.saveThemePreference(themeName);
        
        logger.info('Theme changed successfully', { 
          theme: themeName,
          controller: 'ThemeController'
        });
        
        return { success: true, theme: themeName };
      } else {
        throw new Error(`Failed to change theme to: ${themeName}`);
      }
    });
  }

  async toggleTheme() {
    return this.executeAction('toggleTheme', async () => {
      const success = themeService.toggleTheme();
      
      if (success) {
        const newTheme = themeService.getCurrentTheme();
        this.setState({
          currentTheme: newTheme,
          resolvedTheme: themeService.getResolvedTheme()
        });
        
        await this.saveThemePreference(newTheme);
        
        return { success: true, theme: newTheme };
      } else {
        throw new Error('Failed to toggle theme');
      }
    });
  }

  async createCustomTheme(name, config) {
    return this.executeAction('createCustomTheme', async () => {
      const validation = this.validateThemeConfig(config);
      if (!validation.isValid) {
        throw new Error(`Invalid theme configuration: ${validation.errors.join(', ')}`);
      }

      const success = themeService.createCustomTheme(name, config);
      
      if (success) {
        this.setState({
          availableThemes: themeService.getAvailableThemes(),
          customThemes: themeService.getCustomThemes()
        });
        
        try {
          await this.apiPost('/themes/custom', {
            name,
            config
          });
        } catch (error) {
          logger.warn('Failed to save custom theme to server', { 
            error: error.message,
            themeName: name
          });
        }
        
        logger.info('Custom theme created', { 
          themeName: name,
          controller: 'ThemeController'
        });
        
        return { success: true, name, config };
      } else {
        throw new Error(`Failed to create custom theme: ${name}`);
      }
    });
  }

  async deleteCustomTheme(name) {
    return this.executeAction('deleteCustomTheme', async () => {
      const success = themeService.deleteCustomTheme(name);
      
      if (success) {
        this.setState({
          availableThemes: themeService.getAvailableThemes(),
          customThemes: themeService.getCustomThemes()
        });
        
        try {
          await this.apiDelete(`/themes/custom/${name}`);
        } catch (error) {
          logger.warn('Failed to delete custom theme from server', { 
            error: error.message,
            themeName: name
          });
        }
        
        logger.info('Custom theme deleted', { 
          themeName: name,
          controller: 'ThemeController'
        });
        
        return { success: true, name };
      } else {
        throw new Error(`Failed to delete custom theme: ${name}`);
      }
    });
  }

  getThemeInfo(themeName = null) {
    const theme = themeName || this.state.currentTheme;
    return themeService.getThemeInfo(theme);
  }

  getAvailableThemesWithInfo() {
    return this.state.availableThemes.map(themeName => ({
      name: themeName,
      ...this.getThemeInfo(themeName)
    }));
  }

  isDarkTheme() {
    const themeInfo = this.getThemeInfo();
    return themeInfo.isDark;
  }

  isSystemDarkMode() {
    return this.state.systemDarkMode;
  }

  getResolvedTheme() {
    return themeService.getResolvedTheme();
  }

  async preloadTheme(themeName) {
    return this.executeAction('preloadTheme', async () => {
      await themeService.preloadTheme(themeName);
      
      logger.debug('Theme preloaded', { 
        themeName,
        controller: 'ThemeController'
      });
      
      return { success: true, theme: themeName };
    });
  }

  exportTheme(themeName) {
    const themeData = themeService.exportTheme(themeName);
    
    if (themeData) {
      const blob = new Blob([JSON.stringify(themeData, null, 2)], {
        type: 'application/json'
      });
      
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${themeName}-theme.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      logger.info('Theme exported', { 
        themeName,
        controller: 'ThemeController'
      });
      
      return { success: true, theme: themeName };
    } else {
      throw new Error(`Theme not found: ${themeName}`);
    }
  }

  async importTheme(file) {
    return this.executeAction('importTheme', async () => {
      const text = await file.text();
      const themeData = JSON.parse(text);
      
      const success = themeService.importTheme(themeData);
      
      if (success) {
        this.setState({
          availableThemes: themeService.getAvailableThemes(),
          customThemes: themeService.getCustomThemes()
        });
        
        logger.info('Theme imported', { 
          themeName: themeData.name,
          controller: 'ThemeController'
        });
        
        return { success: true, theme: themeData.name };
      } else {
        throw new Error('Failed to import theme');
      }
    });
  }

  validateThemeConfig(config) {
    const errors = [];
    const requiredProperties = ['primary', 'secondary', 'background', 'text'];
    
    requiredProperties.forEach(prop => {
      if (!config[prop]) {
        errors.push(`Missing required property: ${prop}`);
      }
    });
    
    Object.keys(config).forEach(key => {
      const value = config[key];
      if (typeof value === 'string' && !this.isValidColor(value)) {
        errors.push(`Invalid color value for ${key}: ${value}`);
      }
    });
    
    return {
      isValid: errors.length === 0,
      errors
    };
  }

  isValidColor(color) {
    const colorRegex = /^(#[0-9a-f]{3,8}|rgb\(|rgba\(|hsl\(|hsla\(|[a-z]+)$/i;
    return colorRegex.test(color);
  }

  async loadThemePreferences() {
    try {
      const preferences = await this.apiGet('/user/theme-preferences');
      this.setState({ themePreferences: preferences });
    } catch (error) {
      const stored = localStorage.getItem('themePreferences');
      if (stored) {
        try {
          const preferences = JSON.parse(stored);
          this.setState({ themePreferences: preferences });
        } catch (parseError) {
          logger.warn('Failed to parse stored theme preferences', { 
            error: parseError.message 
          });
        }
      }
    }
  }

  async saveThemePreference(theme) {
    const preferences = {
      ...this.state.themePreferences,
      selectedTheme: theme,
      lastChanged: new Date().toISOString()
    };
    
    this.setState({ themePreferences: preferences });
    
    localStorage.setItem('themePreferences', JSON.stringify(preferences));
    
    try {
      await this.apiPost('/user/theme-preferences', preferences);
    } catch (error) {
      logger.warn('Failed to save theme preferences to server', { 
        error: error.message 
      });
    }
  }

  getThemeStats() {
    return {
      ...themeService.getStats(),
      customThemeCount: this.state.customThemes.size,
      hasPreferences: Object.keys(this.state.themePreferences).length > 0
    };
  }

  async resetToDefault() {
    return this.changeTheme('light');
  }

  async applySystemTheme() {
    return this.changeTheme('auto');
  }

  destroy() {
    if (this.themeChangeListener) {
      this.themeChangeListener();
      this.themeChangeListener = null;
    }
    
    super.destroy();
  }
}

const themeController = new ThemeController();

export default themeController;
