/* Custom DataTable Wrapper */
.custom-datatable-wrapper {
  width: 100%;
  margin: 0;
  padding: 0;
}

/* Debug Info */
.custom-datatable__debug {
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  margin-bottom: 12px;
  font-family: monospace;
  font-size: 12px;
  color: #495057;
}

/* DataTable Header */
.datatable-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  background-color: #ffffff;
  border: 1px solid #dee2e6;
  border-bottom: none;
  border-radius: 6px 6px 0 0;
  gap: 16px;
  flex-wrap: wrap;
}

.datatable-global-filter {
  flex: 1;
  min-width: 250px;
}

.datatable-global-filter .p-input-icon-left {
  width: 100%;
}

.datatable-global-filter .p-input-icon-left input {
  width: 100%;
}

.datatable-actions {
  display: flex;
  gap: 8px;
  align-items: center;
}

/* Custom DataTable Styling */
.custom-datatable {
  border-radius: 0 0 6px 6px;
}

.custom-datatable .p-datatable-header {
  background-color: #f8f9fa;
  border-color: #dee2e6;
  padding: 12px 16px;
}

.custom-datatable .p-datatable-thead > tr > th {
  background-color: #f8f9fa;
  color: #495057;
  font-weight: 600;
  border-color: #dee2e6;
  padding: 12px 16px;
}

.custom-datatable .p-datatable-tbody > tr {
  transition: background-color 0.2s ease;
}

.custom-datatable .p-datatable-tbody > tr:hover {
  background-color: #f8f9fa;
}

.custom-datatable .p-datatable-tbody > tr > td {
  padding: 12px 16px;
  border-color: #dee2e6;
}

/* Table Actions */
.table-actions {
  display: flex;
  gap: 4px;
  align-items: center;
  justify-content: flex-start;
}

.table-actions .p-button {
  padding: 4px 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .datatable-header {
    flex-direction: column;
    align-items: stretch;
  }

  .datatable-global-filter {
    width: 100%;
  }

  .datatable-actions {
    width: 100%;
    justify-content: flex-start;
  }

  .custom-datatable .p-datatable-thead > tr > th,
  .custom-datatable .p-datatable-tbody > tr > td {
    padding: 8px 12px;
  }
}

/* Loading State */
.custom-datatable .p-datatable-loading-overlay {
  background-color: rgba(255, 255, 255, 0.8);
}

/* Empty Message */
.custom-datatable .p-datatable-emptymessage > td {
  text-align: center;
  padding: 32px 16px;
  color: #6c757d;
  font-style: italic;
}

/* Selection Highlight */
.custom-datatable .p-datatable-tbody > tr.p-highlight {
  background-color: #e7f3ff;
  color: #495057;
}

.custom-datatable .p-datatable-tbody > tr.p-highlight:hover {
  background-color: #d0e9ff;
}

/* Frozen Columns */
.custom-datatable .p-datatable-frozen-tbody > tr > td {
  background-color: #ffffff;
}

.custom-datatable .p-datatable-frozen-tbody > tr:hover > td {
  background-color: #f8f9fa;
}

/* Expandable Rows */
.custom-datatable .p-datatable-tbody > tr.p-datatable-row-expansion {
  background-color: #f8f9fa;
}

.custom-datatable .p-datatable-tbody > tr.p-datatable-row-expansion > td {
  padding: 16px;
}

/* Filter Inputs */
.custom-datatable .p-column-filter {
  width: 100%;
}

.custom-datatable .p-column-filter input {
  width: 100%;
}

/* Pagination */
.custom-datatable .p-paginator {
  background-color: #ffffff;
  border-color: #dee2e6;
  padding: 12px 16px;
}

.custom-datatable .p-paginator .p-paginator-current {
  color: #495057;
}

/* Scrollable Table */
.custom-datatable.p-datatable-scrollable .p-datatable-wrapper {
  border-radius: 0 0 6px 6px;
}

/* Striped Rows */
.custom-datatable.p-datatable-striped .p-datatable-tbody > tr:nth-child(even) {
  background-color: #f8f9fa;
}

/* Gridlines */
.custom-datatable.p-datatable-gridlines .p-datatable-thead > tr > th,
.custom-datatable.p-datatable-gridlines .p-datatable-tbody > tr > td {
  border: 1px solid #dee2e6;
}

/* Small Size */
.custom-datatable.p-datatable-sm .p-datatable-thead > tr > th,
.custom-datatable.p-datatable-sm .p-datatable-tbody > tr > td {
  padding: 8px 12px;
}

/* Large Size */
.custom-datatable.p-datatable-lg .p-datatable-thead > tr > th,
.custom-datatable.p-datatable-lg .p-datatable-tbody > tr > td {
  padding: 16px 20px;
}

/* Edit Mode */
.custom-datatable .p-datatable-tbody > tr.p-row-editing {
  background-color: #fff3cd;
}

.custom-datatable .p-datatable-tbody > tr.p-row-editing:hover {
  background-color: #ffe69c;
}

/* Column Resize */
.custom-datatable .p-datatable-thead > tr > th.p-resizable-column {
  position: relative;
}

.custom-datatable .p-column-resizer {
  display: block;
  position: absolute;
  top: 0;
  right: 0;
  margin: 0;
  width: 0.5rem;
  height: 100%;
  padding: 0;
  cursor: col-resize;
  border: 1px solid transparent;
}

.custom-datatable .p-column-resizer:hover {
  border-color: #dee2e6;
}

/* Reorderable Columns */
.custom-datatable .p-datatable-thead > tr > th.p-reorderable-column {
  cursor: move;
}

.custom-datatable .p-datatable-thead > tr > th.p-reorderable-column:hover {
  background-color: #e9ecef;
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .custom-datatable-wrapper {
    color: #e9ecef;
  }

  .datatable-header {
    background-color: #212529;
    border-color: #495057;
  }

  .custom-datatable__debug {
    background-color: #212529;
    border-color: #495057;
    color: #e9ecef;
  }

  .custom-datatable .p-datatable-header,
  .custom-datatable .p-datatable-thead > tr > th {
    background-color: #212529;
    color: #e9ecef;
    border-color: #495057;
  }

  .custom-datatable .p-datatable-tbody > tr {
    background-color: #343a40;
    color: #e9ecef;
  }

  .custom-datatable .p-datatable-tbody > tr:hover {
    background-color: #495057;
  }

  .custom-datatable .p-datatable-tbody > tr > td {
    border-color: #495057;
  }

  .custom-datatable .p-datatable-tbody > tr.p-highlight {
    background-color: #0d6efd;
    color: #ffffff;
  }

  .custom-datatable .p-datatable-tbody > tr.p-highlight:hover {
    background-color: #0b5ed7;
  }

  .custom-datatable .p-paginator {
    background-color: #212529;
    border-color: #495057;
  }

  .custom-datatable .p-paginator .p-paginator-current {
    color: #e9ecef;
  }
}

