import { useState } from "react";
import DynamicCollectionFormGrid from "../../Utils/DynamicFormComponentGrid";
import { formCollections } from "../../Utils/FormCollections";
const SignIn = () => {
  const [visible, setVisible] = useState(false);
  return (
    <div>
      SignIn
      <button type="button" onClick={() => setVisible(!visible)}>
        Show
      </button>
      {visible && (
        <>
        {/* <DynamicCollectionFormGrid
          visible={visible}
          setVisible={setVisible}
          data={formCollections}
          collectionNameToRender={'aviation_services_management_asm_customer_test_form'}
          onSubmit={(formData) => function (formData) {}}
          // auxJson={auxJson}
        />
         */}

        <DynamicCollectionFormGrid
        visible={visible}
        setVisible={setVisible}
        data={formCollections}
        collectionNameToRender={'add_route_details'}
        onSubmit={(formData) => function (formData) {}}
        // auxJson={auxJson}
      />
      </>
        
      )}
    </div>
  );
};
export default SignIn;
