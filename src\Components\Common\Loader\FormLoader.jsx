import React from 'react';
import { Skeleton } from 'primereact/skeleton';

const FormLoader = ({ fields = 3, showButton = true }) => {
    return (

        <div className=""> 
            <div className="glass-card-global flex align-items-center justify-content-start  p-4">
        <div className=" w-full sm:w-10 md:w-8 lg:w-full xl:w-full  justify-content-start">
            {/* Skeleton for form title */}
            <div className="mb-6">
                <Skeleton width="40%" height="2.5rem" />
            </div>

            {/* Skeleton for input fields */}
            {Array.from({ length: fields }).map((_, index) => (
                <div key={index} className="mb-6">
                    <Skeleton width="30%" height="1.2rem" className="mb-3" />
                    <Skeleton width="100%" height="3rem" />
                </div>
            ))}

            {/* Skeleton for helper text */}
            <div className="mb-6">
                <Skeleton width="80%" height="1rem" />
            </div>

            {/* Skeleton for button */}
            {showButton && (
                <div>
                    <Skeleton width="100%" height="3rem" />
                </div>
            )}
        </div>
    </div>
    </div>
);
           
};

export default FormLoader;
