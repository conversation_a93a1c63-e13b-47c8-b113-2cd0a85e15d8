import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Toast } from 'primereact/toast';
import { Tag } from 'primereact/tag';
import { Button } from 'primereact/button';
import { Card } from 'primereact/card';
import DynamicComponent from '../components/framework/core/DynamicComponent';
import componentRegistry from '../components/framework/core/ComponentRegistry';
import CRMNavigation from './CRMNavigation';
import Paginator from '../components/common/Paginator';
import './CRMViews.css';
import '../styles/glassy/glassy-ui.css';

const ProspectListViewDynamic = () => {
  const navigate = useNavigate();
  const toast = useRef(null);
  const [globalFilter, setGlobalFilter] = useState('');
  const [paginatedUsers, setPaginatedUsers] = useState([]);

  useEffect(() => {
    if (!componentRegistry.initialized) {
      componentRegistry.init();
    }
  }, []);

  const prospects = [
    {
      id: 'PROS-001',
      account: 'Emirates Airlines',
      name: '<PERSON>',
      phone: '+971 4 214 4444',
      status: 'Qualification',
      salesperson: 'John Doe',
      tags: 'VIP, Priority',
      createdDate: '2024-01-20'
    },
    {
      id: 'PROS-002',
      account: 'Qatar Airways',
      name: 'Ahmed Ali',
      phone: '+974 4023 0000',
      status: 'Proposal',
      salesperson: 'Jane Smith',
      tags: 'High Value',
      createdDate: '2024-02-01'
    },
    {
      id: 'PROS-003',
      account: 'Etihad Airways',
      name: 'Michael Brown',
      phone: '+971 2 599 0000',
      status: 'Negotiation',
      salesperson: 'Mike Johnson',
      tags: 'Enterprise',
      createdDate: '2024-02-10'
    }
  ];

  const stats = [
    { label: 'Total Prospects', value: prospects.length, icon: 'pi-briefcase', color: '#3b82f6' },
    { label: 'Qualification', value: prospects.filter(p => p.status === 'Qualification').length, icon: 'pi-filter', color: '#f59e0b' },
    { label: 'Proposal', value: prospects.filter(p => p.status === 'Proposal').length, icon: 'pi-file', color: '#10b981' },
    { label: 'Negotiation', value: prospects.filter(p => p.status === 'Negotiation').length, icon: 'pi-comments', color: '#8b5cf6' }
  ];

  const showToast = useCallback((severity, summary, detail) => {
    toast.current?.show({ severity, summary, detail, life: 3000 });
  }, []);

  const handleEdit = useCallback((rowData) => {
    showToast('info', 'Edit Prospect', `Editing prospect: ${rowData.account}`);
    navigate(`/prospect/${rowData.id}`);
  }, [navigate, showToast]);

  const handleCreateCustomer = useCallback((rowData) => {
    showToast('success', 'Create Customer', `Converting ${rowData.account} to customer...`);
    setTimeout(() => navigate('/customers'), 1500);
  }, [navigate, showToast]);

  const handleRowClick = useCallback((eventData) => {
    const rowData = eventData.row || eventData.data;
    if (rowData) {
      navigate(`/prospect/${rowData.id}`);
    }
  }, [navigate]);

  const columns = [
    { field: 'account', header: 'Account', sortable: true },
    { field: 'name', header: 'Name', sortable: true },
    { field: 'phone', header: 'Phone', sortable: true },
    {
      field: 'status',
      header: 'Status',
      sortable: true,
      body: (rowData) => {
        const statusClasses = {
          'Qualification': 'bg-yellow-100 text-yellow-800',
      'Proposal': 'bg-blue-100 text-blue-800',
      'Negotiation': 'bg-green-100 text-green-800'
        };
        return (
          <span className={`glass-badge ${statusClasses[rowData.status] || 'bg-gray-100 text-gray-800'}`}>
        {rowData.status}
      </span>
        );
      }
    },
    { field: 'salesperson', header: 'Salesperson', sortable: true },
    { field: 'tags', header: 'Tags', sortable: false },
    { field: 'createdDate', header: 'Created Date', sortable: true }
  ];

  const tableConfig = {
    pagination: {
      enabled: false
    },
    sorting: { enabled: true },
    filtering: {
      enabled: false,
      globalFilter: globalFilter
    },
    selection: {
      enabled: true,
      mode: 'multiple'
    },
    actions: [
      {
        name: 'view',
        label: '',
        icon: 'pi pi-eye',
        // severity: 'info',
        className: 'glass-badge bg-blue-100 text-blue-800 hover:bg-blue-200',
        onClick: (rowData) => navigate(`/prospect/${rowData.id}`)
      },
      {
        name: 'edit',
        label: '',
        icon: 'pi pi-pencil',
        // severity: 'secondary',
        className: 'glass-badge bg-blue-100 text-blue-800 hover:bg-blue-200',
        onClick: handleEdit
      },
      // {
      //   name: 'createCustomer',
      //   label: 'Create Customer',
      //   icon: 'pi pi-user-plus',
      //   // severity: 'success',
      //   className: 'glass-badge-width bg-blue-100 text-blue-800 hover:bg-blue-200',
      //   onClick: handleCreateCustomer
      // }
    ],
    rowHover: true,
    stripedRows: true,
    showGridlines: false,
    responsiveLayout: 'scroll',
    emptyMessage: 'No prospects found'
  };

  const handleTableEvent = useCallback((eventName, eventData) => {
    console.log('Table event:', eventName, eventData);

    switch (eventName) {
      case 'rowClick':
        handleRowClick(eventData);
        break;
      case 'selectionChange':
        showToast('info', 'Selection Changed', `Selected ${eventData.selection?.length || 0} prospects`);
        break;
      default:
        break;
    }
  }, [handleRowClick, showToast]);

  const onPageChange = (page, paginatedData) => {
    console.log("Page changed to:", page, "Data:", paginatedData);
    setPaginatedUsers(paginatedData);
  };

  return (
    <div className="prospect-list-view">
      <Toast ref={toast} />
      {/* <CRMNavigation /> */}
      
      <div className="mb-4">
        <h1>CRM - Prospects</h1>
        <p>Manage your prospects and convert to customers</p>
      </div>

      <div className="stats-grid">
        {stats.map((stat, index) => (
          <div key={index} className="glass-card-doc-management">
            <div className="stat-content">
              <div className="stat-icon" style={{ backgroundColor: `${stat.color}20` }}>
                <i className={`pi ${stat.icon}`} style={{ color: stat.color }}></i>
              </div>
              <div className="stat-details">
                <div className="stat-value">{stat.value}</div>
                <div className="stat-label">{stat.label}</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="glass-table-container" style={{ marginTop: '1.5rem' }}>
        <DynamicComponent
          type="datatable"
          config={{
            data: paginatedUsers.length > 0 ? paginatedUsers : prospects,
            columns: columns,
            config: tableConfig,
            onEvent: handleTableEvent
          }}
          debug={true}
        />
        
        <Paginator
          data={prospects}
          itemsPerPage={10}
          onPageChange={onPageChange}
        />
      </div>
    </div>
  );
};

export default ProspectListViewDynamic;

