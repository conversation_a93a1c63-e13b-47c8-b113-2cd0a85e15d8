
.theme-selector {
  background: var(--theme-bg-card);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-6);
  box-shadow: var(--theme-card-shadow);
  transition: var(--transition-all);
}

.theme-selector:hover {
  box-shadow: var(--theme-card-hover-shadow);
}

.theme-selector__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-4);
}

.theme-selector__title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0;
}

.theme-selector__loading {
  font-size: var(--font-size-sm);
  color: var(--theme-text-secondary);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.theme-selector__loading::before {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid var(--theme-border-primary);
  border-top-color: var(--theme-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.theme-selector__error {
  background: var(--theme-error-bg);
  color: var(--theme-error-text);
  border: 1px solid var(--theme-error-border);
  border-radius: var(--border-radius-base);
  padding: var(--spacing-3);
  margin-bottom: var(--spacing-4);
  font-size: var(--font-size-sm);
}

.theme-selector__options {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-4);
  margin-bottom: var(--spacing-6);
}

.theme-option {
  position: relative;
  border: 2px solid var(--theme-border-primary);
  border-radius: var(--border-radius-md);
  overflow: hidden;
  transition: var(--transition-all);
}

.theme-option:hover {
  border-color: var(--theme-border-accent);
  transform: translateY(-2px);
}

.theme-option--selected {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
}

.theme-option__button {
  width: 100%;
  background: var(--theme-bg-card);
  border: none;
  padding: var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  cursor: pointer;
  transition: var(--transition-colors);
  position: relative;
}

.theme-option__button:hover {
  background: var(--theme-bg-secondary);
}

.theme-option__button:focus {
  outline: 2px solid var(--theme-focus-ring);
  outline-offset: 2px;
}

.theme-option__button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.theme-option__icon {
  font-size: var(--font-size-xl);
  line-height: 1;
}

.theme-option__name {
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  flex: 1;
  text-align: left;
}

.theme-option__check {
  color: var(--theme-primary);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-lg);
}

.theme-option__preview {
  height: 4px;
  display: flex;
}

.theme-preview__primary {
  flex: 1;
  background: var(--color-primary-500);
}

.theme-preview__secondary {
  flex: 1;
  background: var(--color-secondary-500);
}

.theme-preview__background {
  flex: 2;
  background: var(--color-gray-100);
}

/* Dark theme preview */
.theme-option__preview--dark .theme-preview__primary {
  background: var(--color-primary-400);
}

.theme-option__preview--dark .theme-preview__secondary {
  background: var(--color-secondary-400);
}

.theme-option__preview--dark .theme-preview__background {
  background: var(--color-gray-800);
}

/* Auto theme preview */
.theme-option__preview--auto {
  background: linear-gradient(90deg, 
    var(--color-gray-100) 0%, 
    var(--color-gray-100) 50%, 
    var(--color-gray-800) 50%, 
    var(--color-gray-800) 100%
  );
}

.theme-selector__custom {
  border-top: 1px solid var(--theme-border-primary);
  padding-top: var(--spacing-4);
  margin-bottom: var(--spacing-4);
}

.theme-selector__create-btn {
  width: 100%;
  background: var(--theme-bg-secondary);
  border: 2px dashed var(--theme-border-secondary);
  border-radius: var(--border-radius-md);
  padding: var(--spacing-4);
  color: var(--theme-text-secondary);
  cursor: pointer;
  transition: var(--transition-all);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  font-weight: var(--font-weight-medium);
}

.theme-selector__create-btn:hover {
  border-color: var(--theme-border-accent);
  color: var(--theme-text-primary);
  background: var(--theme-bg-tertiary);
}

.theme-selector__create-btn:focus {
  outline: 2px solid var(--theme-focus-ring);
  outline-offset: 2px;
}

.theme-selector__custom-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin: var(--spacing-4) 0 var(--spacing-2) 0;
}

.theme-selector__custom-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-2);
}

.custom-theme-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.custom-theme-item__button {
  flex: 1;
  background: var(--theme-bg-card);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--border-radius-base);
  padding: var(--spacing-2) var(--spacing-3);
  color: var(--theme-text-primary);
  cursor: pointer;
  transition: var(--transition-colors);
  text-align: left;
}

.custom-theme-item__button:hover {
  background: var(--theme-bg-secondary);
  border-color: var(--theme-border-accent);
}

.custom-theme-item__button--selected {
  background: var(--theme-primary-light);
  border-color: var(--theme-primary);
  color: var(--theme-primary);
}

.custom-theme-item__delete {
  background: var(--theme-error);
  border: none;
  border-radius: var(--border-radius-base);
  width: 28px;
  height: 28px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-lg);
  line-height: 1;
  transition: var(--transition-colors);
}

.custom-theme-item__delete:hover {
  background: var(--color-error-700);
}

.theme-selector__actions {
  display: flex;
  gap: var(--spacing-3);
  flex-wrap: wrap;
}

.theme-selector__action-btn {
  background: var(--theme-button-secondary-bg);
  border: 1px solid var(--theme-button-secondary-border);
  border-radius: var(--border-radius-base);
  padding: var(--spacing-2) var(--spacing-4);
  color: var(--theme-button-secondary-text);
  cursor: pointer;
  transition: var(--transition-colors);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.theme-selector__action-btn:hover {
  background: var(--theme-button-secondary-hover);
}

.theme-selector__action-btn:focus {
  outline: 2px solid var(--theme-focus-ring);
  outline-offset: 2px;
}

.theme-selector__action-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* Compact Theme Selector */
.theme-selector--compact {
  padding: 0;
  background: transparent;
  border: none;
  box-shadow: none;
  display: inline-block;
}

.theme-toggle-btn {
  background: var(--theme-bg-card);
  border: 1px solid var(--theme-border-primary);
  border-radius: var(--border-radius-full);
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: var(--transition-all);
  font-size: var(--font-size-lg);
}

.theme-toggle-btn:hover {
  background: var(--theme-bg-secondary);
  border-color: var(--theme-border-accent);
  transform: scale(1.05);
}

.theme-toggle-btn:focus {
  outline: 2px solid var(--theme-focus-ring);
  outline-offset: 2px;
}

.theme-toggle-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Custom Theme Modal */
.custom-theme-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: var(--z-index-modal);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-4);
}

.custom-theme-modal__backdrop {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--theme-modal-overlay);
  backdrop-filter: blur(4px);
}

.custom-theme-modal__content {
  position: relative;
  background: var(--theme-modal-bg);
  border: 1px solid var(--theme-modal-border);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--theme-elevation-5);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.custom-theme-modal__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--theme-border-primary);
}

.custom-theme-modal__title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-semibold);
  color: var(--theme-text-primary);
  margin: 0;
}

.custom-theme-modal__close {
  background: none;
  border: none;
  font-size: var(--font-size-2xl);
  color: var(--theme-text-secondary);
  cursor: pointer;
  padding: var(--spacing-1);
  border-radius: var(--border-radius-base);
  transition: var(--transition-colors);
  line-height: 1;
}

.custom-theme-modal__close:hover {
  color: var(--theme-text-primary);
  background: var(--theme-bg-secondary);
}

.custom-theme-modal__body {
  padding: var(--spacing-6);
  overflow-y: auto;
  flex: 1;
}

.custom-theme-modal__footer {
  display: flex;
  gap: var(--spacing-3);
  justify-content: flex-end;
  padding: var(--spacing-6);
  border-top: 1px solid var(--theme-border-primary);
}

.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: block;
  font-weight: var(--font-weight-medium);
  color: var(--theme-text-primary);
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
}

.form-input {
  width: 100%;
  background: var(--theme-input-bg);
  border: 1px solid var(--theme-input-border);
  border-radius: var(--border-radius-base);
  padding: var(--spacing-3);
  color: var(--theme-input-text);
  font-size: var(--font-size-base);
  transition: var(--transition-colors);
}

.form-input:focus {
  outline: none;
  border-color: var(--theme-input-focus);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled {
  background: var(--theme-input-disabled);
  opacity: 0.6;
  cursor: not-allowed;
}

.color-input-group {
  display: flex;
  gap: var(--spacing-2);
  align-items: center;
}

.color-input {
  width: 50px;
  height: 40px;
  border: 1px solid var(--theme-input-border);
  border-radius: var(--border-radius-base);
  cursor: pointer;
  background: none;
  padding: 0;
}

.color-input::-webkit-color-swatch-wrapper {
  padding: 0;
}

.color-input::-webkit-color-swatch {
  border: none;
  border-radius: calc(var(--border-radius-base) - 1px);
}

.btn {
  background: var(--theme-button-primary-bg);
  border: none;
  border-radius: var(--border-radius-base);
  padding: var(--spacing-3) var(--spacing-6);
  color: var(--theme-button-primary-text);
  cursor: pointer;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  transition: var(--transition-colors);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
}

.btn:hover {
  background: var(--theme-button-primary-hover);
}

.btn:focus {
  outline: 2px solid var(--theme-focus-ring);
  outline-offset: 2px;
}

.btn:disabled {
  background: var(--theme-button-primary-disabled);
  cursor: not-allowed;
}

.btn--secondary {
  background: var(--theme-button-secondary-bg);
  border: 1px solid var(--theme-button-secondary-border);
  color: var(--theme-button-secondary-text);
}

.btn--secondary:hover {
  background: var(--theme-button-secondary-hover);
}

/* Screen reader only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Animations */
@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 640px) {
  .theme-selector__options {
    grid-template-columns: 1fr;
  }
  
  .theme-selector__actions {
    flex-direction: column;
  }
  
  .custom-theme-modal__content {
    margin: var(--spacing-4);
    max-height: calc(100vh - 2 * var(--spacing-4));
  }
  
  .custom-theme-modal__header,
  .custom-theme-modal__body,
  .custom-theme-modal__footer {
    padding: var(--spacing-4);
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .theme-option {
    border-width: 3px;
  }
  
  .theme-option--selected {
    border-width: 4px;
  }
  
  .theme-toggle-btn {
    border-width: 2px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .theme-selector,
  .theme-option,
  .theme-option__button,
  .theme-toggle-btn,
  .theme-selector__action-btn,
  .custom-theme-item__button {
    transition: none;
  }
  
  .theme-option:hover {
    transform: none;
  }
  
  .theme-toggle-btn:hover {
    transform: none;
  }
}
