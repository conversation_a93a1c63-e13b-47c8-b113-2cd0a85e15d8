import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { Toast } from 'primereact/toast';
import { RiAlignItemTopLine } from 'react-icons/ri';
import { TfiViewGrid } from 'react-icons/tfi';

import { Card } from 'primereact/card';
import { Tag } from 'primereact/tag';
import { Rating } from 'primereact/rating';
import { Button } from 'primereact/button';
import DynamicComponent from '../components/framework/core/DynamicComponent';
import componentRegistry from '../components/framework/core/ComponentRegistry';
import CRMNavigation from '../crm/CRMNavigation';
import '../styles/glassy/glassy-ui.css'
import '../styles/glassy/global.css'

import Paginator from '../components/common/Paginator';
import SearchInput from '../components/common/SearchInput';
import FilterBy from '../components/common/FilterBy';
import FromToDateInput from '../components/common/FromToDateInput';
import GlassyBlueButton from '../components/common/GlassyBlueButton';
import useTableData from '../store/Reducers/useTableData';
import LeadController from '@controllers/leadmanagement/LeadController';

const LeadManagementRootDynamic = () => {
  const navigate = useNavigate();
  const toast = useRef(null);
  const [globalFilter, setGlobalFilter] = useState('');
  const [paginatedUsers, setPaginatedUsers] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [dateRange, setDateRange] = useState({ from: null, to: null });
  const [selectedFilter, setSelectedFilter] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [loading, setLoading] = useState(false);

  const {
    data: fetchedStateData,
    isLoading: stateLoading,
    isError: isStateError,
    error: stateError,
    refetch: reftechStateData,
  } = useTableData("state");


  useEffect(() => {
    if (!componentRegistry.initialized) {
      componentRegistry.init();
    }
  }, []);

  // const leads = [
  //   {
  //     id: 'ASK-000001',
  //     company: 'Mahan Air',
  //     contact: 'John Doe',
  //     email: '<EMAIL>',
  //     phone: '+98 21 4884 5000',
  //     status: 'Introduction',
  //     priority: 'Medium',
  //     stages: 'New',
  //     revenue: '$7000',
  //     source: 'Event',
  //     salesPerson: 'Jessica Marlowe',
  //     rating: 2,
  //     createdDate: '2024-01-15'
  //   },
  //   {
  //     id: 'ASK-000002',
  //     company: 'Emirates Airlines',
  //     contact: 'Sarah Smith',
  //     email: '<EMAIL>',
  //     phone: '+971 4 214 4444',
  //     status: 'Qualification',
  //     priority: 'High',
  //     stages: 'Won',
  //     revenue: '$15000',
  //     source: 'Website',
  //     salesPerson: 'Michael Johnson',
  //     rating: 4,
  //     createdDate: '2024-01-20'
  //   },
  //   {
  //     id: 'ASK-000003',
  //     company: 'Qatar Airways',
  //     contact: 'Ahmed Ali',
  //     email: '<EMAIL>',
  //     phone: '+974 4023 0000',
  //     status: 'Proposal',
  //     priority: 'High',
  //     stages: 'Won',
  //     revenue: '$25000',
  //     source: 'Referral',
  //     salesPerson: 'Sarah Williams',
  //     rating: 5,
  //     createdDate: '2024-02-01'
  //   },
  //   {
  //     id: 'ASK-000004',
  //     company: 'Etihad Airways',
  //     contact: 'Michael Brown',
  //     email: '<EMAIL>',
  //     phone: '+971 2 599 0000',
  //     status: 'Negotiation',
  //     priority: 'Medium',
  //     stages: 'Opportunity',
  //     revenue: '$12000',
  //     source: 'Cold Call',
  //     salesPerson: 'David Chen',
  //     rating: 3,
  //     createdDate: '2024-02-10'
  //   },
  //   {
  //     id: 'ASK-000005',
  //     company: 'Turkish Airlines',
  //     contact: 'Ayse Yilmaz',
  //     email: '<EMAIL>',
  //     phone: '+90 ************',
  //     status: 'Introduction',
  //     priority: 'Low',
  //     stages: 'Lost',
  //     revenue: '$5000',
  //     source: 'Event',
  //     salesPerson: 'Emily Rodriguez',
  //     rating: 2,
  //     createdDate: '2024-02-15'
  //   },
  //   {
  //     id: 'ASK-000006',
  //     company: 'khan Airlines',
  //     contact: 'Sajeed syed',
  //     email: '<EMAIL>',
  //     phone: '+90 ************',
  //     status: 'Introduction',
  //     priority: 'Low',
  //     stages: 'Lost',
  //     revenue: '$5000',
  //     source: 'Cold Call',
  //     salesPerson: 'Jessica Marlowe',
  //     rating: 4,
  //     createdDate: '2024-02-15'
  //   }
  // ];

  
  const [leads, setLeads] = useState([]);

  useEffect(() => {
    const listener = ({ type, newValue }) => {
      if (type === 'state' && newValue.leads) {
        setLeads(newValue.leads);
      }
    };
    LeadController.addEventListener(listener);
    LeadController.fetchLeads();
    return () => LeadController.removeEventListener(listener);
  }, []);

  const showToast = useCallback((severity, summary, detail) => {
    toast.current?.show({ severity, summary, detail, life: 3000 });
  }, []);

  const handleRowClick = useCallback((eventData) => {
    const rowData = eventData.row || eventData.data;
    if (rowData) {
      navigate(`/lead/${rowData.id}`);
    }
  }, [navigate]);

  const handleEdit = useCallback((rowData) => {
    showToast('info', 'Edit Lead', `Editing lead: ${rowData.company}`);
    navigate(`/lead/${rowData.id}`);
  }, [navigate, showToast]);

  const handleDelete = useCallback((rowData) => {
    showToast('warn', 'Delete Lead', `Delete lead: ${rowData.company}`);
  }, [showToast]);

  const handleConvertToProspect = useCallback((rowData) => {
    showToast('success', 'Convert to Prospect', `Converting ${rowData.company} to prospect...`);
    setTimeout(() => navigate('/prospects'), 1500);
  }, [navigate, showToast]);

  // Filter options for "Filter by" dropdown
  const filterOptions = [
    { label: 'All Leads', value: 'all' },
    { label: 'New', value: 'new' },
    { label: 'Opportunity', value: 'opportunity' },
    { label: 'Won', value: 'won' },
    { label: 'Lost', value: 'lost' },
  ];

  // Category options for "Select" dropdown
  const categoryOptions = [
    { label: 'All Categories', value: 'all' },
    { label: 'Airlines', value: 'airlines' },
    { label: 'Aviation', value: 'aviation' },
    { label: 'Skyways', value: 'skyways' },
  ];

  // Handle search
  const handleSearch = () => {
    console.log('Search:', searchValue);
    // Implement search logic here
  };

  // Handle filter change
  const handleFilterChange = (value) => {
    setSelectedFilter(value);
    console.log('Filter changed:', value);
    // Implement filter logic here
  };

  // Handle category change
  const handleCategoryChange = (value) => {
    setSelectedCategory(value);
    console.log('Category changed:', value);
    // Implement category filter logic here
  };

  const handleAdd = () => {
    console.log('Add new lead');
    showToast('info', 'Add Lead', 'Add new lead functionality');
    // Implement add logic
  };

  const handleFavorite = () => {
    console.log('Toggle favorite');
    showToast('info', 'Favorite', 'Toggle favorite functionality');
    // Implement favorite logic
  };

  const handleExport = () => {
    console.log('Export leads');
    showToast('success', 'Export', 'Exporting leads...');
    // Implement export logic
  };

  const handleSalesPipeline = () => {
    navigate('/sales-pipeline');
  };

  const handleChart = () => {
    console.log('View chart');
    showToast('info', 'Chart', 'View chart functionality');
    // Implement chart view logic
  };

  const stats = [
    { label: 'Total Leads', value: leads.length, icon: 'pi-users', color: '#3b82f6' },
    { label: 'New Leads', value: leads.filter(l => l.status === 'Introduction').length, icon: 'pi-clock', color: '#f59e0b' },
    { label: 'Qualified', value: leads.filter(l => l.status === 'Qualification').length, icon: 'pi-check-circle', color: '#10b981' },
    { label: 'High Priority', value: leads.filter(l => l.priority === 'High').length, icon: 'pi-exclamation-triangle', color: '#ef4444' }
  ];

  const columns = [
   
    {
      field: 'company',
      header: 'Company Name',
      sortable: true,
      body: (rowData) => (
        <div className="company-cell">
          <strong>{rowData.company}</strong>
          {/* <div className="text-sm text-gray-600">{rowData.id}</div> */}
        </div>
      )
    },
    {
      field: 'contact',
      header: 'Contact Name',
      sortable: true,
      body: (rowData) => (
        <div>
          <div>{rowData.contact}</div>
        </div>
      )
    },
    { field: 'phone', header: 'Contact Number', sortable: true},
    { field: 'email', header: 'Email', sortable: true},
    { field: 'salesPerson', header: 'Sales Person', sortable: true},
    // {
    //   field: 'status',
    //   header: 'Status',
    //   sortable: true,
    //   body: (rowData) => {
    //     const statusClasses = {
    //       'Introduction': 'info',
    //       'Qualification': 'warning',
    //       'Proposal': 'success',
    //       'Negotiation': 'danger'
    //     };
    //     return <span className={`glass-badge ${statusClasses[rowData.status] || 'bg-gray-100 text-gray-800'}`}>
    //     {rowData.status}
    //   </span>;
    //   }
    // },
    
    //{
    //   field: 'priority',
    //   header: 'Priority',
    //   sortable: true,
    //   body: (rowData) => {
    //     const priorityClasses = {
    //       'Low': 'success',
    //       'Medium': 'warning',
    //       'High': 'danger'
    //     };
    //     return (
    //      <span className={`glass-badge ${priorityClasses[rowData.priority] || 'bg-gray-100 text-gray-800'}`}>
    //     {rowData.priority}
    //   </span>
    //   );
    //   }
    // },
    { field: 'revenue', header: 'Expected Revenue', sortable: true },

{
      field: 'stages',
      header: 'Stages',
      sortable: true,
      body: (rowData) => {
        const stagesClasses = {
          'Won': 'success',
          'Opportunity': 'warning',
          'Lost': 'danger',
          'New': 'info'
        };
        return (
         <span className={`glass-badge ${stagesClasses[rowData.stages] || 'bg-gray-100 text-gray-800'}`}>
        {rowData.stages}
      </span>
      );
      }
    },


    // { field: 'source', header: 'Source', sortable: true },
    // {
    //   field: 'rating',
    //   header: 'Rating',
    //   sortable: true,
    //   body: (rowData) => (
    //     <Rating value={rowData.rating} readOnly cancel={false} stars={5} />
    //   )
    // },
    // { field: 'createdDate', header: 'Created', sortable: true }
  ];

  const tableConfig = {
    pagination: {
      enabled: false
    },
    sorting: { enabled: true },
    filtering: { 
      enabled: false, 
      globalFilter: globalFilter 
    },
    selection: { 
      enabled: true, 
      mode: 'multiple' 
    },
    actions: [
      {
        name: 'view',
        label: '',
        icon: 'pi pi-eye',
        className: 'glass-badge bg-blue-100 text-blue-800 hover:bg-blue-200',
        onClick: (rowData) => navigate(`/lead/${rowData.id}`)
      },
      {
        name: 'edit',
        label: '',
        icon: 'pi pi-pencil',
        // severity: 'secondary',
        className: 'glass-badge bg-blue-100 text-blue-800 hover:bg-blue-200',
        onClick: handleEdit
      },
      //{
      //   name: 'convertToProspect',
      //   label: 'Convert to Prospect',
      //   icon: 'pi pi-arrow-right',
      //   // severity: 'success',
      //   className: 'glass-badge-width bg-blue-100 text-blue-800 hover:bg-blue-200 flex',
      //   onClick: handleConvertToProspect
      // },
      {
        name: 'delete',
        label: '',
        icon: 'pi pi-trash',
        // severity: 'danger',
        className: 'glass-badge bg-blue-100 text-blue-800 hover:bg-blue-200',
        onClick: handleDelete
      }
    ],
    rowHover: true,
    stripedRows: false,
    showGridlines: false,
    responsiveLayout: 'scroll',
    emptyMessage: 'No leads found'
  };

  const handleTableEvent = useCallback((eventName, eventData) => {
    console.log('Table event:', eventName, eventData);

    switch (eventName) {
      case 'rowClick':
        handleRowClick(eventData);
        break;
      case 'selectionChange':
        showToast('info', 'Selection Changed', `Selected ${eventData.selection?.length || 0} leads`);
        break;
      default:
        break;
    }
  }, [handleRowClick, showToast]);
  const onPageChange = (page, paginatedData) => {
    console.log("Page changed to:", page, "Data:", paginatedData);
    setPaginatedUsers(paginatedData);
  };

  console.log('=== LeadManagementRootDynamic DEBUG ===');
  console.log('Leads data:', leads);
  console.log('Leads length:', leads?.length);
  console.log('Columns:', columns);
  console.log('Table config:', tableConfig);

  return (
    <div className="lead-management-root">
      <Toast ref={toast} />
      {/* <CRMNavigation /> */}
      
      {/* Content Above Table - Filter and Search Section */}
      <div className="glass-content-section">
        {/* Controls */}
        <div className="leads-controls-container">
          <div className="leads-filter-section">
            {/* Filter By Dropdown */}
            <FilterBy
              value={selectedFilter}
              options={filterOptions}
              onChange={handleFilterChange}
              placeholder="Filter by"
            />

            {/* Select Category Dropdown */}
            <FilterBy
              value={selectedCategory}
              options={categoryOptions}
              onChange={handleCategoryChange}
              placeholder="Select"
            />

            {/* Search Input */}
            <div className="leads-search-wrapper">
              <SearchInput
                iconPosition="start"
                value={searchValue}
                onChange={setSearchValue}
                onSearch={handleSearch}
                placeholder="Search"
              />
            </div>

            {/* Date Range */}
            <FromToDateInput
              fromDate={dateRange.from}
              toDate={dateRange.to}
              onFromDateChange={(date) =>
                setDateRange({ ...dateRange, from: date })
              }
              onToDateChange={(date) =>
                setDateRange({ ...dateRange, to: date })
              }
            />

            {/* Action Buttons */}
            <div className="leads-action-buttons">
              <Button
                icon="pi pi-plus"
                className="p-button-rounded p-button-text"
                onClick={handleAdd}
                tooltip="Add"
              />
              <Button
                icon="pi pi-star"
                className="p-button-rounded p-button-text"
                onClick={handleFavorite}
                tooltip="Favorite"
              />
              <GlassyBlueButton
                label="Export"
                icon="pi pi-download"
                onClick={handleExport}
              />
              <Button
                icon={<RiAlignItemTopLine />}
                className="p-button-rounded p-button-text"
                onClick={handleSalesPipeline}
                tooltip="Sales"
              />
              <Button
                icon="pi pi-chart-bar"
                className="p-button-rounded p-button-text"
                onClick={handleChart}
                tooltip="Chart"
              />
            </div>
          </div>
        </div>
      </div>

      <div className="glass-card-container mb-4 mt-4">
        <h1>CRM - Lead Management</h1>
        <p>Manage your leads and track opportunities</p>
      </div>

      <div className="stats-grid">
        {stats.map((stat, index) => (
          <div key={index} className="glass-card-doc-management">
            <div className="stat-content">
              <div className="stat-icon" style={{ backgroundColor: `${stat.color}20` }}>
                <i className={`pi ${stat.icon}`} style={{ color: stat.color }}></i>
              </div>
              <div className="stat-details">
                <div className="stat-value">{stat.value}</div>
                <div className="stat-label">{stat.label}</div>
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="glass-table-container" style={{ marginTop: '1.5rem' }}>
        <DynamicComponent
          type="datatable"
          config={{
            data: paginatedUsers.length > 0 ? paginatedUsers : leads,
            columns: columns,
            config: tableConfig,
            onEvent: handleTableEvent
          }}
          debug={true}
        />
        
        <Paginator
          data={leads}
          itemsPerPage={10}
          onPageChange={onPageChange}
        />
      </div>
    </div>
  );
};

export default LeadManagementRootDynamic;

