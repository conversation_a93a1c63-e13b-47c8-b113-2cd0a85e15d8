import AuthService from '@services/auth/authService';


export const handleLogin = async (credentials) => {
  try {
    const result = await AuthService.loginApi(credentials);

    const isMfaRequired =
      result?.requiresMfa ||
      result?.requiresMFA ||
      result?.mfaRequired ||
      result?.errorMessage ===
        'Multi-factor authentication or conditional access required' ||
      result?.message ===
        'Multi-factor authentication or conditional access required';

    const oAuthUrl =
      result?.oAuth2Url || result?.authUrl || result?.oauthUrl || result?.oauth2Url;

    return {
      isSuccess: !!result?.isSuccess,
      requiresMfa: !!isMfaRequired,
      oauthUrl: oAuthUrl,
      data: result,
      errorMessage: result?.errorMessage || null,
    };
  } catch (error) {
    const errorData = error.response?.data;
    const errorMessage = errorData?.errorMessage || errorData?.message || error.message;

    const isMfaRequired =
      errorData?.requiresMfa ||
      errorData?.requiresMFA ||
      errorData?.mfaRequired ||
      errorMessage === 'Multi-factor authentication or conditional access required' ||
      errorMessage?.includes('Multi-factor authentication') ||
      errorMessage?.includes('conditional access');

    const oAuthUrl =
      errorData?.oAuth2Url || errorData?.authUrl || errorData?.oauthUrl || errorData?.oauth2Url;

    return {
      isSuccess: false,
      requiresMfa: !!isMfaRequired,
      oauthUrl: oAuthUrl,
      data: errorData || null,
      errorMessage,
    };
  }
};

export const handleOauth2Callback = async (callbackData) => {
  try {
    const result = await AuthService.oauth2Callback(callbackData);
    return {
      isSuccess: !!result?.isSuccess,
      data: result,
      errorMessage: result?.errorMessage || null,
    };
  } catch (error) {
    const errorMessage = error.response?.data?.errorMessage || error.message;
    return {
      isSuccess: false,
      data: error.response?.data || null,
      errorMessage,
    };
  }
};

export const handleForgotPassword = async (email) => {
  try {
    const result = await AuthService.forgotPassword(email);
    return {
      isSuccess: !!result?.isSuccess,
      data: result,
      message: result?.message || 'Password reset OTP sent successfully',
      errorMessage: null,
    };
  } catch (error) {
    const errorData = error.response?.data;
    const errorMessage = errorData?.message || error.message || 'Failed to send reset code. Please try again.';

    return {
      isSuccess: false,
      data: errorData || null,
      message: null,
      errorMessage,
    };
  }
};

export const handleVerifyOtp = async (email, otp) => {
  try {
    const result = await AuthService.verifyOtp(email, otp);
    return {
      isSuccess: !!result?.isSuccess,
      data: result,
      message: result?.message || 'OTP verified successfully',
      errorMessage: null,
    };
  } catch (error) {
    const errorData = error.response?.data;
    const errorMessage = errorData?.message || error.message || 'Invalid verification code. Please try again.';

    return {
      isSuccess: false,
      data: errorData || null,
      message: null,
      errorMessage,
    };
  }
};

export const handleResetPassword = async (email, newPassword, confirmPassword) => {
  try {
    const result = await AuthService.resetPasswordNew(email, newPassword, confirmPassword);
    return {
      isSuccess: !!result?.isSuccess,
      data: result,
      message: result?.message || 'Password reset successfully',
      errorMessage: null,
    };
  } catch (error) {
    const errorData = error.response?.data;
    const errorMessage = errorData?.message || error.message || 'Failed to reset password. Please try again.';

    return {
      isSuccess: false,
      data: errorData || null,
      message: null,
      errorMessage,
    };
  }
};
