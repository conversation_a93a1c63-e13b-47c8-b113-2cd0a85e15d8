import React, { memo } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthContext } from '../../contexts/AuthContext';
import { hasAnyRole } from '../../Utils/auth/roles';

// Optimized ProtectedRoute with error boundaries and validation
const ProtectedRoute = memo(({ children, allowedRoles = [] }) => {
  const { isAuthenticated, roles, isLoading } = useAuthContext();
  const location = useLocation();

  // Validate allowedRoles prop
  if (!Array.isArray(allowedRoles)) {
    console.error('ProtectedRoute: allowedRoles must be an array');
    return <div>Configuration Error</div>;
  }

  // Show loading while checking auth
  if (isLoading) {
    return (
      <div className="flex justify-content-center align-items-center" style={{ height: '200px' }}>
        <i className="pi pi-spinner pi-spin" style={{ fontSize: '1.5rem' }}></i>
        <span style={{ marginLeft: '10px' }}>Verifying access...</span>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check if user has any of the allowed roles
  if (allowedRoles.length > 0 && !hasAnyRole(roles, allowedRoles)) {
    return (
      <div className="flex justify-content-center align-items-center flex-column" style={{ height: '400px' }}>
        <i className="pi pi-ban" style={{ fontSize: '3rem', color: '#e74c3c', marginBottom: '20px' }}></i>
        <h2>Access Denied</h2>
        <p>You don't have permission to access this page.</p>
        <div className="flex gap-3 mt-4">
          <button 
            className="p-button p-button-primary"
            onClick={() => window.history.back()}
            style={{ padding: '8px 16px' }}
          >
            <i className="pi pi-arrow-left mr-2"></i>
            Go Back
          </button>
        </div>
      </div>
    );
  }

  // User is authenticated and has correct role
  return children;
});

export default ProtectedRoute;
