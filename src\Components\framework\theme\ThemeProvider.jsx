import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';

const ThemeContext = createContext({
  currentTheme: 'lara-light-blue',
  themes: [],
  setTheme: () => {},
  isDarkMode: false,
  toggleDarkMode: () => {},
  customTheme: null,
  setCustomTheme: () => {},
  themeConfig: {}
});

const PRIMEREACT_THEMES = [
  { 
    name: 'lara-light-blue', 
    label: 'Lara Light Blue', 
    type: 'light',
    primary: '#3B82F6',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/lara-light-blue/theme.css'
  },
  { 
    name: 'lara-light-indigo', 
    label: 'Lara Light Indigo', 
    type: 'light',
    primary: '#6366F1',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/lara-light-indigo/theme.css'
  },
  { 
    name: 'lara-light-purple', 
    label: 'Lara Light Purple', 
    type: 'light',
    primary: '#8B5CF6',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/lara-light-purple/theme.css'
  },
  { 
    name: 'lara-light-teal', 
    label: 'Lara Light Teal', 
    type: 'light',
    primary: '#14B8A6',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/lara-light-teal/theme.css'
  },
  { 
    name: 'saga-blue', 
    label: 'Saga Blue', 
    type: 'light',
    primary: '#007ad9',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/saga-blue/theme.css'
  },
  { 
    name: 'saga-green', 
    label: 'Saga Green', 
    type: 'light',
    primary: '#28a745',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/saga-green/theme.css'
  },
  { 
    name: 'saga-orange', 
    label: 'Saga Orange', 
    type: 'light',
    primary: '#fd7e14',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/saga-orange/theme.css'
  },
  { 
    name: 'saga-purple', 
    label: 'Saga Purple', 
    type: 'light',
    primary: '#6f42c1',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/saga-purple/theme.css'
  },
  
  { 
    name: 'lara-dark-blue', 
    label: 'Lara Dark Blue', 
    type: 'dark',
    primary: '#3B82F6',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/lara-dark-blue/theme.css'
  },
  { 
    name: 'lara-dark-indigo', 
    label: 'Lara Dark Indigo', 
    type: 'dark',
    primary: '#6366F1',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/lara-dark-indigo/theme.css'
  },
  { 
    name: 'lara-dark-purple', 
    label: 'Lara Dark Purple', 
    type: 'dark',
    primary: '#8B5CF6',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/lara-dark-purple/theme.css'
  },
  { 
    name: 'lara-dark-teal', 
    label: 'Lara Dark Teal', 
    type: 'dark',
    primary: '#14B8A6',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/lara-dark-teal/theme.css'
  },
  { 
    name: 'vela-blue', 
    label: 'Vela Blue', 
    type: 'dark',
    primary: '#007ad9',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/vela-blue/theme.css'
  },
  { 
    name: 'vela-green', 
    label: 'Vela Green', 
    type: 'dark',
    primary: '#28a745',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/vela-green/theme.css'
  },
  { 
    name: 'vela-orange', 
    label: 'Vela Orange', 
    type: 'dark',
    primary: '#fd7e14',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/vela-orange/theme.css'
  },
  { 
    name: 'vela-purple', 
    label: 'Vela Purple', 
    type: 'dark',
    primary: '#6f42c1',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/vela-purple/theme.css'
  },
  
  { 
    name: 'bootstrap4-light-blue', 
    label: 'Bootstrap Light Blue', 
    type: 'light',
    primary: '#007bff',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/bootstrap4-light-blue/theme.css'
  },
  { 
    name: 'bootstrap4-dark-blue', 
    label: 'Bootstrap Dark Blue', 
    type: 'dark',
    primary: '#007bff',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/bootstrap4-dark-blue/theme.css'
  },
  
  { 
    name: 'md-light-indigo', 
    label: 'Material Light Indigo', 
    type: 'light',
    primary: '#3f51b5',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/md-light-indigo/theme.css'
  },
  { 
    name: 'md-dark-indigo', 
    label: 'Material Dark Indigo', 
    type: 'dark',
    primary: '#3f51b5',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/md-dark-indigo/theme.css'
  },
  
  { 
    name: 'fluent-light', 
    label: 'Fluent Light', 
    type: 'light',
    primary: '#0078d4',
    css: 'https://cdn.jsdelivr.net/npm/primereact@10.9.7/resources/themes/fluent-light/theme.css'
  }
];

const ThemeProvider = ({ 
  children, 
  defaultTheme = 'lara-light-blue',
  enableCustomThemes = true,
  enableDarkModeToggle = true,
  persistTheme = true,
  onThemeChange,
  debug = false
}) => {
  const [currentTheme, setCurrentTheme] = useState(defaultTheme);
  const [customTheme, setCustomTheme] = useState(null);
  const [themeConfig, setThemeConfig] = useState({});

  useEffect(() => {
    if (persistTheme) {
      const savedTheme = localStorage.getItem('app-theme');
      const savedCustomTheme = localStorage.getItem('app-custom-theme');
      const savedThemeConfig = localStorage.getItem('app-theme-config');
      
      if (savedTheme && PRIMEREACT_THEMES.find(t => t.name === savedTheme)) {
        setCurrentTheme(savedTheme);
      }
      
      if (savedCustomTheme) {
        try {
          setCustomTheme(JSON.parse(savedCustomTheme));
        } catch (e) {
          console.warn('Failed to parse saved custom theme:', e);
        }
      }
      
      if (savedThemeConfig) {
        try {
          setThemeConfig(JSON.parse(savedThemeConfig));
        } catch (e) {
          console.warn('Failed to parse saved theme config:', e);
        }
      }
    }
  }, [persistTheme]);

  const loadThemeCSS = useCallback((theme) => {
    const themeData = PRIMEREACT_THEMES.find(t => t.name === theme);
    if (!themeData) return;

    const existingLink = document.getElementById('theme-css');
    if (existingLink) {
      existingLink.remove();
    }

    const link = document.createElement('link');
    link.id = 'theme-css';
    link.rel = 'stylesheet';
    link.href = themeData.css;
    document.head.appendChild(link);

    if (debug) {
      console.log('Theme loaded:', theme, themeData);
    }
  }, [debug]);

  const setTheme = useCallback((themeName) => {
    const themeData = PRIMEREACT_THEMES.find(t => t.name === themeName);
    if (!themeData) {
      console.warn('Theme not found:', themeName);
      return;
    }

    setCurrentTheme(themeName);
    loadThemeCSS(themeName);

    document.body.className = document.body.className
      .replace(/theme-\w+/g, '')
      .replace(/\s+/g, ' ')
      .trim();
    document.body.classList.add(`theme-${themeData.type}`);

    if (persistTheme) {
      localStorage.setItem('app-theme', themeName);
    }

    if (onThemeChange) {
      onThemeChange(themeName, themeData);
    }

    if (debug) {
      console.log('Theme changed to:', themeName);
    }
  }, [loadThemeCSS, persistTheme, onThemeChange, debug]);

  const toggleDarkMode = useCallback(() => {
    const currentThemeData = PRIMEREACT_THEMES.find(t => t.name === currentTheme);
    if (!currentThemeData) return;

    const targetType = currentThemeData.type === 'light' ? 'dark' : 'light';
    
    const baseName = currentTheme.replace(/(light|dark)/, '').replace(/^-|-$/g, '');
    const targetTheme = PRIMEREACT_THEMES.find(t => 
      t.name.includes(baseName) && t.type === targetType
    );

    if (targetTheme) {
      setTheme(targetTheme.name);
    } else {
      const fallbackTheme = targetType === 'dark' ? 'lara-dark-blue' : 'lara-light-blue';
      setTheme(fallbackTheme);
    }
  }, [currentTheme, setTheme]);

  const applyCustomTheme = useCallback((customThemeData) => {
    setCustomTheme(customThemeData);
    
    if (customThemeData && customThemeData.variables) {
      const root = document.documentElement;
      Object.entries(customThemeData.variables).forEach(([key, value]) => {
        root.style.setProperty(key, value);
      });
    }

    if (persistTheme) {
      localStorage.setItem('app-custom-theme', JSON.stringify(customThemeData));
    }

    if (debug) {
      console.log('Custom theme applied:', customThemeData);
    }
  }, [persistTheme, debug]);

  useEffect(() => {
    loadThemeCSS(currentTheme);
    const themeData = PRIMEREACT_THEMES.find(t => t.name === currentTheme);
    if (themeData) {
      document.body.classList.add(`theme-${themeData.type}`);
    }
  }, [currentTheme, loadThemeCSS]);

  const getCurrentThemeData = useCallback(() => {
    return PRIMEREACT_THEMES.find(t => t.name === currentTheme);
  }, [currentTheme]);

  const isDarkMode = getCurrentThemeData()?.type === 'dark';

  const contextValue = {
    currentTheme,
    themes: PRIMEREACT_THEMES,
    setTheme,
    isDarkMode,
    toggleDarkMode: enableDarkModeToggle ? toggleDarkMode : null,
    customTheme,
    setCustomTheme: enableCustomThemes ? applyCustomTheme : null,
    themeConfig,
    setThemeConfig,
    getCurrentThemeData,
    enableCustomThemes,
    enableDarkModeToggle
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      {children}
    </ThemeContext.Provider>
  );
};

ThemeProvider.propTypes = {
  children: PropTypes.node.isRequired,
  defaultTheme: PropTypes.string,
  enableCustomThemes: PropTypes.bool,
  enableDarkModeToggle: PropTypes.bool,
  persistTheme: PropTypes.bool,
  onThemeChange: PropTypes.func,
  debug: PropTypes.bool
};

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

export { PRIMEREACT_THEMES };
export default ThemeProvider;
