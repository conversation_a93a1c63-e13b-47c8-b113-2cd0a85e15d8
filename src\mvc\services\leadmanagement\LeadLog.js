import ApiService from '../ApiService.js';

class LeadLogService {
	async save(logData) {
		return await new ApiService().request('POST', '/LeadLog/save', logData);
	}
	async update(id, logData) {
		return await new ApiService().request('PUT', `/LeadLog/update/${id}`, logData);
	}
	async deleteById(id) {
		return await new ApiService().request('DELETE', `/LeadLog/deleteById/${id}`);
	}
		async getAll() {
			return await new ApiService().request('GET', '/LeadLog/getAll');
		}
		async getById(id) {
			return await new ApiService().request('GET', `/LeadLog/getById/${id}`);
		}
}

export default new LeadLogService();
