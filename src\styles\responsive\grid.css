
/* Grid Container */
.grid-container {
  display: grid;
  width: 100%;
  gap: var(--grid-gap, var(--spacing-6));
}

/* 12-Column Grid System */
.grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: var(--grid-gap, var(--spacing-6));
}

/* Grid Column Spans */
.col-1 { grid-column: span 1; }
.col-2 { grid-column: span 2; }
.col-3 { grid-column: span 3; }
.col-4 { grid-column: span 4; }
.col-5 { grid-column: span 5; }
.col-6 { grid-column: span 6; }
.col-7 { grid-column: span 7; }
.col-8 { grid-column: span 8; }
.col-9 { grid-column: span 9; }
.col-10 { grid-column: span 10; }
.col-11 { grid-column: span 11; }
.col-12 { grid-column: span 12; }

/* Grid Column Start Positions */
.col-start-1 { grid-column-start: 1; }
.col-start-2 { grid-column-start: 2; }
.col-start-3 { grid-column-start: 3; }
.col-start-4 { grid-column-start: 4; }
.col-start-5 { grid-column-start: 5; }
.col-start-6 { grid-column-start: 6; }
.col-start-7 { grid-column-start: 7; }
.col-start-8 { grid-column-start: 8; }
.col-start-9 { grid-column-start: 9; }
.col-start-10 { grid-column-start: 10; }
.col-start-11 { grid-column-start: 11; }
.col-start-12 { grid-column-start: 12; }
.col-start-13 { grid-column-start: 13; }

/* Grid Column End Positions */
.col-end-1 { grid-column-end: 1; }
.col-end-2 { grid-column-end: 2; }
.col-end-3 { grid-column-end: 3; }
.col-end-4 { grid-column-end: 4; }
.col-end-5 { grid-column-end: 5; }
.col-end-6 { grid-column-end: 6; }
.col-end-7 { grid-column-end: 7; }
.col-end-8 { grid-column-end: 8; }
.col-end-9 { grid-column-end: 9; }
.col-end-10 { grid-column-end: 10; }
.col-end-11 { grid-column-end: 11; }
.col-end-12 { grid-column-end: 12; }
.col-end-13 { grid-column-end: 13; }

/* Grid Row Spans */
.row-span-1 { grid-row: span 1; }
.row-span-2 { grid-row: span 2; }
.row-span-3 { grid-row: span 3; }
.row-span-4 { grid-row: span 4; }
.row-span-5 { grid-row: span 5; }
.row-span-6 { grid-row: span 6; }

/* Grid Row Start/End */
.row-start-1 { grid-row-start: 1; }
.row-start-2 { grid-row-start: 2; }
.row-start-3 { grid-row-start: 3; }
.row-start-4 { grid-row-start: 4; }
.row-start-5 { grid-row-start: 5; }
.row-start-6 { grid-row-start: 6; }

.row-end-1 { grid-row-end: 1; }
.row-end-2 { grid-row-end: 2; }
.row-end-3 { grid-row-end: 3; }
.row-end-4 { grid-row-end: 4; }
.row-end-5 { grid-row-end: 5; }
.row-end-6 { grid-row-end: 6; }
.row-end-7 { grid-row-end: 7; }

/* Grid Gap Utilities */
.gap-0 { gap: 0; }
.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }
.gap-5 { gap: var(--spacing-5); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }
.gap-10 { gap: var(--spacing-10); }
.gap-12 { gap: var(--spacing-12); }
.gap-16 { gap: var(--spacing-16); }
.gap-20 { gap: var(--spacing-20); }
.gap-24 { gap: var(--spacing-24); }

/* Column Gap */
.gap-x-0 { column-gap: 0; }
.gap-x-1 { column-gap: var(--spacing-1); }
.gap-x-2 { column-gap: var(--spacing-2); }
.gap-x-3 { column-gap: var(--spacing-3); }
.gap-x-4 { column-gap: var(--spacing-4); }
.gap-x-5 { column-gap: var(--spacing-5); }
.gap-x-6 { column-gap: var(--spacing-6); }
.gap-x-8 { column-gap: var(--spacing-8); }
.gap-x-10 { column-gap: var(--spacing-10); }
.gap-x-12 { column-gap: var(--spacing-12); }
.gap-x-16 { column-gap: var(--spacing-16); }
.gap-x-20 { column-gap: var(--spacing-20); }
.gap-x-24 { column-gap: var(--spacing-24); }

/* Row Gap */
.gap-y-0 { row-gap: 0; }
.gap-y-1 { row-gap: var(--spacing-1); }
.gap-y-2 { row-gap: var(--spacing-2); }
.gap-y-3 { row-gap: var(--spacing-3); }
.gap-y-4 { row-gap: var(--spacing-4); }
.gap-y-5 { row-gap: var(--spacing-5); }
.gap-y-6 { row-gap: var(--spacing-6); }
.gap-y-8 { row-gap: var(--spacing-8); }
.gap-y-10 { row-gap: var(--spacing-10); }
.gap-y-12 { row-gap: var(--spacing-12); }
.gap-y-16 { row-gap: var(--spacing-16); }
.gap-y-20 { row-gap: var(--spacing-20); }
.gap-y-24 { row-gap: var(--spacing-24); }

/* Auto-fit and Auto-fill Grids */
.grid-auto-fit {
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
}

.grid-auto-fill {
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
}

/* Grid Template Columns */
.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-7 { grid-template-columns: repeat(7, minmax(0, 1fr)); }
.grid-cols-8 { grid-template-columns: repeat(8, minmax(0, 1fr)); }
.grid-cols-9 { grid-template-columns: repeat(9, minmax(0, 1fr)); }
.grid-cols-10 { grid-template-columns: repeat(10, minmax(0, 1fr)); }
.grid-cols-11 { grid-template-columns: repeat(11, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }
.grid-cols-none { grid-template-columns: none; }

/* Grid Template Rows */
.grid-rows-1 { grid-template-rows: repeat(1, minmax(0, 1fr)); }
.grid-rows-2 { grid-template-rows: repeat(2, minmax(0, 1fr)); }
.grid-rows-3 { grid-template-rows: repeat(3, minmax(0, 1fr)); }
.grid-rows-4 { grid-template-rows: repeat(4, minmax(0, 1fr)); }
.grid-rows-5 { grid-template-rows: repeat(5, minmax(0, 1fr)); }
.grid-rows-6 { grid-template-rows: repeat(6, minmax(0, 1fr)); }
.grid-rows-none { grid-template-rows: none; }

/* Grid Auto Flow */
.grid-flow-row { grid-auto-flow: row; }
.grid-flow-col { grid-auto-flow: column; }
.grid-flow-row-dense { grid-auto-flow: row dense; }
.grid-flow-col-dense { grid-auto-flow: column dense; }

/* Flexbox Grid Alternative */
.flex-grid {
  display: flex;
  flex-wrap: wrap;
  margin: calc(var(--grid-gap, var(--spacing-6)) / -2);
}

.flex-grid > * {
  padding: calc(var(--grid-gap, var(--spacing-6)) / 2);
}

/* Flex Grid Column Widths */
.flex-col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.flex-col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.flex-col-3 { flex: 0 0 25%; max-width: 25%; }
.flex-col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.flex-col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.flex-col-6 { flex: 0 0 50%; max-width: 50%; }
.flex-col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.flex-col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.flex-col-9 { flex: 0 0 75%; max-width: 75%; }
.flex-col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.flex-col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.flex-col-12 { flex: 0 0 100%; max-width: 100%; }

/* Auto columns */
.flex-col-auto { flex: 0 0 auto; width: auto; max-width: none; }
.flex-col { flex: 1 0 0%; max-width: 100%; }
