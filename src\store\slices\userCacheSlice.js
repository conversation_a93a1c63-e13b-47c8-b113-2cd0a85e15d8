import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export const cacheUserData = createAsyncThunk(
  'userCache/cacheUserData',
  async ({ userId, dataType, data, options = {} }, { dispatch, getState }) => {
    const {
      ttl = 30 * 60 * 1000, // 30 minutes default
      tags = [],
      priority = 1,
      encrypt = false
    } = options;

    const cacheKey = `user:${userId}:${dataType}`;
    const now = Date.now();

    let processedData = data;
    
    // Encrypt sensitive data if requested
    if (encrypt && typeof window !== 'undefined' && window.crypto) {
      try {
        // Simple encryption for demo - in production use proper encryption
        processedData = btoa(JSON.stringify(data));
      } catch (error) {
        console.warn('Failed to encrypt user data:', error);
      }
    }

    const entry = {
      data: processedData,
      userId,
      dataType,
      createdAt: now,
      lastAccessed: now,
      expiresAt: now + ttl,
      tags: [...tags, `user:${userId}`, `type:${dataType}`],
      priority,
      encrypted: encrypt,
      size: JSON.stringify(processedData).length,
      version: 1
    };

    dispatch(userCacheSlice.actions.set({ key: cacheKey, entry }));
    
    return { key: cacheKey, data: processedData, userId, dataType };
  }
);

export const getUserData = createAsyncThunk(
  'userCache/getUserData',
  async ({ userId, dataType }, { dispatch, getState }) => {
    const cacheKey = `user:${userId}:${dataType}`;
    const state = getState();
    const entry = state.userCache.entries[cacheKey];
    const now = Date.now();

    if (!entry) {
      dispatch(userCacheSlice.actions.recordMiss({ key: cacheKey }));
      return null;
    }

    // Check if expired
    if (entry.expiresAt && entry.expiresAt <= now) {
      dispatch(userCacheSlice.actions.delete({ key: cacheKey }));
      dispatch(userCacheSlice.actions.recordMiss({ key: cacheKey, reason: 'expired' }));
      return null;
    }

    // Update last accessed
    dispatch(userCacheSlice.actions.updateLastAccessed({ key: cacheKey, timestamp: now }));
    dispatch(userCacheSlice.actions.recordHit({ key: cacheKey }));

    let data = entry.data;
    
    // Decrypt if encrypted
    if (entry.encrypted) {
      try {
        data = JSON.parse(atob(entry.data));
      } catch (error) {
        console.error('Failed to decrypt user data:', error);
        return null;
      }
    }

    return { key: cacheKey, data, userId, dataType };
  }
);

export const invalidateUserCache = createAsyncThunk(
  'userCache/invalidateUser',
  async ({ userId, dataTypes = [] }, { dispatch, getState }) => {
    const state = getState();
    const entries = state.userCache.entries;
    const keysToInvalidate = [];

    Object.keys(entries).forEach(key => {
      const entry = entries[key];
      
      if (entry.userId === userId) {
        if (dataTypes.length === 0 || dataTypes.includes(entry.dataType)) {
          keysToInvalidate.push(key);
        }
      }
    });

    keysToInvalidate.forEach(key => {
      dispatch(userCacheSlice.actions.delete({ key }));
    });

    return { userId, dataTypes, invalidatedKeys: keysToInvalidate };
  }
);

const initialState = {
  entries: {},
  analytics: {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalRequests: 0,
    userCount: 0,
    dataTypeStats: {}
  },
  config: {
    maxEntries: 100,
    defaultTTL: 30 * 60 * 1000, // 30 minutes
    maxUsersInCache: 50,
    encryptSensitiveData: true,
    strategy: 'user-lru'
  },
  loading: {},
  errors: {}
};

const userCacheSlice = createSlice({
  name: 'userCache',
  initialState,
  reducers: {
    set: (state, action) => {
      const { key, entry } = action.payload;
      
      // Apply eviction strategy if needed
      if (Object.keys(state.entries).length >= state.config.maxEntries) {
        state.entries = applyUserEvictionStrategy(state.entries, state.config.strategy, state.config.maxEntries);
      }
      
      state.entries[key] = entry;
      
      // Update analytics
      updateUserAnalytics(state);
    },

    delete: (state, action) => {
      const { key } = action.payload;
      delete state.entries[key];
      delete state.loading[key];
      delete state.errors[key];
      
      // Update analytics
      updateUserAnalytics(state);
    },

    clear: (state) => {
      state.entries = {};
      state.loading = {};
      state.errors = {};
      state.analytics.userCount = 0;
      state.analytics.dataTypeStats = {};
    },

    updateLastAccessed: (state, action) => {
      const { key, timestamp } = action.payload;
      if (state.entries[key]) {
        state.entries[key].lastAccessed = timestamp;
      }
    },

    recordHit: (state, action) => {
      const { key } = action.payload;
      state.analytics.hits++;
      state.analytics.totalRequests++;
      state.analytics.hitRate = (state.analytics.hits / state.analytics.totalRequests) * 100;
      
      // Update data type stats
      const entry = state.entries[key];
      if (entry && entry.dataType) {
        if (!state.analytics.dataTypeStats[entry.dataType]) {
          state.analytics.dataTypeStats[entry.dataType] = { hits: 0, misses: 0 };
        }
        state.analytics.dataTypeStats[entry.dataType].hits++;
      }
    },

    recordMiss: (state, action) => {
      const { key, reason } = action.payload;
      state.analytics.misses++;
      state.analytics.totalRequests++;
      state.analytics.hitRate = (state.analytics.hits / state.analytics.totalRequests) * 100;
      
      // Try to extract data type from key for stats
      const keyParts = key.split(':');
      if (keyParts.length >= 3) {
        const dataType = keyParts[2];
        if (!state.analytics.dataTypeStats[dataType]) {
          state.analytics.dataTypeStats[dataType] = { hits: 0, misses: 0 };
        }
        state.analytics.dataTypeStats[dataType].misses++;
      }
    },

    cleanupExpired: (state) => {
      const now = Date.now();
      const validEntries = {};
      
      Object.entries(state.entries).forEach(([key, entry]) => {
        if (!entry.expiresAt || entry.expiresAt > now) {
          validEntries[key] = entry;
        }
      });
      
      state.entries = validEntries;
      updateUserAnalytics(state);
    },

    cleanupUser: (state, action) => {
      const { userId } = action.payload;
      const validEntries = {};
      
      Object.entries(state.entries).forEach(([key, entry]) => {
        if (entry.userId !== userId) {
          validEntries[key] = entry;
        }
      });
      
      state.entries = validEntries;
      updateUserAnalytics(state);
    },

    updateConfig: (state, action) => {
      state.config = { ...state.config, ...action.payload };
    },

    setLoading: (state, action) => {
      const { key, loading } = action.payload;
      if (loading) {
        state.loading[key] = true;
      } else {
        delete state.loading[key];
      }
    },

    setError: (state, action) => {
      const { key, error } = action.payload;
      if (error) {
        state.errors[key] = error;
      } else {
        delete state.errors[key];
      }
    },

    importEntries: (state, action) => {
      const entries = action.payload;
      const now = Date.now();
      
      // Filter out expired entries during import
      Object.entries(entries).forEach(([key, entry]) => {
        if (!entry.expiresAt || entry.expiresAt > now) {
          state.entries[key] = entry;
        }
      });
      
      updateUserAnalytics(state);
    }
  },

  extraReducers: (builder) => {
    builder
      .addCase(cacheUserData.pending, (state, action) => {
        const { userId, dataType } = action.meta.arg;
        const cacheKey = `user:${userId}:${dataType}`;
        state.loading[cacheKey] = true;
        delete state.errors[cacheKey];
      })
      .addCase(cacheUserData.fulfilled, (state, action) => {
        const { key } = action.payload;
        delete state.loading[key];
      })
      .addCase(cacheUserData.rejected, (state, action) => {
        const { userId, dataType } = action.meta.arg;
        const cacheKey = `user:${userId}:${dataType}`;
        delete state.loading[cacheKey];
        state.errors[cacheKey] = action.error.message;
      })
      .addCase(getUserData.fulfilled, (state, action) => {
        // Data retrieval completed
      })
      .addCase(invalidateUserCache.fulfilled, (state, action) => {
        updateUserAnalytics(state);
      });
  }
});

function updateUserAnalytics(state) {
  const uniqueUsers = new Set();
  
  Object.values(state.entries).forEach(entry => {
    if (entry.userId) {
      uniqueUsers.add(entry.userId);
    }
  });
  
  state.analytics.userCount = uniqueUsers.size;
}

function applyUserEvictionStrategy(entries, strategy, maxEntries) {
  const entryCount = Object.keys(entries).length;
  if (entryCount <= maxEntries) return entries;

  switch (strategy) {
    case 'user-lru': {
      // Group by user and apply LRU per user
      const userGroups = {};
      Object.entries(entries).forEach(([key, entry]) => {
        if (!userGroups[entry.userId]) {
          userGroups[entry.userId] = [];
        }
        userGroups[entry.userId].push([key, entry]);
      });

      // Sort users by most recent activity
      const sortedUsers = Object.entries(userGroups)
        .sort(([, aEntries], [, bEntries]) => {
          const aLatest = Math.max(...aEntries.map(([, entry]) => entry.lastAccessed || 0));
          const bLatest = Math.max(...bEntries.map(([, entry]) => entry.lastAccessed || 0));
          return bLatest - aLatest;
        });

      const newEntries = {};
      let count = 0;

      for (const [userId, userEntries] of sortedUsers) {
        if (count >= maxEntries) break;
        
        // Sort user entries by last accessed
        const sortedUserEntries = userEntries
          .sort(([, a], [, b]) => (b.lastAccessed || 0) - (a.lastAccessed || 0));

        for (const [key, entry] of sortedUserEntries) {
          if (count >= maxEntries) break;
          newEntries[key] = entry;
          count++;
        }
      }

      return newEntries;
    }

    case 'data-type-priority': {
      // Prioritize certain data types
      const priorityTypes = ['profile', 'preferences', 'session'];
      const sortedEntries = Object.entries(entries)
        .sort(([, a], [, b]) => {
          const aPriority = priorityTypes.indexOf(a.dataType) !== -1 ? 10 : (a.priority || 1);
          const bPriority = priorityTypes.indexOf(b.dataType) !== -1 ? 10 : (b.priority || 1);
          return bPriority - aPriority;
        });

      const newEntries = {};
      sortedEntries.slice(0, maxEntries).forEach(([key, entry]) => {
        newEntries[key] = entry;
      });

      return newEntries;
    }

    default:
      return applyUserEvictionStrategy(entries, 'user-lru', maxEntries);
  }
}

// Selectors
export const selectUserCacheEntry = (state, userId, dataType) => {
  const key = `user:${userId}:${dataType}`;
  return state.userCache.entries[key];
};

export const selectUserCacheEntries = (state) => state.userCache.entries;
export const selectUserCacheAnalytics = (state) => state.userCache.analytics;
export const selectUserCacheConfig = (state) => state.userCache.config;
export const selectUserCacheLoading = (state, userId, dataType) => {
  const key = `user:${userId}:${dataType}`;
  return state.userCache.loading[key] || false;
};
export const selectUserCacheError = (state, userId, dataType) => {
  const key = `user:${userId}:${dataType}`;
  return state.userCache.errors[key];
};

export const selectUserData = (state, userId) => {
  return Object.entries(state.userCache.entries)
    .filter(([, entry]) => entry.userId === userId)
    .reduce((acc, [key, entry]) => {
      acc[entry.dataType] = entry;
      return acc;
    }, {});
};

export const selectDataTypeStats = (state) => state.userCache.analytics.dataTypeStats;

export const {
  set,
  delete: deleteEntry,
  clear,
  updateLastAccessed,
  recordHit,
  recordMiss,
  cleanupExpired,
  cleanupUser,
  updateConfig,
  setLoading,
  setError,
  importEntries
} = userCacheSlice.actions;

export default userCacheSlice.reducer;
