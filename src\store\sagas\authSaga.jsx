import { takeLatest, put, call } from 'redux-saga/effects';
import { loginRequest, loginSuccess, loginFailure } from '../authSlice';
import axios from 'axios';
import encryption from '../../services/CryptoClass';
import { initAuth } from '../../services/authHeader';

function* loginSaga(action) {
  try {
    const { username, password } = action.payload;
    
    const dataToEncrypt = {
      endpoint: '/api/auth/login',
      payload: { username, password },
      type: 'post',
    };

    const encryptedData = encryption.encryptData(dataToEncrypt);
    
    const response = yield call(axios.post, '/decrypt', encryptedData, {
      headers: initAuth(),
    });

    if (response.data.token) {
      // Store the token in localStorage
      localStorage.setItem('userToken', response.data.token);
      
      yield put(loginSuccess(response.data.user));
    } else {
      yield put(loginFailure('Invalid credentials'));
    }
  } catch (error) {
    yield put(loginFailure(error.response?.data?.message || 'Login failed'));
  }
}

function* logoutSaga() {
  try {
    localStorage.removeItem('userToken');
    // Additional cleanup if needed
  } catch (error) {
    console.error('Logout error:', error);
  }
}

export function* watchAuth() {
  yield takeLatest(loginRequest.type, loginSaga);
  yield takeLatest('auth/logout', logoutSaga);
}