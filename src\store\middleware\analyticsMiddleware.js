

class CacheAnalytics {
  constructor() {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      clears: 0,
      errors: 0,
      totalRequests: 0,
      responseTimeSum: 0,
      startTime: Date.now(),
      hourlyStats: {},
      cacheTypeStats: {},
      keyPatterns: new Map(),
      performanceMetrics: {
        averageResponseTime: 0,
        hitRate: 0,
        missRate: 0,
        errorRate: 0
      }
    };
    
    this.performanceObserver = null;
    this.setupPerformanceMonitoring();
  }

  setupPerformanceMonitoring() {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      try {
        this.performanceObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach(entry => {
            if (entry.name.includes('cache')) {
              this.recordPerformanceMetric(entry);
            }
          });
        });
        
        this.performanceObserver.observe({ entryTypes: ['measure', 'navigation'] });
      } catch (error) {
        console.warn('Performance monitoring not available:', error);
      }
    }
  }

  recordOperation(operation, cacheType, key, metadata = {}) {
    const timestamp = Date.now();
    const hour = new Date(timestamp).getHours();
    
    this.metrics[operation]++;
    this.metrics.totalRequests++;
    
    if (!this.metrics.hourlyStats[hour]) {
      this.metrics.hourlyStats[hour] = {
        hits: 0,
        misses: 0,
        sets: 0,
        deletes: 0,
        clears: 0,
        errors: 0
      };
    }
    this.metrics.hourlyStats[hour][operation]++;
    
    if (!this.metrics.cacheTypeStats[cacheType]) {
      this.metrics.cacheTypeStats[cacheType] = {
        hits: 0,
        misses: 0,
        sets: 0,
        deletes: 0,
        clears: 0,
        errors: 0,
        totalRequests: 0,
        averageSize: 0,
        totalSize: 0
      };
    }
    this.metrics.cacheTypeStats[cacheType][operation]++;
    this.metrics.cacheTypeStats[cacheType].totalRequests++;
    
    if (key) {
      this.trackKeyPattern(key, operation);
    }
    
    this.updatePerformanceMetrics();
    
    if (operation === 'errors' || (operation === 'misses' && this.metrics.missRate > 50)) {
      console.warn(`[Cache Analytics] High ${operation} rate detected`, {
        operation,
        cacheType,
        key,
        currentRate: this.metrics.performanceMetrics[`${operation.slice(0, -1)}Rate`]
      });
    }
  }

  trackKeyPattern(key, operation) {
    const pattern = this.extractKeyPattern(key);
    
    if (!this.metrics.keyPatterns.has(pattern)) {
      this.metrics.keyPatterns.set(pattern, {
        hits: 0,
        misses: 0,
        sets: 0,
        deletes: 0,
        totalAccesses: 0,
        lastAccessed: null,
        frequency: 0
      });
    }
    
    const patternStats = this.metrics.keyPatterns.get(pattern);
    patternStats[operation]++;
    patternStats.totalAccesses++;
    patternStats.lastAccessed = Date.now();
    patternStats.frequency = patternStats.totalAccesses / ((Date.now() - this.metrics.startTime) / (60 * 1000)); // accesses per minute
  }

  extractKeyPattern(key) {
    return key
      .replace(/\d+/g, '{id}')
      .replace(/[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}/gi, '{uuid}')
      .replace(/user:[^:]+/g, 'user:{id}')
      .replace(/theme:[^:]+/g, 'theme:{id}')
      .replace(/i18n:[^:]+/g, 'i18n:{locale}');
  }

  recordPerformanceMetric(entry) {
    if (entry.duration) {
      this.metrics.responseTimeSum += entry.duration;
      this.updatePerformanceMetrics();
    }
  }

  updatePerformanceMetrics() {
    const totalRequests = this.metrics.totalRequests;
    
    if (totalRequests > 0) {
      this.metrics.performanceMetrics.hitRate = (this.metrics.hits / totalRequests) * 100;
      this.metrics.performanceMetrics.missRate = (this.metrics.misses / totalRequests) * 100;
      this.metrics.performanceMetrics.errorRate = (this.metrics.errors / totalRequests) * 100;
      this.metrics.performanceMetrics.averageResponseTime = this.metrics.responseTimeSum / totalRequests;
    }
  }

  getReport() {
    const uptime = Date.now() - this.metrics.startTime;
    const uptimeHours = uptime / (1000 * 60 * 60);
    
    return {
      summary: {
        uptime: uptime,
        uptimeHours: Math.round(uptimeHours * 100) / 100,
        totalRequests: this.metrics.totalRequests,
        requestsPerHour: Math.round((this.metrics.totalRequests / uptimeHours) * 100) / 100,
        ...this.metrics.performanceMetrics
      },
      operations: {
        hits: this.metrics.hits,
        misses: this.metrics.misses,
        sets: this.metrics.sets,
        deletes: this.metrics.deletes,
        clears: this.metrics.clears,
        errors: this.metrics.errors
      },
      hourlyStats: this.metrics.hourlyStats,
      cacheTypeStats: this.metrics.cacheTypeStats,
      topKeyPatterns: this.getTopKeyPatterns(),
      recommendations: this.generateRecommendations()
    };
  }

  getTopKeyPatterns(limit = 10) {
    return Array.from(this.metrics.keyPatterns.entries())
      .sort(([, a], [, b]) => b.frequency - a.frequency)
      .slice(0, limit)
      .map(([pattern, stats]) => ({
        pattern,
        ...stats
      }));
  }

  generateRecommendations() {
    const recommendations = [];
    const { performanceMetrics, cacheTypeStats } = this.metrics;
    
    if (performanceMetrics.hitRate < 70) {
      recommendations.push({
        type: 'performance',
        severity: 'high',
        message: `Low cache hit rate (${performanceMetrics.hitRate.toFixed(1)}%). Consider increasing TTL or preloading frequently accessed data.`
      });
    }
    
    if (performanceMetrics.errorRate > 5) {
      recommendations.push({
        type: 'reliability',
        severity: 'high',
        message: `High error rate (${performanceMetrics.errorRate.toFixed(1)}%). Check cache configuration and error handling.`
      });
    }
    
    Object.entries(cacheTypeStats).forEach(([cacheType, stats]) => {
      const hitRate = stats.totalRequests > 0 ? (stats.hits / stats.totalRequests) * 100 : 0;
      
      if (hitRate < 50 && stats.totalRequests > 100) {
        recommendations.push({
          type: 'optimization',
          severity: 'medium',
          message: `${cacheType} has low hit rate (${hitRate.toFixed(1)}%). Consider optimizing caching strategy.`
        });
      }
    });
    
    const totalSize = Object.values(cacheTypeStats).reduce((sum, stats) => sum + (stats.totalSize || 0), 0);
    if (totalSize > 50 * 1024 * 1024) { 
      recommendations.push({
        type: 'memory',
        severity: 'medium',
        message: `High memory usage (${(totalSize / 1024 / 1024).toFixed(1)}MB). Consider reducing cache sizes or implementing more aggressive eviction.`
      });
    }
    
    return recommendations;
  }

  reset() {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      clears: 0,
      errors: 0,
      totalRequests: 0,
      responseTimeSum: 0,
      startTime: Date.now(),
      hourlyStats: {},
      cacheTypeStats: {},
      keyPatterns: new Map(),
      performanceMetrics: {
        averageResponseTime: 0,
        hitRate: 0,
        missRate: 0,
        errorRate: 0
      }
    };
  }
}

const cacheAnalytics = new CacheAnalytics();

const analyticsMiddleware = (store) => (next) => (action) => {
  const startTime = performance.now();
  
  const result = next(action);
  
  if (action.type && action.type.includes('Cache/')) {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    recordCacheAnalytics(action, duration);
  }
  
  return result;
};

function recordCacheAnalytics(action, duration) {
  const actionParts = action.type.split('/');
  const cacheType = actionParts[0];
  const operation = actionParts[1];
  const { key, entry } = action.payload || {};
  
  let analyticsOperation;
  switch (operation) {
    case 'set':
      analyticsOperation = 'sets';
      break;
    case 'recordHit':
      analyticsOperation = 'hits';
      break;
    case 'recordMiss':
      analyticsOperation = 'misses';
      break;
    case 'delete':
      analyticsOperation = 'deletes';
      break;
    case 'clear':
      analyticsOperation = 'clears';
      break;
    case 'recordError':
      analyticsOperation = 'errors';
      break;
    default:
      return; 
  }
  
  cacheAnalytics.recordOperation(analyticsOperation, cacheType, key, {
    duration,
    size: entry?.size,
    tags: entry?.tags
  });
}

export const getCacheAnalytics = () => cacheAnalytics.getReport();
export const resetCacheAnalytics = () => cacheAnalytics.reset();
export const getCacheAnalyticsInstance = () => cacheAnalytics;

export default analyticsMiddleware;
