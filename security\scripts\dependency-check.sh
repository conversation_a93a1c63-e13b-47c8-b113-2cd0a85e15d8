
echo "🔍 Starting Dependency Security Check"
echo "===================================="

RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' 

mkdir -p security/reports

command_exists() {
    command -v "$1" >/dev/null 2>&1
}

log_result() {
    echo -e "${2}${1}${NC}"
    echo "$(date): $1" >> security/reports/dependency-check.log
}

echo -e "${BLUE}📦 Running npm audit...${NC}"
if npm audit --audit-level=moderate --json > security/reports/npm-audit.json 2>&1; then
    log_result "✅ npm audit completed successfully" $GREEN
    
    VULNERABILITIES=$(cat security/reports/npm-audit.json | grep -o '"vulnerabilities":[0-9]*' | cut -d':' -f2 || echo "0")
    if [ "$VULNERABILITIES" -gt 0 ]; then
        log_result "⚠️  Found $VULNERABILITIES vulnerabilities" $YELLOW
        
        npm audit --audit-level=moderate > security/reports/npm-audit-readable.txt 2>&1
    else
        log_result "✅ No vulnerabilities found in npm audit" $GREEN
    fi
else
    log_result "❌ npm audit failed" $RED
fi

echo -e "${BLUE}🛡️  Running Snyk security scan...${NC}"
if command_exists snyk; then
    if snyk test --severity-threshold=medium --json > security/reports/snyk-test.json 2>&1; then
        log_result "✅ Snyk scan completed successfully" $GREEN
    else
        SNYK_ISSUES=$(cat security/reports/snyk-test.json | grep -o '"vulnerabilities":\[[^]]*\]' | wc -l || echo "0")
        if [ "$SNYK_ISSUES" -gt 0 ]; then
            log_result "⚠️  Snyk found security issues" $YELLOW
            
            snyk test --severity-threshold=medium > security/reports/snyk-readable.txt 2>&1
        else
            log_result "✅ No issues found by Snyk" $GREEN
        fi
    fi
    
    echo -e "${BLUE}📄 Checking license compliance with Snyk...${NC}"
    if snyk test --severity-threshold=medium --json > security/reports/snyk-license.json 2>&1; then
        log_result "✅ License check completed" $GREEN
    else
        log_result "⚠️  License issues may exist" $YELLOW
    fi
else
    log_result "⚠️  Snyk not installed. Install with: npm install -g snyk" $YELLOW
fi

echo -e "${BLUE}🔍 Checking for known vulnerable packages...${NC}"
VULNERABLE_PACKAGES=(
    "event-stream@3.3.6"
    "eslint-scope@3.7.2"
    "bootstrap@<4.1.2"
    "jquery@<3.4.0"
    "lodash@<4.17.12"
    "handlebars@<4.5.3"
    "serialize-javascript@<2.1.1"
    "acorn@<5.7.4"
    "minimist@<0.2.1"
    "kind-of@<6.0.3"
)

FOUND_VULNERABLE=false
for package in "${VULNERABLE_PACKAGES[@]}"; do
    PACKAGE_NAME=$(echo $package | cut -d'@' -f1)
    if npm list $PACKAGE_NAME --depth=0 >/dev/null 2>&1; then
        log_result "⚠️  Found potentially vulnerable package: $package" $YELLOW
        FOUND_VULNERABLE=true
    fi
done

if [ "$FOUND_VULNERABLE" = false ]; then
    log_result "✅ No known vulnerable packages found" $GREEN
fi

echo -e "${BLUE}🔒 Checking package-lock.json integrity...${NC}"
if [ -f "package-lock.json" ]; then
    if npm ci --dry-run >/dev/null 2>&1; then
        log_result "✅ package-lock.json is valid" $GREEN
    else
        log_result "❌ package-lock.json integrity check failed" $RED
    fi
else
    log_result "⚠️  package-lock.json not found" $YELLOW
fi

echo -e "${BLUE}📅 Checking for outdated packages...${NC}"
if command_exists npm-check-updates; then
    ncu --jsonUpgraded > security/reports/outdated-packages.json 2>&1
    OUTDATED_COUNT=$(cat security/reports/outdated-packages.json | grep -o '"[^"]*":' | wc -l || echo "0")
    
    if [ "$OUTDATED_COUNT" -gt 0 ]; then
        log_result "⚠️  Found $OUTDATED_COUNT outdated packages" $YELLOW
        ncu > security/reports/outdated-packages.txt 2>&1
    else
        log_result "✅ All packages are up to date" $GREEN
    fi
else
    log_result "⚠️  npm-check-updates not installed. Install with: npm install -g npm-check-updates" $YELLOW
fi

echo -e "${BLUE}📜 Checking license compliance...${NC}"
if command_exists license-checker; then
    license-checker --json > security/reports/licenses.json 2>&1
    license-checker --summary > security/reports/license-summary.txt 2>&1
    
    PROBLEMATIC_LICENSES=("GPL" "AGPL" "LGPL" "CPAL" "EPL")
    FOUND_PROBLEMATIC=false
    
    for license in "${PROBLEMATIC_LICENSES[@]}"; do
        if grep -i "$license" security/reports/licenses.json >/dev/null 2>&1; then
            log_result "⚠️  Found potentially problematic license: $license" $YELLOW
            FOUND_PROBLEMATIC=true
        fi
    done
    
    if [ "$FOUND_PROBLEMATIC" = false ]; then
        log_result "✅ No problematic licenses found" $GREEN
    fi
else
    log_result "⚠️  license-checker not installed. Install with: npm install -g license-checker" $YELLOW
fi

echo -e "${BLUE}⚙️  Checking security configuration...${NC}"

if [ -f ".nvmrc" ]; then
    log_result "✅ .nvmrc found - Node version pinned" $GREEN
else
    log_result "⚠️  .nvmrc not found - consider pinning Node version" $YELLOW
fi

if grep -q "security:" package.json; then
    log_result "✅ Security scripts found in package.json" $GREEN
else
    log_result "⚠️  No security scripts found in package.json" $YELLOW
fi

if [ -f ".gitignore" ]; then
    SECURITY_PATTERNS=("*.env" "*.key" "*.pem" "config/secrets" ".env.local")
    MISSING_PATTERNS=()
    
    for pattern in "${SECURITY_PATTERNS[@]}"; do
        if ! grep -q "$pattern" .gitignore; then
            MISSING_PATTERNS+=("$pattern")
        fi
    done
    
    if [ ${#MISSING_PATTERNS[@]} -eq 0 ]; then
        log_result "✅ .gitignore has good security patterns" $GREEN
    else
        log_result "⚠️  .gitignore missing security patterns: ${MISSING_PATTERNS[*]}" $YELLOW
    fi
else
    log_result "⚠️  .gitignore not found" $YELLOW
fi

echo -e "${BLUE}📊 Generating summary report...${NC}"
cat > security/reports/dependency-check-summary.json << EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "checks": {
    "npm_audit": "$([ -f security/reports/npm-audit.json ] && echo "completed" || echo "failed")",
    "snyk_scan": "$(command_exists snyk && echo "completed" || echo "not_available")",
    "vulnerable_packages": "$([ "$FOUND_VULNERABLE" = false ] && echo "clean" || echo "issues_found")",
    "package_lock_integrity": "$([ -f package-lock.json ] && echo "valid" || echo "missing")",
    "license_compliance": "$(command_exists license-checker && echo "checked" || echo "not_available")"
  },
  "summary": {
    "total_vulnerabilities": ${VULNERABILITIES:-0},
    "outdated_packages": ${OUTDATED_COUNT:-0},
    "problematic_licenses": $([ "$FOUND_PROBLEMATIC" = false ] && echo "0" || echo "1"),
    "overall_status": "$([ "$VULNERABILITIES" -eq 0 ] && [ "$FOUND_VULNERABLE" = false ] && echo "clean" || echo "issues_found")"
  }
}
EOF

echo ""
echo -e "${GREEN}✅ Dependency security check completed!${NC}"
echo -e "${BLUE}📊 Reports available in security/reports/${NC}"
echo ""

if [ "$VULNERABILITIES" -gt 0 ] || [ "$FOUND_VULNERABLE" = true ]; then
    echo -e "${RED}❌ Critical security issues found. Please review and fix.${NC}"
    exit 1
else
    echo -e "${GREEN}✅ No critical security issues found.${NC}"
    exit 0
fi
