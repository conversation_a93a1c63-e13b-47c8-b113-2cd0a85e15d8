import React, { useState } from 'react';
import { Card } from 'primereact/card';
import { InputTextarea } from 'primereact/inputtextarea';
import { Button } from 'primereact/button';
import { Avatar } from 'primereact/avatar';
import { Badge } from 'primereact/badge';

/**
 * NotesMessagesTab - Displays notes and internal messages
 */
const NotesMessagesTab = ({ prospectId }) => {
  const [newNote, setNewNote] = useState('');
  const [notes] = useState([
    {
      id: 1,
      author: '<PERSON>',
      date: '2024-01-20 10:30 AM',
      content: 'Initial contact made. Client showed interest in our premium services. Follow-up scheduled for next week.',
      type: 'Note'
    },
    {
      id: 2,
      author: 'Fatima Al <PERSON>',
      date: '2024-01-21 02:15 PM',
      content: 'Financial documents received and verified. Credit rating is excellent. Approved for standard payment terms.',
      type: 'Note'
    },
    {
      id: 3,
      author: '<PERSON>',
      date: '2024-01-22 11:00 AM',
      content: 'Product demo completed successfully. Client requested customization options. Preparing detailed proposal.',
      type: 'Note'
    },
    {
      id: 4,
      author: 'System',
      date: '2024-01-23 09:00 AM',
      content: 'Proposal sent to client. Awaiting response.',
      type: 'System'
    }
  ]);

  const handleAddNote = () => {
    if (newNote.trim()) {
      // Add note logic here
      console.log('Adding note:', newNote);
      setNewNote('');
    }
  };

  return (
    <div className="notes-messages-tab">
      <div className="tab-header" style={{ marginBottom: '1.5rem' }}>
        <h3>Notes & Messages</h3>
      </div>

      {/* Add New Note */}
      <Card title="Add New Note" style={{ marginBottom: '1.5rem' }}>
        <div className="p-fluid">
          <InputTextarea
            value={newNote}
            onChange={(e) => setNewNote(e.target.value)}
            rows={4}
            placeholder="Enter your note here..."
            autoResize
          />
          <div style={{ marginTop: '1rem', display: 'flex', gap: '0.5rem', justifyContent: 'flex-end' }}>
            <Button 
              label="Add Note" 
              icon="pi pi-plus" 
              className="p-button-sm p-button-success"
              onClick={handleAddNote}
              disabled={!newNote.trim()}
            />
            <Button 
              label="Clear" 
              icon="pi pi-times" 
              className="p-button-sm p-button-secondary"
              onClick={() => setNewNote('')}
            />
          </div>
        </div>
      </Card>

      {/* Notes List */}
      <Card title={`All Notes (${notes.length})`}>
        <div className="notes-list">
          {notes.map((note, index) => (
            <div 
              key={note.id} 
              className="note-item"
              style={{
                padding: '1rem',
                borderBottom: index < notes.length - 1 ? '1px solid #e5e7eb' : 'none',
                display: 'flex',
                gap: '1rem'
              }}
            >
              <div>
                <Avatar 
                  label={note.author.split(' ').map(n => n[0]).join('')} 
                  size="large" 
                  shape="circle"
                  style={{ 
                    backgroundColor: note.type === 'System' ? '#6c757d' : '#2196F3', 
                    color: '#ffffff' 
                  }}
                />
              </div>
              <div style={{ flex: 1 }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '0.5rem' }}>
                  <div>
                    <strong>{note.author}</strong>
                    {note.type === 'System' && (
                      <Badge value="System" severity="info" style={{ marginLeft: '0.5rem' }} />
                    )}
                  </div>
                  <span style={{ fontSize: '0.875rem', color: '#6c757d' }}>{note.date}</span>
                </div>
                <p style={{ margin: 0, color: '#4b5563' }}>{note.content}</p>
                <div style={{ marginTop: '0.5rem', display: 'flex', gap: '0.5rem' }}>
                  <Button
                    icon="pi pi-pencil"
                    className="p-button-sm p-button-text p-button-secondary"
                    tooltip="Edit"
                  />
                  <Button
                    icon="pi pi-trash"
                    className="p-button-sm p-button-text p-button-danger"
                    tooltip="Delete"
                  />
                  <Button
                    icon="pi pi-reply"
                    className="p-button-sm p-button-text p-button-info"
                    tooltip="Reply"
                  />
                </div>
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
};

export default NotesMessagesTab;

