

const cacheMiddleware = (store) => (next) => (action) => {
  const result = next(action);
  
  if (action.type && action.type.includes('Cache/')) {
    handleCacheAction(store, action);
  }
  
  if (shouldTriggerCleanup(action)) {
    scheduleCleanup(store);
  }
  
  if (shouldInvalidateCache(action)) {
    handleCacheInvalidation(store, action);
  }
  
  if (shouldPreloadCache(action)) {
    handleCachePreloading(store, action);
  }
  
  return result;
};

function handleCacheAction(store, action) {
  const state = store.getState();
  
  switch (action.type) {
    case 'cache/set':
    case 'userCache/set':
    case 'apiCache/set':
    case 'themeCache/set':
    case 'i18nCache/set':
      handleCacheSet(store, action);
      break;
      
    case 'cache/get':
    case 'userCache/recordHit':
    case 'apiCache/recordHit':
    case 'themeCache/recordHit':
    case 'i18nCache/recordHit':
      handleCacheHit(store, action);
      break;
      
    case 'cache/delete':
    case 'userCache/delete':
    case 'apiCache/delete':
    case 'themeCache/delete':
    case 'i18nCache/delete':
      handleCacheDelete(store, action);
      break;
      
    case 'cache/clear':
    case 'userCache/clear':
    case 'apiCache/clear':
    case 'themeCache/clear':
    case 'i18nCache/clear':
      handleCacheClear(store, action);
      break;
  }
}

function handleCacheSet(store, action) {
  const { key, entry } = action.payload;
  const cacheType = getCacheTypeFromAction(action);
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Cache] Set ${cacheType}:${key}`, {
      size: entry.size,
      ttl: entry.expiresAt ? entry.expiresAt - entry.createdAt : 'never',
      tags: entry.tags
    });
  }
  
  const state = store.getState();
  const cacheState = state[cacheType];
  const entryCount = Object.keys(cacheState.entries || {}).length;
  const maxEntries = cacheState.config?.maxEntries || 1000;
  
  if (entryCount > maxEntries * 0.9) {
    console.warn(`[Cache] ${cacheType} approaching limit: ${entryCount}/${maxEntries}`);
    
    store.dispatch({
      type: `${cacheType}/cleanupExpired`
    });
  }
  
  updateGlobalCacheStats(store);
}

function handleCacheHit(store, action) {
  const cacheType = getCacheTypeFromAction(action);
  
  updateAccessPatterns(store, cacheType, action);
  
  if (process.env.NODE_ENV === 'development') {
    const { key, stale } = action.payload || {};
    console.log(`[Cache] Hit ${cacheType}:${key}${stale ? ' (stale)' : ''}`);
  }
}

function handleCacheDelete(store, action) {
  const { key } = action.payload;
  const cacheType = getCacheTypeFromAction(action);
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Cache] Delete ${cacheType}:${key}`);
  }
  
  updateGlobalCacheStats(store);
}

function handleCacheClear(store, action) {
  const cacheType = getCacheTypeFromAction(action);
  
  if (process.env.NODE_ENV === 'development') {
    console.log(`[Cache] Clear ${cacheType}`);
  }
  
  updateGlobalCacheStats(store);
}

function shouldTriggerCleanup(action) {
  const cleanupTriggers = [
    'user/logout',
    'auth/sessionExpired',
    'app/lowMemory',
    'cache/forceCleanup'
  ];
  
  return cleanupTriggers.some(trigger => action.type === trigger);
}

function scheduleCleanup(store) {
  setTimeout(() => {
    const cacheTypes = ['cache', 'userCache', 'apiCache', 'themeCache', 'i18nCache'];
    
    cacheTypes.forEach(cacheType => {
      store.dispatch({
        type: `${cacheType}/cleanupExpired`
      });
    });
    
    console.log('[Cache] Scheduled cleanup completed');
  }, 0);
}

function shouldInvalidateCache(action) {
  const invalidationTriggers = [
    'user/profileUpdated',
    'theme/customThemeCreated',
    'theme/customThemeUpdated',
    'i18n/localeChanged',
    'api/dataUpdated'
  ];
  
  return invalidationTriggers.some(trigger => action.type === trigger);
}

function handleCacheInvalidation(store, action) {
  switch (action.type) {
    case 'user/profileUpdated':
      if (action.payload?.userId) {
        store.dispatch({
          type: 'userCache/invalidateUser',
          payload: { userId: action.payload.userId }
        });
      }
      break;
      
    case 'theme/customThemeCreated':
    case 'theme/customThemeUpdated':
      if (action.payload?.themeId) {
        store.dispatch({
          type: 'themeCache/invalidateTheme',
          payload: { themeId: action.payload.themeId }
        });
      }
      break;
      
    case 'i18n/localeChanged':
      if (action.payload?.locale) {
        store.dispatch({
          type: 'i18nCache/preloadLocale',
          payload: { locale: action.payload.locale }
        });
      }
      break;
      
    case 'api/dataUpdated':
      if (action.payload?.tags) {
        store.dispatch({
          type: 'apiCache/invalidate',
          payload: { tags: action.payload.tags }
        });
      }
      break;
  }
}

function shouldPreloadCache(action) {
  const preloadTriggers = [
    'user/login',
    'app/initialized',
    'theme/changed',
    'i18n/localeChanged'
  ];
  
  return preloadTriggers.some(trigger => action.type === trigger);
}

function handleCachePreloading(store, action) {
  switch (action.type) {
    case 'user/login':
      if (action.payload?.userId) {
        preloadUserData(store, action.payload.userId);
      }
      break;
      
    case 'app/initialized':
      preloadCommonData(store);
      break;
      
    case 'theme/changed':
      if (action.payload?.theme) {
        preloadThemeData(store, action.payload.theme);
      }
      break;
      
    case 'i18n/localeChanged':
      if (action.payload?.locale) {
        store.dispatch({
          type: 'i18nCache/preloadLocale',
          payload: { locale: action.payload.locale }
        });
      }
      break;
  }
}

function preloadUserData(store, userId) {
  const commonUserDataTypes = ['profile', 'preferences', 'settings'];
  
  commonUserDataTypes.forEach(dataType => {
    store.dispatch({
      type: 'userCache/cacheUserData',
      payload: {
        userId,
        dataType,
        data: { placeholder: true },
        options: { ttl: 30 * 60 * 1000 } 
      }
    });
  });
}

function preloadCommonData(store) {
  const commonThemes = ['light', 'dark'];
  commonThemes.forEach(themeId => {
    preloadThemeData(store, themeId);
  });
  
  const commonLocales = ['en', 'es'];
  commonLocales.forEach(locale => {
    store.dispatch({
      type: 'i18nCache/preloadLocale',
      payload: { locale }
    });
  });
}

function preloadThemeData(store, themeId) {
  const themeDataTypes = ['config', 'variables'];
  
  themeDataTypes.forEach(dataType => {
    store.dispatch({
      type: 'themeCache/cacheThemeData',
      payload: {
        themeId,
        dataType,
        data: { placeholder: true },
        options: { ttl: 24 * 60 * 60 * 1000 } 
      }
    });
  });
}

function updateAccessPatterns(store, cacheType, action) {
  
  const { key } = action.payload || {};
  if (!key) return;
  
  const accessTime = Date.now();
  const accessPattern = {
    key,
    cacheType,
    timestamp: accessTime,
    hour: new Date(accessTime).getHours(),
    dayOfWeek: new Date(accessTime).getDay()
  };
  
  if (process.env.NODE_ENV === 'development') {
    console.log('[Cache] Access pattern:', accessPattern);
  }
}

function updateGlobalCacheStats(store) {
  const state = store.getState();
  
  const globalStats = {
    totalEntries: 0,
    totalSize: 0,
    cacheTypes: {}
  };
  
  ['cache', 'userCache', 'apiCache', 'themeCache', 'i18nCache'].forEach(cacheType => {
    const cacheState = state[cacheType];
    if (cacheState && cacheState.entries) {
      const entries = Object.values(cacheState.entries);
      const entryCount = entries.length;
      const totalSize = entries.reduce((sum, entry) => sum + (entry.size || 0), 0);
      
      globalStats.totalEntries += entryCount;
      globalStats.totalSize += totalSize;
      globalStats.cacheTypes[cacheType] = {
        entries: entryCount,
        size: totalSize
      };
    }
  });
  
  if (typeof window !== 'undefined') {
    window.__CACHE_STATS__ = globalStats;
  }
}

function getCacheTypeFromAction(action) {
  if (action.type.startsWith('userCache/')) return 'userCache';
  if (action.type.startsWith('apiCache/')) return 'apiCache';
  if (action.type.startsWith('themeCache/')) return 'themeCache';
  if (action.type.startsWith('i18nCache/')) return 'i18nCache';
  return 'cache';
}

export default cacheMiddleware;
