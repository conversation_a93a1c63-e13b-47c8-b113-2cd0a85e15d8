import { logger } from '../../core/logging/logger.js';
import securityService from '../../core/security/securityService.js';

class SQLInjectionProtectionService {
  constructor() {
    this.sqlKeywords = [

      'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'REPLACE', 'MERGE',
      

      'CREATE', 'ALTER', 'DROP', 'TRUNCATE', 'RENAME',
      

      'GRANT', 'REVO<PERSON>',
      

      'COMMIT', 'ROLLBACK', 'SAVEPOINT',
      

      'EXEC', 'EXECUTE', 'UNION', 'JOIN', 'HAVING', 'GROUP BY', 'ORDER BY',
      'WHERE', 'FROM', 'INTO', 'VALUES', 'SET', 'DECLARE', 'CAST', 'CONVERT',
      'SUBSTRING', 'CHAR', 'ASCII', 'CONCAT', 'LOAD_FILE', 'OUTFILE',
      'DUMPFILE', 'INFORMATION_SCHEMA', 'SYSOBJEC<PERSON>', 'SYSCOLUMNS',
      'MASTER', 'MSDB', 'TEMPDB', 'MODEL', 'NORTHWIND', 'PUBS'
    ];

    this.sqlFunctions = [
      // System functions
      'USER', 'CURRENT_USER', 'SESSION_USER', 'SYSTEM_USER', 'VERSION',
      'DATABASE', 'SCHEMA', 'CONNECTION_ID', 'LAST_INSERT_ID',
      
      // String functions
      'CONCAT', 'SUBSTRING', 'LEFT', 'RIGHT', 'UPPER', 'LOWER', 'TRIM',
      'REPLACE', 'REVERSE', 'LEN', 'LENGTH', 'CHARINDEX', 'PATINDEX',
      
      // Conversion functions
      'CAST', 'CONVERT', 'STR', 'CHAR', 'ASCII', 'UNICODE',
      
      // Date functions
      'NOW', 'CURDATE', 'CURTIME', 'SYSDATE', 'GETDATE', 'DATEADD',
      'DATEDIFF', 'DATEPART', 'YEAR', 'MONTH', 'DAY',
      
      // Mathematical functions
      'ABS', 'CEILING', 'FLOOR', 'ROUND', 'SQRT', 'POWER', 'RAND',
      
      // Aggregate functions
      'COUNT', 'SUM', 'AVG', 'MIN', 'MAX', 'FIRST', 'LAST',
      
      // Conditional functions
      'IF', 'CASE', 'WHEN', 'THEN', 'ELSE', 'END', 'COALESCE', 'NULLIF',
      'ISNULL', 'IFNULL'
    ];

    this.sqlOperators = [
      // Comparison operators
      '=', '!=', '<>', '<', '>', '<=', '>=', 'LIKE', 'NOT LIKE',
      'IN', 'NOT IN', 'BETWEEN', 'NOT BETWEEN', 'EXISTS', 'NOT EXISTS',
      'IS NULL', 'IS NOT NULL',
      
      // Logical operators
      'AND', 'OR', 'NOT', 'ALL', 'ANY', 'SOME',
      
      // Pattern matching
      'REGEXP', 'RLIKE', 'SOUNDS LIKE'
    ];

    this.dangerousPatterns = [
      // Comment patterns
      /--[\s\S]*$/gm,           // SQL line comments
      /\/\*[\s\S]*?\*\//gm,     // SQL block comments
      /#[\s\S]*$/gm,            // MySQL comments
      
      // Union-based injection
      /\bunion\s+select\b/gi,
      /\bunion\s+all\s+select\b/gi,
      
      // Boolean-based blind injection
      /\b(and|or)\s+\d+\s*=\s*\d+/gi,
      /\b(and|or)\s+['"]\w+['"]?\s*=\s*['"]\w+['"]?/gi,
      /\b(and|or)\s+\d+\s*<>\s*\d+/gi,
      
      // Time-based blind injection
      /\bwaitfor\s+delay\b/gi,
      /\bsleep\s*\(/gi,
      /\bbenchmark\s*\(/gi,
      /\bpg_sleep\s*\(/gi,
      
      // Error-based injection
      /\bextractvalue\s*\(/gi,
      /\bupdatexml\s*\(/gi,
      /\bexp\s*\(/gi,
      
      // Stacked queries
      /;\s*(select|insert|update|delete|drop|create|alter)/gi,
      
      // Information gathering
      /\binformation_schema\b/gi,
      /\bsysobjects\b/gi,
      /\bsyscolumns\b/gi,
      /\bsys\.tables\b/gi,
      /\bsys\.columns\b/gi,
      
      // File operations
      /\bload_file\s*\(/gi,
      /\binto\s+outfile\b/gi,
      /\binto\s+dumpfile\b/gi,
      
      // Stored procedures
      /\bxp_cmdshell\b/gi,
      /\bsp_executesql\b/gi,
      /\bsp_prepare\b/gi,
      /\bsp_execute\b/gi,
      
      // Database-specific functions
      /\bversion\s*\(\s*\)/gi,
      /\buser\s*\(\s*\)/gi,
      /\bdatabase\s*\(\s*\)/gi,
      /\bschema\s*\(\s*\)/gi,
      
      // Hex encoding attempts
      /0x[0-9a-f]+/gi,
      
      // Char function abuse
      /\bchar\s*\(\s*\d+/gi,
      
      // Concatenation attempts
      /\|\|/g,                  // Oracle/PostgreSQL concatenation
      /\+/g,                    // SQL Server concatenation (context-dependent)
      
      // Escape sequence abuse
      /\\x[0-9a-f]{2}/gi,
      /\\[0-7]{3}/g,
      
      // Multiple statement separators
      /;\s*;/g,
      
      // Parentheses imbalance (potential function injection)
      /\(\s*\)/g,
      
      // Quote manipulation
      /['"]\s*\+\s*['"]/g,
      /['"]\s*\|\|\s*['"]/g,
      
      // Conditional logic abuse
      /\bif\s*\(/gi,
      /\bcase\s+when\b/gi,
      
      // Subquery patterns
      /\(\s*select\b/gi,
      
      // Database fingerprinting
      /\b@@version\b/gi,
      /\b@@servername\b/gi,
      /\b@@spid\b/gi,
      
      // Privilege escalation
      /\bgrant\s+/gi,
      /\brevoke\s+/gi,
      
      // Backup/restore operations
      /\bbackup\s+/gi,
      /\brestore\s+/gi,
      
      // Bulk operations
      /\bbulk\s+insert\b/gi,
      /\bopenrowset\b/gi,
      /\bopendatasource\b/gi
    ];

    this.whitelistPatterns = [
      // Safe patterns that might look suspicious but are legitimate
      /^[a-zA-Z0-9_\-\s]+$/,    // Alphanumeric with spaces, hyphens, underscores
      /^\d{4}-\d{2}-\d{2}$/,    // Date format YYYY-MM-DD
      /^\d{2}:\d{2}:\d{2}$/,    // Time format HH:MM:SS
      /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/, // Email format
      /^https?:\/\/[^\s]+$/,    // URL format
      /^[0-9a-fA-F-]{36}$/,     // UUID format
      /^\+?[1-9]\d{1,14}$/      // Phone number format
    ];

    this.maxQueryLength = 10000;
    this.maxParameterCount = 100;
    this.suspiciousThreshold = 3; 
  }

  validateInput(input, options = {}) {
    if (!input || typeof input !== 'string') {
      return {
        isValid: true,
        sanitized: input,
        threats: [],
        riskLevel: 'none'
      };
    }

    const result = {
      isValid: true,
      sanitized: input,
      threats: [],
      riskLevel: 'none',
      confidence: 0
    };

    try {
      if (input.length > this.maxQueryLength) {
        result.threats.push({
          type: 'length_exceeded',
          description: `Input length exceeds maximum allowed (${this.maxQueryLength})`,
          severity: 'high'
        });
        result.riskLevel = 'high';
        result.isValid = false;
      }

      if (this.isWhitelisted(input)) {
        return result;
      }

      const normalizedInput = this.normalizeInput(input);

      const patternThreats = this.checkDangerousPatterns(normalizedInput);
      result.threats.push(...patternThreats);

      const keywordThreats = this.checkSQLKeywords(normalizedInput);
      result.threats.push(...keywordThreats);

      const functionThreats = this.checkSQLFunctions(normalizedInput);
      result.threats.push(...functionThreats);

      const operatorThreats = this.checkSQLOperators(normalizedInput);
      result.threats.push(...operatorThreats);

      const encodingThreats = this.checkEncodingAttempts(input);
      result.threats.push(...encodingThreats);

      this.calculateRisk(result);

      if (options.sanitize !== false) {
        result.sanitized = this.sanitizeInput(input, result.threats);
      }

      result.isValid = result.riskLevel !== 'high' && 
                      result.threats.filter(t => t.severity === 'high').length === 0;

      if (result.threats.length > 0) {
        logger.security('sql_injection_attempt', {
          input: input.substring(0, 200), 
          threats: result.threats.length,
          riskLevel: result.riskLevel,
          userAgent: options.userAgent,
          ip: options.ip
        });
      }

      return result;

    } catch (error) {
      logger.error('Error in SQL injection validation', { 
        error: error.message,
        input: input.substring(0, 100)
      });
      
      return {
        isValid: false,
        sanitized: '',
        threats: [{
          type: 'validation_error',
          description: 'Error during validation - input blocked for security',
          severity: 'high'
        }],
        riskLevel: 'high',
        confidence: 100
      };
    }
  }

  isWhitelisted(input) {
    return this.whitelistPatterns.some(pattern => pattern.test(input));
  }

  normalizeInput(input) {
    return input
      .toLowerCase()
      .replace(/\s+/g, ' ')           
      .replace(/\/\*.*?\*\//g, ' ')   
      .replace(/--.*$/gm, ' ')       
      .replace(/#.*$/gm, ' ')        
      .trim();
  }

  checkDangerousPatterns(input) {
    const threats = [];
    
    this.dangerousPatterns.forEach((pattern, index) => {
      const matches = input.match(pattern);
      if (matches) {
        threats.push({
          type: 'dangerous_pattern',
          pattern: pattern.toString(),
          matches: matches.length,
          description: `Detected dangerous SQL pattern: ${matches[0]}`,
          severity: this.getPatternSeverity(pattern),
          confidence: 85
        });
      }
    });

    return threats;
  }

  checkSQLKeywords(input) {
    const threats = [];
    const words = input.split(/\s+/);
    
    this.sqlKeywords.forEach(keyword => {
      const regex = new RegExp(`\\b${keyword.toLowerCase()}\\b`, 'gi');
      const matches = input.match(regex);
      if (matches) {
        threats.push({
          type: 'sql_keyword',
          keyword: keyword,
          matches: matches.length,
          description: `Detected SQL keyword: ${keyword}`,
          severity: this.getKeywordSeverity(keyword),
          confidence: 70
        });
      }
    });

    return threats;
  }

  checkSQLFunctions(input) {
    const threats = [];
    
    this.sqlFunctions.forEach(func => {
      const regex = new RegExp(`\\b${func.toLowerCase()}\\s*\\(`, 'gi');
      const matches = input.match(regex);
      if (matches) {
        threats.push({
          type: 'sql_function',
          function: func,
          matches: matches.length,
          description: `Detected SQL function: ${func}()`,
          severity: this.getFunctionSeverity(func),
          confidence: 75
        });
      }
    });

    return threats;
  }

  checkSQLOperators(input) {
    const threats = [];
    
    this.sqlOperators.forEach(operator => {
      const regex = new RegExp(`\\b${operator.toLowerCase()}\\b`, 'gi');
      const matches = input.match(regex);
      if (matches) {
        threats.push({
          type: 'sql_operator',
          operator: operator,
          matches: matches.length,
          description: `Detected SQL operator: ${operator}`,
          severity: this.getOperatorSeverity(operator),
          confidence: 60
        });
      }
    });

    return threats;
  }

  checkEncodingAttempts(input) {
    const threats = [];
    
    const hexMatches = input.match(/0x[0-9a-f]+/gi);
    if (hexMatches) {
      threats.push({
        type: 'hex_encoding',
        matches: hexMatches.length,
        description: 'Detected hexadecimal encoding attempt',
        severity: 'medium',
        confidence: 80
      });
    }

    const urlEncodedSql = /%[0-9a-f]{2}/gi;
    const urlMatches = input.match(urlEncodedSql);
    if (urlMatches && urlMatches.length > 5) {
      threats.push({
        type: 'url_encoding',
        matches: urlMatches.length,
        description: 'Detected excessive URL encoding (possible obfuscation)',
        severity: 'medium',
        confidence: 70
      });
    }

    const unicodeMatches = input.match(/\\u[0-9a-f]{4}/gi);
    if (unicodeMatches) {
      threats.push({
        type: 'unicode_encoding',
        matches: unicodeMatches.length,
        description: 'Detected Unicode encoding attempt',
        severity: 'medium',
        confidence: 75
      });
    }

    return threats;
  }

  calculateRisk(result) {
    const threats = result.threats;
    const highSeverityCount = threats.filter(t => t.severity === 'high').length;
    const mediumSeverityCount = threats.filter(t => t.severity === 'medium').length;
    const lowSeverityCount = threats.filter(t => t.severity === 'low').length;

    const totalThreats = threats.length;
    const avgConfidence = totalThreats > 0 
      ? threats.reduce((sum, t) => sum + (t.confidence || 50), 0) / totalThreats
      : 0;

    result.confidence = Math.round(avgConfidence);

    if (highSeverityCount > 0 || totalThreats >= this.suspiciousThreshold) {
      result.riskLevel = 'high';
    } else if (mediumSeverityCount > 1 || totalThreats > 1) {
      result.riskLevel = 'medium';
    } else if (totalThreats > 0) {
      result.riskLevel = 'low';
    } else {
      result.riskLevel = 'none';
    }
  }

  sanitizeInput(input, threats) {
    let sanitized = input;

    sanitized = sanitized.replace(/\/\*[\s\S]*?\*\//g, '');
    sanitized = sanitized.replace(/--.*$/gm, '');
    sanitized = sanitized.replace(/#.*$/gm, '');

    sanitized = sanitized.replace(/'/g, "''");
    sanitized = sanitized.replace(/"/g, '""');

    this.dangerousPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });

    sanitized = sanitized.replace(/\s+/g, ' ').trim();

    return sanitized;
  }

  getPatternSeverity(pattern) {
    const highRiskPatterns = [
      /\bunion\s+select\b/gi,
      /\bxp_cmdshell\b/gi,
      /\bload_file\s*\(/gi,
      /\binto\s+outfile\b/gi
    ];

    if (highRiskPatterns.some(p => p.toString() === pattern.toString())) {
      return 'high';
    }

    return 'medium';
  }

  getKeywordSeverity(keyword) {
    const highRiskKeywords = ['DROP', 'DELETE', 'TRUNCATE', 'EXEC', 'EXECUTE'];
    const mediumRiskKeywords = ['SELECT', 'INSERT', 'UPDATE', 'UNION'];

    if (highRiskKeywords.includes(keyword.toUpperCase())) {
      return 'high';
    } else if (mediumRiskKeywords.includes(keyword.toUpperCase())) {
      return 'medium';
    }

    return 'low';
  }

  getFunctionSeverity(func) {
    const highRiskFunctions = ['LOAD_FILE', 'OUTFILE', 'DUMPFILE'];
    const mediumRiskFunctions = ['USER', 'VERSION', 'DATABASE', 'SCHEMA'];

    if (highRiskFunctions.includes(func.toUpperCase())) {
      return 'high';
    } else if (mediumRiskFunctions.includes(func.toUpperCase())) {
      return 'medium';
    }

    return 'low';
  }

  getOperatorSeverity(operator) {
    const highRiskOperators = ['UNION', 'EXEC'];
    
    if (highRiskOperators.includes(operator.toUpperCase())) {
      return 'high';
    }

    return 'low';
  }

  validateBatch(inputs, options = {}) {
    const results = [];
    
    inputs.forEach((input, index) => {
      const result = this.validateInput(input, {
        ...options,
        batchIndex: index
      });
      results.push(result);
    });

    return {
      results,
      overallValid: results.every(r => r.isValid),
      highRiskCount: results.filter(r => r.riskLevel === 'high').length,
      totalThreats: results.reduce((sum, r) => sum + r.threats.length, 0)
    };
  }

  getStats() {
    return {
      sqlKeywords: this.sqlKeywords.length,
      sqlFunctions: this.sqlFunctions.length,
      sqlOperators: this.sqlOperators.length,
      dangerousPatterns: this.dangerousPatterns.length,
      whitelistPatterns: this.whitelistPatterns.length,
      maxQueryLength: this.maxQueryLength,
      suspiciousThreshold: this.suspiciousThreshold
    };
  }
}

const sqlInjectionProtectionService = new SQLInjectionProtectionService();

export default sqlInjectionProtectionService;
