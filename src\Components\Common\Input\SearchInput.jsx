import React, { useState } from "react";
import { InputText } from "primereact/inputtext";
import { Button } from "primereact/button";
import PropTypes from "prop-types";

const SearchInput = ({
  defaultValue = "",
  onChange,
  onSearch,
  placeholder = "Search...",
  showSearchButton = false,
  className = "",
  size = "normal",
  iconPosition = "left",
  clearable = true,
  disabled = false,
  loading = false,
  ...props
}) => {
  const [searchValue, setSearchValue] = useState(defaultValue);

  const handleInputChange = (e) => {
    const val = e.target.value;
    setSearchValue(val); // instant local update
    if (onChange) onChange(val); // notify parent
  };

  const handleSearchClick = () => {
    if (onSearch) onSearch(searchValue);
  };

  const handleClear = () => {
    setSearchValue("");
    if (onChange) onChange("");
    if (onSearch) onSearch("");
  };

  const handleKeyDown = (e) => {
    if (e.key === "Enter" && onSearch) {
      onSearch(searchValue);
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case "small":
        return "p-inputtext-sm";
      case "large":
        return "p-inputtext-lg";
      default:
        return "";
    }
  };

  return (
    <div className={`search-component ${className}`}>
      <div className="glass-search-container">
        {iconPosition === "left" && (
          <i
            className={`pi ${
              loading ? "pi-spin pi-spinner" : "pi-search"
            } glass-search-icon`}
          />
        )}

        <InputText
          value={searchValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={`glass-search-input ${getSizeClass()}`}
          disabled={disabled}
          {...props}
        />

        {iconPosition === "right" && (
          <i
            className={`pi ${
              loading ? "pi-spin pi-spinner" : "pi-search"
            } glass-search-icon-right`}
          />
        )}

        {clearable && searchValue && !disabled && (
          <i
            className="pi pi-times search-glass-clear-btn"
            onClick={handleClear}
          />
        )}

        {showSearchButton && (
          <Button
            icon="pi pi-search"
            className="glass-search-btn"
            onClick={handleSearchClick}
            disabled={disabled || loading}
            loading={loading}
          />
        )}
      </div>
    </div>
  );
};

SearchInput.propTypes = {
  defaultValue: PropTypes.string,
  onChange: PropTypes.func,
  onSearch: PropTypes.func,
  placeholder: PropTypes.string,
  showSearchButton: PropTypes.bool,
  className: PropTypes.string,
  size: PropTypes.oneOf(["small", "normal", "large"]),
  iconPosition: PropTypes.oneOf(["left", "right"]),
  clearable: PropTypes.bool,
  disabled: PropTypes.bool,
  loading: PropTypes.bool,
};

export default SearchInput;
