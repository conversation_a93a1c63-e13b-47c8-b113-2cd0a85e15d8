import React, { useState, useEffect } from 'react';
import themeController from '../../mvc/controllers/ThemeController.js';
import i18nController from '../../mvc/controllers/I18nController.js';
import './ThemeSelector.css';

const ThemeSelector = ({ 
  className = '',
  showCustomThemes = true,
  showPreview = true,
  compact = false
}) => {
  const [currentTheme, setCurrentTheme] = useState('light');
  const [availableThemes, setAvailableThemes] = useState([]);
  const [customThemes, setCustomThemes] = useState(new Map());
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [showCustomThemeModal, setShowCustomThemeModal] = useState(false);

  useEffect(() => {
    const initializeThemeController = async () => {
      try {
        await themeController.initialize();
        
        const state = themeController.getState();
        setCurrentTheme(state.currentTheme);
        setAvailableThemes(state.availableThemes);
        setCustomThemes(state.customThemes);
      } catch (error) {
        setError(error.message);
      }
    };

    initializeThemeController();

    const unsubscribe = themeController.addEventListener((event) => {
      switch (event.type) {
        case 'loading':
          setLoading(event.newValue);
          break;
        case 'error':
          setError(event.newValue?.message || null);
          break;
        case 'currentTheme':
          setCurrentTheme(event.newValue);
          break;
        case 'availableThemes':
          setAvailableThemes(event.newValue);
          break;
        case 'customThemes':
          setCustomThemes(event.newValue);
          break;
      }
    });

    return unsubscribe;
  }, []);

  const handleThemeChange = async (themeName) => {
    try {
      await themeController.changeTheme(themeName);
    } catch (error) {
      setError(error.message);
    }
  };

  const handleToggleTheme = async () => {
    try {
      await themeController.toggleTheme();
    } catch (error) {
      setError(error.message);
    }
  };

  const getThemeDisplayName = (themeName) => {
    const themeNames = {
      light: i18nController.t('theme.light'),
      dark: i18nController.t('theme.dark'),
      auto: i18nController.t('theme.auto'),
      custom: i18nController.t('theme.custom')
    };
    
    return themeNames[themeName] || themeName;
  };

  const getThemeIcon = (themeName) => {
    const icons = {
      light: '☀️',
      dark: '🌙',
      auto: '🔄',
      custom: '🎨'
    };
    
    return icons[themeName] || '🎨';
  };

  if (compact) {
    return (
      <div className={`theme-selector theme-selector--compact ${className}`}>
        <button
          className="theme-toggle-btn"
          onClick={handleToggleTheme}
          disabled={loading}
          title={i18nController.t('theme.toggleTheme')}
          aria-label={i18nController.t('theme.toggleTheme')}
        >
          {getThemeIcon(currentTheme)}
        </button>
        {error && (
          <div className="theme-selector__error" role="alert">
            {error}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`theme-selector ${className}`}>
      <div className="theme-selector__header">
        <h3 className="theme-selector__title">
          {i18nController.t('theme.themeSelector')}
        </h3>
        {loading && (
          <div className="theme-selector__loading" aria-live="polite">
            {i18nController.t('common.loading')}
          </div>
        )}
      </div>

      {error && (
        <div className="theme-selector__error" role="alert">
          {error}
        </div>
      )}

      <div className="theme-selector__options">
        {availableThemes.map((themeName) => {
          const isCustom = customThemes.has(themeName);
          const isSelected = currentTheme === themeName;
          
          return (
            <div
              key={themeName}
              className={`theme-option ${isSelected ? 'theme-option--selected' : ''} ${isCustom ? 'theme-option--custom' : ''}`}
            >
              <button
                className="theme-option__button"
                onClick={() => handleThemeChange(themeName)}
                disabled={loading}
                aria-pressed={isSelected}
                aria-describedby={`theme-${themeName}-desc`}
              >
                <span className="theme-option__icon">
                  {getThemeIcon(themeName)}
                </span>
                <span className="theme-option__name">
                  {getThemeDisplayName(themeName)}
                </span>
                {isSelected && (
                  <span className="theme-option__check" aria-hidden="true">
                    ✓
                  </span>
                )}
              </button>
              
              {showPreview && (
                <div 
                  className={`theme-option__preview theme-option__preview--${themeName}`}
                  aria-hidden="true"
                >
                  <div className="theme-preview__primary"></div>
                  <div className="theme-preview__secondary"></div>
                  <div className="theme-preview__background"></div>
                </div>
              )}
              
              <div 
                id={`theme-${themeName}-desc`} 
                className="theme-option__description sr-only"
              >
                {i18nController.t(`theme.${themeName}Description`, {
                  defaultValue: `${getThemeDisplayName(themeName)} theme`
                })}
              </div>
            </div>
          );
        })}
      </div>

      {showCustomThemes && (
        <div className="theme-selector__custom">
          <button
            className="theme-selector__create-btn"
            onClick={() => setShowCustomThemeModal(true)}
            disabled={loading}
          >
            <span aria-hidden="true">+</span>
            {i18nController.t('theme.createCustomTheme')}
          </button>
          
          {customThemes.size > 0 && (
            <div className="theme-selector__custom-list">
              <h4 className="theme-selector__custom-title">
                {i18nController.t('theme.customThemes')}
              </h4>
              {Array.from(customThemes.keys()).map((themeName) => (
                <div key={themeName} className="custom-theme-item">
                  <button
                    className={`custom-theme-item__button ${currentTheme === themeName ? 'custom-theme-item__button--selected' : ''}`}
                    onClick={() => handleThemeChange(themeName)}
                    disabled={loading}
                  >
                    {themeName}
                  </button>
                  <button
                    className="custom-theme-item__delete"
                    onClick={() => handleDeleteCustomTheme(themeName)}
                    disabled={loading}
                    title={i18nController.t('common.delete')}
                    aria-label={i18nController.t('theme.deleteCustomTheme', { name: themeName })}
                  >
                    ×
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      <div className="theme-selector__actions">
        <button
          className="theme-selector__action-btn"
          onClick={handleToggleTheme}
          disabled={loading}
        >
          {i18nController.t('theme.toggleTheme')}
        </button>
        
        <button
          className="theme-selector__action-btn"
          onClick={() => handleThemeChange('auto')}
          disabled={loading}
        >
          {i18nController.t('theme.useSystemTheme')}
        </button>
      </div>

      {showCustomThemeModal && (
        <CustomThemeModal
          onClose={() => setShowCustomThemeModal(false)}
          onSave={handleCreateCustomTheme}
        />
      )}
    </div>
  );

  async function handleDeleteCustomTheme(themeName) {
    if (window.confirm(i18nController.t('theme.confirmDeleteTheme', { name: themeName }))) {
      try {
        await themeController.deleteCustomTheme(themeName);
      } catch (error) {
        setError(error.message);
      }
    }
  }

  async function handleCreateCustomTheme(name, config) {
    try {
      await themeController.createCustomTheme(name, config);
      setShowCustomThemeModal(false);
    } catch (error) {
      setError(error.message);
    }
  }
};

const CustomThemeModal = ({ onClose, onSave }) => {
  const [themeName, setThemeName] = useState('');
  const [themeConfig, setThemeConfig] = useState({
    primary: '#3b82f6',
    secondary: '#64748b',
    background: '#ffffff',
    text: '#1f2937',
    border: '#e5e7eb'
  });
  const [saving, setSaving] = useState(false);

  const handleSave = async () => {
    if (!themeName.trim()) {
      alert(i18nController.t('theme.themeNameRequired'));
      return;
    }

    setSaving(true);
    try {
      await onSave(themeName.trim(), themeConfig);
    } catch (error) {
      alert(error.message);
    } finally {
      setSaving(false);
    }
  };

  const handleConfigChange = (key, value) => {
    setThemeConfig(prev => ({
      ...prev,
      [key]: value
    }));
  };

  return (
    <div className="custom-theme-modal" role="dialog" aria-modal="true">
      <div className="custom-theme-modal__backdrop" onClick={onClose}></div>
      <div className="custom-theme-modal__content">
        <div className="custom-theme-modal__header">
          <h2 className="custom-theme-modal__title">
            {i18nController.t('theme.createCustomTheme')}
          </h2>
          <button
            className="custom-theme-modal__close"
            onClick={onClose}
            aria-label={i18nController.t('common.close')}
          >
            ×
          </button>
        </div>

        <div className="custom-theme-modal__body">
          <div className="form-group">
            <label htmlFor="theme-name" className="form-label">
              {i18nController.t('theme.themeName')}
            </label>
            <input
              id="theme-name"
              type="text"
              className="form-input"
              value={themeName}
              onChange={(e) => setThemeName(e.target.value)}
              placeholder={i18nController.t('theme.enterThemeName')}
              required
            />
          </div>

          <div className="theme-config">
            {Object.keys(themeConfig).map((key) => (
              <div key={key} className="form-group">
                <label htmlFor={`theme-${key}`} className="form-label">
                  {i18nController.t(`theme.${key}Color`)}
                </label>
                <div className="color-input-group">
                  <input
                    id={`theme-${key}`}
                    type="color"
                    className="color-input"
                    value={themeConfig[key]}
                    onChange={(e) => handleConfigChange(key, e.target.value)}
                  />
                  <input
                    type="text"
                    className="form-input"
                    value={themeConfig[key]}
                    onChange={(e) => handleConfigChange(key, e.target.value)}
                    placeholder="#000000"
                  />
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="custom-theme-modal__footer">
          <button
            className="btn btn--secondary"
            onClick={onClose}
            disabled={saving}
          >
            {i18nController.t('common.cancel')}
          </button>
          <button
            className="btn btn--primary"
            onClick={handleSave}
            disabled={saving || !themeName.trim()}
          >
            {saving ? i18nController.t('common.saving') : i18nController.t('common.save')}
          </button>
        </div>
      </div>
    </div>
  );
};

export default ThemeSelector;
