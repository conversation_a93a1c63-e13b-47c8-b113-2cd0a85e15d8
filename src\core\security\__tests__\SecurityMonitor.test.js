import SecurityMonitor from '../SecurityMonitor.js';

// Mock logger
jest.mock('../../logging/logger.js', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    security: jest.fn()
  }
}));

// Mock securityService
jest.mock('../securityService.js', () => ({
  validateSecureHeaders: jest.fn(() => true),
  handleCSPViolation: jest.fn()
}));

// Mock DOM APIs
Object.defineProperty(window, 'MutationObserver', {
  writable: true,
  value: jest.fn().mockImplementation(() => ({
    observe: jest.fn(),
    disconnect: jest.fn()
  }))
});

Object.defineProperty(window, 'location', {
  writable: true,
  value: {
    href: 'https://example.com',
    protocol: 'https:',
    hostname: 'example.com'
  }
});

Object.defineProperty(window, 'navigator', {
  writable: true,
  value: {
    userAgent: 'Mozilla/5.0 (Test Browser)'
  }
});

describe('SecurityMonitor', () => {
  beforeEach(() => {
    // Reset the monitor state
    SecurityMonitor.threats.clear();
    SecurityMonitor.anomalies = [];
    SecurityMonitor.securityMetrics = {
      xssAttempts: 0,
      csrfAttempts: 0,
      rateLimitViolations: 0,
      suspiciousRequests: 0,
      malformedRequests: 0
    };
    SecurityMonitor.isMonitoring = false;
    
    // Clear all mocks
    jest.clearAllMocks();
  });

  describe('initMonitoring', () => {
    it('should initialize monitoring successfully', () => {
      SecurityMonitor.initMonitoring();
      
      expect(SecurityMonitor.isMonitoring).toBe(true);
    });

    it('should not reinitialize if already monitoring', () => {
      SecurityMonitor.initMonitoring();
      SecurityMonitor.initMonitoring(); // Second call
      
      expect(SecurityMonitor.isMonitoring).toBe(true);
    });
  });

  describe('recordThreat', () => {
    it('should record a threat successfully', () => {
      const threatType = 'xss_attempt';
      const details = { element: 'script', value: 'alert("xss")' };
      
      SecurityMonitor.recordThreat(threatType, details);
      
      expect(SecurityMonitor.threats.size).toBe(1);
      expect(SecurityMonitor.securityMetrics.xss_attempts).toBe(1);
    });

    it('should generate unique threat IDs', () => {
      SecurityMonitor.recordThreat('xss_attempt', {});
      SecurityMonitor.recordThreat('xss_attempt', {});
      
      const threatIds = Array.from(SecurityMonitor.threats.keys());
      expect(threatIds[0]).not.toBe(threatIds[1]);
    });

    it('should log security events', () => {
      const { logger } = require('../../logging/logger.js');
      
      SecurityMonitor.recordThreat('xss_attempt', { test: 'data' });
      
      expect(logger.security).toHaveBeenCalledWith('xss_attempt', { test: 'data' });
    });
  });

  describe('analyzeNetworkRequest', () => {
    it('should detect suspicious URLs', () => {
      const suspiciousUrl = 'https://example.com?param=<script>alert("xss")</script>';
      
      SecurityMonitor.analyzeNetworkRequest(suspiciousUrl);
      
      expect(SecurityMonitor.threats.size).toBeGreaterThan(0);
    });

    it('should detect suspicious request bodies', () => {
      const url = 'https://example.com/api';
      const options = {
        method: 'POST',
        body: JSON.stringify({ data: '<script>alert("xss")</script>' })
      };
      
      SecurityMonitor.analyzeNetworkRequest(url, options);
      
      expect(SecurityMonitor.threats.size).toBeGreaterThan(0);
    });

    it('should not flag safe requests', () => {
      const safeUrl = 'https://example.com/api/users';
      const options = {
        method: 'GET'
      };
      
      SecurityMonitor.analyzeNetworkRequest(safeUrl, options);
      
      expect(SecurityMonitor.threats.size).toBe(0);
    });
  });

  describe('scanElementForThreats', () => {
    it('should detect dangerous attributes', () => {
      const element = document.createElement('div');
      element.setAttribute('onclick', 'alert("xss")');
      
      SecurityMonitor.scanElementForThreats(element);
      
      expect(SecurityMonitor.threats.size).toBeGreaterThan(0);
    });

    it('should detect javascript: URLs', () => {
      const element = document.createElement('a');
      element.setAttribute('href', 'javascript:alert("xss")');
      
      SecurityMonitor.scanElementForThreats(element);
      
      expect(SecurityMonitor.threats.size).toBeGreaterThan(0);
    });

    it('should not flag safe elements', () => {
      const element = document.createElement('p');
      element.textContent = 'Safe content';
      
      SecurityMonitor.scanElementForThreats(element);
      
      expect(SecurityMonitor.threats.size).toBe(0);
    });
  });

  describe('analyzeError', () => {
    it('should detect security-related errors', () => {
      const errorEvent = {
        message: 'Script blocked by Content Security Policy',
        filename: 'https://example.com/script.js',
        lineno: 10,
        colno: 5
      };
      
      SecurityMonitor.analyzeError(errorEvent);
      
      expect(SecurityMonitor.threats.size).toBeGreaterThan(0);
    });

    it('should ignore non-security errors', () => {
      const errorEvent = {
        message: 'TypeError: Cannot read property of undefined',
        filename: 'https://example.com/app.js',
        lineno: 100,
        colno: 20
      };
      
      SecurityMonitor.analyzeError(errorEvent);
      
      expect(SecurityMonitor.threats.size).toBe(0);
    });
  });

  describe('analyzeConsoleOutput', () => {
    it('should detect security-related console messages', () => {
      const args = ['Security warning:', 'XSS attempt detected'];
      
      SecurityMonitor.analyzeConsoleOutput('warn', args);
      
      expect(SecurityMonitor.threats.size).toBeGreaterThan(0);
    });

    it('should ignore normal console messages', () => {
      const args = ['Debug info:', 'User clicked button'];
      
      SecurityMonitor.analyzeConsoleOutput('log', args);
      
      expect(SecurityMonitor.threats.size).toBe(0);
    });
  });

  describe('checkThreatLevel', () => {
    it('should trigger alert when threshold exceeded', () => {
      // Mock window.dispatchEvent
      const dispatchEventSpy = jest.spyOn(window, 'dispatchEvent').mockImplementation();
      
      // Record multiple threats of the same type
      for (let i = 0; i < 6; i++) {
        SecurityMonitor.recordThreat('xss_attempt', { test: i });
      }
      
      expect(dispatchEventSpy).toHaveBeenCalled();
      
      dispatchEventSpy.mockRestore();
    });
  });

  describe('cleanupOldThreats', () => {
    it('should remove old threats', () => {
      // Add a threat
      SecurityMonitor.recordThreat('xss_attempt', {});
      
      // Mock old timestamp
      const threats = Array.from(SecurityMonitor.threats.values());
      threats[0].timestamp = Date.now() - (25 * 60 * 60 * 1000); // 25 hours ago
      
      SecurityMonitor.cleanupOldThreats();
      
      expect(SecurityMonitor.threats.size).toBe(0);
    });

    it('should keep recent threats', () => {
      SecurityMonitor.recordThreat('xss_attempt', {});
      
      SecurityMonitor.cleanupOldThreats();
      
      expect(SecurityMonitor.threats.size).toBe(1);
    });
  });

  describe('getSecurityStatus', () => {
    it('should return current security status', () => {
      SecurityMonitor.initMonitoring();
      SecurityMonitor.recordThreat('xss_attempt', {});
      
      const status = SecurityMonitor.getSecurityStatus();
      
      expect(status).toEqual({
        isMonitoring: true,
        threatCount: 1,
        recentMetrics: expect.any(Object),
        anomalyCount: 0
      });
    });
  });

  describe('handleSecurityEvent', () => {
    it('should handle custom security events', () => {
      const eventData = {
        type: 'custom_threat',
        details: { source: 'external' }
      };
      
      SecurityMonitor.handleSecurityEvent(eventData);
      
      expect(SecurityMonitor.threats.size).toBe(1);
    });
  });

  describe('generateThreatId', () => {
    it('should generate unique threat IDs', () => {
      const id1 = SecurityMonitor.generateThreatId();
      const id2 = SecurityMonitor.generateThreatId();
      
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^threat_\d+_[a-z0-9]+$/);
    });
  });
});
