{"nav": {"home": "Home", "dashboard": "Dashboard", "components": "Components", "forms": "Forms", "tables": "Tables", "settings": "Settings", "help": "Help", "logout": "Logout"}, "common": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Information", "cancel": "Cancel", "ok": "OK", "yes": "Yes", "no": "No", "save": "Save", "delete": "Delete", "edit": "Edit", "add": "Add", "remove": "Remove", "search": "Search", "filter": "Filter", "clear": "Clear", "reset": "Reset", "submit": "Submit", "close": "Close", "back": "Back", "next": "Next", "previous": "Previous", "select": "Select", "selectAll": "Select All", "deselectAll": "Deselect All"}, "navigation": {"home": "Home", "dashboard": "Dashboard", "profile": "Profile", "settings": "Settings", "users": "Users", "admin": "Admin", "help": "Help", "about": "About", "contact": "Contact", "logout": "Logout", "login": "<PERSON><PERSON>", "register": "Register"}, "theme": {"selectTheme": "Select Theme", "lightMode": "Light Mode", "darkMode": "Dark Mode", "customTheme": "Custom Theme", "applyTheme": "Apply Theme"}, "language": {"selectLanguage": "Select Language", "currentLanguage": "Current Language"}, "user": {"profile": "User Profile", "account": "Account", "personalInfo": "Personal Information", "contactInfo": "Contact Information", "preferences": "Preferences", "security": "Security", "privacy": "Privacy", "notifications": "Notifications", "firstName": "First Name", "lastName": "Last Name", "fullName": "Full Name", "displayName": "Display Name", "bio": "Biography", "avatar": "Avatar", "role": "Role", "permissions": "Permissions", "lastLogin": "Last Login", "joinDate": "Join Date", "emailVerified": "<PERSON><PERSON>", "phoneVerified": "Phone Verified", "twoFactorEnabled": "Two-Factor Authentication Enabled"}, "form": {"required": "This field is required", "invalid": "Invalid value", "minLength": "Minimum length is {min} characters", "maxLength": "Maximum length is {max} characters", "email": "Please enter a valid email address", "number": "Please enter a valid number", "date": "Please enter a valid date", "url": "Please enter a valid URL", "phone": "Please enter a valid phone number"}, "datatable": {"noRecords": "No records found", "loading": "Loading data...", "rowsPerPage": "Rows per page", "of": "of", "first": "First", "last": "Last", "next": "Next", "previous": "Previous", "sortAscending": "Sort ascending", "sortDescending": "Sort descending", "filter": "Filter", "globalFilter": "Global search", "export": "Export", "print": "Print"}, "demo": {"title": "Dynamic Component Framework Demo", "description": "Explore the comprehensive component framework with theme and language support", "componentShowcase": "Component Showcase", "formComponents": "Form Components", "layoutComponents": "Layout Components", "dataComponents": "Data Components", "messageComponents": "Message Components"}, "forms": {"validation": {"required": "This field is required", "email": "Please enter a valid email address", "password": "Password must be at least 8 characters long", "passwordMatch": "Passwords do not match", "minLength": "Must be at least {min} characters", "maxLength": "Must be no more than {max} characters", "numeric": "Must be a number", "alphanumeric": "Must contain only letters and numbers", "phone": "Please enter a valid phone number", "url": "Please enter a valid URL", "date": "Please enter a valid date", "time": "Please enter a valid time", "fileSize": "File size must be less than {size}MB", "fileType": "Invalid file type. Allowed types: {types}", "unique": "This value already exists", "min": "Value must be at least {min}", "max": "Value must be no more than {max}", "pattern": "Invalid format", "custom": "Invalid value"}, "placeholders": {"enterName": "Enter your name", "enterEmail": "Enter your email", "enterPassword": "Enter your password", "confirmPassword": "Confirm your password", "enterPhone": "Enter your phone number", "enterAddress": "Enter your address", "selectOption": "Select an option", "searchHere": "Search here...", "typeMessage": "Type your message...", "selectDate": "Select date", "selectTime": "Select time", "uploadFile": "Upload file", "dragDropFile": "Drag and drop file here"}}, "messages": {"success": {"saved": "Successfully saved", "updated": "Successfully updated", "deleted": "Successfully deleted", "created": "Successfully created", "sent": "Successfully sent", "uploaded": "Successfully uploaded", "downloaded": "Successfully downloaded", "copied": "Successfully copied", "imported": "Successfully imported", "exported": "Successfully exported"}, "error": {"general": "An error occurred. Please try again.", "network": "Network error. Please check your connection.", "server": "Server error. Please try again later.", "notFound": "The requested resource was not found.", "unauthorized": "You are not authorized to perform this action.", "forbidden": "Access denied.", "validation": "Please correct the errors and try again.", "timeout": "Request timed out. Please try again.", "fileUpload": "File upload failed. Please try again.", "fileTooLarge": "File is too large. Maximum size is {size}MB.", "invalidFileType": "Invalid file type. Allowed types: {types}", "sessionExpired": "Your session has expired. Please log in again.", "maintenance": "The system is under maintenance. Please try again later."}, "warning": {"unsavedChanges": "You have unsaved changes. Are you sure you want to leave?", "deleteConfirm": "Are you sure you want to delete this item?", "irreversible": "This action cannot be undone.", "dataLoss": "This action may result in data loss.", "slowConnection": "Your connection seems slow. This may take a while.", "browserSupport": "Your browser may not support all features.", "cookiesDisabled": "Cookies are disabled. Some features may not work properly.", "javascriptDisabled": "JavaScript is disabled. Please enable it for full functionality."}, "info": {"noData": "No data available", "noResults": "No results found", "emptyList": "The list is empty", "processing": "Processing your request...", "uploading": "Uploading file...", "downloading": "Downloading file...", "connecting": "Connecting...", "syncing": "Syncing data...", "offline": "You are currently offline", "online": "You are back online", "newVersion": "A new version is available", "updateRequired": "Please update to continue"}}, "security": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmNewPassword": "Confirm New Password", "twoFactor": "Two-Factor Authentication", "securityCode": "Security Code", "backupCodes": "Backup Codes", "securityQuestions": "Security Questions", "sessionTimeout": "Session Timeout", "loginAttempts": "Login Attempts", "accountLocked": "Account Locked", "passwordStrength": "Password Strength", "weak": "Weak", "medium": "Medium", "strong": "Strong", "veryStrong": "Very Strong", "securityAlert": "Security Alert", "suspiciousActivity": "Suspicious Activity Detected", "loginFromNewDevice": "Login from new device", "passwordChanged": "Password changed successfully", "accountSecured": "Your account is secured"}, "accessibility": {"skipToContent": "Skip to main content", "openMenu": "Open menu", "closeMenu": "Close menu", "toggleTheme": "Toggle theme", "changeLanguage": "Change language", "screenReader": "Screen reader", "keyboardNavigation": "Keyboard navigation", "highContrast": "High contrast", "largeText": "Large text", "reducedMotion": "Reduced motion", "altText": "Alternative text", "ariaLabel": "Accessible label", "ariaDescription": "Accessible description"}}