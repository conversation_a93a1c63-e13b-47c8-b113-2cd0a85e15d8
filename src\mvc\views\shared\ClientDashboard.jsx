import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { Chart } from 'primereact/chart';
import FromToDateInput from '../../Components/Common/FromToDateInput';
import FilterBy from '../../Components/Common/FilterBy';
import 'chart.js/auto';
import SearchInput from '../../Components/Common/Input/SearchInput';
import GlassyTabs from '../../Components/Common/Tab/GlassyTabs';


// Using PrimeReact Chart component which internally uses Chart.js

const ClientDashboard = () => {
     const [searchValue, setSearchValue] = useState("");
    const [activeTab, setActiveTab] = useState(0);
    const [fromDate, setFromDate] = useState(null);
    const [toDate, setToDate] = useState(null);
    const [globalFilter, setGlobalFilter] = useState('');
    const [filters, setFilters] = useState({
        global: { value: null, matchMode: 'contains' },
    });

    // Bar Chart Data
    const serviceOrdersData = {
        labels: ['01-02', '01-03', '01-04', '01-05', '01-06', '01-12'],
        datasets: [
            {
                type: 'bar',
                label: 'Closed',
                backgroundColor: '#22C55E',
                data: [80, 120, 60, 100, 60, 110],
                barPercentage: 0.8,
                categoryPercentage: 0.9,
                borderRadius: 0,
                stack: 'stack1'
            },
            {
                type: 'bar',
                label: 'Pending',
                backgroundColor: '#DC2626',
                data: [20, 40, 15, 30, 20, 35],
                barPercentage: 0.8,
                categoryPercentage: 0.9,
                borderRadius: 0,
                stack: 'stack1'
            }
        ]
    };

    // Line Chart Data
    const creditExposureData = {
        labels: ['Sales', 'Current', 'Usage', 'Approved', 'Available', 'Overdue', 'Past'],
        datasets: [{
            label: 'Credit Exposure',
            data: [8000, 10000, 9000, 8500, 8500, 7000, 2000],
            borderColor: '#2563EB',
            backgroundColor: 'rgba(37, 99, 235, 0.05)',
            tension: 0.3,
            fill: true,
            pointStyle: 'circle',
            pointRadius: 4,
            pointHoverRadius: 6,
            pointBackgroundColor: '#ffffff',
            pointBorderColor: '#2563EB',
            pointBorderWidth: 2,
            borderWidth: 2
        }]
    };

    const getChartOptions = (isLineChart = false) => ({
        plugins: {
            legend: {
                position: 'bottom',
                align: 'start',
                labels: {
                    usePointStyle: true,
                    padding: 20,
                    font: {
                        size: 12,
                        family: "'Inter', sans-serif"
                    },
                    boxWidth: 6,
                    color: '#64748B'
                }
            },
            title: {
                display: true,
                text: isLineChart ? 'Customer Credit Exposure' : 'Service Orders Over Time by Orders',
                align: 'start',
                color: '#1E293B',
                font: {
                    size: 16,
                    family: "'Inter', sans-serif",
                    weight: '600'
                },
                padding: {
                    bottom: 25
                }
            },
            tooltip: {
                mode: 'index',
                intersect: false,
                backgroundColor: 'white',
                titleColor: '#1E293B',
                bodyColor: '#1E293B',
                borderColor: '#E2E8F0',
                borderWidth: 1,
                padding: 12,
                callbacks: {
                    label: function(context) {
                        let label = context.dataset.label || '';
                        if (label) {
                            label += ': ';
                        }
                        if (context.parsed.y !== null) {
                            label += isLineChart ? context.parsed.y.toLocaleString() : context.parsed.y;
                        }
                        return label;
                    }
                }
            }
        },
        scales: {
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    color: '#64748B',
                    font: {
                        size: 12
                    }
                }
            },
            y: {
                beginAtZero: true,
                grid: {
                    color: '#E2E8F0',
                    drawBorder: false
                },
                border: {
                    display: false
                },
                ticks: {
                    color: '#64748B',
                    font: {
                        size: 12,
                        family: "'Inter', sans-serif"
                    },
                    padding: 8,
                    maxTicksLimit: 6,
                    ...(isLineChart && {
                        callback: (value) => value.toLocaleString()
                    })
                }
            }
        },
        maintainAspectRatio: false,
        interaction: {
            intersect: false,
            mode: 'index'
        },
        layout: {
            padding: {
                top: 20
            }
        }
    });

    // Chart Options
    const chartOptions = {
        maintainAspectRatio: false,
        responsive: true,
        plugins: {
            legend: {
                position: 'bottom',
                align: 'start',
                labels: {
                    usePointStyle: true,
                    padding: 25,
                    font: { size: 12 }
                }
            }
        },
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    drawBorder: false,
                    color: '#E2E8F0'
                },
                ticks: {
                    font: { size: 12 }
                }
            },
            x: {
                grid: {
                    display: false
                },
                ticks: {
                    font: { size: 12 }
                }
            }
        }
    };

    // Sample flight data
    const flights = [
        {
            flightNumber: 'FR123',
            aircraft: 'Boeing 737',
            fromAirport: 'VIDP',
            toAirport: 'LTDD',
            purpose: 'Passenger',
            crewCount: 6,
            passengerCount: 5,
            departureDate: '5/1/2025',
            arrivalDate: '12/1/2025',
            status: 'Reached'
        },
        {
            flightNumber: 'QR456',
            aircraft: 'Airbus A320',
            fromAirport: 'OMDB',
            toAirport: 'EGLL',
            purpose: 'Cargo',
            crewCount: 4,
            passengerCount: 0,
            departureDate: '5/1/2025',
            arrivalDate: '12/1/2025',
            status: 'In Transit'
        },
        {
            flightNumber: 'EK789',
            aircraft: 'Boeing 777',
            fromAirport: 'OMDB',
            toAirport: 'KJFK',
            purpose: 'Passenger',
            crewCount: 8,
            passengerCount: 12,
            departureDate: '6/1/2025',
            arrivalDate: '13/1/2025',
            status: 'Scheduled'
        },
        {
            flightNumber: 'BA234',
            aircraft: 'Airbus A380',
            fromAirport: 'EGLL',
            toAirport: 'WSSS',
            purpose: 'Passenger',
            crewCount: 10,
            passengerCount: 15,
            departureDate: '7/1/2025',
            arrivalDate: '14/1/2025',
            status: 'Delayed'
        },
        {
            flightNumber: 'LH567',
            aircraft: 'Boeing 747',
            fromAirport: 'EDDF',
            toAirport: 'VABB',
            purpose: 'Cargo',
            crewCount: 6,
            passengerCount: 0,
            departureDate: '8/1/2025',
            arrivalDate: '15/1/2025',
            status: 'Cancelled'
        }
    ];

    const tabItems = [
        { label: 'Active Flight', icon: 'pi pi-clock' },
        { label: 'Past Flight', icon: 'pi pi-calendar' }
    ];

    const filterOptions = [
        { label: 'Filter', value: '' },
        { label: 'Flight Number', value: 'flightNumber' },
        { label: 'Aircraft', value: 'aircraft' },
        { label: 'From Airport', value: 'fromAirport' },
        { label: 'To Airport', value: 'toAirport' },
        { label: 'Status', value: 'status' }
    ];

    const [selectedFilter, setSelectedFilter] = useState(filterOptions[0]);

    const statusBodyTemplate = (rowData) => {
        const statusClasses = {
            'Reached': 'client-status-reached',
            'In Transit': 'client-status-transit',
            'Scheduled': 'client-status-scheduled',
            'Delayed': 'client-status-delayed',
            'Cancelled': 'client-status-cancelled'
        };
        return <span className={`client-status-badge ${statusClasses[rowData.status]}`}>{rowData.status}</span>;
    };

    const actionBodyTemplate = () => {
        return (
            <div className="client-action-buttons">
                <Button 
                    icon="pi pi-eye" 
                    className="p-button-text p-button-rounded p-button-plain"
                    style={{ padding: '6px', color: '#64748B' }}
                />
                <Button 
                    icon="pi pi-share-alt" 
                    className="p-button-text p-button-rounded p-button-plain"
                    style={{ padding: '6px', color: '#64748B' }}
                />
            </div>
        );
    };

    const  handleactiveTab = (index, tab) => {
        setActiveTab(index);
       
      };

    return (
        <div className="manage-users-page p-3">
            <div className="gap-5 grid mb-3">
                <div className="client-chart-box glass-card-global w-48">
                  
                        <Chart type="bar" data={serviceOrdersData} options={getChartOptions(false)} />
                   
                </div>
                
                <div className="client-chart-box glass-card-global w-48">
                  
                        <Chart type="line" data={creditExposureData} options={getChartOptions(true)} />
                </div>
            </div>

            <div className="glass-card-global p-4">
    <div className="flex flex-column gap-3">
       
            <GlassyTabs
                  tabs={tabItems}
                  activeIndex={activeTab}
                  onTabChange={handleactiveTab}
                  variant="default"
                  showBadges={false} // We show counts in labels instead
                />
        
        <div className="flex flex-column md:flex-row md:justify-content-between gap-3 mb-4">
            <div className="flex flex-column md:flex-row gap-3 flex-1">
                <span className="p-input-icon-left w-full md:w-auto">
                    <i className="pi pi-search" />
                   <SearchInput
                               value={searchValue}
                               onChange={setSearchValue}
                            //    onSearch={handleSearch}
                               placeholder="Search employees by name, email, or ID..."
                             />
                </span>
                <FilterBy
                    value={selectedFilter}
                    onChange={(value) => setSelectedFilter(value)}
                    options={filterOptions}
                    placeholder="Filter"
                    className="w-full md:w-auto"
                />
            </div>
            <div className="w-full md:w-auto">
                <FromToDateInput
                    fromDate={fromDate}
                    toDate={toDate}
                    onFromDateChange={setFromDate}
                    onToDateChange={setToDate}
                    className="w-full"
                />
            </div>
        </div>

        <div className="card">
            <DataTable
                value={flights}
                responsiveLayout="scroll"
                stripedRows
                filters={filters}
                globalFilterFields={['flightNumber', 'aircraft', 'fromAirport', 'toAirport', 'purpose', 'status']}
                scrollable
                scrollHeight="flex"
                className=" glass-table"
                emptyMessage="No flights found."
            >
                <Column field="flightNumber" header="Flight Number" sortable />
                <Column field="aircraft" header="Aircraft" sortable />
                <Column field="fromAirport" header="From" sortable />
                <Column field="toAirport" header="To" sortable />
                <Column field="purpose" header="Purpose" sortable />
                <Column field="crewCount" header="Crew" sortable />
                <Column field="passengerCount" header="Pax" sortable />
                <Column 
                    field="departureDate" 
                    header="Departure" 
                    body={(rowData) => (rowData.departureDate)}
                    sortable 
                />
                <Column 
                    field="arrivalDate" 
                    header="Arrival" 
                    body={(rowData) => (rowData.arrivalDate)}
                    sortable 
                />
                <Column 
                    field="status" 
                    header="Status" 
                    body={statusBodyTemplate} 
                    sortable 
                />
                <Column 
                    header="Actions" 
                    body={actionBodyTemplate} 
                    // style={{ width: '120px' }} 
                    // headerStyle={{ width: '120px' }}
                />
            </DataTable>
        </div>
    </div>
</div>
        </div>
    );
};

export default ClientDashboard;
