import BaseController from './BaseController.js';
import i18nService from '../services/I18nService.js';
import { logger } from '../../core/logging/logger.js';

class I18nController extends BaseController {
  constructor() {
    super();
    
    this.state = {
      currentLocale: 'en',
      supportedLocales: [],
      isRTL: false,
      loadedLocales: [],
      translations: {},
      localePreferences: {}
    };
    
    this.localeChangeListener = null;
  }

  async onInitialize() {
    this.setState({
      currentLocale: i18nService.getCurrentLocale(),
      supportedLocales: i18nService.getSupportedLocales(),
      isRTL: i18nService.isRTL(),
      loadedLocales: Array.from(i18nService.loadedMessages.keys())
    });

    this.localeChangeListener = i18nService.addListener((newLocale, oldLocale) => {
      this.setState({
        currentLocale: newLocale,
        isRTL: i18nService.isRTL(newLocale)
      });
      
      this.notifyListeners('localeChanged', {
        newLocale,
        oldLocale,
        isRTL: i18nService.isRTL(newLocale)
      });
    });

    await this.loadLocalePreferences();
  }

  async changeLocale(locale) {
    return this.executeAction('changeLocale', async () => {
      if (!this.state.supportedLocales.includes(locale)) {
        throw new Error(`Unsupported locale: ${locale}`);
      }

      const success = await i18nService.changeLocale(locale);
      
      if (success) {
        this.setState({
          currentLocale: locale,
          isRTL: i18nService.isRTL(locale),
          loadedLocales: Array.from(i18nService.loadedMessages.keys())
        });
        
        await this.saveLocalePreference(locale);
        
        logger.info('Locale changed successfully', { 
          locale,
          controller: 'I18nController'
        });
        
        return { success: true, locale };
      } else {
        throw new Error(`Failed to change locale to: ${locale}`);
      }
    });
  }

  translate(key, values = {}) {
    try {
      return i18nService.t(key, values);
    } catch (error) {
      logger.warn('Translation failed', { 
        key,
        locale: this.state.currentLocale,
        error: error.message
      });
      return key; 
    }
  }

  t(key, values = {}) {
    return this.translate(key, values);
  }

  formatDate(date, options = {}) {
    try {
      return i18nService.formatDate(date, options);
    } catch (error) {
      logger.warn('Date formatting failed', { 
        date,
        locale: this.state.currentLocale,
        error: error.message
      });
      return date.toString();
    }
  }

  formatTime(time, options = {}) {
    try {
      return i18nService.formatTime(time, options);
    } catch (error) {
      logger.warn('Time formatting failed', { 
        time,
        locale: this.state.currentLocale,
        error: error.message
      });
      return time.toString();
    }
  }

  formatDateTime(datetime, options = {}) {
    try {
      return i18nService.formatDateTime(datetime, options);
    } catch (error) {
      logger.warn('DateTime formatting failed', { 
        datetime,
        locale: this.state.currentLocale,
        error: error.message
      });
      return datetime.toString();
    }
  }

  formatNumber(number, options = {}) {
    try {
      return i18nService.formatNumber(number, options);
    } catch (error) {
      logger.warn('Number formatting failed', { 
        number,
        locale: this.state.currentLocale,
        error: error.message
      });
      return number.toString();
    }
  }

  formatCurrency(amount, currency = 'USD', options = {}) {
    try {
      return i18nService.formatCurrency(amount, currency, options);
    } catch (error) {
      logger.warn('Currency formatting failed', { 
        amount,
        currency,
        locale: this.state.currentLocale,
        error: error.message
      });
      return `${currency} ${amount}`;
    }
  }

  formatPercent(value, options = {}) {
    try {
      return i18nService.formatPercent(value, options);
    } catch (error) {
      logger.warn('Percent formatting failed', { 
        value,
        locale: this.state.currentLocale,
        error: error.message
      });
      return `${value}%`;
    }
  }

  formatRelativeTime(value, unit, options = {}) {
    try {
      return i18nService.formatRelativeTime(value, unit, options);
    } catch (error) {
      logger.warn('Relative time formatting failed', { 
        value,
        unit,
        locale: this.state.currentLocale,
        error: error.message
      });
      return `${value} ${unit}`;
    }
  }

  formatList(list, options = {}) {
    try {
      return i18nService.formatList(list, options);
    } catch (error) {
      logger.warn('List formatting failed', { 
        list,
        locale: this.state.currentLocale,
        error: error.message
      });
      return list.join(', ');
    }
  }

  getLocaleInfo(locale = null) {
    const targetLocale = locale || this.state.currentLocale;
    return i18nService.getLocaleInfo(targetLocale);
  }

  getSupportedLocalesWithInfo() {
    return this.state.supportedLocales.map(locale => ({
      code: locale,
      ...this.getLocaleInfo(locale)
    }));
  }

  isRTL() {
    return this.state.isRTL;
  }

  getDirection() {
    return this.state.isRTL ? 'rtl' : 'ltr';
  }

  async preloadLocale(locale) {
    return this.executeAction('preloadLocale', async () => {
      const success = await i18nService.preloadLocale(locale);
      
      if (success) {
        this.setState({
          loadedLocales: Array.from(i18nService.loadedMessages.keys())
        });
        
        logger.debug('Locale preloaded', { 
          locale,
          controller: 'I18nController'
        });
        
        return { success: true, locale };
      } else {
        throw new Error(`Failed to preload locale: ${locale}`);
      }
    });
  }

  getBrowserLocales() {
    return i18nService.getBrowserLocales();
  }

  getBestMatchingLocale() {
    return i18nService.getBestMatchingLocale();
  }

  async applyBrowserLocale() {
    return this.executeAction('applyBrowserLocale', async () => {
      const bestLocale = this.getBestMatchingLocale();
      
      if (bestLocale !== this.state.currentLocale) {
        return await this.changeLocale(bestLocale);
      }
      
      return { success: true, locale: bestLocale, unchanged: true };
    });
  }

  async loadLocalePreferences() {
    try {
      const preferences = await this.apiGet('/user/locale-preferences');
      this.setState({ localePreferences: preferences });
    } catch (error) {
      const stored = localStorage.getItem('localePreferences');
      if (stored) {
        try {
          const preferences = JSON.parse(stored);
          this.setState({ localePreferences: preferences });
        } catch (parseError) {
          logger.warn('Failed to parse stored locale preferences', { 
            error: parseError.message 
          });
        }
      }
    }
  }

  async saveLocalePreference(locale) {
    const preferences = {
      ...this.state.localePreferences,
      selectedLocale: locale,
      lastChanged: new Date().toISOString(),
      browserLocales: this.getBrowserLocales()
    };
    
    this.setState({ localePreferences: preferences });
    
    localStorage.setItem('localePreferences', JSON.stringify(preferences));
    
    try {
      await this.apiPost('/user/locale-preferences', preferences);
    } catch (error) {
      logger.warn('Failed to save locale preferences to server', { 
        error: error.message 
      });
    }
  }

  getTranslationStats() {
    return {
      currentLocale: this.state.currentLocale,
      supportedLocales: this.state.supportedLocales.length,
      loadedLocales: this.state.loadedLocales.length,
      isRTL: this.state.isRTL,
      hasPreferences: Object.keys(this.state.localePreferences).length > 0,
      ...i18nService.getStats()
    };
  }

  hasTranslation(key) {
    try {
      const translation = this.translate(key);
      return translation !== key;
    } catch (error) {
      return false;
    }
  }

  getMissingTranslations(keys) {
    const missing = [];
    
    keys.forEach(key => {
      if (!this.hasTranslation(key)) {
        missing.push(key);
      }
    });
    
    return missing;
  }

  async switchToNextLocale() {
    const currentIndex = this.state.supportedLocales.indexOf(this.state.currentLocale);
    const nextIndex = (currentIndex + 1) % this.state.supportedLocales.length;
    const nextLocale = this.state.supportedLocales[nextIndex];
    
    return await this.changeLocale(nextLocale);
  }

  async resetToDefault() {
    return await this.changeLocale('en');
  }

  getLocaleConfig() {
    return {
      locale: this.state.currentLocale,
      direction: this.getDirection(),
      isRTL: this.state.isRTL,
      dateFormat: this.getDateFormat(),
      timeFormat: this.getTimeFormat(),
      numberFormat: this.getNumberFormat()
    };
  }

  getDateFormat() {
    const formats = {
      'en': 'MM/DD/YYYY',
      'es': 'DD/MM/YYYY',
      'fr': 'DD/MM/YYYY',
      'de': 'DD.MM.YYYY',
      'ja': 'YYYY/MM/DD',
      'zh': 'YYYY/MM/DD'
    };
    
    return formats[this.state.currentLocale] || formats['en'];
  }

  getTimeFormat() {
    const formats = {
      'en': '12', 
      'es': '24', 
      'fr': '24',
      'de': '24',
      'ja': '24',
      'zh': '24'
    };
    
    return formats[this.state.currentLocale] || formats['en'];
  }

  getNumberFormat() {
    const formats = {
      'en': { decimal: '.', thousands: ',' },
      'es': { decimal: ',', thousands: '.' },
      'fr': { decimal: ',', thousands: ' ' },
      'de': { decimal: ',', thousands: '.' },
      'ja': { decimal: '.', thousands: ',' },
      'zh': { decimal: '.', thousands: ',' }
    };
    
    return formats[this.state.currentLocale] || formats['en'];
  }

  destroy() {
    if (this.localeChangeListener) {
      this.localeChangeListener();
      this.localeChangeListener = null;
    }
    
    super.destroy();
  }
}

const i18nController = new I18nController();

export default i18nController;
