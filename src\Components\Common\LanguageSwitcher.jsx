import React from 'react';
import { useTranslation } from 'react-i18next';
import { Dropdown } from 'primereact/dropdown';

const LanguageSwitcher = () => {
  const { t, i18n } = useTranslation();

  const languages = [
    { label: t('language.english'), value: 'en' },
    { label: t('language.spanish'), value: 'es' },
    { label: t('language.hindi'), value: 'hi' },
    { label: t('language.marathi'), value: 'mr' }
  ];

  const handleLanguageChange = (e) => {
    i18n.changeLanguage(e.value);
  };

  return (
    <div className="language-switcher">
      <Dropdown
        value={i18n.language}
        options={languages}
        onChange={handleLanguageChange}
        className="w-full md:w-14rem language-dropdown"
        optionLabel="label"
        placeholder={t('language.select')}
      />
    </div>
  );
};

export default LanguageSwitcher;