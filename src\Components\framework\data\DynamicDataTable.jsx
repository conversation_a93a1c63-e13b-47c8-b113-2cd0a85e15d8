import React, { useState, useCallback, useMemo, useRef } from 'react';
import PropTypes from 'prop-types';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { MultiSelect } from 'primereact/multiselect';
import { Calendar } from 'primereact/calendar';
import { TriStateCheckbox } from 'primereact/tristatecheckbox';
import { FilterMatchMode, FilterOperator } from 'primereact/api';

const DynamicDataTable = ({
  data = [],
  columns = [],
  config = {},
  onSelectionChange,
  onRowSelect,
  onRowUnselect,
  onRowClick,
  onRowDoubleClick,
  onSort,
  onFilter,
  onPage,
  onRowReorder,
  onRowExpand,
  onRowCollapse,
  onCellEditComplete,
  onRowEditComplete,
  onRowEditCancel,
  onEvent,
  className = '',
  style = {},
  loading = false,
  debug = false,
  ...props
}) => {
  const [selectedRows, setSelectedRows] = useState(config.selection?.multiple ? [] : null);
  const [filters, setFilters] = useState(config.filtering?.defaultFilters || {});
  const [globalFilterValue, setGlobalFilterValue] = useState('');
  const [first, setFirst] = useState(0);
  const [rows, setRows] = useState(config.pagination?.rows || 10);
  const [sortField, setSortField] = useState(config.sorting?.defaultSortField || null);
  const [sortOrder, setSortOrder] = useState(config.sorting?.defaultSortOrder || null);
  const [expandedRows, setExpandedRows] = useState(null);
  const [editingRows, setEditingRows] = useState({});

  console.log(data)
  
  const dt = useRef(null);

  // Ensure data is always an array
  const safeData = useMemo(() => {
    if (!data) return [];
    if (Array.isArray(data)) return data;
    if (typeof data === 'object' && data.data && Array.isArray(data.data)) return data.data;
    console.warn('DynamicDataTable: data is not an array, converting to empty array', data);
    return [];
  }, [data]);

  // Debug logging
  console.log('=== DynamicDataTable DEBUG ===');
  console.log('data (original):', data);
  console.log('data type:', typeof data);
  console.log('data is array:', Array.isArray(data));
  console.log('safeData:', safeData);
  console.log('safeData length:', safeData?.length);
  console.log('columns:', columns);
  console.log('columns length:', columns?.length);
  console.log('config:', config);
  console.log('debug prop:', debug);

  const processedColumns = useMemo(() => {
    return columns.map(col => ({
      field: col.field,
      header: col.header || col.field,
      sortable: col.sortable !== false && config.sorting?.enabled !== false,
      filter: col.filter !== false && config.filtering?.enabled !== false,
      filterMatchMode: col.filterMatchMode || FilterMatchMode.CONTAINS,
      filterElement: col.filterElement,
      body: col.body || col.template,
      editor: col.editor,
      exportable: col.exportable !== false,
      selectionMode: col.selectionMode,
      frozen: col.frozen || false,
      alignFrozen: col.alignFrozen || 'left',
      style: col.style || {},
      headerStyle: col.headerStyle || {},
      bodyStyle: col.bodyStyle || {},
      className: col.className || '',
      headerClassName: col.headerClassName || '',
      bodyClassName: col.bodyClassName || '',
      ...col
    }));
  }, [columns, config]);

  const handleSelectionChange = useCallback((e) => {
    setSelectedRows(e.value);
    
    if (debug) {
      console.log('DynamicDataTable selection change:', e.value);
    }

    if (onSelectionChange) {
      onSelectionChange(e.value, e);
    }

    if (onEvent) {
      onEvent('selectionChange', { selection: e.value, event: e });
    }
  }, [onSelectionChange, onEvent, debug]);

  const handleRowSelect = useCallback((e) => {
    if (onRowSelect) {
      onRowSelect(e.data, e);
    }

    if (onEvent) {
      onEvent('rowSelect', { row: e.data, event: e });
    }
  }, [onRowSelect, onEvent]);

  const handleRowUnselect = useCallback((e) => {
    if (onRowUnselect) {
      onRowUnselect(e.data, e);
    }

    if (onEvent) {
      onEvent('rowUnselect', { row: e.data, event: e });
    }
  }, [onRowUnselect, onEvent]);

  const handleRowClick = useCallback((e) => {
    if (onRowClick) {
      onRowClick(e.data, e);
    }

    if (onEvent) {
      onEvent('rowClick', { row: e.data, event: e });
    }
  }, [onRowClick, onEvent]);

  const handleRowDoubleClick = useCallback((e) => {
    if (onRowDoubleClick) {
      onRowDoubleClick(e.data, e);
    }

    if (onEvent) {
      onEvent('rowDoubleClick', { row: e.data, event: e });
    }
  }, [onRowDoubleClick, onEvent]);

  const handleSort = useCallback((e) => {
    setSortField(e.sortField);
    setSortOrder(e.sortOrder);

    if (onSort) {
      onSort(e.sortField, e.sortOrder, e);
    }

    if (onEvent) {
      onEvent('sort', { field: e.sortField, order: e.sortOrder, event: e });
    }
  }, [onSort, onEvent]);

  const handleFilter = useCallback((e) => {
    setFilters(e.filters);

    if (onFilter) {
      onFilter(e.filters, e);
    }

    if (onEvent) {
      onEvent('filter', { filters: e.filters, event: e });
    }
  }, [onFilter, onEvent]);

  const handlePage = useCallback((e) => {
    setFirst(e.first);
    setRows(e.rows);

    if (onPage) {
      onPage(e.first, e.rows, e);
    }

    if (onEvent) {
      onEvent('page', { first: e.first, rows: e.rows, event: e });
    }
  }, [onPage, onEvent]);

  const handleGlobalFilter = useCallback((e) => {
    const value = e.target.value;
    setGlobalFilterValue(value);

    const newFilters = { ...filters };
    newFilters['global'] = { value, matchMode: FilterMatchMode.CONTAINS };
    setFilters(newFilters);

    if (onFilter) {
      onFilter(newFilters, e);
    }
  }, [filters, onFilter]);

  const clearFilters = useCallback(() => {
    setFilters({});
    setGlobalFilterValue('');
    
    if (onFilter) {
      onFilter({});
    }
  }, [onFilter]);

  const exportCSV = useCallback(() => {
    dt.current.exportCSV();
    
    if (onEvent) {
      onEvent('export', { format: 'csv', data });
    }
  }, [data, onEvent]);

  const renderColumnFilter = useCallback((column) => {
    const field = column.field;
    
    if (column.filterElement) {
      return column.filterElement;
    }

    const sampleValue = data?.length > 0 ? data[0][field] : null;
    
    if (typeof sampleValue === 'boolean') {
      return (
        <TriStateCheckbox
          value={filters[field]?.value}
          onChange={(e) => {
            const newFilters = { ...filters };
            newFilters[field] = { value: e.value, matchMode: FilterMatchMode.EQUALS };
            setFilters(newFilters);
          }}
        />
      );
    }

    if (sampleValue instanceof Date || (typeof sampleValue === 'string' && !isNaN(Date.parse(sampleValue)))) {
      return (
        <Calendar
          value={filters[field]?.value}
          onChange={(e) => {
            const newFilters = { ...filters };
            newFilters[field] = { value: e.value, matchMode: FilterMatchMode.DATE_IS };
            setFilters(newFilters);
          }}
          dateFormat="mm/dd/yy"
          placeholder="mm/dd/yyyy"
        />
      );
    }

    return (
      <InputText
        value={filters[field]?.value || ''}
        onChange={(e) => {
          const newFilters = { ...filters };
          newFilters[field] = { value: e.target.value, matchMode: FilterMatchMode.CONTAINS };
          setFilters(newFilters);
        }}
        placeholder={`Search ${column.header}`}
      />
    );
  }, [data, filters]);

  const renderActionButtons = useCallback((rowData, options) => {
  const actions = config.actions || [];
  
  return (
    <div className="table-actions flex" style={{ display: 'flex', justifyContent: 'center', gap: '0.5rem' }}>
      {actions.map((action, index) => {
        // Default classes from the original implementation
        const defaultClass = `p-button-${action.severity || 'secondary'} p-button-sm`;
        // Use custom className if provided, otherwise use default
        const buttonClass = action.className 
          ? `${action.className} p-button-sm` 
          : defaultClass;

        return (
          <Button
            key={index}
            icon={action.icon}
            label={action.label}
            className={buttonClass}
            onClick={() => {
              if (action.onClick) {
                action.onClick(rowData, options);
              }
              if (onEvent) {
                onEvent('action', { action: action.name, row: rowData });
              }
            }}
            disabled={action.disabled && action.disabled(rowData)}
            tooltip={action.tooltip}
          />
        );
      })}
    </div>
  );
}, [config.actions, onEvent]);

  const tableProps = {
    ref: dt,
    value: safeData,
    selection: selectedRows,
    onSelectionChange: handleSelectionChange,
    onRowSelect: handleRowSelect,
    onRowUnselect: handleRowUnselect,
    onRowClick: handleRowClick,
    onRowDoubleClick: handleRowDoubleClick,
    onSort: handleSort,
    onFilter: handleFilter,
    onPage: handlePage,
    filters,
    globalFilterFields: config.globalFilter?.fields,
    sortField,
    sortOrder,
    first,
    rows,
    totalRecords: config.pagination?.totalRecords || safeData?.length,
    lazy: config.lazy || false,
    paginator: config.pagination?.enabled !== false,
    paginatorTemplate: config.pagination?.template || 'FirstPageLink PrevPageLink PageLinks NextPageLink LastPageLink CurrentPageReport RowsPerPageDropdown',
    rowsPerPageOptions: config.pagination?.rowsPerPageOptions || [5, 10, 25, 50],
    currentPageReportTemplate: config.pagination?.currentPageReportTemplate || 'Showing {first} to {last} of {totalRecords} entries',
    selectionMode: config.selection?.mode,
    metaKeySelection: config.selection?.metaKeySelection !== false,
    dragSelection: config.selection?.dragSelection || false,
    rowHover: config.rowHover !== false,
    showGridlines: config.showGridlines || false,
    stripedRows: config.stripedRows || false,
    size: config.size || 'normal',
    responsiveLayout: config.responsiveLayout || 'scroll',
    breakpoint: config.breakpoint || '960px',
    scrollable: config.scrollable || false,
    scrollHeight: config.scrollHeight,
    frozenWidth: config.frozenWidth,
    resizableColumns: config.resizableColumns || false,
    columnResizeMode: config.columnResizeMode || 'fit',
    reorderableColumns: config.reorderableColumns || false,
    ...(config.rowExpansionTemplate ? {
      expandedRows,
      onRowToggle: (e) => setExpandedRows(e.data),
      rowExpansionTemplate: config.rowExpansionTemplate
    } : {}),
    ...(config.editMode && config.editMode !== 'cell' ? {
      editMode: config.editMode,
      editingRows,
      onRowEditSave: (e) => {
        if (onRowEditComplete) {
          onRowEditComplete(e.newData, e);
        }
      },
      onRowEditCancel: (e) => {
        if (onRowEditCancel) {
          onRowEditCancel(e);
        }
      }
    } : {}),
    loading,
    loadingIcon: config.loadingIcon || 'pi pi-spinner',
    emptyMessage: config.emptyMessage || 'No data found',
    className: `dynamic-datatable ${className}`,
    paginatorClassName: 'glass-paginator',
    style,
    'data-component-type': 'datatable',
    ...props
  };

  Object.keys(tableProps).forEach(key => {
    if (tableProps[key] === undefined) {
      delete tableProps[key];
    }
  });

  return (
    <div className="dynamic-datatable-wrapper">
      {debug && (
        <div className="dynamic-datatable__debug">
          <small>
            Rows: {safeData?.length} |
            Columns: {processedColumns.length} |
            Selected: {Array.isArray(selectedRows) ? selectedRows.length : (selectedRows ? 1 : 0)} |
            Filters: {Object.keys(filters).length}
          </small>
        </div>
      )}

      {(config.globalFilter?.enabled || config.export?.enabled) && (
        <div className="datatable-header">
          {config.globalFilter?.enabled && (
            <div className="datatable-global-filter">
              <span className="p-input-icon-left">
                <i className="pi pi-search" />
                <InputText
                  value={globalFilterValue}
                  onChange={handleGlobalFilter}
                  placeholder={config.globalFilter?.placeholder || 'Global Search'}
                />
              </span>
            </div>
          )}
          
          <div className="datatable-actions">
            {config.filtering?.enabled && (
              <Button
                type="button"
                icon="pi pi-filter-slash"
                label="Clear"
                className="p-button-outlined"
                onClick={clearFilters}
              />
            )}
            
            {config.export?.enabled && (
              <Button
                type="button"
                icon="pi pi-file-excel"
                label="Export"
                className="p-button-success"
                onClick={exportCSV}
              />
            )}
          </div>
        </div>
      )}

      <DataTable {...tableProps} className='glass-table'>
        {processedColumns.map((col, index) => (
          <Column
            key={col.field || index}
            field={col.field}
            header={col.header}
            sortable={col.sortable}
            filter={col.filter}
            filterMatchMode={col.filterMatchMode}
            filterElement={col.filter ? renderColumnFilter(col) : null}
            body={col.body}
            editor={col.editor}
            frozen={col.frozen}
            alignFrozen={col.alignFrozen}
            style={col.style}
            headerStyle={col.headerStyle}
            bodyStyle={col.bodyStyle}
            className={col.className}
            headerClassName={col.headerClassName}
            bodyClassName={col.bodyClassName}
            exportable={col.exportable}
            selectionMode={col.selectionMode}
            {...col.props}
          />
        ))}
        
        {config.actions && config.actions.length > 0 && (
          <Column
            header="Actions"
            body={renderActionButtons}
            exportable={false}
            style={{ minWidth: '8rem' }}
            frozen={config.actions.frozen}
            alignFrozen="right"
          />
        )}
      </DataTable>
    </div>
  );
};

DynamicDataTable.propTypes = {
  data: PropTypes.array,
  columns: PropTypes.array,
  config: PropTypes.object,
  onSelectionChange: PropTypes.func,
  onRowSelect: PropTypes.func,
  onRowUnselect: PropTypes.func,
  onRowClick: PropTypes.func,
  onRowDoubleClick: PropTypes.func,
  onSort: PropTypes.func,
  onFilter: PropTypes.func,
  onPage: PropTypes.func,
  onRowReorder: PropTypes.func,
  onRowExpand: PropTypes.func,
  onRowCollapse: PropTypes.func,
  onCellEditComplete: PropTypes.func,
  onRowEditComplete: PropTypes.func,
  onRowEditCancel: PropTypes.func,
  onEvent: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object,
  loading: PropTypes.bool,
  debug: PropTypes.bool
};

export default DynamicDataTable;
