import React from 'react';
import PropTypes from 'prop-types';
import { Tag } from 'primereact/tag';

const DynamicTag = ({
  value,
  severity = 'info',
  rounded = false,
  icon = null,
  className = '',
  style = {},
  id,
  onEvent,
  debug = false,
  ...props
}) => {
  const tagProps = {
    value,
    severity,
    rounded,
    icon,
    className: `dynamic-tag ${className}`,
    style,
    id,
    ...props
  };

  if (debug) {
    console.log('DynamicTag: Rendering with props', tagProps);
  }

  return (
    <div className="dynamic-tag-wrapper">
      {debug && (
        <div className="dynamic-tag__debug">
          <small>
            Value: {value} | 
            Severity: {severity} | 
            Rounded: {rounded ? 'Yes' : 'No'}
          </small>
        </div>
      )}
      <Tag {...tagProps} />
    </div>
  );
};

DynamicTag.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  severity: PropTypes.oneOf(['success', 'info', 'warning', 'danger', 'secondary', 'contrast']),
  rounded: PropTypes.bool,
  icon: PropTypes.string,
  className: PropTypes.string,
  style: PropTypes.object,
  id: PropTypes.string,
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicTag;

