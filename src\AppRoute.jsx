import React, { Suspense } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ToastContainer } from "react-toastify";
import LeadManagementRootDynamic from './lead-management/LeadManagementRootDynamic';
import LeadDetailViewDynamic from './lead-management/LeadDetailViewDynamic';
import ProspectListViewDynamic from './crm/ProspectListViewDynamic';
import ProspectDetailViewDynamic from './crm/ProspectDetailViewDynamic';
import CustomerListViewDynamic from './crm/CustomerListViewDynamic';
import CustomDataTablePage from './pages/CustomDataTablePage';
import ServiceOrdersPage from './pages/ServiceOrdersPage';
import ServiceOrdersView from './views/ServiceOrdersView';
import './lead-management/LeadManagement.css';
import './crm/CRMViews.css';
import DemoApp from './components/framework/demo/DemoApp';
import LandingPage from '@views/auth/LandingPage';
import { protectedRoutes, publicRoutes } from './routes/routes.jsx';
import { PrimeReactProvider } from "primereact/api";
import "primereact/resources/themes/lara-light-blue/theme.css";
import "primereact/resources/primereact.min.css";
import "primeicons/primeicons.css";
import "primeflex/primeflex.css";
import '../src/styles/glassy/glassy-ui.css';
import DashboardLayout from "@components/layout/DashboardLayout";
import { LoadingProvider, useLoading } from "@utils/loader/LoadingContext";
import Loader from "@utils/loader/Loader";
import { AuthProvider } from "@contexts/AuthContext";
import ProtectedRoute from "@components/auth/ProtectedRoute";

import { configurePublicAxios } from "@utils/loader/publicAxiosInstance.js";


const LeadManagementApp = () => {
  return (
    <Router>
      <Routes>
         {/* Protected Dashboard Routes */}
                    <Route path="/" element={<DashboardLayout />}>
                      {protectedRoutes.map(({ path, component: Component, roles }) => (
                        <Route
                          key={path}
                          path={path}
                          element={
                            <ProtectedRoute allowedRoles={roles}>
                              <Component />
                            </ProtectedRoute>
                          }
                        />
                      ))}
                      <Route path="*" element={<h1>404 Not Found</h1>} />
                    </Route>

         {publicRoutes.map(({ path, component: Component }) => (
                      <Route
                        key={path}
                        path={path}
                        element={
                          <Suspense fallback={<div>Loading...</div>}>
                            <Component />
                          </Suspense>
                        }
                      />
                    ))}

           
        {/* <Route path="/" element={<LeadManagementRootDynamic />} />
        <Route path="/lead/:leadId" element={<LeadDetailViewDynamic />} />
        <Route path="/prospects" element={<ProspectListViewDynamic />} />
        <Route path="/prospect/:id" element={<ProspectDetailViewDynamic />} />
        <Route path="/customers" element={<CustomerListViewDynamic />} />
        <Route path="/custom-datatable" element={<CustomDataTablePage />} />
        <Route path="/service-orders" element={<ServiceOrdersPage />} />
        <Route path="/service-orders-dynamic" element={<ServiceOrdersView />} />
        <Route path="/demo" element={<DemoApp/>} />
        <Route path="*" element={<Navigate to="/" replace />} /> */}

      </Routes>
    </Router>
  );
};

export default LeadManagementApp;

