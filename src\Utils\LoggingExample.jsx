import React, { useEffect, useState } from 'react';
import logger from './logger';
import logStorage from './logStorage';

// Create a component-specific logger
const componentLogger = logger.createLogger('LoggingExample');

const LoggingExample = () => {
  const [logs, setLogs] = useState([]);
  const [logLevel, setLogLevel] = useState(logger.getLogLevel());

  // Load logs when component mounts
  useEffect(() => {
    componentLogger.info('Component mounted');
    setLogs(logStorage.getLogs());
  }, []);

  // Example function to generate different types of logs
  const generateLogs = () => {
    componentLogger.debug('This is a debug message', { timestamp: Date.now() });
    componentLogger.info('This is an info message', { user: 'example' });
    componentLogger.warn('This is a warning message', { attention: true });
    
    try {
      // Simulate an error
      throw new Error('This is a simulated error');
    } catch (error) {
      componentLogger.error('An error occurred', error);
    }

    // Refresh logs
    setLogs(logStorage.getLogs());
  };

  // Change log level
  const changeLogLevel = (level) => {
    logger.setLogLevel(level);
    setLogLevel(level);
    componentLogger.info(`Log level changed to ${level}`);
  };

  // Download logs
  const handleDownloadLogs = () => {
    logStorage.downloadLogs();
  };

  // Clear logs
  const handleClearLogs = () => {
    logStorage.clearLogs();
    setLogs([]);
    componentLogger.info('Logs cleared');
  };

  return (
    <div className="p-4">
      <h2 className="text-xl font-bold mb-4">Logging Example</h2>
      
      <div className="mb-4">
        <h3 className="text-lg font-semibold mb-2">Controls</h3>
        <div className="flex gap-2 mb-2">
          <button 
            onClick={generateLogs}
            className="p-button p-component"
          >
            Generate Logs
          </button>
          <button 
            onClick={handleDownloadLogs}
            className="p-button p-component p-button-secondary"
          >
            Download Logs
          </button>
          <button 
            onClick={handleClearLogs}
            className="p-button p-component p-button-danger"
          >
            Clear Logs
          </button>
        </div>
        
        <div className="mb-2">
          <span className="mr-2">Log Level:</span>
          <select 
            value={logLevel} 
            onChange={(e) => changeLogLevel(e.target.value)}
            className="p-inputtext p-component"
          >
            <option value="DEBUG">DEBUG</option>
            <option value="INFO">INFO</option>
            <option value="WARN">WARN</option>
            <option value="ERROR">ERROR</option>
            <option value="NONE">NONE</option>
          </select>
        </div>
      </div>
      
      <div>
        <h3 className="text-lg font-semibold mb-2">Log Entries ({logs.length})</h3>
        <div className="border rounded p-2 bg-gray-50 h-64 overflow-auto">
          {logs.length === 0 ? (
            <p className="text-gray-500">No logs yet. Click "Generate Logs" to create some.</p>
          ) : (
            logs.map((log, index) => (
              <div 
                key={index} 
                className={`mb-1 p-1 rounded ${
                  log.level === 'ERROR' ? 'bg-red-100' : 
                  log.level === 'WARN' ? 'bg-yellow-100' : 
                  log.level === 'INFO' ? 'bg-blue-100' : 
                  'bg-gray-100'
                }`}
              >
                <div className="text-xs text-gray-500">{log.formattedDateTime}</div>
                <div className="flex items-center">
                  <span className={`inline-block w-16 font-semibold ${
                    log.level === 'ERROR' ? 'text-red-700' : 
                    log.level === 'WARN' ? 'text-yellow-700' : 
                    log.level === 'INFO' ? 'text-blue-700' : 
                    'text-gray-700'
                  }`}>
                    {log.level}
                  </span>
                  <span>{log.message}</span>
                </div>
                {log.data && (
                  <pre className="text-xs bg-white p-1 mt-1 rounded overflow-auto">
                    {JSON.stringify(log.data, null, 2)}
                  </pre>
                )}
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default LoggingExample;
