import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Card } from 'primereact/card';
import { Tag } from 'primereact/tag';
import CRMNavigation from './CRMNavigation';

const ProspectListView = () => {
  const navigate = useNavigate();
  const [globalFilter, setGlobalFilter] = useState('');
  const [selectedProspects, setSelectedProspects] = useState([]);

  const prospects = [
    {
      id: 'ASMK-000012',
      account: 'Emirates Airlines',
      name: 'Emirates Airlines',
      email: '<EMAIL>',
      phone: '+971 4 214 4444',
      mobileNumber: '+971 4 214 4444',
      website: 'https://www.emirates.com',
      tradeLicense: '************',
      language: 'English',
      tags: ['COP28', 'Event2025'],
      vat: 'Local 18%',
      vatNo: '**********',
      customerGroup: 'Active',
      openingTime: '',
      closingTime: '',
      businessType: '',
      salesperson: 'Sarah Johnson',
      lineOfBusiness: 'Aviation Services',
      segment: 'Commercial Aviation',
      subSegment: 'Fuel Supply',
      iata: '************',
      icao: '************',
      iataNo: '01-123456',
      blacklistProspect: 'No',
      reason: '',
      status: 'Qualification',
      createdDate: '2024-02-01'
    },
    {
      id: 'ASMK-000013',
      account: 'Qatar Airways',
      name: 'Qatar Airways',
      email: '<EMAIL>',
      phone: '+974 4023 0000',
      mobileNumber: '+974 4023 0000',
      website: 'https://www.qatarairways.com',
      tradeLicense: '************',
      language: 'English',
      tags: ['Premium', 'VIP'],
      vat: 'Local 18%',
      vatNo: '**********',
      customerGroup: 'Active',
      openingTime: '',
      closingTime: '',
      businessType: '',
      salesperson: 'John Smith',
      lineOfBusiness: 'Aviation Services',
      segment: 'Commercial Aviation',
      subSegment: 'Fuel Supply',
      iata: '************',
      icao: '************',
      iataNo: '02-234567',
      blacklistProspect: 'No',
      reason: '',
      status: 'Proposal',
      createdDate: '2024-02-05'
    },
    {
      id: 'ASMK-000014',
      account: 'Etihad Airways',
      name: 'Etihad Airways',
      email: '<EMAIL>',
      phone: '+971 2 599 0000',
      mobileNumber: '+971 2 599 0000',
      website: 'https://www.etihad.com',
      tradeLicense: '************',
      language: 'English',
      tags: ['Partner'],
      vat: 'Local 18%',
      vatNo: '**********',
      customerGroup: 'Active',
      openingTime: '',
      closingTime: '',
      businessType: '',
      salesperson: 'Sarah Johnson',
      lineOfBusiness: 'Aviation Services',
      segment: 'Commercial Aviation',
      subSegment: 'Fuel Supply',
      iata: '************',
      icao: '************',
      iataNo: '03-345678',
      blacklistProspect: 'No',
      reason: '',
      status: 'Negotiation',
      createdDate: '2024-02-10'
    }
  ];

  const handleRowClick = (rowData) => {
    navigate(`/prospect/${rowData.id}`);
  };

  const accountTemplate = (rowData) => {
    return (
      <div className="account-cell">
        <strong>{rowData.account}</strong>
        <div className="text-sm text-gray-600">{rowData.id}</div>
      </div>
    );
  };

  const contactTemplate = (rowData) => {
    return (
      <div>
        <div>{rowData.name}</div>
        <div className="text-sm text-gray-600">{rowData.email}</div>
      </div>
    );
  };

  const statusTemplate = (rowData) => {
    const severityMap = {
      'Qualification': 'warning',
      'Proposal': 'success',
      'Negotiation': 'danger'
    };
    return <Tag value={rowData.status} severity={severityMap[rowData.status]} />;
  };

  const tagsTemplate = (rowData) => {
    return (
      <div className="tags-cell">
        {rowData.tags.map((tag, index) => (
          <Tag key={index} value={tag} className="mr-1" />
        ))}
      </div>
    );
  };

  const actionTemplate = (rowData) => {
    return (
      <div className="action-buttons">
        <Button
          icon="pi pi-eye"
          className="p-button-rounded p-button-text p-button-sm"
          onClick={() => handleRowClick(rowData)}
          tooltip="View Details"
        />
        <Button
          icon="pi pi-pencil"
          className="p-button-rounded p-button-text p-button-sm"
          tooltip="Edit"
        />
        <Button
          label="Create Customer"
          className="p-button-sm p-button-success"
          onClick={() => navigate(`/prospect/${rowData.id}/create-customer`)}
        />
      </div>
    );
  };

  const header = (
    <div className="table-header">
      <div className="header-left">
        <h2>Prospects</h2>
        <span className="prospect-count">{prospects.length} Prospects</span>
      </div>
      <div className="header-right">
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText
            type="search"
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            placeholder="Search prospects..."
          />
        </span>
        <Button
          label="New Prospect"
          icon="pi pi-plus"
          className="p-button-success ml-2"
          onClick={() => navigate('/prospect/new')}
        />
      </div>
    </div>
  );

  const statsCards = (
    <div className="stats-grid">
      <Card className="stat-card">
        <div className="stat-content">
          <div className="stat-icon" style={{ backgroundColor: '#E3F2FD' }}>
            <i className="pi pi-briefcase" style={{ color: '#2196F3' }}></i>
          </div>
          <div className="stat-details">
            <div className="stat-value">{prospects.length}</div>
            <div className="stat-label">Total Prospects</div>
          </div>
        </div>
      </Card>

      <Card className="stat-card">
        <div className="stat-content">
          <div className="stat-icon" style={{ backgroundColor: '#FFF3E0' }}>
            <i className="pi pi-chart-line" style={{ color: '#FF9800' }}></i>
          </div>
          <div className="stat-details">
            <div className="stat-value">
              {prospects.filter(p => p.status === 'Qualification').length}
            </div>
            <div className="stat-label">In Qualification</div>
          </div>
        </div>
      </Card>

      <Card className="stat-card">
        <div className="stat-content">
          <div className="stat-icon" style={{ backgroundColor: '#E8F5E9' }}>
            <i className="pi pi-file" style={{ color: '#4CAF50' }}></i>
          </div>
          <div className="stat-details">
            <div className="stat-value">
              {prospects.filter(p => p.status === 'Proposal').length}
            </div>
            <div className="stat-label">Proposal Stage</div>
          </div>
        </div>
      </Card>

      <Card className="stat-card">
        <div className="stat-content">
          <div className="stat-icon" style={{ backgroundColor: '#F3E5F5' }}>
            <i className="pi pi-users" style={{ color: '#9C27B0' }}></i>
          </div>
          <div className="stat-details">
            <div className="stat-value">
              {prospects.filter(p => p.status === 'Negotiation').length}
            </div>
            <div className="stat-label">In Negotiation</div>
          </div>
        </div>
      </Card>
    </div>
  );

  return (
    <div className="prospect-list-view">
      {/* <CRMNavigation /> */}

      <div className="glass-card-container">
        <h1>CRM - Prospects</h1>
        <p>Manage your prospects and convert to customers</p>
      </div>

      {statsCards}

      <Card className="prospects-table-card">
        <DataTable
          value={prospects}
          selection={selectedProspects}
          onSelectionChange={(e) => setSelectedProspects(e.value)}
          dataKey="id"
          paginator
          rows={10}
          rowsPerPageOptions={[5, 10, 25, 50]}
          globalFilter={globalFilter}
          header={header}
          emptyMessage="No prospects found"
          className="prospects-datatable"
          onRowClick={(e) => handleRowClick(e.data)}
          rowHover
        >
          <Column selectionMode="multiple" headerStyle={{ width: '3rem' }} />
          <Column field="account" header="Account" body={accountTemplate} sortable />
          <Column field="name" header="Name" body={contactTemplate} sortable />
          <Column field="phone" header="Phone" sortable />
          <Column field="status" header="Status" body={statusTemplate} sortable />
          <Column field="salesperson" header="Salesperson" sortable />
          <Column field="tags" header="Tags" body={tagsTemplate} />
          <Column field="createdDate" header="Created" sortable />
          <Column body={actionTemplate} headerStyle={{ width: '15rem' }} />
        </DataTable>
      </Card>
    </div>
  );
};

export default ProspectListView;

