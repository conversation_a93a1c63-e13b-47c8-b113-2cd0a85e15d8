import securityService from '../securityService.js';
import SecurityMonitor from '../SecurityMonitor.js';
import SecurityHeaders from '../SecurityHeaders.js';

// Mock dependencies
jest.mock('../../logging/logger.js', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    security: jest.fn(),
    debug: jest.fn()
  }
}));

jest.mock('dompurify', () => ({
  sanitize: jest.fn((html) => html.replace(/<script.*?<\/script>/gi, '')),
  addHook: jest.fn()
}));

jest.mock('crypto-js', () => ({
  AES: {
    encrypt: jest.fn(() => ({ toString: () => 'encrypted_data' })),
    decrypt: jest.fn(() => ({ toString: () => '{"test":"data"}' }))
  },
  SHA256: jest.fn(() => ({ toString: () => 'hashed_token' })),
  enc: { Utf8: 'utf8' }
}));

// Mock DOM APIs
Object.defineProperty(window, 'location', {
  writable: true,
  value: {
    href: 'https://example.com',
    origin: 'https://example.com',
    protocol: 'https:',
    hostname: 'example.com'
  }
});

Object.defineProperty(window, 'navigator', {
  writable: true,
  value: {
    userAgent: 'Mozilla/5.0 (Test Browser)'
  }
});

global.fetch = jest.fn();

describe('Security Integration Tests', () => {
  beforeEach(() => {
    // Reset all security services
    securityService.csrfTokens.clear();
    securityService.rateLimitMap.clear();
    SecurityMonitor.threats.clear();
    SecurityMonitor.anomalies = [];
    SecurityMonitor.isMonitoring = false;
    SecurityHeaders.headers.clear();
    SecurityHeaders.violations = [];
    
    // Clear all mocks
    jest.clearAllMocks();
    
    // Set test environment
    process.env.NODE_ENV = 'test';
    process.env.REACT_APP_ENCRYPTION_KEY = 'test_key';
  });

  describe('Complete Security Workflow', () => {
    it('should handle a complete secure request workflow', async () => {
      // 1. Initialize security services
      SecurityMonitor.initMonitoring();
      SecurityHeaders.initializeHeaders();
      
      expect(SecurityMonitor.isMonitoring).toBe(true);
      expect(SecurityHeaders.headers.size).toBeGreaterThan(0);

      // 2. Generate CSRF token
      const csrfToken = securityService.generateCSRFToken('user123');
      expect(csrfToken).toBeTruthy();

      // 3. Validate user input
      const userInput = 'Safe user input for testing';
      expect(() => {
        securityService.validateInput(userInput);
      }).not.toThrow();

      // 4. Encrypt sensitive data
      const sensitiveData = { userId: 123, sessionId: 'abc123' };
      const encryptedData = securityService.encryptData(sensitiveData);
      expect(encryptedData).toBe('encrypted_data');

      // 5. Validate CSRF token
      const isValidToken = securityService.validateCSRFToken(csrfToken, 'user123');
      expect(isValidToken).toBe(true);

      // 6. Check rate limiting
      const rateLimitOk = securityService.checkRateLimit('user123', 10, 60000);
      expect(rateLimitOk).toBe(true);

      // 7. Verify no threats detected
      expect(SecurityMonitor.threats.size).toBe(0);
    });

    it('should detect and handle security threats', () => {
      // Initialize monitoring
      SecurityMonitor.initMonitoring();
      
      // Simulate XSS attempt
      expect(() => {
        securityService.validateInput('<script>alert("xss")</script>');
      }).toThrow('XSS attempt detected');

      // Simulate malicious element
      const maliciousElement = document.createElement('div');
      maliciousElement.setAttribute('onclick', 'alert("xss")');
      SecurityMonitor.scanElementForThreats(maliciousElement);

      // Simulate suspicious network request
      SecurityMonitor.analyzeNetworkRequest('https://example.com?param=<script>');

      // Verify threats were recorded
      expect(SecurityMonitor.threats.size).toBeGreaterThan(0);
    });

    it('should handle CSP violations properly', () => {
      // Initialize security headers
      SecurityHeaders.initializeHeaders();

      // Mock CSP violation event
      const violationEvent = {
        blockedURI: 'https://malicious.com/script.js',
        violatedDirective: 'script-src',
        originalPolicy: "default-src 'self'",
        disposition: 'enforce',
        documentURI: 'https://example.com',
        referrer: '',
        statusCode: 200
      };

      fetch.mockResolvedValueOnce({ ok: true });

      SecurityHeaders.handleCSPViolation(violationEvent);

      expect(SecurityHeaders.violations).toHaveLength(1);
      expect(fetch).toHaveBeenCalledWith('/api/csp-report', expect.any(Object));
    });

    it('should enforce rate limiting under attack', () => {
      const attackerId = 'attacker123';
      const maxRequests = 5;
      const windowMs = 60000;

      // Simulate rapid requests
      let blockedCount = 0;
      for (let i = 0; i < 10; i++) {
        const allowed = securityService.checkRateLimit(attackerId, maxRequests, windowMs);
        if (!allowed) {
          blockedCount++;
        }
      }

      expect(blockedCount).toBeGreaterThan(0);
    });
  });

  describe('Security Service Integration', () => {
    it('should sanitize and validate input in sequence', () => {
      const maliciousInput = '<script>alert("xss")</script><p>Safe content</p>';
      
      // First sanitize
      const sanitized = securityService.sanitizeHTML(maliciousInput);
      expect(sanitized).not.toContain('<script>');
      
      // Then validate
      expect(() => {
        securityService.validateInput(sanitized);
      }).not.toThrow();
    });

    it('should handle encryption/decryption workflow', () => {
      const originalData = { secret: 'confidential', userId: 123 };
      
      // Encrypt
      const encrypted = securityService.encryptData(originalData);
      expect(encrypted).toBe('encrypted_data');
      
      // Decrypt
      const decrypted = securityService.decryptData(encrypted);
      expect(decrypted).toEqual({ test: 'data' }); // Mocked response
    });

    it('should manage CSRF tokens lifecycle', () => {
      const sessionId = 'session123';
      
      // Generate token
      const token = securityService.generateCSRFToken(sessionId);
      expect(token).toBeTruthy();
      
      // Validate token
      expect(securityService.validateCSRFToken(token, sessionId)).toBe(true);
      
      // Reject wrong session
      expect(securityService.validateCSRFToken(token, 'wrong_session')).toBe(false);
      
      // Cleanup expired tokens
      const tokenData = securityService.csrfTokens.get(token);
      tokenData.expiry = Date.now() - 1000; // Expire it
      
      securityService.cleanupExpiredTokens();
      expect(securityService.csrfTokens.has(token)).toBe(false);
    });
  });

  describe('Security Monitor Integration', () => {
    it('should monitor and analyze security events', () => {
      SecurityMonitor.initMonitoring();
      
      // Simulate various security events
      SecurityMonitor.recordThreat('xss_attempt', { source: 'user_input' });
      SecurityMonitor.recordThreat('csrf_attempt', { source: 'form_submission' });
      SecurityMonitor.recordThreat('suspicious_request', { url: 'https://evil.com' });
      
      expect(SecurityMonitor.threats.size).toBe(3);
      
      // Check security status
      const status = SecurityMonitor.getSecurityStatus();
      expect(status.threatCount).toBe(3);
      expect(status.isMonitoring).toBe(true);
    });

    it('should trigger alerts on threat threshold', () => {
      const dispatchEventSpy = jest.spyOn(window, 'dispatchEvent').mockImplementation();
      
      SecurityMonitor.initMonitoring();
      
      // Generate multiple XSS attempts to trigger threshold
      for (let i = 0; i < 6; i++) {
        SecurityMonitor.recordThreat('xss_attempt', { attempt: i });
      }
      
      expect(dispatchEventSpy).toHaveBeenCalled();
      
      dispatchEventSpy.mockRestore();
    });

    it('should clean up old threats and anomalies', () => {
      SecurityMonitor.recordThreat('old_threat', {});
      
      // Mock old timestamp
      const threats = Array.from(SecurityMonitor.threats.values());
      threats[0].timestamp = Date.now() - (25 * 60 * 60 * 1000); // 25 hours ago
      
      SecurityMonitor.cleanupOldThreats();
      expect(SecurityMonitor.threats.size).toBe(0);
    });
  });

  describe('Security Headers Integration', () => {
    it('should configure headers based on environment', () => {
      // Test development environment
      process.env.NODE_ENV = 'development';
      SecurityHeaders.initializeHeaders();
      
      const devCSP = SecurityHeaders.headers.get('Content-Security-Policy');
      expect(devCSP).toContain('ws:');
      expect(SecurityHeaders.headers.has('Strict-Transport-Security')).toBe(false);
      
      // Test production environment
      process.env.NODE_ENV = 'production';
      SecurityHeaders.initializeHeaders();
      
      expect(SecurityHeaders.headers.has('Strict-Transport-Security')).toBe(true);
    });

    it('should validate response headers', () => {
      const { logger } = require('../../logging/logger.js');
      
      const mockResponse = {
        headers: new Map([['x-content-type-options', 'nosniff']]),
        url: 'https://example.com'
      };
      
      mockResponse.headers.has = jest.fn((header) => 
        mockResponse.headers.get(header) !== undefined
      );
      
      const isValid = SecurityHeaders.validateResponseHeaders(mockResponse);
      
      expect(isValid).toBe(false);
      expect(logger.security).toHaveBeenCalledWith('missing_security_headers', {
        url: 'https://example.com',
        missingHeaders: ['x-frame-options', 'x-xss-protection']
      });
    });
  });

  describe('Cross-Service Security Coordination', () => {
    it('should coordinate between all security services', () => {
      // Initialize all services
      SecurityMonitor.initMonitoring();
      SecurityHeaders.initializeHeaders();
      
      // Generate CSRF token
      const token = securityService.generateCSRFToken('test_session');
      
      // Simulate a complete request with security checks
      const userInput = 'Normal user input';
      securityService.validateInput(userInput);
      
      const sanitizedHTML = securityService.sanitizeHTML('<p>Safe content</p>');
      expect(sanitizedHTML).toContain('<p>Safe content</p>');
      
      // Check rate limiting
      const rateLimitOk = securityService.checkRateLimit('user123', 10, 60000);
      expect(rateLimitOk).toBe(true);
      
      // Verify all services are working
      expect(SecurityMonitor.isMonitoring).toBe(true);
      expect(SecurityHeaders.headers.size).toBeGreaterThan(0);
      expect(securityService.validateCSRFToken(token, 'test_session')).toBe(true);
    });

    it('should handle coordinated attack simulation', () => {
      const { logger } = require('../../logging/logger.js');
      
      SecurityMonitor.initMonitoring();
      
      // Simulate coordinated attack
      const attackScenarios = [
        () => securityService.validateInput('<script>alert("xss1")</script>'),
        () => securityService.validateInput('<img src=x onerror=alert("xss2")>'),
        () => SecurityMonitor.analyzeNetworkRequest('https://evil.com?param=<script>'),
        () => {
          const element = document.createElement('div');
          element.setAttribute('onclick', 'malicious()');
          SecurityMonitor.scanElementForThreats(element);
        }
      ];
      
      let attacksBlocked = 0;
      attackScenarios.forEach((attack, index) => {
        try {
          attack();
        } catch (error) {
          if (error.message.includes('XSS attempt detected')) {
            attacksBlocked++;
          }
        }
      });
      
      expect(attacksBlocked).toBeGreaterThan(0);
      expect(SecurityMonitor.threats.size).toBeGreaterThan(0);
      expect(logger.security).toHaveBeenCalled();
    });
  });
});
