import React, { useState } from 'react';
import { Button } from 'primereact/button';
import { Tag } from 'primereact/tag';
import '../../styles/glassy/glassy-ui.css';
import '../../styles/glassy/global.css';
import GlassyBlueButton from '../../components/common/GlassyBlueButton';
import GlassyWhiteButton from '../../components/common/GlassyWhiteButton';

/**
 * ActivityLogTab - Displays activity logs with status cards and timeline
 * Based on the provided design with Email, Call, and Meeting status cards
 */
const ActivityLogTab = ({ prospectId }) => {
  const [visibleActivities, setVisibleActivities] = useState(2);

  // Activity status cards data
  const statusCards = [
    {
      icon: 'pi-envelope',
      iconBg: '#dbeafe',
      iconColor: '#3b82f6',
      title: 'Email Status',
      status: 'Active',
      statusSeverity: 'success',
      details: [
        { label: 'From', value: '<EMAIL>' },
        { label: 'To', value: '<EMAIL>' },
        { label: 'Updated', value: '16/01/2025 5:40 PM' }
      ]
    },
    {
      icon: 'pi-phone',
      iconBg: '#dbeafe',
      iconColor: '#3b82f6',
      title: 'Call Status',
      status: 'Scheduled',
      statusSeverity: 'info',
      details: [
        { label: 'Duration', value: '45 minutes' },
        { label: 'Contact', value: 'John Doe' },
        { label: 'Next Call', value: '17/01/2025 11:00 AM' }
      ]
    },
    {
      icon: 'pi-calendar',
      iconBg: '#fef3c7',
      iconColor: '#f59e0b',
      title: 'Meeting Status',
      status: 'Pending',
      statusSeverity: 'warning',
      details: [
        { label: 'Type', value: 'Product Demo' },
        { label: 'Attendees', value: '5 people' },
        { label: 'Scheduled', value: '17/01/2025 11:00 AM' }
      ]
    }
  ];

  // Activity timeline data
  const activities = [
    {
      id: 1,
      type: 'email',
      icon: 'pi-envelope',
      iconBg: '#dbeafe',
      iconColor: '#3b82f6',
      title: 'Email Communication',
      status: 'Sent',
      statusSeverity: 'success',
      timestamp: '16/01/2025 5:40 PM',
      subject: 'Follow-up on Fuel Supply Proposal',
      from: '<EMAIL>',
      to: '<EMAIL>',
      updatedBy: 'John Doe',
      content: '"Thank you for your interest in our fuel supply services. We have updated our proposal based on your technical requirements. Please find the revised pricing and delivery schedule attached."',
      actions: [
        { label: 'View Details', icon: 'pi-eye' },
        { label: 'Reply', icon: 'pi-reply' },
        { label: 'Attachments (2)', icon: 'pi-paperclip' }
      ]
    },
    {
      id: 2,
      type: 'call',
      icon: 'pi-phone',
      iconBg: '#d1fae5',
      iconColor: '#10b981',
      title: 'Phone Call',
      status: 'Completed',
      statusSeverity: 'success',
      timestamp: '16/01/2025 2:30 PM',
      duration: '45 minutes',
      contact: 'John Doe - Procurement Manager',
      phone: '+98 21 4884 5000',
      calledBy: 'Sarah Johnson',
      summary: 'Discussed technical specifications for jet fuel supply. Customer confirmed quantity requirements and delivery timeline. Scheduled follow-up meeting for contract negotiation.',
      actions: [
        { label: 'Play Recording', icon: 'pi-play' },
        { label: 'Call Notes', icon: 'pi-file' },
        { label: 'Schedule Follow-up', icon: 'pi-calendar-plus' }
      ]
    }
  ];

  const handleLoadMore = () => {
    setVisibleActivities(prev => prev + 2);
  };

  const getSeverityColor = (severity) => {
    switch (severity) {
      case 'success': return 'success';
      case 'info': return 'info';
      case 'warning': return 'warning';
      case 'danger': return 'danger';
      default: return 'info';
    }
  };

  return (
    <div className="activity-log-tab" style={{ padding: '1.5rem' }}>
      {/* Header */}
      <div className="tab-header" style={{ marginBottom: '1.5rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2 className="text-900" style={{ margin: 0, fontWeight: 600, fontSize: '1.25rem' }}>Activity Logs</h2>
        <div style={{ display: 'flex', gap: '0.75rem' }}>
          <GlassyBlueButton 
            label="Add Activity" 
            icon="pi pi-plus"
            className="p-button-sm" 
            severity="info"
            style={{ fontWeight: 500 }}
          />
          <GlassyWhiteButton 
            label="Filter" 
            icon="pi pi-filter"
            className="p-button-sm" 
            severity="secondary"
            style={{ fontWeight: 500 }}
          />
        </div>
      </div>

      {/* Status Cards Row */}
      <div className="status-cards-row" style={{ 
        display: 'grid', 
        gridTemplateColumns: 'repeat(auto-fit, minmax(280px, 1fr))', 
        gap: '1rem',
        marginBottom: '2rem'
      }}>
        {statusCards.map((card, index) => (
          <div 
            key={index}
            className="glass-card-global"
            style={{
              padding: '1.25rem',
              display: 'flex',
              flexDirection: 'column',
              gap: '1rem'
            }}
          >
            {/* Card Header */}
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.75rem' }}>
                <div 
                  style={{
                    width: 36,
                    height: 36,
                    borderRadius: '8px',
                    backgroundColor: card.iconBg,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <i className={`pi ${card.icon}`} style={{ fontSize: '1rem', color: card.iconColor }} />
                </div>
                <span style={{ fontWeight: 600, color: '#374151' }}>{card.title}</span>
              </div>
              <Tag value={card.status} severity={getSeverityColor(card.statusSeverity)} />
            </div>

            {/* Card Details */}
            <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem' }}>
              {card.details.map((detail, idx) => (
                <div key={idx} style={{ display: 'flex', justifyContent: 'space-between', fontSize: '0.875rem' }}>
                  <span style={{ color: '#6b7280' }}>{detail.label}:</span>
                  <span style={{ color: '#374151', fontWeight: 500 }}>{detail.value}</span>
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Activity Timeline */}
      <div className="activity-timeline">
        <h3 className="text-900" style={{ marginBottom: '1.5rem', fontWeight: 600, fontSize: '1.125rem' }}>Activity Timeline</h3>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '1.5rem' }}>
          {activities.slice(0, visibleActivities).map((activity) => (
            <div 
              key={activity.id}
              className="glass-card-global"
              style={{
                padding: '1.5rem',
                backgroundColor: activity.type === 'email' ? '#eff6ff' : '#f0fdf4'
              }}
            >
              {/* Activity Header */}
              <div style={{ display: 'flex', alignItems: 'flex-start', gap: '1rem', marginBottom: '1rem' }}>
                <div 
                  style={{
                    width: 48,
                    height: 48,
                    borderRadius: '50%',
                    backgroundColor: activity.iconBg,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    flexShrink: 0
                  }}
                >
                  <i className={`pi ${activity.icon}`} style={{ fontSize: '1.25rem', color: activity.iconColor }} />
                </div>

                <div style={{ flex: 1 }}>
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', marginBottom: '0.5rem' }}>
                    <div>
                      <h4 style={{ margin: 0, marginBottom: '0.25rem', color: '#111827', fontWeight: 600 }}>
                        {activity.title}
                      </h4>
                      <Tag value={activity.status} severity={getSeverityColor(activity.statusSeverity)} />
                    </div>
                    <span style={{ fontSize: '0.875rem', color: '#6b7280' }}>{activity.timestamp}</span>
                  </div>

                  {/* Activity Details */}
                  {activity.type === 'email' && (
                    <div style={{ marginTop: '1rem' }}>
                      <div style={{ marginBottom: '0.75rem' }}>
                        <strong style={{ color: '#3b82f6' }}>Subject:</strong>{' '}
                        <span style={{ color: '#374151' }}>{activity.subject}</span>
                      </div>
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '0.25rem', fontSize: '0.875rem', marginBottom: '0.75rem' }}>
                        <div>
                          <strong style={{ color: '#3b82f6' }}>From:</strong>{' '}
                          <span style={{ color: '#374151' }}>{activity.from}</span>
                        </div>
                        <div>
                          <strong style={{ color: '#3b82f6' }}>To:</strong>{' '}
                          <span style={{ color: '#374151' }}>{activity.to}</span>
                        </div>
                        <div>
                          <strong style={{ color: '#3b82f6' }}>Updated by:</strong>{' '}
                          <span style={{ color: '#374151' }}>{activity.updatedBy}</span>
                        </div>
                      </div>
                      <div style={{ 
                        padding: '1rem', 
                        backgroundColor: 'white', 
                        borderRadius: '8px',
                        borderLeft: '3px solid #3b82f6',
                        marginBottom: '1rem'
                      }}>
                        <strong style={{ color: '#3b82f6' }}>Email content:</strong>{' '}
                        <span style={{ color: '#4b5563' }}>{activity.content}</span>
                      </div>
                    </div>
                  )}

                  {activity.type === 'call' && (
                    <div style={{ marginTop: '1rem',color: 'black' }}>
                      <div style={{ display: 'flex', flexDirection: 'column', gap: '0.5rem', fontSize: '0.875rem', marginBottom: '0.75rem' }}>
                        <div>
                          <strong>Duration:</strong> {activity.duration}
                        </div>
                        <div>
                          <strong>Contact:</strong> {activity.contact}
                        </div>
                        <div>
                          <strong>Phone:</strong> {activity.phone}
                        </div>
                        <div>
                          <strong>Called by:</strong> {activity.calledBy}
                        </div>
                      </div>
                      <div style={{ 
                        padding: '1rem', 
                        backgroundColor: 'white', 
                        borderRadius: '8px',
                        borderLeft: '3px solid #10b981',
                        marginBottom: '1rem'
                      }}>
                        <strong style={{ color: '#10b981' }}>Call summary:</strong>{' '}
                        <span style={{ color: '#4b5563' }}>{activity.summary}</span>
                      </div>
                    </div>
                  )}

                  {/* Action Buttons */}
                  <div style={{ display: 'flex', gap: '0.75rem', flexWrap: 'wrap' }}>
                    {activity.actions.map((action, idx) => (
                      <Button
                        key={idx}
                        label={action.label}
                        icon={action.icon}
                        className="p-button-sm p-button-text"
                        style={{ color: activity.type === 'email' ? '#3b82f6' : '#10b981' }}
                      />
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        {visibleActivities < activities.length && (
          <div style={{ textAlign: 'center', marginTop: '1.5rem' }}>
            <Button
              label="Load More Activities"
              icon="pi pi-angle-down"
              className="p-button-sm p-button-text"
              onClick={handleLoadMore}
              style={{ color: '#3b82f6' }}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default ActivityLogTab;