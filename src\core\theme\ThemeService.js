class ThemeService {
  constructor() {
    this.themes = ['light', 'dark'];
    this.currentTheme = this.getSavedTheme() || this.getSystemTheme();
    this.applyTheme(this.currentTheme);
  }

  getSystemTheme() {
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  }

  getSavedTheme() {
    return localStorage.getItem('theme');
  }

  applyTheme(theme) {
    document.documentElement.setAttribute('data-theme', theme);
    localStorage.setItem('theme', theme);
    this.currentTheme = theme;
    
    window.dispatchEvent(new CustomEvent('themechange', { detail: theme }));
  }

  toggleTheme() {
    const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
    this.applyTheme(newTheme);
  }

  getCSSVariable(name) {
    return getComputedStyle(document.documentElement).getPropertyValue(name);
  }

  setCSSVariable(name, value) {
    document.documentElement.style.setProperty(name, value);
  }

  getColor(variableName) {
    return this.getCSSVariable(`--color-${variableName}`);
  }
}

export default new ThemeService();