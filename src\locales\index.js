// Locale messages index
import en from './en.json';
import es from './es.json';
import fr from './fr.json';
import de from './de.json';
import it from './it.json';
import pt from './pt.json';
import ru from './ru.json';
import zh from './zh.json';
import ja from './ja.json';
import ar from './ar.json';
import ko from './ko.json';
import hi from './hi.json';

const messages = {
  en,
  es,
  fr,
  de,
  it,
  pt,
  ru,
  zh,
  ja,
  ar,
  ko,
  hi
};

export const supportedLocales = ['en', 'es', 'fr', 'de', 'it', 'pt', 'ru', 'zh', 'ja', 'ar', 'ko', 'hi'];

export default messages;
