import { logger } from '../logging/logger.js';
import securityService from './securityService.js';

class SecurityMonitor {
  constructor() {
    this.isMonitoring = false;
    this.threats = new Map();
    this.anomalies = [];
    this.securityMetrics = {
      xssAttempts: 0,
      csrfAttempts: 0,
      rateLimitViolations: 0,
      suspiciousRequests: 0,
      malformedRequests: 0
    };
    
    this.thresholds = {
      maxXSSAttempts: 5,
      maxCSRFAttempts: 3,
      maxRateLimitViolations: 10,
      maxSuspiciousRequests: 15,
      timeWindow: 300000 
    };

    this.observers = [];
    this.initializeEventListeners();
  }

  initMonitoring() {
    if (this.isMonitoring) {
      return;
    }

    this.isMonitoring = true;
    logger.info('Security monitoring initialized');

    this.startPeriodicChecks();
    
    this.startDOMMonitoring();
    
    this.startNetworkMonitoring();
    
    this.startConsoleMonitoring();
    
    this.setupCSPReporting();
  }

  stopMonitoring() {
    this.isMonitoring = false;
    logger.info('Security monitoring stopped');
  }

  initializeEventListeners() {
    if (typeof window !== 'undefined') {
      window.addEventListener('securityEvent', (event) => {
        this.handleSecurityEvent(event.detail);
      });

      window.addEventListener('error', (event) => {
        this.analyzeError(event);
      });

      window.addEventListener('unhandledrejection', (event) => {
        this.analyzePromiseRejection(event);
      });
    }
  }

  startPeriodicChecks() {
    setInterval(() => {
      if (!this.isMonitoring) return;
      
      this.checkSecurityMetrics();
      this.cleanupOldThreats();
      this.analyzeAnomalies();
    }, 60000); 
  }

  startDOMMonitoring() {
    if (typeof window === 'undefined' || !window.MutationObserver) {
      return;
    }

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.scanElementForThreats(node);
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ['onclick', 'onload', 'onerror', 'src', 'href']
    });

    this.observers.push(observer);
  }

  startNetworkMonitoring() {
    if (typeof window === 'undefined') return;

    const originalFetch = window.fetch;
    window.fetch = async (...args) => {
      const [url, options] = args;
      
      this.analyzeNetworkRequest(url, options);
      
      try {
        const response = await originalFetch(...args);
        
        securityService.validateSecureHeaders(response);
        
        return response;
      } catch (error) {
        console.error(url,error)
        // this.handleNetworkError(url, error);
        throw error;
      }
    };

    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
      this._securityMonitor_url = url;
      this._securityMonitor_method = method;
      return originalXHROpen.call(this, method, url, ...args);
    };

    const originalXHRSend = XMLHttpRequest.prototype.send;
    XMLHttpRequest.prototype.send = function(data) {
      if (this._securityMonitor_url) {
        SecurityMonitor.instance.analyzeNetworkRequest(this._securityMonitor_url, {
          method: this._securityMonitor_method,
          body: data
        });
      }
      return originalXHRSend.call(this, data);
    };
  }

  startConsoleMonitoring() {
    if (typeof window === 'undefined') return;

    const originalConsoleError = console.error;
    console.error = (...args) => {
      this.analyzeConsoleOutput('error', args);
      return originalConsoleError.apply(console, args);
    };

    const originalConsoleWarn = console.warn;
    console.warn = (...args) => {
      this.analyzeConsoleOutput('warn', args);
      return originalConsoleWarn.apply(console, args);
    };
  }

  setupCSPReporting() {
    if (typeof window === 'undefined') return;

    document.addEventListener('securitypolicyviolation', (event) => {
      securityService.handleCSPViolation({
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective,
        originalPolicy: event.originalPolicy,
        disposition: event.disposition
      });
      
      this.recordThreat('csp_violation', {
        blockedURI: event.blockedURI,
        violatedDirective: event.violatedDirective
      });
    });
  }

  scanElementForThreats(element) {
    const dangerousAttributes = ['onclick', 'onload', 'onerror', 'onmouseover'];
    dangerousAttributes.forEach(attr => {
      if (element.hasAttribute(attr)) {
        this.recordThreat('xss_attempt', {
          element: element.tagName,
          attribute: attr,
          value: element.getAttribute(attr)
        });
      }
    });

    ['href', 'src'].forEach(attr => {
      const value = element.getAttribute(attr);
      if (value && value.toLowerCase().startsWith('javascript:')) {
        this.recordThreat('xss_attempt', {
          element: element.tagName,
          attribute: attr,
          value: value
        });
      }
    });

    element.querySelectorAll('*').forEach(child => {
      this.scanElementForThreats(child);
    });
  }

  analyzeNetworkRequest(url, options = {}) {
    const suspiciousPatterns = [
      /<script/i,
      /javascript:/i,
      /data:text\/html/i,
      /vbscript:/i,
      /on\w+=/i
    ];

    suspiciousPatterns.forEach(pattern => {
      if (pattern.test(url)) {
        this.recordThreat('suspicious_request', {
          url: url,
          pattern: pattern.toString(),
          method: options.method || 'GET'
        });
      }
    });

    if (options.body) {
      const body = typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
      suspiciousPatterns.forEach(pattern => {
        if (pattern.test(body)) {
          this.recordThreat('suspicious_request', {
            url: url,
            pattern: pattern.toString(),
            location: 'body'
          });
        }
      });
    }
  }

  analyzeError(errorEvent) {
    const securityRelatedErrors = [
      /blocked by content security policy/i,
      /unsafe-eval/i,
      /unsafe-inline/i,
      /script load error/i
    ];

    securityRelatedErrors.forEach(pattern => {
      if (pattern.test(errorEvent.message)) {
        this.recordThreat('security_error', {
          message: errorEvent.message,
          filename: errorEvent.filename,
          lineno: errorEvent.lineno,
          colno: errorEvent.colno
        });
      }
    });
  }

  analyzePromiseRejection(event) {
    if (event.reason && typeof event.reason === 'string') {
      if (/security|xss|csrf|injection/i.test(event.reason)) {
        this.recordThreat('security_promise_rejection', {
          reason: event.reason
        });
      }
    }
  }

  analyzeConsoleOutput(level, args) {
    const message = args.join(' ');
    
    const securityKeywords = [
      'security',
      'xss',
      'csrf',
      'injection',
      'vulnerability',
      'attack',
      'malicious'
    ];

    securityKeywords.forEach(keyword => {
      if (message.toLowerCase().includes(keyword)) {
        this.recordThreat('security_console_output', {
          level: level,
          message: message,
          keyword: keyword
        });
      }
    });
  }

  recordThreat(type, details) {
    const threat = {
      type,
      timestamp: Date.now(),
      details,
      id: this.generateThreatId()
    };

    this.threats.set(threat.id, threat);
    this.securityMetrics[type + 's'] = (this.securityMetrics[type + 's'] || 0) + 1;

    logger.security(type, details);

    this.checkThreatLevel(type);
  }

  generateThreatId() {
    return `threat_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  checkThreatLevel(type) {
    const recentThreats = Array.from(this.threats.values())
      .filter(threat => 
        threat.type === type && 
        (Date.now() - threat.timestamp) < this.thresholds.timeWindow
      );

    const threshold = this.thresholds[`max${type.charAt(0).toUpperCase() + type.slice(1)}s`];
    
    if (threshold && recentThreats.length >= threshold) {
      this.triggerSecurityAlert(type, recentThreats.length);
    }
  }

  triggerSecurityAlert(type, count) {
    const alert = {
      type: 'security_alert',
      threatType: type,
      count: count,
      timestamp: new Date().toISOString(),
      severity: 'high'
    };

    logger.security('security_alert_triggered', alert);

    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('securityAlert', { detail: alert }));
    }
  }

  checkSecurityMetrics() {
    const metrics = { ...this.securityMetrics };
    
    Object.keys(this.securityMetrics).forEach(key => {
      this.securityMetrics[key] = 0;
    });

    logger.info('Security metrics', metrics);
  }

  cleanupOldThreats() {
    const cutoff = Date.now() - (24 * 60 * 60 * 1000); 
    
    for (const [id, threat] of this.threats.entries()) {
      if (threat.timestamp < cutoff) {
        this.threats.delete(id);
      }
    }
  }

  analyzeAnomalies() {
    
    const currentMetrics = { ...this.securityMetrics };
    this.anomalies.push({
      timestamp: Date.now(),
      metrics: currentMetrics
    });

    this.anomalies = this.anomalies.filter(anomaly => 
      (Date.now() - anomaly.timestamp) < (60 * 60 * 1000) 
    );
  }

  handleSecurityEvent(eventData) {
    this.recordThreat(eventData.type, eventData.details);
  }

  getSecurityStatus() {
    return {
      isMonitoring: this.isMonitoring,
      threatCount: this.threats.size,
      recentMetrics: { ...this.securityMetrics },
      anomalyCount: this.anomalies.length
    };
  }
}

SecurityMonitor.instance = new SecurityMonitor();

export default SecurityMonitor.instance;
