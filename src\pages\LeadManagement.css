.lead-management {
  padding: 1.5rem;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.lead-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.lead-breadcrumb {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.breadcrumb-text {
  font-size: 0.95rem;
  color: #495057;
}

.company-selector {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.company-selector label {
  font-weight: 600;
  color: #495057;
}

.company-dropdown {
  min-width: 300px;
}

/* Wizard Steps */
.wizard-steps {
  margin-bottom: 1.5rem;
  padding: 1.5rem;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.wizard-steps .p-steps {
  padding: 0;
}

.wizard-steps .p-steps .p-steps-item .p-menuitem-link {
  background-color: transparent;
}

.wizard-steps .p-steps .p-steps-item.p-highlight .p-steps-number {
  background-color: #2196F3;
  color: white;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 0.75rem;
  align-items: center;
  margin-bottom: 1.5rem;
  padding: 1rem;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  flex-wrap: wrap;
}

.activities-section {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: auto;
  padding: 0.5rem 1rem;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.activity-count {
  font-weight: 600;
  color: #2196F3;
}

/* Main Content */
.lead-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.general-section {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.general-section .p-card-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #212529;
  margin-bottom: 1.5rem;
}

.general-section .p-card-body {
  padding: 1.5rem;
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
}

.form-column {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field label {
  font-weight: 600;
  color: #495057;
  font-size: 0.95rem;
}

.field-hint {
  font-size: 0.85rem;
  color: #6c757d;
  font-style: italic;
  margin-top: 0.25rem;
}

.field-hint.text-warning {
  color: #ff9800;
}

/* Lead Status Field */
.lead-status-field {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-badge {
  flex: 1;
  padding: 0.75rem 1rem;
  background-color: #2196F3;
  color: white;
  text-align: center;
  border-radius: 4px;
  font-weight: 600;
}

/* Tabs Section */
.tabs-section {
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  padding: 1rem;
}

.tabs-section .p-tabview-nav {
  background-color: transparent;
  border-bottom: 2px solid #dee2e6;
}

.tabs-section .p-tabview-nav li .p-tabview-nav-link {
  background-color: transparent;
  border: none;
  color: #495057;
  padding: 1rem 1.5rem;
}

.tabs-section .p-tabview-nav li.p-highlight .p-tabview-nav-link {
  background-color: transparent;
  border-bottom: 2px solid #2196F3;
  color: #2196F3;
}

.tabs-section .p-tabview-panels {
  background-color: transparent;
  padding: 1.5rem 0;
}

/* Address Section */
.address-section {
  box-shadow: none;
  border: 1px solid #dee2e6;
}

.address-header {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 1.5rem;
}

.address-form-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.address-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
}

/* Footer */
.lead-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1.5rem;
  padding: 1rem 1.5rem;
  background-color: #fff;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.footer-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 600;
  color: #495057;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .form-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .address-row {
    grid-template-columns: 1fr;
  }

  .action-buttons {
    flex-direction: column;
    align-items: stretch;
  }

  .activities-section {
    margin-left: 0;
  }

  .lead-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .company-dropdown {
    min-width: 100%;
  }
}

/* PrimeReact Overrides */
.p-card .p-card-content {
  padding: 0;
}

.p-inputtext,
.p-dropdown,
.p-inputtextarea {
  width: 100%;
}

.p-rating .p-rating-icon {
  color: #ffc107;
}

.p-rating .p-rating-icon.pi-star-fill {
  color: #ffc107;
}

/* Button Styles */
.p-button-sm {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.p-button.p-button-text {
  background-color: transparent;
  border: none;
}

.p-button.p-button-success {
  background-color: #4caf50;
  border-color: #4caf50;
}

.p-button.p-button-danger {
  background-color: #f44336;
  border-color: #f44336;
}

.p-button.p-button-primary {
  background-color: #2196F3;
  border-color: #2196F3;
}

