import React, { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { But<PERSON> } from "primereact/button";
import { Checkbox } from "primereact/checkbox";
import { useAuthContext } from "@contexts/AuthContext.jsx";
import Alert from "@components/Common/Alert";
import AuthHeader from "@components/Auth/AuthHeader";
import EmailInput from "@components/Auth/EmailInput";
import PasswordInput from "@components/Auth/PasswordInput";
import * as authController from '@controllers/auth/authController';
import { openMfaWindow, closeMfaWindow } from '@utils/auth/authUtils.js';
import { getFriendlyLoginError, getFriendlyLoginMessage } from '@utils/loginErrorMessages.js';
import "@styles/glassy/glassy-ui.css";
import "@styles/glassy/global.css";


const LoginPage = () => {
  const [currentStep, setCurrentStep] = useState(1); // 1: Login, 2: OTP Verification
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [otp, setOtp] = useState("");
  const [keepSignedIn, setKeepSignedIn] = useState(false);
  const [showPassword, setShowPassword] = useState(false); // By default, password will be hidden
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [successMessage, setSuccessMessage] = useState("");
  const [error, setError] = useState("");
  const [validationErrors, setValidationErrors] = useState({
    email: "",
    password: "",
  });
  const [isEmailValid, setIsEmailValid] = useState(false);
  const [isPasswordValid, setIsPasswordValid] = useState(false);

  // MFA states
  const [requiresMfa, setRequiresMfa] = useState(false);
  const [oAuth2Url, setOAuth2Url] = useState("");
  const [mfaWindow, setMfaWindow] = useState(null);

  const navigate = useNavigate();
  const location = useLocation();
  const [loginRole, setLoginRole] = useState("");

  // Read role from query string (?role=asm-admin|asm-employee|customer-admin|customer-user)
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    let roleParam = params.get("role") || "";
    if (!roleParam) {
      try {
        const stored = sessionStorage.getItem('loginRole');
        if (stored) roleParam = stored;
      } catch {}
    }
    setLoginRole(roleParam);
  }, [location.search]);
  const {
    isAuthenticated,
    login,
    error: authError,
    isLoading: authLoading,
    clearError,
    getDefaultRoute,
  } = useAuthContext();

  React.useEffect(() => {
    if (isAuthenticated) {
      const defaultRoute = getDefaultRoute();
      navigate(defaultRoute);
    }
  }, [isAuthenticated, navigate, getDefaultRoute]);

  // Listen for OAuth2 callback messages
  useEffect(() => {
    const handleMessage = async (event) => {
      // Ensure the message is from the expected origin
      if (event.origin !== window.location.origin) {
        return;
      }

      if (event.data.type === "OAUTH2_CALLBACK") {
        const { code, state, redirectUri } = event.data;

        setIsLoading(true);
        setError("");
        const resp = await authController.handleOauth2Callback({ code, state, redirectUri });

        if (resp.isSuccess) {
          // pass rememberMe based on Keep me signed in
          login({ ...resp.data, rememberMe: keepSignedIn }); // set auth state via context
          setSuccessMessage("Authentication successful! Redirecting...");
          setShowSuccessMessage(true);

          if (mfaWindow) {
            mfaWindow.close();
            setMfaWindow(null);
          }

          setTimeout(() => {
            const defaultRoute = getDefaultRoute();
            navigate(defaultRoute);
          }, 1500);
        } else {
          setError(resp.errorMessage || 'Authentication failed. Please try again.');
          if (mfaWindow) {
            mfaWindow.close();
            setMfaWindow(null);
          }
        }

        setIsLoading(false);
        setRequiresMfa(false);
      } else if (event.data.type === "OAUTH2_ERROR") {
        // Handle OAuth2 errors
        const { error, errorDescription } = event.data;
        setError(`Authentication failed: ${errorDescription || error}`);
        console.error("OAuth2 error:", error, errorDescription);

        // Close MFA window on error
        if (mfaWindow) {
          mfaWindow.close();
          setMfaWindow(null);
        }

        setIsLoading(false);
        setRequiresMfa(false);
      }
    };

    window.addEventListener("message", handleMessage);

    return () => {
      window.removeEventListener("message", handleMessage);
    };
  }, [mfaWindow, navigate]);

  // Clean up MFA window on component unmount
  useEffect(() => {
    return () => {
      if (mfaWindow) {
        mfaWindow.close();
      }
    };
  }, [mfaWindow]);

  // Open an MFA/OAuth popup using reusable helper
  const handleMfaAuthentication = (oAuth2Url) => {
    const { popup, error } = openMfaWindow(oAuth2Url, {
      onOpened: (p) => setMfaWindow(p),
      onClosed: () => {
        setMfaWindow(null);
        setRequiresMfa(false);
        setIsLoading(false);
      },
    });

    if (error) {
      setError(error);
      return;
    }

    setMfaWindow(popup);
  };

  // Validation is now handled by the EmailInput and PasswordInput components
  // No need for separate validation functions as the components handle validation internally


  // Step 1: Login with email and password
  const handleLoginSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setShowSuccessMessage(false);

    // Basic validation check for empty fields - detailed validation is handled by components
    if (!email.trim() || !password.trim()) {
      setError("Both Employee ID/Email and Password are required.");
      setIsLoading(false);
      return;
    }

    try {
      const resp = await authController.handleLogin({ email, password });

      if (resp.requiresMfa) {
        if (resp.oauthUrl) {
          setRequiresMfa(true);
          setOAuth2Url(resp.oauthUrl);
          handleMfaAuthentication(resp.oauthUrl);
        } else {
          setError('Multi-factor authentication is required but no authentication URL was provided.');
        }
        return;
      }

      if (resp.isSuccess) {
        // persist session based on Keep me signed in
        login({ ...resp.data, rememberMe: keepSignedIn });
        setSuccessMessage('Login successful! Redirecting...');
        setShowSuccessMessage(true);

        setTimeout(() => {
          const defaultRoute = getDefaultRoute();
          navigate(defaultRoute);
        }, 1500);
      } else {
        console.log(resp);
        if (resp?.data?.statusCode == 401) {
          // Handle specific 401 Unauthorized case
          setError('Invalid username or password.');
        } else
          setError(resp?.errorMessage || 'Login failed. Please try again.');
      }
    } catch (err) {
      // Friendly error message for login failures or network errors
      const friendly = getFriendlyLoginError(err);
      setError(friendly);
      console.error('Login error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  // Step 2: OTP Verification
  const handleOtpSubmit = async (e) => {
    e.preventDefault();
    setIsLoading(true);
    setError("");
    setShowSuccessMessage(false);

    try {
      // Simulate API call for OTP verification
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // Show success message briefly
      setSuccessMessage("OTP verified successfully");
      setShowSuccessMessage(true);

      // Navigate to dashboard after success
      setTimeout(() => {
        navigate("/dashboard");
      }, 1500);
    } catch (error) {
      setError("Invalid OTP. Please try again.");
      console.error("OTP verification failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  // Resend OTP
  const handleResendOtp = async () => {
  };

  const handleBack = () => {
    setCurrentStep(1);
    setOtp("");
    setError("");
    setShowSuccessMessage(false);
  };

  // Step 1: Login Form
  if (currentStep === 1) {
    return (
      <div className="auth-page-layout">
        <div className="glass-card"
        >
          <AuthHeader showLogo={true} />

          {/* Success Message */}
          {showSuccessMessage && (
            <div className="mb-4">
              <Alert
                type="success"
                message={successMessage}
                show={showSuccessMessage}
              />
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-4">
              <Alert type="error" message={error} show={!!error} />
            </div>
          )}

          {/* MFA in Progress Message */}
          {requiresMfa && (
            <div className="mb-4">
              <Alert
                type="info"
                message="Multi-factor authentication required. A new authentication window has opened. Please complete the authentication process there."
                show={requiresMfa}
              />
              <div className="text-center mt-2">
                <p className="text-sm text-gray-600 mb-2">
                  <i className="pi pi-info-circle mr-1"></i>
                  The authentication window will ask you to sign in again for
                  security purposes.
                </p>
                <button
                  type="button"
                  onClick={() => {
                    if (mfaWindow) {
                      mfaWindow.close();
                      setMfaWindow(null);
                    }
                    setRequiresMfa(false);
                    setIsLoading(false);
                  }}
                  className="btn-link text-sm"
                  disabled={isLoading}
                >
                  Cancel Authentication
                </button>
              </div>
            </div>
          )}

          <form
            onSubmit={handleLoginSubmit}
            className="auth-form-layout"
          >
            <div className="form-group">
              <label htmlFor="email" className="form-label">
                Employee ID / Email ID <span className="required">*</span>
              </label>
              <EmailInput
                value={email}
                onChange={(e) => {
                  setEmail(e.target.value);
                  setValidationErrors(prev => ({ ...prev, email: '' }));
                  setError('');
                }}
                onValidationChange={setIsEmailValid}
                disabled={isLoading}
                placeholder="Enter Employee ID or Email ID"
                className={validationErrors.email ? "p-invalid" : ""}
              />
            </div>

            <div className="form-group">
              <label htmlFor="password" className="form-label">
                Password <span className="required">*</span>
              </label>
              <PasswordInput
                value={password}
                onChange={(e) => {
                  setPassword(e.target.value);
                  setValidationErrors(prev => ({ ...prev, password: '' }));
                  setError('');
                }}
                onValidationChange={setIsPasswordValid}
                username={email}
                disabled={isLoading}
                placeholder="Enter your password"
                className={validationErrors.password ? "p-invalid" : ""}
              />
            </div>

            {/* Password Complexity Indicator - Temporarily disabled
             * This section showed password requirements and complexity validation
             * - Real-time password strength feedback
             * - Password requirements checklist
             * - Dynamic styling based on password input
             */}
            {/* <div
              className={`form-group login-password-instructions ${
                showPasswordInstructions ? "active" : ""
              }`}
            >
              <PasswordComplexityIndicator
                password={password}
                username={email}
                showStrengthBar={false}
                showRequirements={true}
              />
            </div> */}

            <div className="form-options">
              <div className="checkbox-wrapper">
                <Checkbox
                  inputId="keepSignedIn"
                  checked={keepSignedIn}
                  onChange={(e) => setKeepSignedIn(e.checked)}
                  className="checkbox-base"
                  disabled={isLoading}
                />
                <label htmlFor="keepSignedIn" className="checkbox-label">
                  Keep me signed in
                </label>
              </div>
              { (loginRole === "customer-admin" || loginRole === "customer-user") && (
                <button
                  type="button"
                  onClick={() => navigate("/forgot-password")}
                  className="btn-link link-primary login-forgot-btn"
                  disabled={isLoading}
                >
                  Forgot Password?
                </button>
              )}
            </div>

            <Button
              type="submit"
              label={
                requiresMfa
                  ? "Authenticating..."
                  : isLoading
                    ? "Signing In..."
                    : "Sign In"
              }
              className="btn-base btn-primary mt-2"
              loading={isLoading || requiresMfa}
              disabled={
                isLoading ||
                requiresMfa ||
                !email.trim() ||
                !password.trim() ||
                !isEmailValid ||
                !isPasswordValid
              }
            />

            <div className="text-center mt-2">
              <button
                type="button"
                onClick={() => navigate("/")}
                className="btn-link"
                disabled={isLoading}
              >
                Back to Home
              </button>
            </div>
          </form>
        </div>
      </div>
    );
  }

  // Step 2: OTP Verification
  if (currentStep === 2) {
    return (
      <div className="auth-page-layout">
        <div className="glass-card">
          <AuthHeader
            showLogo={true}
            showBackButton={true}
            onBackClick={handleBack}
            backButtonText="Enter OTP"
            title=""
            subtitle={`We've sent a verification code to your registered device for ${email}`}
          />

          {/* Success Message */}
          {showSuccessMessage && (
            <div className="mb-4">
              <Alert
                type="success"
                message={successMessage}
                show={showSuccessMessage}
              />
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className="mb-4">
              <Alert type="error" message={error} show={!!error} />
            </div>
          )}

          <form onSubmit={handleOtpSubmit} className="auth-form-layout">
            <div className="form-group">
              <label htmlFor="otp" className="form-label">
                Enter OTP
              </label>
              <div className="input-wrapper">
                <InputText
                  id="otp"
                  value={otp}
                  onChange={(e) => setOtp(e.target.value)}
                  placeholder="Enter OTP"
                  className="input-base input-no-icon text-center login-otp-input"
                  required
                  disabled={isLoading}
                  maxLength={6}
                />
              </div>
            </div>

            <Button
              type="submit"
              label={isLoading ? "Verifying..." : "Verify OTP"}
              className="btn-base btn-primary mt-4"
              loading={isLoading}
              disabled={isLoading || !otp.trim()}
            />

            <div className="text-center mt-2">
              <button
                type="button"
                onClick={() => setCurrentStep(1)}
                className="btn-link"
                disabled={isLoading}
              >
                Back to Login
              </button>
            </div>
            <div className="text-center mt-2">
              <p className="text-sm text-gray-500 mb-2">
                Didn't receive the OTP?{" "}
                <button
                  type="button"
                  onClick={handleResendOtp}
                  className="btn-link"
                  disabled={isLoading}
                >
                  Resend OTP
                </button>
              </p>
            </div>
          </form>
        </div>
      </div>
    );
  }

  return null;
};

export default LoginPage;
