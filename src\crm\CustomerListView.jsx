import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Card } from 'primereact/card';
import { Tag } from 'primereact/tag';
import { Calendar } from 'primereact/calendar';
import CRMNavigation from './CRMNavigation';

const CustomerListView = () => {
  const navigate = useNavigate();
  const [globalFilter, setGlobalFilter] = useState('');
  const [selectedCustomers, setSelectedCustomers] = useState([]);
  const [dateFrom, setDateFrom] = useState(null);
  const [dateTo, setDateTo] = useState(null);

  const customers = [
    {
      id: 1,
      companyName: 'SkyHaven Airways',
      contactNumber: '******-123-4567',
      email: '<EMAIL>',
      location: 'Abu Dhabi, UAE',
      salesperson: '<PERSON>',
      lastInvoice: '05/01/2025',
      totalDue: '$0',
      customerGroup: 'Lorel Ipsum',
      complianceStatus: 'Compliant'
    },
    {
      id: 2,
      companyName: 'AeroVista Airlines',
      contactNumber: '******-234-5678',
      email: 'support@aerovistaa...',
      location: 'Nairobi, Kenya',
      salesperson: 'Daniel Crestwood',
      lastInvoice: '05/01/2025',
      totalDue: '$0',
      customerGroup: 'Lorel Ipsum',
      complianceStatus: 'Non-Compliant'
    },
    {
      id: 3,
      companyName: 'CloudLiner Aviation',
      contactNumber: '******-345-6789',
      email: 'contact@cloudliner...',
      location: 'Sydney, Australia',
      salesperson: 'Sophia Langston',
      lastInvoice: '05/01/2025',
      totalDue: '$0',
      customerGroup: 'Lorel Ipsum',
      complianceStatus: 'Compliant'
    },
    {
      id: 4,
      companyName: 'BlueHorizon Air',
      contactNumber: '******-456-7890',
      email: 'helpdesk@bluehor...',
      location: 'Tokyo, Japan',
      salesperson: 'Ethan Caldwell',
      lastInvoice: '05/01/2025',
      totalDue: '$234880',
      customerGroup: 'Lorel Ipsum',
      complianceStatus: 'Compliant'
    },
    {
      id: 5,
      companyName: 'StarLink Airlines',
      contactNumber: '******-567-8901',
      email: 'customerservice@star...',
      location: 'New York, USA',
      salesperson: 'Isabella Hartley',
      lastInvoice: '05/01/2025',
      totalDue: '$67400',
      customerGroup: 'Lorel Ipsum',
      complianceStatus: 'Compliant'
    },
    {
      id: 6,
      companyName: 'Summit Skyways',
      contactNumber: '******-345-6789',
      email: 'inquiries@summit...',
      location: 'Toronto, Canada',
      salesperson: 'Liam Redmond',
      lastInvoice: '05/01/2025',
      totalDue: '$34490',
      customerGroup: 'Lorel Ipsum',
      complianceStatus: 'Non-Compliant'
    },
    {
      id: 7,
      companyName: 'JetNova Aviation',
      contactNumber: '******-345-6789',
      email: 'admin@jetnovaav...',
      location: 'Paris, France',
      salesperson: 'Olivia Sterling',
      lastInvoice: '05/01/2025',
      totalDue: '$2000',
      customerGroup: 'Lorel Ipsum',
      complianceStatus: 'Non-Compliant'
    },
    {
      id: 8,
      companyName: 'SilverStream Airlines',
      contactNumber: '******-345-6789',
      email: 'service@silverstream...',
      location: 'Mumbai, India',
      salesperson: 'James Ashford',
      lastInvoice: '05/01/2025',
      totalDue: '$2000',
      customerGroup: 'Lorel Ipsum',
      complianceStatus: 'Compliant'
    }
  ];

  const handleRowClick = (rowData) => {
    navigate(`/customer/${rowData.id}`);
  };

  const companyTemplate = (rowData) => {
    return (
      <div className="company-cell">
        <strong>{rowData.companyName}</strong>
      </div>
    );
  };

  const contactTemplate = (rowData) => {
    return (
      <div>
        <div className="text-sm">{rowData.contactNumber}</div>
      </div>
    );
  };

  const emailTemplate = (rowData) => {
    return (
      <div className="text-sm">{rowData.email}</div>
    );
  };

  const complianceTemplate = (rowData) => {
    const severity = rowData.complianceStatus === 'Compliant' ? 'success' : 'danger';
    return <Tag value={rowData.complianceStatus} severity={severity} />;
  };

  const totalDueTemplate = (rowData) => {
    const amount = rowData.totalDue;
    const color = amount === '$0' ? 'inherit' : '#f44336';
    return <span style={{ color, fontWeight: amount !== '$0' ? 'bold' : 'normal' }}>{amount}</span>;
  };

  const actionTemplate = (rowData) => {
    return (
      <div className="action-buttons">
        <Button
          icon="pi pi-pencil"
          className="p-button-rounded p-button-text p-button-sm"
          onClick={(e) => {
            e.stopPropagation();
            navigate(`/customer/${rowData.id}/edit`);
          }}
          tooltip="Edit"
        />
      </div>
    );
  };

  const header = (
    <div className="table-header">
      <div className="header-left">
        <h2>Customer</h2>
      </div>
      <div className="header-right">
        <Dropdown
          placeholder="Filter by --"
          className="filter-dropdown"
          style={{ marginRight: '0.5rem' }}
        />
        <Dropdown
          placeholder="Select --"
          className="filter-dropdown"
          style={{ marginRight: '0.5rem' }}
        />
        <span className="p-input-icon-left" style={{ marginRight: '0.5rem' }}>
          <i className="pi pi-search" />
          <InputText
            type="search"
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            placeholder="Search customers..."
          />
        </span>
        <span style={{ marginRight: '0.5rem' }}>
          <label style={{ marginRight: '0.25rem', fontSize: '0.875rem' }}>From</label>
          <Calendar
            value={dateFrom}
            onChange={(e) => setDateFrom(e.value)}
            placeholder="-/-/-"
            showIcon
            dateFormat="dd/mm/yy"
          />
        </span>
        <span style={{ marginRight: '0.5rem' }}>
          <label style={{ marginRight: '0.25rem', fontSize: '0.875rem' }}>To</label>
          <Calendar
            value={dateTo}
            onChange={(e) => setDateTo(e.value)}
            placeholder="-/-/-"
            showIcon
            dateFormat="dd/mm/yy"
          />
        </span>
        <Button
          label="Export"
          icon="pi pi-download"
          className="p-button-outlined p-button-sm"
          style={{ marginRight: '0.5rem' }}
        />
        <Button
          label="Delete"
          icon="pi pi-trash"
          className="p-button-outlined p-button-danger p-button-sm"
        />
      </div>
    </div>
  );

  return (
    <div className="customer-list-view">
      {/* <CRMNavigation /> */}

      <div className="glass-card-container">
        <div className="breadcrumb-nav">
          <Button icon="pi pi-home" className="p-button-text p-button-sm" />
          <span className="breadcrumb-separator">/</span>
          <span>Customer</span>
        </div>
      </div>

      <Card className="customers-table-card">
        <DataTable
          value={customers}
          selection={selectedCustomers}
          onSelectionChange={(e) => setSelectedCustomers(e.value)}
          dataKey="id"
          paginator
          rows={10}
          rowsPerPageOptions={[5, 10, 25, 50]}
          globalFilter={globalFilter}
          header={header}
          emptyMessage="No customers found"
          className="customers-datatable"
          onRowClick={(e) => handleRowClick(e.data)}
          rowHover
        >
          <Column selectionMode="multiple" headerStyle={{ width: '3rem' }} />
          <Column field="companyName" header="Company Name" body={companyTemplate} sortable />
          <Column field="contactNumber" header="Contact Number" body={contactTemplate} sortable />
          <Column field="email" header="Email" body={emailTemplate} sortable />
          <Column field="location" header="Location" sortable />
          <Column field="salesperson" header="Salesperson" sortable />
          <Column field="lastInvoice" header="Last Invoice" sortable />
          <Column field="totalDue" header="Total Due" body={totalDueTemplate} sortable />
          <Column field="customerGroup" header="Customer Group" sortable />
          <Column field="complianceStatus" header="Compliance Status" body={complianceTemplate} sortable />
          <Column body={actionTemplate} header="Action" headerStyle={{ width: '8rem' }} />
        </DataTable>
      </Card>
    </div>
  );
};

export default CustomerListView;

