import React, { useState } from 'react';
import { Button } from 'primereact/button';
import FinancialDocumentCard from '../../components/common/FinancialDocumentCard';
import GlassyBlueButton from '../../components/common/GlassyBlueButton';

/**
 * FinancialsTab - Displays financial documents and information
 */
const FinancialsTab = ({ prospectId }) => {
  const [documents] = useState([
    {
      id: 1,
      documentName: "Balance Sheet",
      rating: 2,
      internalNotes: "The company's balance sheet reflects strong financial health, with robust asset growth and a healthy equity-to-liabilities ratio, indicating stability and efficient resource management."
    },
    {
      id: 2,
      documentName: "Income Statement",
      rating: 4,
      internalNotes: "Strong revenue growth with consistent profitability margins. Operating expenses are well-managed and show positive trends year-over-year."
    },
    {
      id: 3,
      documentName: "Cash Flow Statement",
      rating: 3.5,
      internalNotes: "Healthy cash flow from operations. Investment activities show strategic capital allocation. Financing activities indicate stable debt management."
    }
  ]);

  const handleEdit = (documentId) => {
    console.log('Edit document:', documentId);
    // Add edit logic here
  };

  const handleDelete = (documentId) => {
    console.log('Delete document:', documentId);
    // Add delete logic here
  };

  return (
    <div className="financials-tab" style={{ position: 'relative' }}>
      {/* Header */}
      <div style={{ 
        marginBottom: '1.5rem', 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        flexWrap: 'wrap',
        gap: '1rem',
        position: 'relative',
        zIndex: 10
      }}>
        <h3 style={{ margin: 0, fontSize: '1.5rem', fontWeight: '600', color: 'black' }}>
          Financial Documents
        </h3>
        <GlassyBlueButton 
          label="New Document" 
          icon="pi pi-plus" 
          className="p-button-sm"
          style={{ backgroundColor: '#3B82F6', border: 'none' }}
        />
      </div>

      {/* Financial Document Cards */}
      {documents.map((doc) => (
        <FinancialDocumentCard
          key={doc.id}
          documentName={doc.documentName}
          rating={doc.rating}
          internalNotes={doc.internalNotes}
          onEdit={() => handleEdit(doc.id)}
          onDelete={() => handleDelete(doc.id)}
        />
      ))}
    </div>
  );
};

export default FinancialsTab;

