/* eslint-disable react/jsx-no-comment-textnodes */
"use client"

/*
  Enhanced Paginator.jsx
  - Handles all pagination logic internally
  - Pass data array and get paginated results via onPageChange
  - Supports both render prop pattern and callback pattern
*/

import { useMemo, useState, useEffect } from "react"

export default function Paginator({ 
  data = [], 
  itemsPerPage = 10, 
  initialPage = 1, 
  onPageChange, 
  showPrevNext = true,
  // Legacy props for backward compatibility
  totalItems
}) {
  // Use data length if data is provided, otherwise use totalItems
  const totalItemsCount = data.length > 0 ? data.length : (totalItems || 0)
  const totalPages = Math.max(1, Math.ceil(totalItemsCount / itemsPerPage))
  const [page, setPage] = useState(Math.min(Math.max(1, Number(initialPage) || 1), totalPages))

  // Update paginated data when page or data changes
  useEffect(() => {
    if (data.length > 0) {
      const startIndex = (page - 1) * itemsPerPage
      const endIndex = startIndex + itemsPerPage
      const sliced = data.slice(startIndex, endIndex)
      
      // Call onPageChange with paginated data
      if (typeof onPageChange === "function") {
        onPageChange(page, sliced, startIndex, endIndex)
      }
    }
  }, [data, page, itemsPerPage])

  // Reset to page 1 when data changes (for filtering/searching)
  useEffect(() => {
    if (page > totalPages) {
      setPage(1)
    }
  }, [totalPages, page])

  // PrimeFlex classes for consistent styling
  const navClasses = 'w-full flex align-items-center justify-content-center p-2 bg-transparent box-border';
  const listClasses = 'list-none flex align-items-center justify-content-center gap-4 m-0 p-0';
  const listItemClasses = 'flex align-items-center justify-content-center';
  const linkButtonClasses = 'bg-transparent border-none text-blue-500 text-sm cursor-pointer p-0 line-height-1';
  const linkButtonDisabledClasses = 'opacity-50 cursor-not-allowed';
  const pageItemClasses = 'bg-transparent border-none text-blue-500 text-sm cursor-pointer px-3 py-2 border-round-3xl line-height-1';
  const pageItemActiveClasses = 'bg-primary text-white';
  const visuallyHiddenClasses = 'sr-only';

  const pages = Array.from({ length: totalPages }, (_, i) => i + 1)
  const canPrev = page > 1
  const canNext = page < totalPages

  function changePage(nextPage) {
    const clamped = Math.min(Math.max(1, nextPage), totalPages)
    if (clamped !== page) {
      setPage(clamped)
      
      // If data is provided, handle pagination internally
      if (data.length > 0) {
        const startIndex = (clamped - 1) * itemsPerPage
        const endIndex = startIndex + itemsPerPage
        const sliced = data.slice(startIndex, endIndex)
        
        if (typeof onPageChange === "function") {
          onPageChange(clamped, sliced, startIndex, endIndex) // Use `clamped`, not `page`
        }
      } else {
        // Legacy mode - just pass indices
        if (typeof onPageChange === "function") {
          const startIndex = (clamped - 1) * itemsPerPage
          const endIndex = startIndex + itemsPerPage
          onPageChange(clamped, null, startIndex, endIndex) // Use `clamped`, not `page`
        }
      }
    }
  }

  return (
    <nav aria-label="Pagination" className={navClasses}>
      <ul className={listClasses} role="list">
        {showPrevNext && (
          <li className={listItemClasses} role="listitem">
            <button
              type="button"
              onClick={() => canPrev && changePage(page - 1)}
              disabled={!canPrev}
              className={`${linkButtonClasses} ${!canPrev ? linkButtonDisabledClasses : ''}`}
              aria-label="Previous page"
            >
              Previous
            </button>
          </li>
        )}

        {pages.map((p) => {
          const isActive = p === page;
          return (
            <li key={p} className={listItemClasses} role="listitem">
              <button
                type="button"
                onClick={() => changePage(p)}
                className={`${pageItemClasses} ${isActive ? pageItemActiveClasses : ''}`}
                aria-current={isActive ? "page" : undefined}
                aria-label={`Page ${p}`}
              >
                {String(p)}
              </button>
            </li>
          );
        })}

        {showPrevNext && (
          <li className={listItemClasses} role="listitem">
            <button
              type="button"
              onClick={() => canNext && changePage(page + 1)}
              disabled={!canNext}
              className={`${linkButtonClasses} ${!canNext ? linkButtonDisabledClasses : ''}`}
              aria-label="Next page"
            >
              Next
            </button>
          </li>
        )}
      </ul>

      {/* Screen-reader announcement of current page */}
      {/* <span className={visuallyHiddenClasses} aria-live="polite">
        {`Page ${page} of ${totalPages}`}
      </span> */}
    </nav>
  )
}
