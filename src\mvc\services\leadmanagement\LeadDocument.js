import ApiService from '../ApiService.js';

class LeadDocumentService {
	async save(docData) {
		return await new ApiService().request('POST', '/LeadDocument/save', docData);
	}
	async update(id, docData) {
		return await new ApiService().request('PUT', `/LeadDocument/update/${id}`, docData);
	}
	async deleteById(id) {
		return await new ApiService().request('DELETE', `/LeadDocument/deleteById/${id}`);
	}
		async getAll() {
			return await new ApiService().request('GET', '/LeadDocument/getAll');
		}
		async getById(id) {
			return await new ApiService().request('GET', `/LeadDocument/getById/${id}`);
		}
}

export default new LeadDocumentService();
