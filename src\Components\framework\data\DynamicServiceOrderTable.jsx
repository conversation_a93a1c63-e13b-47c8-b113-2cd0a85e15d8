// import React, { useState, useEffect, useCallback, useMemo } from 'react';
// import PropTypes from 'prop-types';
// import { DataTable } from 'primereact/datatable';
// import { Column } from 'primereact/column';
// import { Button } from 'primereact/button';
// import { InputText } from 'primereact/inputtext';
// import { Dropdown } from 'primereact/dropdown';
// import { TabView, TabPanel } from 'primereact/tabview';
// import '../../custom/ServiceOrdersDataTable.css';

// const DynamicServiceOrderTable = ({
//   config = {},
//   data = [],
//   onEvent,
//   className = '',
//   style = {},
// }) => {
//   const [expandedRows, setExpandedRows] = useState(null);
//   const [globalFilter, setGlobalFilter] = useState('');
//   const [activeTabIndex, setActiveTabIndex] = useState({});


//   const {
//     title = 'Service Orders',
//     columns = [],
//     groupBy = null,
//     statusBar = { enabled: false },
//     expansion = { enabled: false },
//     header = { enabled: true },
//     filters = [],
//     actions = { enabled: false },
//     serviceFields = [],
//   } = config;

//   useEffect(() => {
//     if (!statusBar.enabled) return;
//     if (!serviceFields || serviceFields.length === 0) return;

//     const addStatusBars = () => {
//       data.forEach((row) => {
//         const rowElement = document.querySelector(`.dynamic-row-${row[statusBar.idField || 'id']}`);

//         if (rowElement) {
//           const existingBars = rowElement.querySelectorAll('.row-progress-bars');
//           existingBars.forEach(bar => bar.remove());

//           const statusData = row[statusBar.field];

//           if (!statusData || statusData.shouldShow === false) return;

//           const cells = rowElement.querySelectorAll('td');

//           let firstServiceCell = null;
//           let lastServiceCell = null;

//           columns.forEach((col, idx) => {
//             const isServiceColumn = serviceFields.some(sf =>
//               col.field && col.field.toLowerCase() === sf.toLowerCase()
//             ) || col.type === 'status';

//             if (isServiceColumn) {
//               const cellIndex = expansion.enabled ? idx + 2 : idx + 1;
//               const cell = cells[cellIndex];
//               if (cell) {
//                 if (!firstServiceCell) firstServiceCell = cell;
//                 lastServiceCell = cell;
//               }
//             }
//           });

//           if (!firstServiceCell || !lastServiceCell) return;

//           const rowRect = rowElement.getBoundingClientRect();
//           const firstCellRect = firstServiceCell.getBoundingClientRect();
//           const lastCellRect = lastServiceCell.getBoundingClientRect();

//           const leftOffset = firstCellRect.left - rowRect.left;
//           const width = lastCellRect.right - firstCellRect.left;

//           const progressContainer = document.createElement('div');
//           progressContainer.className = 'row-progress-bars';
//           progressContainer.style.cssText = `
//             position: absolute !important;
//             bottom: 0 !important;
//             left: ${leftOffset}px !important;
//             width: ${width}px !important;
//             height: 4px !important;
//             z-index: 10 !important;
//             pointer-events: none !important;
//             display: flex !important;
//           `;

//           const barColor = statusData.color === 'green' ? '#22c55e' : '#ef4444';

//           const progressBar = document.createElement('div');
//           progressBar.className = 'progress-bar-container';
//           progressBar.style.cssText = `
//             width: 100%;
//             height: 100%;
//             background-color: rgba(229, 231, 235, 0.5);
//             overflow: hidden;
//           `;

//           const progress = document.createElement('div');
//           progress.className = 'progress-bar';
//           progress.style.cssText = `
//             width: ${statusData.progress}%;
//             height: 100%;
//             background-color: ${barColor};
//             transition: width 0.3s ease;
//           `;

//           progressBar.appendChild(progress);
//           progressContainer.appendChild(progressBar);

//           rowElement.appendChild(progressContainer);
//         }
//       });
//     };

//     const timer1 = setTimeout(addStatusBars, 50);
//     const timer2 = setTimeout(addStatusBars, 200);
//     const timer3 = setTimeout(addStatusBars, 500);

//     return () => {
//       clearTimeout(timer1);
//       clearTimeout(timer2);
//       clearTimeout(timer3);
//     };
//   }, [data, expandedRows, statusBar, columns, expansion, serviceFields]);

//   const getGroupStatistics = useCallback((groupData) => {
//     if (!config.groupStatistics) return null;

//     const stats = {};
//     config.groupStatistics.forEach((stat) => {
//       if (stat.type === 'count') {
//         stats[stat.name] = groupData.length;
//       } else if (stat.type === 'countIf') {
//         stats[stat.name] = groupData.filter(stat.condition).length;
//       }
//     });
//     return stats;
//   }, [config.groupStatistics]);

//   const headerTemplate = useCallback((data) => {
//     if (!groupBy) return null;

//     const groupValue = data[groupBy.field];
//     const groupData = config.data?.filter(item => item[groupBy.field] === groupValue) || [];
//     const stats = getGroupStatistics(groupData);

//     return (
//       <div className="date-summary-row">
//         <span className="date-text">{groupValue}</span>
//         {stats && (
//           <span className="summary-text">
//             {Object.entries(stats).map(([key, value]) => `${key} ${value}`).join(' ')}
//           </span>
//         )}
//         {groupBy.actions && (
//           <Button
//             icon={groupBy.actions.icon || 'pi pi-cog'}
//             className="p-button-text p-button-sm"
//             onClick={() => onEvent?.('groupAction', { group: groupValue, data: groupData })}
//           />
//         )}
//       </div>
//     );
//   }, [groupBy, config.data, getGroupStatistics, onEvent]);

//   const rowExpansionTemplate = useCallback((rowData) => {
//     if (!expansion.enabled || !expansion.tabs) return null;

//     const currentTabIndex = activeTabIndex[rowData[statusBar.idField || 'id']] || 0;

//     return (
//       <div className="expansion-panel">

//         <div className="expansion-header-bar">
//           <Button
//             icon="pi pi-times"
//             className="p-button-rounded p-button-text close-expansion-btn"
//             onClick={() => setExpandedRows(null)}
//           />
//         </div>
//         <TabView
//           activeIndex={currentTabIndex}
//           onTabChange={(e) => {
//             setActiveTabIndex({
//               ...activeTabIndex,
//               [rowData[statusBar.idField || 'id']]: e.index,
//             });
//             onEvent?.('tabChange', { rowData, tabIndex: e.index });
//           }}
//         >
//           {expansion.tabs.map((tab, index) => (
//             <TabPanel key={index} header={tab.header} leftIcon={tab.icon}>
//               <div className="expansion-content">
//                 {tab.render ? tab.render(rowData) : <div>{tab.content}</div>}
//               </div>
//             </TabPanel>
//           ))}
//         </TabView>
//       </div>
//     );
//   }, [expansion, activeTabIndex, statusBar.idField, onEvent]);

//   const handleStatusClick = useCallback((rowData, tabIndex) => {
//     setExpandedRows({ [rowData[statusBar.idField || 'id']]: true });
//     setActiveTabIndex({ ...activeTabIndex, [rowData[statusBar.idField || 'id']]: tabIndex });
//     onEvent?.('statusClick', { rowData, tabIndex });
//   }, [activeTabIndex, statusBar.idField, onEvent]);

//   const createClickableStatus = useCallback((rowData, field, tabIndex, tabName) => {
//     const status = rowData[field];
//     const statusClass = `status-badge status-${status?.toLowerCase()} clickable-status`;
//     return (
//       <span
//         className={statusClass}
//         onClick={(e) => {
//           e.stopPropagation();
//           handleStatusClick(rowData, tabIndex);
//         }}
//         style={{ cursor: 'pointer' }}
//         title={`Click to view ${tabName} details`}
//       >
//         {status}
//       </span>
//     );
//   }, [handleStatusClick]);

//   const renderColumnBody = useCallback((rowData, column) => {
//     if (column.body) {
//       return column.body(rowData);
//     }

//     if (column.type === 'status' && column.clickable) {
//       return createClickableStatus(rowData, column.field, column.tabIndex || 0, column.tabName || column.header);
//     }

//     if (column.type === 'composite') {
//       return (
//         <div className={column.className || 'composite-cell'}>
//           {column.fields.map((subField, idx) => (
//             <div key={idx} className={subField.className}>
//               {rowData[subField.field]}
//             </div>
//           ))}
//         </div>
//       );
//     }

//     if (column.type === 'route') {
//       const routeData = rowData[column.field];
//       return (
//         <div className="route-cell">
//           <span className="route-from">{routeData.from}</span>
//           <i className="pi pi-arrow-right" style={{ fontSize: '10px', margin: '0 4px' }}></i>
//           <span className="route-to">{routeData.to}</span>
//         </div>
//       );
//     }

//     if (column.type === 'object') {
//       const objData = rowData[column.field];
//       return (
//         <div className={column.className || 'object-cell'}>
//           {column.displayFields?.map((df, idx) => (
//             <div key={idx} className={df.className}>
//               {objData[df.field]}
//             </div>
//           ))}
//         </div>
//       );
//     }

//     return rowData[column.field];
//   }, [createClickableStatus]);

//   const renderActions = useCallback((rowData) => {
//     if (!actions.enabled || !actions.buttons) return null;

//     return (
//       <div className="action-buttons">
//         {actions.buttons.map((action, idx) => (
//           <Button
//             key={idx}
//             icon={action.icon}
//             className={action.className || 'p-button-rounded p-button-text p-button-sm'}
//             onClick={() => {
//               if (action.onClick) {
//                 action.onClick(rowData);
//               }
//               onEvent?.('action', { action: action.name, rowData });
//             }}
//             tooltip={action.tooltip}
//           />
//         ))}
//       </div>
//     );
//   }, [actions, onEvent]);

//   const renderHeader = useMemo(() => {
//     if (!header.enabled) return null;

//     return (
//       <div className="table-header">
//         <div className="header-left">
//           <h2>{title}</h2>
//         </div>
//         <div className="header-center">
//           {header.search && (
//             <span className="p-input-icon-left">
//               <i className="pi pi-search" />
//               <InputText
//                 value={globalFilter}
//                 onChange={(e) => setGlobalFilter(e.target.value)}
//                 placeholder={header.search.placeholder || 'Search...'}
//               />
//             </span>
//           )}
//           {filters.map((filter, idx) => (
//             <Dropdown
//               key={idx}
//               value={filter.value}
//               options={filter.options}
//               onChange={(e) => {
//                 if (filter.onChange) {
//                   filter.onChange(e.value);
//                 }
//                 onEvent?.('filterChange', { filter: filter.name, value: e.value });
//               }}
//               placeholder={filter.placeholder}
//             />
//           ))}
//         </div>
//         <div className="header-right">
//           {header.actions?.map((action, idx) => (
//             <Button
//               key={idx}
//               icon={action.icon}
//               label={action.label}
//               className={action.className || 'p-button-sm'}
//               onClick={() => {
//                 if (action.onClick) {
//                   action.onClick();
//                 }
//                 onEvent?.('headerAction', { action: action.name });
//               }}
//             />
//           ))}
//         </div>
//       </div>
//     );
//   }, [header, title, globalFilter, filters, onEvent]);

//   return (
//     <div className={`dynamic-service-order-table ${className}`} style={style}>
//       {renderHeader}

//       <DataTable
//         value={data}
//         expandedRows={expandedRows}
//         onRowToggle={(e) => setExpandedRows(e.data)}
//         rowExpansionTemplate={expansion.enabled ? rowExpansionTemplate : null}
//         dataKey={statusBar.idField || 'id'}
//         className="service-orders-table"
//         rowGroupMode={groupBy ? 'subheader' : null}
//         groupRowsBy={groupBy?.field}
//         sortMode="single"
//         sortField={groupBy?.field}
//         sortOrder={1}
//         rowGroupHeaderTemplate={groupBy ? headerTemplate : null}
//         rowClassName={(rowData) => `service-order-row dynamic-row-${rowData[statusBar.idField || 'id']}`}
//         globalFilter={globalFilter}
//       >
//         {expansion.enabled && <Column expander style={{ width: '3em' }} />}

//         {columns.map((col, idx) => (
//           <Column
//             key={idx}
//             field={col.field}
//             header={col.header}
//             body={(rowData) => renderColumnBody(rowData, col)}
//             style={col.style}
//           />
//         ))}

//         {actions.enabled && (
//           <Column
//             body={renderActions}
//             style={actions.style || { width: '120px' }}
//           />
//         )}
//       </DataTable>
//     </div>
//   );
// };

// DynamicServiceOrderTable.propTypes = {
//   config: PropTypes.object.isRequired,
//   data: PropTypes.array.isRequired,
//   onEvent: PropTypes.func,
//   className: PropTypes.string,
//   style: PropTypes.object,
// };

// export default DynamicServiceOrderTable;



import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import PropTypes from 'prop-types';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { TabView, TabPanel } from 'primereact/tabview';
import '../../custom/ServiceOrdersDataTable.css';

const DynamicServiceOrderTable = ({
  config = {},
  data = [],
  onEvent,
  className = '',
  style = {},
}) => {
  const [expandedRows, setExpandedRows] = useState(null);
  const [globalFilter, setGlobalFilter] = useState('');
  const [activeTabIndex, setActiveTabIndex] = useState({});
  const [pointerPosition, setPointerPosition] = useState({ left: 0, visible: false });
  const tableRef = useRef(null);
  const expansionRef = useRef(null);
  const pendingPointerCalculation = useRef(null);

  const {
    title = 'Service Orders',
    columns = [],
    groupBy = null,
    statusBar = { enabled: false },
    expansion = { enabled: false },
    header = { enabled: true },
    filters = [],
    actions = { enabled: false },
    serviceFields = [],
  } = config;

  useEffect(() => {
    if (!statusBar.enabled) return;
    if (!serviceFields || serviceFields.length === 0) return;

    const addStatusBars = () => {
      data.forEach((row) => {
        const rowElement = document.querySelector(`.dynamic-row-${row[statusBar.idField || 'id']}`);

        if (rowElement) {
          const existingBars = rowElement.querySelectorAll('.row-progress-bars');
          existingBars.forEach(bar => bar.remove());

          const statusData = row[statusBar.field];

          if (!statusData || statusData.shouldShow === false) return;

          const cells = rowElement.querySelectorAll('td');

          let firstServiceCell = null;
          let lastServiceCell = null;

          columns.forEach((col, idx) => {
            const isServiceColumn = serviceFields.some(sf =>
              col.field && col.field.toLowerCase() === sf.toLowerCase()
            ) || col.type === 'status';

            if (isServiceColumn) {
              const cellIndex = expansion.enabled ? idx + 2 : idx + 1;
              const cell = cells[cellIndex];
              if (cell) {
                if (!firstServiceCell) firstServiceCell = cell;
                lastServiceCell = cell;
              }
            }
          });

          if (!firstServiceCell || !lastServiceCell) return;

          const rowRect = rowElement.getBoundingClientRect();
          const firstCellRect = firstServiceCell.getBoundingClientRect();
          const lastCellRect = lastServiceCell.getBoundingClientRect();

          const leftOffset = firstCellRect.left - rowRect.left;
          const width = lastCellRect.right - firstCellRect.left;

          const progressContainer = document.createElement('div');
          progressContainer.className = 'row-progress-bars';
          progressContainer.style.cssText = `
            position: absolute !important;
            bottom: 0 !important;
            left: ${leftOffset}px !important;
            width: ${width}px !important;
            height: 4px !important;
            z-index: 10 !important;
            pointer-events: none !important;
            display: flex !important;
          `;

          const barColor = statusData.color === 'green' ? '#22c55e' : '#ef4444';

          const progressBar = document.createElement('div');
          progressBar.className = 'progress-bar-container';
          progressBar.style.cssText = `
            width: 100%;
            height: 100%;
            background-color: rgba(229, 231, 235, 0.5);
            overflow: hidden;
          `;

          const progress = document.createElement('div');
          progress.className = 'progress-bar';
          progress.style.cssText = `
            width: ${statusData.progress}%;
            height: 100%;
            background-color: ${barColor};
            transition: width 0.3s ease;
          `;

          progressBar.appendChild(progress);
          progressContainer.appendChild(progressBar);

          rowElement.appendChild(progressContainer);
        }
      });
    };

    const timer1 = setTimeout(addStatusBars, 50);
    const timer2 = setTimeout(addStatusBars, 200);
    const timer3 = setTimeout(addStatusBars, 500);

    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, [data, expandedRows, statusBar, columns, expansion, serviceFields]);

  const getGroupStatistics = useCallback((groupData) => {
    if (!config.groupStatistics) return null;

    const stats = {};
    config.groupStatistics.forEach((stat) => {
      if (stat.type === 'count') {
        stats[stat.name] = groupData.length;
      } else if (stat.type === 'countIf') {
        stats[stat.name] = groupData.filter(stat.condition).length;
      }
    });
    return stats;
  }, [config.groupStatistics]);

  const headerTemplate = useCallback((data) => {
    if (!groupBy) return null;

    const groupValue = data[groupBy.field];
    const groupData = config.data?.filter(item => item[groupBy.field] === groupValue) || [];
    const stats = getGroupStatistics(groupData);

    return (
      <div className="date-summary-row">
        <span className="date-text">{groupValue}</span>
        {stats && (
          <span className="summary-text">
            {Object.entries(stats).map(([key, value]) => `${key} ${value}`).join(' ')}
          </span>
        )}
        {groupBy.actions && (
          <Button
            icon={groupBy.actions.icon || 'pi pi-cog'}
            className="p-button-text p-button-sm"
            onClick={() => onEvent?.('groupAction', { group: groupValue, data: groupData })}
          />
        )}
      </div>
    );
  }, [groupBy, config.data, getGroupStatistics, onEvent]);

  const rowExpansionTemplate = useCallback((rowData) => {
    if (!expansion.enabled || !expansion.tabs) return null;

    const currentTabIndex = activeTabIndex[rowData[statusBar.idField || 'id']] || 0;

    return (
      <div className="expansion-panel" ref={expansionRef}>
        {pointerPosition.visible && (
          <div
            className="expansion-pointer"
            style={{
              position: 'absolute',
              top: '-10px',
              left: pointerPosition.left,
              width: 0,
              height: 0,
              borderLeft: '10px solid transparent',
              borderRight: '10px solid transparent',
              borderBottom: '10px solid red',
              zIndex: 12,
            }}
          />
        )}
        <div className="expansion-header-bar">
          <Button
            icon="pi pi-times"
            className="p-button-rounded p-button-text close-expansion-btn"
            onClick={() => {
              setExpandedRows(null);
              setPointerPosition({ ...pointerPosition, visible: false });
            }}
          />
        </div>
        <TabView
          activeIndex={currentTabIndex}
          onTabChange={(e) => {
            setActiveTabIndex({
              ...activeTabIndex,
              [rowData[statusBar.idField || 'id']]: e.index,
            });
            onEvent?.('tabChange', { rowData, tabIndex: e.index });
          }}
        >
          {expansion.tabs.map((tab, index) => (
            <TabPanel key={index} header={tab.header} leftIcon={tab.icon}>
              <div className="expansion-content">
                {tab.render ? tab.render(rowData) : <div>{tab.content}</div>}
              </div>
            </TabPanel>
          ))}
        </TabView>
      </div>
    );
  }, [expansion, activeTabIndex, statusBar.idField, onEvent, pointerPosition]);

  const handleStatusClick = useCallback((rowData, tabIndex, cellRect) => {
    pendingPointerCalculation.current = { rowData, tabIndex, cellRect };
    setExpandedRows({ [rowData[statusBar.idField || 'id']]: true });
    setActiveTabIndex({ ...activeTabIndex, [rowData[statusBar.idField || 'id']]: tabIndex });
    onEvent?.('statusClick', { rowData, tabIndex });
  }, [activeTabIndex, statusBar.idField, onEvent]);

  useEffect(() => {
    if (
      pendingPointerCalculation.current &&
      expandedRows &&
      expansionRef.current &&
      tableRef.current
    ) {
      const timer = setTimeout(() => {
        const { rowData, tabIndex, cellRect } = pendingPointerCalculation.current;
        const tableRect = tableRef.current.getBoundingClientRect();
        const expansionRect = expansionRef.current.getBoundingClientRect();
        if (tableRect && expansionRect) {
          const relativeLeft = cellRect.left - tableRect.left + cellRect.width / 2 - (expansionRect.left - tableRect.left);
          setPointerPosition({ left: relativeLeft, visible: true });
        } else {
          setPointerPosition({ left: 0, visible: false }); 
        }
        pendingPointerCalculation.current = null;
      }, 50); 
      return () => clearTimeout(timer);
    }
  }, [expandedRows]);

  const createClickableStatus = useCallback((rowData, field, tabIndex, tabName) => {
    const status = rowData[field];
    const statusClass = `status-badge status-${status?.toLowerCase()} clickable-status`;
    const cellRef = useRef(null);

    useEffect(() => {
      if (expandedRows && expandedRows[rowData[statusBar.idField || 'id']] && cellRef.current) {
        const cellRect = cellRef.current.getBoundingClientRect();
        handleStatusClick(rowData, tabIndex, cellRect);
      }
    }, [expandedRows, rowData, tabIndex, handleStatusClick]);

    return (
      <span
        ref={cellRef}
        className={statusClass}
        onClick={(e) => {
          e.stopPropagation();
          const cellRect = e.target.getBoundingClientRect();
          handleStatusClick(rowData, tabIndex, cellRect);
        }}
        style={{ cursor: 'pointer' }}
        title={`Click to view ${tabName} details`}
      >
        {status}
      </span>
    );
  }, [handleStatusClick, statusBar.idField]);

  const renderColumnBody = useCallback((rowData, column) => {
    if (column.body) {
      return column.body(rowData);
    }

    if (column.type === 'status' && column.clickable) {
      return createClickableStatus(rowData, column.field, column.tabIndex || 0, column.tabName || column.header);
    }

    if (column.type === 'composite') {
      return (
        <div className={column.className || 'composite-cell'}>
          {column.fields.map((subField, idx) => (
            <div key={idx} className={subField.className}>
              {rowData[subField.field]}
            </div>
          ))}
        </div>
      );
    }

    if (column.type === 'route') {
      const routeData = rowData[column.field];
      return (
        <div className="route-cell">
          <span className="route-from">{routeData.from}</span>
          <i className="pi pi-arrow-right" style={{ fontSize: '10px', margin: '0 4px' }}></i>
          <span className="route-to">{routeData.to}</span>
        </div>
      );
    }

    if (column.type === 'object') {
      const objData = rowData[column.field];
      return (
        <div className={column.className || 'object-cell'}>
          {column.displayFields?.map((df, idx) => (
            <div key={idx} className={df.className}>
              {objData[df.field]}
            </div>
          ))}
        </div>
      );
    }

    return rowData[column.field];
  }, [createClickableStatus]);

  const renderActions = useCallback((rowData) => {
    if (!actions.enabled || !actions.buttons) return null;

    return (
      <div className="action-buttons">
        {actions.buttons.map((action, idx) => (
          <Button
            key={idx}
            icon={action.icon}
            className={action.className || 'p-button-rounded p-button-text p-button-sm'}
            onClick={() => {
              if (action.onClick) {
                action.onClick(rowData);
              }
              onEvent?.('action', { action: action.name, rowData });
            }}
            tooltip={action.tooltip}
          />
        ))}
      </div>
    );
  }, [actions, onEvent]);

  const renderHeader = useMemo(() => {
    if (!header.enabled) return null;

    return (
      <div className="table-header">
        <div className="header-left">
          <h2>{title}</h2>
        </div>
        <div className="header-center">
          {header.search && (
            <span className="p-input-icon-left">
              <i className="pi pi-search" />
              <InputText
                value={globalFilter}
                onChange={(e) => setGlobalFilter(e.target.value)}
                placeholder={header.search.placeholder || 'Search...'}
              />
            </span>
          )}
          {filters.map((filter, idx) => (
            <Dropdown
              key={idx}
              value={filter.value}
              options={filter.options}
              onChange={(e) => {
                if (filter.onChange) {
                  filter.onChange(e.value);
                }
                onEvent?.('filterChange', { filter: filter.name, value: e.value });
              }}
              placeholder={filter.placeholder}
            />
          ))}
        </div>
        <div className="header-right">
          {header.actions?.map((action, idx) => (
            <Button
              key={idx}
              icon={action.icon}
              label={action.label}
              className={action.className || 'p-button-sm'}
              onClick={() => {
                if (action.onClick) {
                  action.onClick();
                }
                onEvent?.('headerAction', { action: action.name });
              }}
            />
          ))}
        </div>
      </div>
    );
  }, [header, title, globalFilter, filters, onEvent]);

  return (
    <div className={`dynamic-service-order-table ${className}`} style={style} ref={tableRef}>
      {renderHeader}

      <DataTable
        value={data}
        expandedRows={expandedRows}
        onRowToggle={(e) => setExpandedRows(e.data)}
        rowExpansionTemplate={expansion.enabled ? rowExpansionTemplate : null}
        dataKey={statusBar.idField || 'id'}
        className="service-orders-table"
        rowGroupMode={groupBy ? 'subheader' : null}
        groupRowsBy={groupBy?.field}
        sortMode="single"
        sortField={groupBy?.field}
        sortOrder={1}
        rowGroupHeaderTemplate={groupBy ? headerTemplate : null}
        rowClassName={(rowData) => `service-order-row dynamic-row-${rowData[statusBar.idField || 'id']}`}
        globalFilter={globalFilter}
      >
        {expansion.enabled && <Column expander style={{ width: '3em' }} />}
        
        {columns.map((col, idx) => (
          <Column
            key={idx}
            field={col.field}
            header={col.header}
            body={(rowData) => renderColumnBody(rowData, col)}
            style={col.style}
          />
        ))}

        {actions.enabled && (
          <Column
            body={renderActions}
            style={actions.style || { width: '120px' }}
          />
        )}
      </DataTable>
    </div>
  );
};

DynamicServiceOrderTable.propTypes = {
  config: PropTypes.object.isRequired,
  data: PropTypes.array.isRequired,
  onEvent: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object,
};

export default DynamicServiceOrderTable;