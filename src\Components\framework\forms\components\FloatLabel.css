/* FloatLabel styles for PrimeReact v9 compatibility */
.p-float-label {
  display: block;
  position: relative;
}

.p-float-label > label {
  position: absolute;
  pointer-events: none;
  top: 50%;
  margin-top: -0.5rem;
  transition: 0.2s ease all;
  color: var(--text-color-secondary, #6c757d);
  left: 0.75rem;
}

.p-float-label > .p-inputwrapper-focus ~ label,
.p-float-label > input:focus ~ label,
.p-float-label > input.p-filled ~ label,
.p-float-label > .p-inputwrapper-filled ~ label,
.p-float-label > .p-calendar.p-calendar-w-btn .p-inputtext:focus ~ label,
.p-float-label > .p-calendar.p-calendar-w-btn input.p-filled ~ label,
.p-float-label > textarea:focus ~ label,
.p-float-label > textarea.p-filled ~ label,
.p-float-label > .p-dropdown:focus ~ label,
.p-float-label > .p-dropdown.p-filled ~ label,
.p-float-label > .p-multiselect:focus ~ label,
.p-float-label > .p-multiselect.p-filled ~ label {
  top: -0.75rem;
  font-size: 12px;
  background: var(--surface-ground, #ffffff);
  padding: 2px 4px;
  margin-left: -4px;
}

/* For textarea specific handling */
.p-float-label > textarea ~ label {
  top: 1rem;
}

.p-float-label > textarea:focus ~ label,
.p-float-label > textarea.p-filled ~ label {
  top: -0.75rem;
}
