import axios from 'axios';

// ========================================
// FAKE API CONFIGURATION
// ========================================
// Replace this URL with your actual API endpoint
const FAKE_API_BASE_URL = 'https://jsonplaceholder.typicode.com';

// ========================================
// REAL API CONFIGURATION (REPLACE WHEN READY)
// ========================================
// Uncomment and modify this when you have your real API
// const API_BASE_URL = 'https://your-real-api.com/api';
// const API_ENDPOINTS = {
//   USERS: '/users',
//   DEPARTMENTS: '/departments',
//   ROLES: '/roles'
// };

class UserService {
  constructor() {
    // Using fake API for now
    this.baseURL = FAKE_API_BASE_URL;
    
    // When you switch to real API, uncomment this:
    // this.baseURL = API_BASE_URL;
  }

  // ========================================
  // FAKE API METHODS (CURRENT IMPLEMENTATION)
  // ========================================

  /**
   * Get all users with fake data
   * REPLACE THIS: When you have real API, replace the entire method
   */
  async getAllUsers() {
    try {
      // Using fake API that returns posts, we'll transform to user format
      const response = await axios.get(`${this.baseURL}/users`);
      
      // Transform fake data to match our user structure
      const transformedUsers = response.data.map((user, index) => ({
        id: `ASME-${String(user.id).padStart(4, '0')}`,
        firstName: user.name.split(' ')[0] || 'John',
        lastName: user.name.split(' ')[1] || 'Doe',
        role: this.getRandomRole(),
        email: user.email,
        lastLoginDate: this.getRandomDate(),
        activity: 'View Logs',
        status: Math.random() > 0.5,
        phone: user.phone,
        website: user.website,
        company: user.company.name,
        address: user.address
      }));

      return {
        success: true,
        data: transformedUsers,
        total: transformedUsers.length
      };
    } catch (error) {
      console.error('Error fetching users:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * Get users by department
   * REPLACE THIS: When you have real API, replace with actual endpoint
   */
  async getUsersByDepartment(department) {
    try {
      const allUsers = await this.getAllUsers();
      
      if (!allUsers.success) {
        return allUsers;
      }

      let filteredUsers = allUsers.data;
      
      if (department && department !== 'all') {
        filteredUsers = allUsers.data.filter(user => 
          user.role.toLowerCase().includes(department.toLowerCase())
        );
      }

      return {
        success: true,
        data: filteredUsers,
        total: filteredUsers.length
      };
    } catch (error) {
      console.error('Error fetching users by department:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * Search users by name or email
   * REPLACE THIS: When you have real API, replace with actual search endpoint
   */
  async searchUsers(searchTerm) {
    try {
      const allUsers = await this.getAllUsers();
      
      if (!allUsers.success) {
        return allUsers;
      }

      const filteredUsers = allUsers.data.filter(user =>
        user.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.id.toLowerCase().includes(searchTerm.toLowerCase())
      );

      return {
        success: true,
        data: filteredUsers,
        total: filteredUsers.length
      };
    } catch (error) {
      console.error('Error searching users:', error);
      return {
        success: false,
        error: error.message,
        data: []
      };
    }
  }

  /**
   * Create new user
   * REPLACE THIS: When you have real API, replace with actual POST endpoint
   */
  async createUser(userData) {
    try {
      // Simulate API call
      const response = await axios.post(`${this.baseURL}/users`, userData);
      
      return {
        success: true,
        data: {
          ...userData,
          id: `ASME-${String(Date.now()).slice(-4)}`,
          lastLoginDate: new Date().toLocaleDateString(),
          activity: 'View Logs',
          status: true
        },
        message: 'User created successfully'
      };
    } catch (error) {
      console.error('Error creating user:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Update user
   * REPLACE THIS: When you have real API, replace with actual PUT endpoint
   */
  async updateUser(userId, userData) {
    try {
      // Simulate API call
      const response = await axios.put(`${this.baseURL}/users/${userId}`, userData);
      
      return {
        success: true,
        data: { ...userData, id: userId },
        message: 'User updated successfully'
      };
    } catch (error) {
      console.error('Error updating user:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete user
   * REPLACE THIS: When you have real API, replace with actual DELETE endpoint
   */
  async deleteUser(userId) {
    try {
      // Simulate API call
      await axios.delete(`${this.baseURL}/users/${userId}`);
      
      return {
        success: true,
        message: 'User deleted successfully'
      };
    } catch (error) {
      console.error('Error deleting user:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Get department statistics
   * REPLACE THIS: When you have real API, replace with actual endpoint
   */
  async getDepartmentStats() {
    try {
      // Simulate department statistics
      return {
        success: true,
        data: {
          'all': 80,
          'sales': 15,
          'finance': 15,
          'fuel-supply': 15,
          'operational': 15,
          'marketing': 15,
          'legal': 15
        }
      };
    } catch (error) {
      console.error('Error fetching department stats:', error);
      return {
        success: false,
        error: error.message,
        data: {}
      };
    }
  }

  // ========================================
  // HELPER METHODS (REMOVE WHEN USING REAL API)
  // ========================================

  getRandomRole() {
    const roles = ['Sales', 'Finance', 'Fuel Supply', 'Operational', 'Marketing', 'Legal', 'Sales Support'];
    return roles[Math.floor(Math.random() * roles.length)];
  }

  getRandomDate() {
    const start = new Date(2024, 0, 1);
    const end = new Date();
    const randomDate = new Date(start.getTime() + Math.random() * (end.getTime() - start.getTime()));
    return randomDate.toLocaleDateString();
  }
}

// ========================================
// REAL API IMPLEMENTATION TEMPLATE
// ========================================
/*
// Uncomment and modify this when you have your real API

class RealUserService {
  constructor() {
    this.baseURL = 'https://your-real-api.com/api';
    this.endpoints = {
      USERS: '/users',
      DEPARTMENTS: '/departments',
      SEARCH: '/users/search'
    };
  }

  async getAllUsers(page = 1, limit = 10) {
    try {
      const response = await axios.get(`${this.baseURL}${this.endpoints.USERS}`, {
        params: { page, limit }
      });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to fetch users: ${error.message}`);
    }
  }

  async getUsersByDepartment(department, page = 1, limit = 10) {
    try {
      const response = await axios.get(`${this.baseURL}${this.endpoints.USERS}`, {
        params: { department, page, limit }
      });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to fetch users by department: ${error.message}`);
    }
  }

  async searchUsers(searchTerm, page = 1, limit = 10) {
    try {
      const response = await axios.get(`${this.baseURL}${this.endpoints.SEARCH}`, {
        params: { q: searchTerm, page, limit }
      });
      return response.data;
    } catch (error) {
      throw new Error(`Failed to search users: ${error.message}`);
    }
  }

  async createUser(userData) {
    try {
      const response = await axios.post(`${this.baseURL}${this.endpoints.USERS}`, userData);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to create user: ${error.message}`);
    }
  }

  async updateUser(userId, userData) {
    try {
      const response = await axios.put(`${this.baseURL}${this.endpoints.USERS}/${userId}`, userData);
      return response.data;
    } catch (error) {
      throw new Error(`Failed to update user: ${error.message}`);
    }
  }

  async deleteUser(userId) {
    try {
      await axios.delete(`${this.baseURL}${this.endpoints.USERS}/${userId}`);
      return { success: true };
    } catch (error) {
      throw new Error(`Failed to delete user: ${error.message}`);
    }
  }
}
*/

// Export the service instance
export default new UserService();
