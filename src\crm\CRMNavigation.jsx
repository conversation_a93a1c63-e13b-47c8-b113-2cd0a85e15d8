import React from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { Button } from 'primereact/button';
import { Badge } from 'primereact/badge';

const CRMNavigation = () => {
  const navigate = useNavigate();
  const location = useLocation();

  const isActive = (path) => {
    return location.pathname.startsWith(path);
  };

  return (
    <div className="mb-4">
      <div className="nav-buttons">
        <Button
          label="Leads"
          icon="pi pi-users"
          className={`nav-button ${isActive('/lead') || location.pathname === '/' ? 'active' : ''}`}
          onClick={() => navigate('/')}
        />
        <Button
          label="Prospects"
          icon="pi pi-briefcase"
          className={`nav-button ${isActive('/prospect') ? 'active' : ''}`}
          onClick={() => navigate('/prospects')}
        />
        <Button
          label="Customers"
          icon="pi pi-user-plus"
          className={`nav-button ${isActive('/customer') ? 'active' : ''}`}
          onClick={() => navigate('/customers')}
        />
      </div>
    </div>
  );
};

export default CRMNavigation;

