import React from "react";
import { TabView, TabPanel } from "primereact/tabview";
import { Badge } from "primereact/badge";
import PropTypes from "prop-types";
import "../../styles/glassy/glassy-ui.css";
import "../../styles/glassy/global.css";

const GlassyTabs = ({
  tabs = [],
  activeIndex = 0,
  onTabChange,
  className = "",
  variant = "default", // default, pills, underline
  showBadges = true,
  hidePanels = false,
  ...props
}) => {
  const handleTabChange = (e) => {
    if (onTabChange) {
      onTabChange(e.index, tabs[e.index]);
    }
  };

  const renderTabHeader = (tab) => (
    <div className={`simple-tab-header ${tab.disabled ? 'disabled' : ''}`}>
      {tab.icon && <i className={`tab-icon ${tab.icon}`} />}
      <span className="tab-label">{tab.label}</span>
      {showBadges && tab.badge !== undefined && tab.badge !== null && (
        <Badge 
          value={tab.badge} 
          className="tab-badge"
          severity={tab.badgeColor || "info"}
        />
      )}
    </div>
  );

  if (!tabs || tabs.length === 0) {
    return <div className="no-tabs">No tabs available</div>;
  }

  return (
    <div className={`glass-tabview ${variant} ${className} ${hidePanels ? 'no-panels' : ''}`}>
      <TabView
        activeIndex={activeIndex}
        onTabChange={handleTabChange}
        className={`simple-dynamic-tabview ${variant}`}
        {...props}
      >
        {tabs.map((tab, index) => (
          <TabPanel
            key={tab.key || tab.value || index}
            header={renderTabHeader(tab)}
            disabled={tab.disabled}
          >
            {/* Empty content - tabs are used for filtering only */}
          </TabPanel>
        ))}
      </TabView>
    </div>
  );
};

GlassyTabs.propTypes = {
  tabs: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string.isRequired,
      value: PropTypes.string,
      key: PropTypes.string,
      icon: PropTypes.string,
      badge: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
      badgeColor: PropTypes.string,
      disabled: PropTypes.bool,
    })
  ).isRequired,
  activeIndex: PropTypes.number,
  onTabChange: PropTypes.func,
  className: PropTypes.string,
  variant: PropTypes.oneOf(["default", "pills", "underline"]),
  showBadges: PropTypes.bool,
  hidePanels: PropTypes.bool,
};

export default GlassyTabs;
