/**
 * Shared utilities for logging functionality
 * Used by both logger.js and logStorage.js to avoid circular dependencies
 */

// In-memory log storage
let logBuffer = [];

// Maximum number of logs to keep in memory
const MAX_LOGS = 1000;

/**
 * Format date and time in a human-readable format
 * @param {Date} date - Date object to format
 * @returns {string} Formatted date and time string
 */
export const formatDateTime = (date) => {
  const pad = (num) => String(num).padStart(2, '0');
  
  const year = date.getFullYear();
  const month = pad(date.getMonth() + 1);
  const day = pad(date.getDate());
  const hours = pad(date.getHours());
  const minutes = pad(date.getMinutes());
  const seconds = pad(date.getSeconds());
  const milliseconds = String(date.getMilliseconds()).padStart(3, '0');
  
  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}.${milliseconds}`;
};

/**
 * Add a log entry to the in-memory buffer
 * @param {Object} logEntry - Log entry to store
 */
export const addLogToBuffer = (logEntry) => {
  // Add timestamp if not present
  if (!logEntry.timestamp) {
    logEntry.timestamp = new Date().toISOString();
  }
  
  // Add formatted date/time if not present
  if (!logEntry.formattedDateTime) {
    const now = new Date(logEntry.timestamp);
    logEntry.formattedDateTime = formatDateTime(now);
  }
  
  // Add to buffer
  logBuffer.push(logEntry);
  
  // Trim buffer if it exceeds maximum size
  if (logBuffer.length > MAX_LOGS) {
    logBuffer = logBuffer.slice(-MAX_LOGS);
  }
};

/**
 * Get all logs from the buffer
 * @returns {Array} Array of log entries
 */
export const getLogs = () => {
  return [...logBuffer];
};

/**
 * Clear the log buffer
 */
export const clearLogs = () => {
  logBuffer = [];
  // We can't use the logger here to avoid circular dependencies
  console.info('[LogUtils] Log buffer cleared');
};

/**
 * Basic console logging function that doesn't depend on the logger
 * Used internally to avoid circular dependencies
 * @param {string} level - Log level
 * @param {string} message - Log message
 * @param {Object} [data] - Optional data
 */
export const consoleLog = (level, message, data = null) => {
  const now = new Date();
  const formattedTime = formatDateTime(now);
  const logPrefix = `[${formattedTime}] [${level}]`;
  
  switch (level) {
    case 'DEBUG':
      console.debug(logPrefix, message, data || '');
      break;
    case 'INFO':
      console.info(logPrefix, message, data || '');
      break;
    case 'WARN':
      console.warn(logPrefix, message, data || '');
      break;
    case 'ERROR':
      console.error(logPrefix, message, data || '');
      break;
    default:
      console.log(logPrefix, message, data || '');
  }
};

export default {
  formatDateTime,
  addLogToBuffer,
  getLogs,
  clearLogs,
  consoleLog
};
