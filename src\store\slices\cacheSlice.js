import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export const fetchAndCache = createAsyncThunk(
  'cache/fetchAndCache',
  async ({ key, fetchFn, options = {} }, { dispatch, getState }) => {
    try {
      // Check if data exists in cache first
      const state = getState();
      const existingEntry = state.cache.entries[key];
      
      if (existingEntry && (!existingEntry.expiresAt || existingEntry.expiresAt > Date.now())) {
        return { key, data: existingEntry.data, fromCache: true };
      }

      // Fetch new data
      const data = await fetchFn();
      
      // Cache the result
      const now = Date.now();
      const entry = {
        data,
        createdAt: now,
        lastAccessed: now,
        expiresAt: options.ttl ? now + options.ttl : null,
        tags: options.tags || [],
        priority: options.priority || 1,
        strategy: options.strategy || 'lru',
        metadata: options.metadata || {},
        version: 1,
        size: JSON.stringify(data).length
      };

      dispatch(cacheSlice.actions.set({ key, entry }));
      
      return { key, data, fromCache: false };
    } catch (error) {
      throw error;
    }
  }
);

export const invalidateCache = createAsyncThunk(
  'cache/invalidate',
  async ({ keys, tags }, { dispatch }) => {
    if (keys) {
      keys.forEach(key => {
        dispatch(cacheSlice.actions.delete({ key }));
      });
    }
    
    if (tags) {
      dispatch(cacheSlice.actions.invalidateByTags({ tags }));
    }
    
    return { keys, tags };
  }
);

const initialState = {
  entries: {},
  analytics: {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalRequests: 0,
    lastCleanup: null
  },
  config: {
    maxEntries: 1000,
    defaultTTL: 5 * 60 * 1000, // 5 minutes
    strategy: 'lru'
  },
  loading: {},
  errors: {}
};

const cacheSlice = createSlice({
  name: 'cache',
  initialState,
  reducers: {
    set: (state, action) => {
      const { key, entry } = action.payload;
      
      // Apply eviction strategy if needed
      if (Object.keys(state.entries).length >= state.config.maxEntries) {
        state.entries = applyEvictionStrategy(state.entries, state.config.strategy, state.config.maxEntries);
      }
      
      state.entries[key] = entry;
    },

    get: (state, action) => {
      const { key } = action.payload;
      const entry = state.entries[key];
      
      if (entry) {
        // Update analytics
        state.analytics.hits++;
        state.analytics.totalRequests++;
        state.analytics.hitRate = (state.analytics.hits / state.analytics.totalRequests) * 100;
        
        // Update last accessed
        entry.lastAccessed = Date.now();
      } else {
        // Update analytics
        state.analytics.misses++;
        state.analytics.totalRequests++;
        state.analytics.hitRate = (state.analytics.hits / state.analytics.totalRequests) * 100;
      }
    },

    delete: (state, action) => {
      const { key } = action.payload;
      delete state.entries[key];
      delete state.loading[key];
      delete state.errors[key];
    },

    clear: (state) => {
      state.entries = {};
      state.loading = {};
      state.errors = {};
    },

    updateLastAccessed: (state, action) => {
      const { key, timestamp } = action.payload;
      if (state.entries[key]) {
        state.entries[key].lastAccessed = timestamp;
      }
    },

    invalidateByTags: (state, action) => {
      const { tags } = action.payload;
      const tagsSet = new Set(tags);
      
      Object.keys(state.entries).forEach(key => {
        const entry = state.entries[key];
        if (entry.tags && entry.tags.some(tag => tagsSet.has(tag))) {
          delete state.entries[key];
        }
      });
    },

    cleanupExpired: (state) => {
      const now = Date.now();
      const validEntries = {};
      
      Object.entries(state.entries).forEach(([key, entry]) => {
        if (!entry.expiresAt || entry.expiresAt > now) {
          validEntries[key] = entry;
        }
      });
      
      state.entries = validEntries;
      state.analytics.lastCleanup = now;
    },

    updateConfig: (state, action) => {
      state.config = { ...state.config, ...action.payload };
    },

    applyStrategy: (state, action) => {
      const { strategy, maxEntries } = action.payload;
      state.config.strategy = strategy;
      if (maxEntries) state.config.maxEntries = maxEntries;
      
      // Apply strategy immediately if over limit
      if (Object.keys(state.entries).length > state.config.maxEntries) {
        state.entries = applyEvictionStrategy(state.entries, strategy, state.config.maxEntries);
      }
    },

    importEntries: (state, action) => {
      const entries = action.payload;
      const now = Date.now();
      
      // Filter out expired entries during import
      Object.entries(entries).forEach(([key, entry]) => {
        if (!entry.expiresAt || entry.expiresAt > now) {
          state.entries[key] = entry;
        }
      });
    },

    setLoading: (state, action) => {
      const { key, loading } = action.payload;
      if (loading) {
        state.loading[key] = true;
      } else {
        delete state.loading[key];
      }
    },

    setError: (state, action) => {
      const { key, error } = action.payload;
      if (error) {
        state.errors[key] = error;
      } else {
        delete state.errors[key];
      }
    }
  },

  extraReducers: (builder) => {
    builder
      .addCase(fetchAndCache.pending, (state, action) => {
        const { key } = action.meta.arg;
        state.loading[key] = true;
        delete state.errors[key];
      })
      .addCase(fetchAndCache.fulfilled, (state, action) => {
        const { key } = action.payload;
        delete state.loading[key];
        
        if (!action.payload.fromCache) {
          // Data was fetched and cached, analytics already updated in set reducer
        }
      })
      .addCase(fetchAndCache.rejected, (state, action) => {
        const { key } = action.meta.arg;
        delete state.loading[key];
        state.errors[key] = action.error.message;
      })
      .addCase(invalidateCache.fulfilled, (state, action) => {
        // Invalidation completed
      });
  }
});

function applyEvictionStrategy(entries, strategy, maxEntries) {
  const entryCount = Object.keys(entries).length;
  if (entryCount <= maxEntries) return entries;

  switch (strategy) {
    case 'lru': {
      // Least Recently Used
      const sortedEntries = Object.entries(entries)
        .sort(([, a], [, b]) => (b.lastAccessed || 0) - (a.lastAccessed || 0));
      
      const newEntries = {};
      sortedEntries.slice(0, maxEntries).forEach(([key, entry]) => {
        newEntries[key] = entry;
      });
      
      return newEntries;
    }

    case 'fifo': {
      // First In, First Out
      const sortedEntries = Object.entries(entries)
        .sort(([, a], [, b]) => (b.createdAt || 0) - (a.createdAt || 0));
      
      const newEntries = {};
      sortedEntries.slice(0, maxEntries).forEach(([key, entry]) => {
        newEntries[key] = entry;
      });
      
      return newEntries;
    }

    case 'priority': {
      // Priority-based
      const sortedEntries = Object.entries(entries)
        .sort(([, a], [, b]) => (b.priority || 1) - (a.priority || 1));
      
      const newEntries = {};
      sortedEntries.slice(0, maxEntries).forEach(([key, entry]) => {
        newEntries[key] = entry;
      });
      
      return newEntries;
    }

    case 'size': {
      // Size-based (keep smaller entries)
      const sortedEntries = Object.entries(entries)
        .sort(([, a], [, b]) => (a.size || 0) - (b.size || 0));
      
      const newEntries = {};
      sortedEntries.slice(0, maxEntries).forEach(([key, entry]) => {
        newEntries[key] = entry;
      });
      
      return newEntries;
    }

    default:
      // Default to LRU
      return applyEvictionStrategy(entries, 'lru', maxEntries);
  }
}

// Selectors
export const selectCacheEntry = (state, key) => state.cache.entries[key];
export const selectCacheEntries = (state) => state.cache.entries;
export const selectCacheAnalytics = (state) => state.cache.analytics;
export const selectCacheConfig = (state) => state.cache.config;
export const selectCacheLoading = (state, key) => state.cache.loading[key] || false;
export const selectCacheError = (state, key) => state.cache.errors[key];
export const selectCacheStats = (state) => {
  const entries = state.cache.entries;
  const now = Date.now();
  
  return {
    totalEntries: Object.keys(entries).length,
    expiredEntries: Object.values(entries).filter(entry => 
      entry.expiresAt && entry.expiresAt <= now
    ).length,
    totalSize: Object.values(entries).reduce((sum, entry) => sum + (entry.size || 0), 0),
    oldestEntry: Math.min(...Object.values(entries).map(e => e.createdAt || now)),
    newestEntry: Math.max(...Object.values(entries).map(e => e.createdAt || 0)),
    ...state.cache.analytics
  };
};

export const { 
  set, 
  get, 
  delete: deleteEntry, 
  clear, 
  updateLastAccessed, 
  invalidateByTags, 
  cleanupExpired, 
  updateConfig, 
  applyStrategy, 
  importEntries,
  setLoading,
  setError
} = cacheSlice.actions;

export default cacheSlice.reducer;
