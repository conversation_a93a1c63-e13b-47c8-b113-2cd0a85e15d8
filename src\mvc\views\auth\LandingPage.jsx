"use client"

import AuthHeader from "@components/auth/AuthHeader";
import "primeicons/primeicons.css"
// import AuthHeader from "../../Components/auth/AuthHeader"
import syncIcon from "@assets/Images/authencation/Icon-57.svg"
import secureIcon from "@assets/Images/authencation/secure.svg"
import { userTypes } from "@utils/menus/menus";
// import { userTypes } from "../../Utils/menus/menus"

export default function Index() {
  const mapTitleToRole = (title) => {
    switch (title) {
      case "ASM Admin":
        return "asm-admin";
      case "ASM Employee":
        return "asm-employee";
      case "Customer Admin":
        return "customer-admin";
      case "Customer User":
        return "customer-user";
      default:
        return "";
    }
  };

  const handleNavigate = (userType) => {
    const role = mapTitleToRole(userType);
    const suffix = role ? `?role=${encodeURIComponent(role)}` : "";
    if (role) {
      try { sessionStorage.setItem('loginRole', role); } catch {}
    }
    window.location.href = `/login${suffix}`;
  };

  const getCardBorderColor = (colorClass) => {
    switch (colorClass) {
      case "card-blue":
        return "#3b82f6"
      case "card-green":
        return "#10b981"
      case "card-purple":
        return "#8b5cf6"
      case "card-orange":
        return "#f59e0b"
      default:
        return "#e5e7eb"
    }
  }

  const getIconContainerClass = (title) => {
    switch (title) {
      case "ASM Admin":
        return "card-icon-container asm-admin mr-3"
      case "ASM Employee":
        return "card-icon-container asm-employee mr-3"
      case "Customer Admin":
        return "card-icon-container customer-admin mr-3"
      case "Customer User":
        return "card-icon-container customer-user mr-3"
      default:
        return "card-icon-container mr-3"
    }
  }

  const getCheckIconClass = (title) => {
    const baseClass = "pi pi-check mr-2 text-xs"
    if (title === "ASM Admin") {
      return `${baseClass} text-blue-600`
    }
    if (title === "ASM Employee") {
      return `${baseClass} text-green-600`
    }
    if (title === "Customer Admin") {
      return `${baseClass} text-purple-600`
    }
    if (title === "Customer User") {
      return `${baseClass} text-orange-600`
    }
    return baseClass
  }

  const getCardClass = (title) => {
    switch (title) {
      case "ASM Admin":
        return "glass-card hover-scale transition-all user-card asm-admin"
      case "ASM Employee":
        return "glass-card hover-scale transition-all user-card asm-employee"
      case "Customer Admin":
        return "glass-card hover-scale transition-all user-card customer-admin"
      case "Customer User":
        return "glass-card hover-scale transition-all user-card customer-user"
      default:
        return "glass-card hover-scale transition-all user-card"
    }
  }

  const getCardBackground = (colorClass) => {
    switch (colorClass) {
      case "card-blue":
        return "rgba(59, 130, 246, 0.05)"
      case "card-green":
        return "rgba(16, 185, 129, 0.05)"
      case "card-purple":
        return "rgba(139, 92, 246, 0.05)"
      case "card-orange":
        return "rgba(245, 158, 11, 0.05)"
      default:
        return "rgba(255, 255, 255, 0.5)"
    }
  }

  return (
    <div className="auth-page-layout landing-height">
      <div className="glass-card landing-width">
        <AuthHeader
          showLogo={false}
          title="Welcome to ASM Portal"
          subtitle="Please select your login type to continue"
          className="mb-3"
          icon={
            <div className="icon-container icon-bg-blue mb-2 landing-icon">
              <img src={syncIcon || "/placeholder.svg"} alt="icon" />
            </div>
          }
        />

        <div className="grid landing-grid justify-content-center gap-3">
          {userTypes.map((userType, index) => (
            <div
              key={index}
              className={`${getCardClass(userType.title)} p-4 cursor-pointer w-full`}
              onClick={() => handleNavigate(userType.title)}
              style={{
                background: `${getCardBackground(userType.colorClass)}`,
              }}
            >
              <div className="flex align-items-start">
                <div className={`${getIconContainerClass(userType.title)} mr-3`}>
                  <img src={userType.icon || "/placeholder.svg"} alt={userType.title} className="card-icon" />
                </div>

                <div className="flex-1">
                  <h4 className="font-semibold text-gray-500 mb-1">{userType.title}</h4>
                  <p className="text-sm text-gray-500">{userType.subtitle}</p>
                  <ul className="mt-2 list-none p-0 m-0">
                    {userType.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex align-items-center mb-2 text-sm text-gray-600">
                        <i className={getCheckIconClass(userType.title)}></i>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                <i className="pi pi-arrow-right text-gray-400 mt-1 text-lg"></i>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-4">
          <div className="flex align-items-center justify-content-center mb-3 gap-2">
            <img src={secureIcon || "/placeholder.svg"} alt="secure" className="w-1rem h-1rem" />
            <span className="text-sm font-medium text-gray-700">Secure Authentication Portal</span>
          </div>
          <p className=" text-xs md:text-sm text-gray-500 mb-2">
            All login types are protected with enterprise-grade security protocols
          </p>
          <div className="flex justify-content-center gap-6 flex-wrap divide-x divide-gray-300">
            <a href="#" className="btn-link text-primary">
              Need Help?
            </a>
            <a href="#" className="btn-link text-primary">
              Privacy Policy
            </a>
            <a href="#" className="btn-link text-primary">
              Terms of Service
            </a>
          </div>
        </div>
      </div>
    </div>
  )
}
