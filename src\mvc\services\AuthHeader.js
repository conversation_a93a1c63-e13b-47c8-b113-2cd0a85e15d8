import logger from "../../utils/logger.js";

// Create a logger instance for auth operations
const authLogger = logger.createLogger("AuthService");

/**
 * Retrieves the authentication token from localStorage
 * @returns {string|null} The authentication token or null if not found
 */
const getAccessToken = () => {
  try {
    const userStr = localStorage.getItem("user");
    if (userStr) {
      const userData = JSON.parse(userStr);
      if (userData && userData.accessToken) {
        return userData.accessToken;
      }
    }

    // Fallback to legacy token
    return localStorage.getItem("userToken");
  } catch (error) {
    authLogger.error("Error getting access token:", error);
    return null;
  }
};

export default function authHeader() {
  try {
    const token = getAccessToken();

    if (token) {
      authLogger.debug("Access token retrieved successfully");
      return {
        Authorization: `Bearer ${token}`,
        "Cache-Control": "no-cache, no-store, must-revalidate",
        Pragma: "no-cache",
        Expires: "0",
      };
    }

    authLogger.debug("No auth token found");
    return {
      "Cache-Control": "no-cache, no-store, must-revalidate",
      Pragma: "no-cache",
      Expires: "0",
    };
  } catch (error) {
    authLogger.error("Error retrieving auth token", error);
    return {
      "Cache-Control": "no-cache, no-store, must-revalidate",
      Pragma: "no-cache",
      Expires: "0",
    };
  }
}
