import React, { useCallback, useRef } from 'react';
import PropTypes from 'prop-types';
import { Menu } from 'primereact/menu';

const DynamicMenu = ({
  model = [],
  popup = false,
  appendTo,
  autoZIndex = true,
  baseZIndex = 0,
  className = '',
  style = {},
  id,
  onShow,
  onHide,
  onFocus,
  onBlur,
  size = 'normal',
  variant = 'default',
  orientation = 'vertical', 
  onEvent,
  debug = false,
  ...props
}) => {
  const menuRef = useRef(null);

  const processedModel = React.useMemo(() => {
    const processItem = (item) => {
      const processedItem = {
        ...item,
        command: (e) => {
          if (debug) {
            console.log('DynamicMenu item click:', { label: item.label, id: item.id });
          }

          if (item.command) {
            item.command(e);
          }

          if (item.onClick) {
            item.onClick(e);
          }

          if (onEvent) {
            onEvent('itemClick', { 
              item: item, 
              originalEvent: e.originalEvent,
              event: e 
            });
          }
        }
      };

      if (item.items && Array.isArray(item.items)) {
        processedItem.items = item.items.map(processItem);
      }

      return processedItem;
    };

    return model.map(processItem);
  }, [model, onEvent, debug]);

  const handleShow = useCallback((e) => {
    if (debug) {
      console.log('DynamicMenu show');
    }

    if (onShow) {
      onShow(e);
    }

    if (onEvent) {
      onEvent('show', { event: e });
    }
  }, [onShow, onEvent, debug]);

  const handleHide = useCallback((e) => {
    if (debug) {
      console.log('DynamicMenu hide');
    }

    if (onHide) {
      onHide(e);
    }

    if (onEvent) {
      onEvent('hide', { event: e });
    }
  }, [onHide, onEvent, debug]);

  const handleFocus = useCallback((e) => {
    if (onFocus) {
      onFocus(e);
    }

    if (onEvent) {
      onEvent('focus', { event: e });
    }
  }, [onFocus, onEvent]);

  const handleBlur = useCallback((e) => {
    if (onBlur) {
      onBlur(e);
    }

    if (onEvent) {
      onEvent('blur', { event: e });
    }
  }, [onBlur, onEvent]);

  const menuClasses = [
    'dynamic-menu',
    `dynamic-menu--${size}`,
    `dynamic-menu--${variant}`,
    `dynamic-menu--${orientation}`,
    popup ? 'dynamic-menu--popup' : '',
    className
  ].filter(Boolean).join(' ');

  const menuProps = {
    ref: menuRef,
    id,
    model: processedModel,
    popup,
    appendTo,
    autoZIndex,
    baseZIndex,
    className: menuClasses,
    style,
    onShow: handleShow,
    onHide: handleHide,
    onFocus: handleFocus,
    onBlur: handleBlur,
    'data-component-type': 'menu',
    'data-menu-size': size,
    'data-menu-variant': variant,
    'data-menu-orientation': orientation,
    ...props
  };

  Object.keys(menuProps).forEach(key => {
    if (menuProps[key] === undefined) {
      delete menuProps[key];
    }
  });

  React.useImperativeHandle(menuRef, () => ({
    show: (event) => {
      if (menuRef.current && popup) {
        menuRef.current.show(event);
      }
    },
    hide: () => {
      if (menuRef.current && popup) {
        menuRef.current.hide();
      }
    },
    toggle: (event) => {
      if (menuRef.current && popup) {
        menuRef.current.toggle(event);
      }
    }
  }));

  return (
    <div className="dynamic-menu-wrapper">
      {debug && (
        <div className="dynamic-menu__debug">
          <small>
            Items: {model.length} | 
            Size: {size} | 
            Variant: {variant} | 
            Orientation: {orientation} |
            Popup: {popup ? 'Yes' : 'No'}
          </small>
        </div>
      )}
      <Menu {...menuProps} />
    </div>
  );
};

DynamicMenu.propTypes = {
  model: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string,
    icon: PropTypes.string,
    command: PropTypes.func,
    onClick: PropTypes.func,
    url: PropTypes.string,
    items: PropTypes.array,
    disabled: PropTypes.bool,
    visible: PropTypes.bool,
    target: PropTypes.string,
    separator: PropTypes.bool,
    style: PropTypes.object,
    className: PropTypes.string,
    id: PropTypes.string,
    template: PropTypes.func
  })),
  popup: PropTypes.bool,
  appendTo: PropTypes.any,
  autoZIndex: PropTypes.bool,
  baseZIndex: PropTypes.number,
  className: PropTypes.string,
  style: PropTypes.object,
  id: PropTypes.string,
  onShow: PropTypes.func,
  onHide: PropTypes.func,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  variant: PropTypes.oneOf(['default', 'primary', 'secondary']),
  orientation: PropTypes.oneOf(['vertical', 'horizontal']),
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export const createMenuItem = (label, options = {}) => ({
  label,
  icon: options.icon,
  command: options.onClick || options.command,
  url: options.url,
  items: options.items,
  disabled: options.disabled || false,
  visible: options.visible !== false,
  target: options.target,
  separator: options.separator || false,
  style: options.style,
  className: options.className,
  id: options.id,
  template: options.template,
  ...options
});

export const createMenuSeparator = () => ({
  separator: true
});

export default DynamicMenu;
