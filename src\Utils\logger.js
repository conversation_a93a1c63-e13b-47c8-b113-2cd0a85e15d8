/**
 * Logger utility for application-wide logging
 * Provides different log levels and formatting options
 */
import { formatDateTime, addLogToBuffer } from './logUtils';

// Log levels with numeric values for comparison
const LOG_LEVELS = {
  DEBUG: 0,
  INFO: 1,
  WARN: 2,
  ERROR: 3,
  NONE: 4
};

// Current log level - can be changed at runtime
let currentLogLevel = LOG_LEVELS.INFO;

// Environment-specific settings
const isDevelopment = import.meta.env.DEV;
const isProduction = import.meta.env.PROD;

// Enable more detailed logs in development
if (isDevelopment) {
  currentLogLevel = LOG_LEVELS.DEBUG;
}

// Configuration options
const config = {
  // Whether to store logs in memory buffer
  storeLogsInMemory: true,
  // Whether to include sensitive data in logs
  includeSensitiveData: false,
  // Maximum log message length
  maxMessageLength: 10000
};

/**
 * Format the log message with timestamp and additional info
 * @param {string} level - Log level (DEBUG, INFO, etc.)
 * @param {string} message - Main log message
 * @param {Object} [data] - Optional data to include in the log
 * @returns {Object} Formatted log object
 */
const formatLog = (level, message, data = null) => {
  const now = new Date();
  const timestamp = now.toISOString();
  const formattedDateTime = formatDateTime(now);

  const logObject = {
    timestamp,
    formattedDateTime,
    level,
    message
  };

  if (data) {
    // Handle Error objects specially
    if (data instanceof Error) {
      logObject.error = {
        name: data.name,
        message: data.message,
        stack: data.stack
      };
    } else {
      logObject.data = data;
    }
  }

  return logObject;
};

// Flag to prevent recursive logging
let isLogging = false;

/**
 * Log a message if the current log level permits
 * @param {number} levelValue - Numeric log level
 * @param {string} levelName - String representation of log level
 * @param {string} message - Log message
 * @param {Object} [data] - Optional data to include
 */
const log = (levelValue, levelName, message, data = null) => {
  // Prevent recursive logging
  if (isLogging) {
    return;
  }

  if (levelValue >= currentLogLevel) {
    try {
      isLogging = true;
      const logObject = formatLog(levelName, message, data);
      const { formattedDateTime } = logObject;

      // In development, use console methods with appropriate styling
      if (isDevelopment) {
        const styles = {
          DEBUG: 'color: #6c757d',
          INFO: 'color: #0d6efd',
          WARN: 'color: #ffc107; font-weight: bold',
          ERROR: 'color: #dc3545; font-weight: bold'
        };

        const timeStyle = 'color: #17a2b8; font-weight: bold';
        const levelStyle = styles[levelName] || '';

        switch (levelName) {
          case 'DEBUG':
            console.debug(`%c[${formattedDateTime}] %c[${levelName}] ${message}`, timeStyle, levelStyle, data ? data : '');
            break;
          case 'INFO':
            console.info(`%c[${formattedDateTime}] %c[${levelName}] ${message}`, timeStyle, levelStyle, data ? data : '');
            break;
          case 'WARN':
            console.warn(`%c[${formattedDateTime}] %c[${levelName}] ${message}`, timeStyle, levelStyle, data ? data : '');
            break;
          case 'ERROR':
            console.error(`%c[${formattedDateTime}] %c[${levelName}] ${message}`, timeStyle, levelStyle, data ? data : '');
            break;
          default:
            console.log(`%c[${formattedDateTime}] %c[${levelName}] ${message}`, timeStyle, levelStyle, data ? data : '');
        }
      } else {
        // In production, use a more compact format but still include the formatted date/time
        const logPrefix = `[${formattedDateTime}] [${levelName}]`;

        switch (levelName) {
          case 'DEBUG':
            console.debug(logPrefix, message, data || '');
            break;
          case 'INFO':
            console.info(logPrefix, message, data || '');
            break;
          case 'WARN':
            console.warn(logPrefix, message, data || '');
            break;
          case 'ERROR':
            console.error(logPrefix, message, data || '');
            break;
          default:
            console.log(logPrefix, message, data || '');
        }
      }

      // Store log in memory buffer if enabled
      if (config.storeLogsInMemory) {
        try {
          // Clone the log object to avoid reference issues
          const logForStorage = { ...logObject };

          // Truncate message if it's too long
          if (logForStorage.message && logForStorage.message.length > config.maxMessageLength) {
            logForStorage.message = logForStorage.message.substring(0, config.maxMessageLength) + '... [truncated]';
          }

          // Remove sensitive data if configured
          if (!config.includeSensitiveData && logForStorage.data) {
            // Filter out sensitive fields like passwords, tokens, etc.
            const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];

            if (typeof logForStorage.data === 'object' && logForStorage.data !== null) {
              for (const field of sensitiveFields) {
                if (field in logForStorage.data) {
                  logForStorage.data[field] = '[REDACTED]';
                }
              }
            }
          }

          // Add to storage buffer
          addLogToBuffer(logForStorage);
        } catch (err) {
          // Use console directly to avoid infinite recursion
          console.error('Failed to store log:', err);
        }
      }
    } catch (err) {
      // If an error occurs during logging, log it directly to console
      // without going through our logging system to avoid recursion
      console.error('Error in logging system:', err);
    } finally {
      // Reset the logging flag
      isLogging = false;
    }
  }
};

/**
 * Set the current log level
 * @param {string} level - Log level name (DEBUG, INFO, WARN, ERROR, NONE)
 */
const setLogLevel = (level) => {
  if (LOG_LEVELS[level] !== undefined) {
    currentLogLevel = LOG_LEVELS[level];
    debug(`Log level set to ${level}`);
  } else {
    warn(`Invalid log level: ${level}`);
  }
};

/**
 * Get the current log level name
 * @returns {string} Current log level name
 */
const getLogLevel = () => {
  return Object.keys(LOG_LEVELS).find(key => LOG_LEVELS[key] === currentLogLevel);
};

/**
 * Log a debug message
 * @param {string} message - Log message
 * @param {Object} [data] - Optional data
 */
const debug = (message, data = null) => {
  log(LOG_LEVELS.DEBUG, 'DEBUG', message, data);
};

/**
 * Log an info message
 * @param {string} message - Log message
 * @param {Object} [data] - Optional data
 */
const info = (message, data = null) => {
  log(LOG_LEVELS.INFO, 'INFO', message, data);
};

/**
 * Log a warning message
 * @param {string} message - Log message
 * @param {Object} [data] - Optional data
 */
const warn = (message, data = null) => {
  log(LOG_LEVELS.WARN, 'WARN', message, data);
};

/**
 * Log an error message
 * @param {string} message - Log message
 * @param {Error|Object} [error] - Error object or data
 */
const error = (message, error = null) => {
  log(LOG_LEVELS.ERROR, 'ERROR', message, error);
};

/**
 * Create a logger instance with a specific context
 * @param {string} context - Context name (e.g., component or module name)
 * @returns {Object} Logger instance with context
 */
const createLogger = (context) => {
  return {
    debug: (message, data) => debug(`[${context}] ${message}`, data),
    info: (message, data) => info(`[${context}] ${message}`, data),
    warn: (message, data) => warn(`[${context}] ${message}`, data),
    error: (message, errorData) => error(`[${context}] ${message}`, errorData),
    setLogLevel,
    getLogLevel
  };
};

/**
 * Configure logger settings
 * @param {Object} options - Configuration options
 */
const configure = (options = {}) => {
  if (typeof options !== 'object') {
    warn('Invalid configuration options');
    return;
  }

  // Update configuration
  Object.assign(config, options);

  debug('Logger configuration updated', config);
};

// Export the logger functions
const logger = {
  debug,
  info,
  warn,
  error,
  setLogLevel,
  getLogLevel,
  createLogger,
  configure,
  LOG_LEVELS,

  // Expose configuration for direct access if needed
  get config() {
    return { ...config }; // Return a copy to prevent direct modification
  }
};

export default logger;
