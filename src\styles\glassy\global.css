/* ========================================
   FONT IMPORTS & BASE TYPOGRAPHY
   ======================================== */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

/* ========================================
   GLOBAL CSS UTILITIES
   Reusable styles for consistent design across pages
   ======================================== */

/* Login Page Specific Styles */
.login-forgot-btn {
  background: none !important;
  border: none !important;
  padding: 0 !important;
}

/* ===============================
   FORM TWO-COLUMN LAYOUT HELPERS
   Used in NewTicketDialog for Ticket Type/Priority
   =============================== */
.form-two-col {
  display: flex;
  gap: 12px; /* matches gap-3 */
  flex-wrap: wrap;
}

.half-col {
  flex: 1 1 50%;
  min-width: 280px; /* prevents too-narrow controls on medium screens */
}

/* Make selects fluid within half columns */

/* ===============================
   KeyDataDocVerify Panel (scoped)
   =============================== */
.keydata-doc-verify-layer {
  position: absolute;
  top: 0px;
  left: 1048.83px;
  z-index: 2002;
  width: 408px;
  min-width: 100.222px;
  transform-origin: center bottom;
  border-radius: 16px;
  overflow: hidden;
}

/* Responsive fallbacks: stack as full-width block on smaller layouts */
@media (max-width: 1400px) {
  .keydata-doc-verify-layer {
    position: static;
    width: 100%;
    max-width: 100%;
    left: auto;
    top: auto;
    z-index: auto;
  }
}

@media (max-width: 768px) {
  .keydata-doc-verify-layer {
    border-radius: 12px;
  }
}
.half-col select.responsive-dropdown,
.half-col .responsive-dropdown {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 0 !important;
}

/* Responsive fixed-width input: 380px on desktop, full-width on small */
.w-380-responsive { width: 380px; min-width: 380px; max-width: 380px; }
@media (max-width: 768px) {
  .w-380-responsive { width: 100%; min-width: 100%; max-width: 100%; }
}

@media (max-width: 768px) {
  .form-two-col { gap: 8px; }
  .half-col { flex: 1 1 100%; min-width: 100%; }
}

.login-otp-input {
  letter-spacing: 0.2em !important;
  font-size: 16px !important;
}

.login-password-instructions {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.login-password-instructions.active {
  background-color: #f8f9fa !important;
}

.login-password-instructions:not(.active) {
  background-color: transparent !important;
}

/* Base font family utility */
.font-inter {
  font-family: Inter, sans-serif;
}

/* Typography utilities */
.text-xs { font-size: 12px !important; line-height: 16px !important; }
.text-sm { font-size: 14px !important; line-height: 20px !important; }
.text-base { font-size: 16px !important; line-height: 24px !important; }
.text-lg { font-size: 18px !important; line-height: 28px !important; }
.text-xl { font-size: 20px !important; line-height: 28px !important; }
.text-2xl { font-size: 24px !important; line-height: 32px !important; }
.text-3xl { font-size: 30px; line-height: 36px; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

/* Text colors */
.text-gray-600 { color: #374151; }
.text-gray-500 { color: #9ca3af; }
.text-primary { color: #012169; }
.text-white { color: white; }




/* Calendar icon color override */
.p-calendar .p-datepicker-trigger {
    color: #012169 !important;  /* Primary color */
    background-color: transparent !important;
    border-color: #012169 !important;

}

.p-calendar .p-datepicker-trigger:hover {
    color: #0056b3 !important;  /* Lighter blue on hover */
    background-color: rgba(1, 33, 105, 0.1) !important;
}

/* ========================================
   BACKGROUND UTILITIES
   ======================================== */

   /*Landing page*/
   .landing-height{
     height: auto;
   
  }
.landing-width {
  width: auto !important;
  max-width: 900px !important;
  padding: 20px 40px !important;
  /* Ensure only this container scrolls on overflow */
  max-height: 90vh;
  overflow-y: auto;
}
.landing-icon{
    width: 32px;
    height: 32px;
  }

/* Airplane background utility */
.bg-airplane {
  background-image: url("../../assets/airoplane-1.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
}

/* Full height page utility */

/* Calendar and Filter Styles */
.filter-calendar-wrapper {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  align-items: center;
}

/* Table Controls */
.table-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  overflow: hidden;
}

/* Date Input Styles */
.date-input-container {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.calendar-input {
  width: 150px !important;
  height: 40px !important;
}

.p-calendar {
  background-color: #F4F7FE;
  border-radius: 0.5rem;
  border: 1px solid #E0E5F2;
}

.p-calendar .p-inputtext {
  font-size: 12px;
  background-color: transparent;
  border: none;
  color: #374151;
  /* height: 40px; */
  padding: 0.5rem 0.5rem 0.5rem 0.5rem;
}

.p-calendar .p-button {
  background-color: transparent;
  border: none;
  height: 40px;
}

.p-calendar .p-button-icon {
  color: #000000;
}

/* Filter Dropdown Styles */
.filter-dropdown {
  background-color: #F4F7FE;
  border-radius: 0.5rem;
  border: 1px solid #E0E5F2;
  width: 138px !important;
  height: 36px !important;
}

.filter-dropdown .p-dropdown {
  background-color: transparent;
  border: none;
  height: 36px;
}

.filter-dropdown .p-dropdown-trigger {
  background-color: transparent;
  width: 36px;
}

.filter-dropdown .p-dropdown-trigger-icon {
  color: #374151;
}

.filter-dropdown .p-dropdown-label {
  color: #374151;
  line-height: 36px;
  padding: 0 0.5rem;
}

/* Master Management Search Input */
.search-input-wrapper {
  position: relative;
  width: 100%;
  max-width: 28rem;
}

.search-input {
  width: 100%;
  padding: 0.5rem 0.75rem 0.5rem 2rem;
  background-color: #F4F7FE;
  border: 1px solid #E0E5F2;
  border-radius: 0.5rem;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #718EBF;
}

/* Master Management Buttons */
.add-master-btn {
  background-color: #1e40af;
  color: white;
  border: none;
  transition: background-color 0.2s;
}

.add-master-btn:hover {
  background-color: rgb(44, 6, 213);
}

.import-master-btn {
  background-color: #1e40af;
  color: white;
  border: none;
  transition: background-color 0.2s;
}

.import-master-btn:hover {
  background-color: rgb(44, 6, 213);
}
.page-container {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
}

/* Combine airplane background with page container */
.page-airplane {
  height: 100vh;
  background-image: url("../../assets/Images/authencation/airoplane-1.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  font-family: Inter, sans-serif;
}

/* ========================================
   MASTER MANAGEMENT STYLES
   ======================================== */

/* Input fields styling */
.master-input {
  background-color: #F4F7FE;
  border: 1px solid #E0E5F2;
  padding: 0.75rem;
}

/* Button styling */
.primary-button {
  background-color: #4318FF;
  color: white;
  border: none;
}

.success-button {
  background-color: #05CD99;
  color: white;
  border: none;
}

/* Table header styling */
.master-table-header {
  width: 35%;
  padding-bottom: 1rem;
  color: #718EBF;
  font-weight: 500;
}

.master-table-header-small {
  width: 20%;
  text-align: center;
}

.master-table-header-action {
  width: 10%;
  text-align: center;
}

/* Search input styling */
.search-input-container {
  position: relative;
  max-width: 28rem;
  width: 100%;
}

.search-input-icon {
  position: absolute;
  left: 0.75rem;
  color: #718EBF;
}

.search-input {
  padding-left: 2rem;
  width: 100%;
}

/* Status badge */
.status-badge {
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  background-color: #E8FFF3;
  color: #05CD99;
}

/* Container layouts */
.glass-panel {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
}

/* ========================================
   GLASS CARD UTILITIES
   ======================================== */

/* Base glass card effect */
.glass-card {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  border: 0.81px solid;
  border-image-source: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(153, 153, 153, 0) 100%
  );
  backdrop-filter: blur(10.67px);
  box-shadow: 0px 2.7px 8.1px 0px #ffffff inset;
  position: relative;
}
.glass-card-global {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 16px;
  border: 0.81px solid;
  border-image-source: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(153, 153, 153, 0) 100%
  );
  backdrop-filter: blur(10.67px);
  box-shadow: 0px 2.7px 8.1px 0px #ffffff inset;
  position: relative;
}

/* Glass card sizes */
.glass-card-sm {
  padding: 20px;
  max-width: 320px;
}

.glass-card-md {
  padding: 24px;
  max-width: 400px;
}

.glass-card-lg {
  padding: 33px;
  width: 448px;
  max-width: 448px;
}

.glass-card-xl {
  padding: 40px;
  max-width: 600px;
}

/* Glass card with fixed height (like login card) */
.glass-card {
  width: 448px;
  /* height: 450px; */
  max-width: 448px;
  background: #ffffff80;
  border-radius: 16px;
  padding: 33px;
  border: 0.81px solid;
  border-image-source: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(153, 153, 153, 0) 100%
  );
  backdrop-filter: blur(10.67px);
  box-shadow: 0px 2.7px 8.1px 0px #ffffff inset;
  position: relative;
  font-family: Inter, sans-serif;
}

/* ========================================
   FORM UTILITIES
   ======================================== */

/* Form container */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Form group */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* Form labels */
.form-label {
  font-family: Inter, sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #374151;
  margin-bottom: 6px;
}
.required {
  color: rgb(214, 110, 110);
}
/* Input wrapper for icons */
.input-wrapper {
  position: relative;
}

/* Base input styles */
.input-base {
  border-radius: 8px !important;
  border: 1px solid #e5e7eb !important;
  padding: 10px 36px 10px 12px !important;
  font-family: Inter, sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 20px !important;
  background: white !important;
  width: 100% !important;
  height: 40px !important;
}

.input-base:focus {
  border-color: #3b82f6 !important;
  outline: none !important;
}

/* Input without icon */
.input-no-icon {
  padding: 10px 12px !important;
}

/* Input icons */
.input-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 16px;
}

.input-icon-clickable {
  cursor: pointer;
}

/* ========================================
   BUTTON UTILITIES
   ======================================== */

/* Global button border radius */
.p-button,
button,
.btn,
.p-button.p-component {
  border-radius: 6px !important;
}

/* Base button styles */
.btn-base {
  border-radius: 6px !important;
  padding: 12px !important;
  font-family: Roboto !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 20px !important;
  cursor: pointer !important;
  transition: background-color 0.2s !important;
  width: 100% !important;
  height: 40px !important;
  border: none !important;
}

/* Primary button */
.btn-primary {
  background: #2563EB !important;
  color: #FFF !important;
  text-align: center !important;
  font-family: Roboto !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 500 !important;
  line-height: 20px !important;
  border-radius: 6px !important;
}

.btn-primary:hover:not(:disabled) {
  background: #1d4ed8 !important;
}

.btn-primary:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

/* Secondary button */
.btn-secondary {
  background: white !important;
  color: #012169 !important;
  border: 1px solid #012169 !important;
}

.btn-secondary:hover:not(:disabled) {
  background: #f8fafc !important;
}

/* ========================================
   CHECKBOX UTILITIES
   ======================================== */

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-base {
  width: 16px !important;
  height: 16px !important;
  border-radius: 4px !important;
  border-width: 1px !important;
  flex-shrink: 0 !important;
}

.checkbox-label {
  font-family: Inter, sans-serif;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  color: #374151;
  cursor: pointer;
  display: flex;
  align-items: center;
  height: 20px;
}

/* ========================================
   LINK UTILITIES
   ======================================== */

.link-primary {
  font-family: Inter, sans-serif;
  font-weight: 400;
  font-size: 14px;
  line-height: 20px;
  color: #012169;
  text-decoration: none;
}

.link-primary:hover {
  text-decoration: underline;
}

/* ========================================
   LAYOUT UTILITIES
   ======================================== */


   /*COLORS*/
   .color-blue{color: #193276;}
   
   /*custom heights*/
.custom-max-h-500{max-height: 500px;}
.custom-min-h-200{min-height: 200px;}
   /* Width Utilities */
   /* Width utilities */
   .w-10p { width: 10%; }
   .w-15p { width: 15%; }
   .w-20p { width: 20%; }
   .w-50px { width: 50px; }
   .w-55px { width: 55px; }
   .w-60px { width: 60px; }
   .w-70px { width: 70px; }
   .w-3rem { width: 3rem; }
   
   /* Font size utilities */
   .text-xxs { font-size: 10px; }
   .text-xs-11 { font-size: 11px; }
   /*custom widths*/
   .custom-min-w-250{min-width: 250px;}
   .custom-min-w-400{min-width: 400px;}
   .custom-min-w-500{min-width: 500px;}
   .custom-min-w-600{min-width:600px;}

   /*font-size*/
   .font-2rem {font-size: 2rem; /* 32px */}

   /*border top*/
   .custom-border-top {
    border-top: 1px solid rgba(229, 231, 235, 0.3);
  }
  
/*Custom color*/
.text-blue-custom {
  color: #193276;
}
.text-gray-custom {
  color: #666;
}

/* Flexbox utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }

/* Spacing utilities */
.gap-1 { gap: 4px; }
.gap-2 { gap: 8px; }
.gap-3 { gap: 12px; }
.gap-4 { gap: 16px; }
.gap-5 { gap: 20px; }
.gap-6 { gap: 24px; }
.gap-8 { gap: 32px; }

/* Misc utilities */
.no-resize { resize: none !important; }
.font-inherit { font-family: inherit !important; }

/* Margin utilities */
.m-0 { margin: 0; }
.m-1 { margin: 4px; }
.m-2 { margin: 8px; }
.m-3 { margin: 12px; }
.m-4 { margin: 16px; }
.m-5 { margin: 20px; }
.m-6 { margin: 24px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 4px; }
.mt-2 { margin-top: 8px; }
.mt-3 { margin-top: 12px; }
.mt-4 { margin-top: 16px; }
.mt-5 { margin-top: 20px; }
.mt-6 { margin-top: 24px; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 4px; }
.mb-2 { margin-bottom: 8px; }
.mb-3 { margin-bottom: 12px; }
.mb-4 { margin-bottom: 16px; }
.mb-5 { margin-bottom: 20px; }
.mb-6 { margin-bottom: 24px; }

.ml-0 { margin-left: 0; }
.ml-1 { margin-left: 4px; }
.ml-2 { margin-left: 8px; }
.ml-3 { margin-left: 12px; }
.ml-4 { margin-left: 16px; }

.mr-0 { margin-right: 0; }
.mr-1 { margin-right: 4px; }
.mr-2 { margin-right: 8px; }
.mr-3 { margin-right: 12px; }
.mr-4 { margin-right: 16px; }

/* Padding utilities */
.p-0 { padding: 0; }
.p-1 { padding: 4px; }
.p-2 { padding: 8px; }
.p-3 { padding: 12px; }
.p-4 { padding: 16px; }
.p-5 { padding: 20px; }
.p-6 { padding: 24px; }
.p-10 { padding: 40px; }

.pt-0 { padding-top: 0; }
.pt-1 { padding-top: 4px; }
.pt-2 { padding-top: 8px; }
.pt-3 { padding-top: 12px; }
.pt-4 { padding-top: 16px; }
.pt-5 { padding-top: 20px; }
.pt-6 { padding-top: 24px; }

.pb-0 { padding-bottom: 0; }
.pb-1 { padding-bottom: 4px; }
.pb-2 { padding-bottom: 8px; }
.pb-3 { padding-bottom: 12px; }
.pb-4 { padding-bottom: 16px; }
.pb-5 { padding-bottom: 20px; }
.pb-6 { padding-bottom: 24px; }

/* Width utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.max-w-xs { max-width: 320px; }
.max-w-sm { max-width: 384px; }
.max-w-md { max-width: 448px; }
.max-w-lg { max-width: 512px; }
.max-w-xl { max-width: 576px; }

.w-23{width: 23%;}
.w-48{width: 48%;}

/* Height utilities */
.h-full { height: 100%; }
.h-screen { height: 100vh; }
.h-auto { height: auto; }

/* Position utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }

/*text-align*/
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }
.text-start { text-align: start; }
.text-end { text-align: end; }

.custom-absolute {
  position: absolute;
  top: 12px;
  left: 12px;
  font-size: 18px;
  color: #9CA3AF;
  z-index: 1;
}

.custom-container {
  width: 100%;
  height: 200px;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 8px;
}

.custom-image {
  max-width: 100% !important;
  max-height: 100% !important;
  width: auto !important;
  height: auto !important;
  object-fit: contain !important;
}

.custom-button {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.6);
  border: none;
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  color: white;
}

/* ========================================
   LOGO SECTION UTILITIES
   ======================================== */

.logo-section {
  width: 100%;
  max-width: 382px;
  height: 48px;
  margin: 0 auto 32px auto; /* center on all screens */
  padding-bottom: 24px;
}

.logo-image {
  width: 232px;
  height: auto;
  display: block;      /* ensure image itself is centered */
  margin: 0 auto;      /* center within its container */
}

/* ========================================
   FORM OPTIONS UTILITIES
   ======================================== */

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 6px 0;
}

/* ========================================
   RESPONSIVE UTILITIES
   ======================================== */

/* Tablet and below */
@media (max-width: 768px) {
  .page-airplane {
    padding: 10px;
  }

  .glass-card,
  .glass-card-lg {
    max-width: 350px;
    padding: 24px;
  }

  .logo-section {
    margin-bottom: 24px;
  }

  /* Responsive text sizes */
  .text-responsive-sm {
    font-size: 14px;
  }

  .text-responsive-xs {
    font-size: 9px;
  }

  /* Hide PrimeReact tooltips on small screens (e.g., sidebar hints) */
  .p-tooltip { display: none !important; }
}

/* Mobile */
@media (max-width: 480px) {
  .glass-card,
  .glass-card-lg,
  .glass-card-md {
    padding: 20px;
    max-width: 320px;
  }

  .text-responsive-sm {
    font-size: 12px;
  }

  .form-options {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }

  /* Mobile spacing adjustments */
  .gap-mobile-3 { gap: 12px; }
  .mt-mobile-4 { margin-top: 16px; }

  /* Ensure Keep me signed in and Forgot Password stay on one line on small screens */
  .form-options {
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
  }
  .form-options .checkbox-wrapper { gap: 6px; flex-wrap: nowrap; }
  .checkbox-label,
  .login-forgot-btn {
    white-space: nowrap;
    font-size: 12px !important;
    line-height: 18px !important;
    font-family: Inter, sans-serif !important;
    font-weight: 500 !important;
  }
}

/* Slightly wider mobile support to ensure 12px applies on common devices */
@media (max-width: 576px) {
  .form-options .btn-link.login-forgot-btn,
  .form-options .login-forgot-btn.link-primary,
  .form-options .login-forgot-btn {
    font-size: 12px !important;
    line-height: 18px !important;
    white-space: nowrap;
    font-family: Inter, sans-serif !important;
    font-weight: 500 !important;
  }
}

/* Ensure consistency on small tablets too */
@media (max-width: 768px) {
  .auth-page-layout .glass-card .form-options .login-forgot-btn,
  .auth-page-layout .glass-card .form-options .login-forgot-btn.link-primary,
  .auth-page-layout .glass-card .form-options .btn-link.login-forgot-btn {
    font-size: 12px !important;
    line-height: 18px !important;
    white-space: nowrap;
  }
  /* match Keep me signed in label size as well */
  .auth-page-layout .glass-card .form-options .checkbox-label {
    font-size: 12px !important;
    line-height: 18px !important;
    white-space: nowrap;
  }
}

/* Ultra-specific mobile override to guarantee 12px on Forgot Password */
@media (max-width: 768px) {
  .auth-page-layout .glass-card .form-options button.login-forgot-btn.btn-link.link-primary[type="button"] {
    font-size: 12px !important;
    line-height: 18px !important;
  }
}

/* Desktop and up */
@media (min-width: 992px) { 
  .landing-width {
    /* Do not scroll the container on desktop */
    overflow: hidden;
    max-height: none;
    /* Slightly tighter padding to gain space without changing width */
    padding: 24px 32px !important;
  }

  .landing-grid {
    display: grid;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: 12px;
    align-items: stretch;
  }

  /* Override fixed glass-card width within landing grid to allow 2x2 fit */
  .landing-grid .glass-card {
    width: 100% !important;
    max-width: none !important;
  }

  /* Tighten card spacing so 2x2 fits neatly */
  .landing-grid .user-card {
    padding: 12px; /* overrides p-4 */
    height: 100%;
  }
}

/* ========================================
   UTILITY COMBINATIONS
   ======================================== */

/* Common page layout with airplane background and glass card */
.auth-page-layout {
  /* Fixed viewport height so page background doesn't scroll */
  height: 100vh;
  height: 100dvh;

  background-image: url("../../assets/Images/authencation/airoplane-1.png");
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;

  display: flex;
  align-items: center;
  justify-content: center;

  /* Base padding for very small screens */
  padding: 12px;
  box-sizing: border-box;
  position: relative;
  font-family: Inter, sans-serif;
  /* Prevent body scroll; inner container will handle its own scroll */
  overflow: hidden;
}

/* Safe-area support on iOS (not harmful elsewhere) */
@supports (padding: max(0px)) {
  .auth-page-layout {
    padding-top: max(12px, env(safe-area-inset-top));
    padding-bottom: max(12px, env(safe-area-inset-bottom));
    padding-left: max(12px, env(safe-area-inset-left));
    padding-right: max(12px, env(safe-area-inset-right));
  }
}

/* Breakpoints: adjust vertical padding to create balanced gaps */
@media (min-width: 576px) { /* SM */
  .auth-page-layout {
    padding: 16px;
  }
}

@media (min-width: 768px) { /* MD / Tablets */
  .auth-page-layout {
    padding: 24px;
  }
}

@media (min-width: 992px) { /* LG / Laptops */
  .auth-page-layout {
    padding-top: 64px;
    padding-bottom: 64px;
    padding-left: 24px;
    padding-right: 24px;
  }
}

@media (min-width: 1200px) { /* XL / Desktops */
  .auth-page-layout {
    padding-top: 72px;
    padding-bottom: 72px;
  }
}



/* Common form layout */
.auth-form-layout {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Common input with icon layout */
.input-with-icon {
  border-radius: 8px !important;
  border: 1px solid #e5e7eb !important;
  padding: 10px 36px 10px 12px !important;
  font-family: Inter, sans-serif !important;
  font-weight: 500 !important;
  font-size: 14px !important;
  line-height: 20px !important;
  background: white !important;
  width: 100% !important;
  height: 40px !important;
}

.input-with-icon:focus {
  border-color: #3b82f6 !important;
  outline: none !important;
}

/* ========================================
   ANIMATION UTILITIES
   ======================================== */

.transition-all {
  transition: all 0.2s ease-in-out;
}

.transition-colors {
  transition: background-color 0.2s, border-color 0.2s, color 0.2s;
}

.hover-scale:hover {
  transform: scale(1.02);
}

.hover-opacity:hover {
  opacity: 0.8;
}

/* ========================================
   SHADOW UTILITIES
   ======================================== */

.shadow-sm {
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.shadow {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-md {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-lg {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Glass shadow (inset white shadow for glassmorphism) */
.shadow-glass {
  box-shadow: 0px 2.7px 8.1px 0px #ffffff inset;
}

/* ========================================
   ICON BACKGROUND UTILITIES
   ======================================== */

/* Icon background circles */
.icon-bg-blue {
  background-color: #dbeafe;
  color: #2563eb;
}

.icon-bg-green {
  background-color: #dcfce7;
  color: #16a34a;
}

.icon-bg-red {
  background-color: #fee2e2;
  color: #dc2626;
}

.icon-bg-yellow {
  background-color: #fef3c7;
  color: #d97706;
}

/* Icon container */
.icon-container {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

/* Card icons styling */
.card-icon {
  display: flex;
  width: 20px;
  height: 28px;
  align-items: center;
  flex-shrink: 0;
}

/* Card icon container styling */
.card-icon-container {
  display: flex;
  width: 48px;
  height: 48px;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
  border-radius: 8px;
}

/* Card icon container background colors */
.card-icon-container.asm-admin {
  background: #DBEAFE;
}

.card-icon-container.asm-employee {
  background: #DCFCE7;
}

.card-icon-container.customer-admin {
  background: #F3E8FF;
}

.card-icon-container.customer-user {
  background: #FFEDD5;
}

/* Card hover effects */
.user-card {
  transition: border-color 0.3s ease;
  border: 2px solid transparent;
}

.user-card:hover {
  border-color: var(--hover-border-color);
}

.user-card.asm-admin:hover {
  border-color: #3b82f6;
}

.user-card.asm-employee:hover {
  border-color: #10b981;
}

.user-card.customer-admin:hover {
  border-color: #8b5cf6;
}

.user-card.customer-user:hover {
  border-color: #f59e0b;
}

/* ========================================
   DASHBOARD LAYOUT STYLES
   ======================================== */

/* Dashboard Layout */
.dashboard-layout {
  display: flex;
  height: 100vh;
  background-color: #f8fafc;
}

/* Main Content Area */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  transition: margin-left 0.3s ease;
  overflow-y: auto;
  overflow-x: hidden;
  margin-top: 70px; /* Account for header height */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

/* Hide scrollbar for WebKit browsers */
.main-content::-webkit-scrollbar {
  display: none;
}

.main-content.sidebar-expanded {
  margin-left: 287px; /* Updated to match sidebar width */
}

.main-content.sidebar-collapsed {
  margin-left: 80px;
}

/* ========================================
   DASHBOARD RESPONSIVE (Mobile/Tablet)
   Make sidebar off-canvas and content full-width without impacting desktop
   ======================================== */
@media (max-width: 992px) {
  /* Navbar: show hamburger only on mobile/tablet */
  .dashboard-navbar .navbar-menu-btn {
    display: inline-flex;
    margin-right: 8px;
  }
  /* Allow navbar to adapt spacing */
  .dashboard-navbar .navbar-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 8px;
    padding: 8px 12px;
  }
  .dashboard-navbar .navbar-left,
  .dashboard-navbar .navbar-right {
    display: flex;
    align-items: center;
    gap: 8px;
    min-width: 0; /* allow children to shrink */
  }
  /* Make dropdown take flexible space, icons fixed */
  .dashboard-navbar .navbar-right .company-dropdown { flex: 1 1 auto; min-width: 0; }
  .dashboard-navbar .navbar-right .navbar-action-btn,
  .dashboard-navbar .navbar-right .notification-wrapper,
  .dashboard-navbar .navbar-right .user-avatar-section { flex: 0 0 auto; }
  /* Constrain company dropdown on small screens */
  .dashboard-navbar .company-dropdown {
    max-width: 55vw;
    min-width: 140px;
  }
}

/* Desktop: hide hamburger */
@media (min-width: 993px) {
  .dashboard-navbar .navbar-menu-btn { display: none; }
}

/* Small devices: tighten navbar below 676px */
@media (max-width: 676px) {
  .dashboard-navbar { padding-left: 0 !important; }
  .dashboard-navbar .navbar-content {
    padding: 4px 8px; /* tighter padding */
    padding-left: 0 !important; /* remove extra left space */
    gap: 4px;        /* reduce gaps */
  }
  .dashboard-navbar .navbar-left,
  .dashboard-navbar .navbar-right {
    gap: 4px;
    flex-wrap: nowrap;
  }
  .dashboard-navbar .navbar-left { padding: 0 !important; }
  .dashboard-navbar .navbar-menu-btn { margin: 0 !important; }
  .dashboard-navbar .navbar-menu-btn.p-button { padding: 2px !important; }
  .dashboard-navbar .navbar-menu-btn .p-button-icon { font-size: 0.95rem; margin: 0 !important; }
  /* ensure logo container has no implicit margin */
  .dashboard-navbar .navbar-svg-placeholder { margin-left: 0 !important; }
  /* Show a compact company dropdown instead of hiding it */
  .dashboard-navbar .company-dropdown {
    display: inline-flex !important;
    max-width: 40vw; /* narrower to fit icons */
    min-width: 120px;
  }
  .dashboard-navbar .company-dropdown .p-dropdown-label {
    font-size: 11px; /* reduce text size only, keep default padding */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  /* keep default dropdown padding; only constrain overall width */
  /* Compact action buttons */
  .dashboard-navbar .navbar-action-btn.p-button { padding: 2px 4px !important; }
  .dashboard-navbar .navbar-action-btn .p-button-icon { font-size: 0.9rem; }
  /* Notification badge smaller */
  .dashboard-navbar .notification-badge {
    transform: scale(0.75);
    transform-origin: top right;
    right: -2px;
    top: -2px;
  }
  /* Avatar compact */
  .dashboard-navbar .user-avatar-btn { padding: 2px 4px !important; }
  .dashboard-navbar .user-avatar { width: 24px; height: 24px; }
  .dashboard-navbar .user-initials { font-size: 11px; }
  /* Logo sizing */
  .dashboard-navbar .navbar-svg-placeholder img { max-height: 20px; height: 20px; width: auto; }

  /* PrimeReact dropdown panel sizing on small screens */
  .p-dropdown-panel { max-width: 65vw; min-width: 120px; }
  .p-dropdown-panel .p-dropdown-items .p-dropdown-item { font-size: 12px; }
}

/* Sidebar becomes an overlay drawer for mobile/tablet */
@media (max-width: 992px) {
  .sidebar {
    position: fixed;
    top: 70px; /* below header */
    left: 0;
    width: 80vw; /* responsive width */
    max-width: 287px; /* keep same desktop max */
    height: calc(100vh - 70px);
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    box-shadow: 0 10px 25px rgba(0,0,0,0.15);
    z-index: 1100; /* above content */
  }

  /* When expanded, slide in */
  .sidebar.expanded {
    transform: translateX(0);
    width: 80vw;
    max-width: 287px;
  }

  /* Content should not be pushed on mobile/tablet */
  .main-content,
  .main-content.sidebar-expanded,
  .main-content.sidebar-collapsed {
    margin-left: 0 !important;
  }

  /* Ensure content scrolls independently and remains visible */
  .content-area {
    overflow-y: auto;
    -webkit-overflow-scrolling: touch;
  }
}

.content-area {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background-color: #f8fafc;
}

/* ========================================
   SIDEBAR STYLES
   ======================================== */

.sidebar {
  position: fixed;
  left: 0;
  top: 70px; /* Position below header */
  width: 287px;
  height: calc(100vh - 70px); /* Full height minus header */
  max-height: 960px;
  background: linear-gradient(180deg, #C5D4E8 0%, #E8F0F8 50%, #F5F8FC 100%);
  color: #374151;
  transition: width 0.3s ease;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  border-right: 1px solid #e5e7eb;
  padding-right: 1px;
  opacity: 1;
  overflow-y: auto;
}

/* Hide scrollbar for sidebar */
.sidebar::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

.sidebar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}
.sidebar:hover {
  overflow-y: auto;
  background-color: red;
}
.menu-item.active:hover {
  background-color: #1e40af !important;
  color: white !important;
}

.sidebar.expanded {
  width: 288px;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar.collapsed .menu-item {
  justify-content: center;
  padding: 12px 8px;
  margin: 4px 8px;
}

.sidebar.collapsed .menu-item span {
  display: none;
}

.sidebar.collapsed .menu-item i {
  margin: 0;
}

.sidebar.collapsed .system-status {
  width: 48px;
  height: 48px;
  padding: 8px;
  margin: 16px 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.sidebar.collapsed .system-status .status-text,
.sidebar.collapsed .system-status .status-subtitle {
  display: none;
}

.sidebar.collapsed .system-status .status-icon {
  font-size: 16px;
}

/* ASM Employee Header */
.sidebar-admin-header {
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 80px;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
}

.admin-avatar {
  width: 40px;
  height: 40px;
  background: #1e40af;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.admin-icon {
  font-size: 18px;
  color: white;
}

.admin-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.admin-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0;
  color: #1e40af;
  font-family: 'Roboto', sans-serif;
}

.admin-subtitle {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
  font-family: 'Roboto', sans-serif;
}

.admin-document-icon {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  flex-shrink: 0;
}

.admin-document-icon i {
  font-size: 16px;
}

/* Sidebar Toggle Button */
.sidebar-toggle-btn {
  color: #6b7280 !important;
  background: transparent !important;
  border: none !important;
  padding: 4px !important;
  width: 24px !important;
  height: 24px !important;
  min-width: 24px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
}

.sidebar-toggle-btn:hover {
  background: rgba(107, 114, 128, 0.1) !important;
  color: #374151 !important;
}

.sidebar-toggle-btn i {
  font-size: 14px !important;
}

/* Collapsed Header */
.sidebar-collapsed-header {
  padding: 16px 12px;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  min-height: 80px;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
}

.sidebar-collapsed-header .admin-avatar {
  width: 32px;
  height: 32px;
}

.sidebar-collapsed-header .admin-icon {
  font-size: 16px;
}

.sidebar-toggle-btn.collapsed {
  margin-top: 8px;
}

.sidebar-toggle {
  color: white !important;
  border: none !important;
  background: transparent !important;
}

.sidebar-toggle:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

/* Middle Toggle Button */
.sidebar-middle-toggle {
  position: absolute;
  top: 50%;
  right: -15px;
  transform: translateY(-50%);
  z-index: 1050;
  pointer-events: auto;
}

.sidebar-toggle-btn-middle {
  color: #6b7280 !important;
  background: rgba(255, 255, 255, 0.95) !important;
  border: 2px solid rgba(0, 0, 0, 0.2) !important;
  padding: 2px !important;
  width: 36px !important;
  height: 36px !important;
  min-width: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: flex-start !important;
  border-radius: 50% !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.25) !important;
  position: relative !important;
}

.sidebar-toggle-btn-middle:hover {
  background: rgba(255, 255, 255, 1) !important;
  color: #374151 !important;
  transform: scale(1.1) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2) !important;
}

.sidebar-toggle-btn-middle img {
  width: 20px !important;
  height: 20px !important;
  opacity: 0.8;
  transition: opacity 0.2s ease;
  object-fit: contain !important;
  max-width: 100% !important;
  max-height: 100% !important;
}

.sidebar-toggle-btn-middle:hover img {
  opacity: 1;
}

/* Sidebar Menu */
.sidebar-menu {
  flex: 1;
  padding: 6px 0;
  overflow-y: visible;
  min-height: auto;
}

.menu-nav {
  display: flex;
  flex-direction: column;
  gap: 0;
  padding: 0;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  margin: 4px 16px;
  border-radius: 8px;
  color: #4a5568;
  text-decoration: none;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  background: transparent;
}

.menu-item:hover {
  background: rgba(255, 255, 255, 0.2);
  color: #2d3748;
}

.menu-item.active {
  background: #1e40af;
  color: white !important;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(30, 64, 175, 0.3);
}

.menu-item i {
  font-size: 16px;
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.menu-item.active i {
  color: white;
}

.menu-label {
  width: 68px;
  height: 20px;
  opacity: 1;
  font-family: Roboto;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0px;
  vertical-align: middle;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
}

/* Sidebar Footer */
.sidebar-footer {
  margin-top: auto;
  padding: 16px;
}

.system-status {
  width: 255px;
  height: 66px;
  opacity: 1;
  border-radius: 8px;
  border: 1px solid #BBF7D0;
  padding: 13px;
  background: #F0FDF4;
  /* margin: 16px; */
  margin-top: auto;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.online {
  background: #22c55e;
}

.status-text {
  width: 138px;
  height: 20px;
  opacity: 1;
  font-family: Roboto;
  font-weight: 500;
  font-size: 14px;
  line-height: 20px;
  letter-spacing: 0px;
  vertical-align: middle;
  color: #166534;
}

.status-subtitle {
  width: 124px;
  height: 16px;
  opacity: 1;
  font-family: Roboto;
  font-weight: 400;
  font-size: 12px;
  line-height: 16px;
  letter-spacing: 0px;
  vertical-align: middle;
  color: #16A34A;
  margin: 0;
}



/* ========================================
   DASHBOARD NAVBAR STYLES
   ======================================== */

.dashboard-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
  background: linear-gradient(90deg, #B8C5D6 0%, #D4DDE8 50%, #E8EDF4 100%);
  border-bottom: 1px solid rgba(229, 231, 235, 0.3);
  padding: 0 24px;
  height: 70px;
  display: flex;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  z-index: 1001; /* Above sidebar */
}

.navbar-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 0 0 auto;
}

.navbar-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 0 0 auto;
}

/* Hamburger Menu Button */
.hamburger-btn {
  color: #6b7280 !important;
  border: none !important;
  background: transparent !important;
  padding: 8px !important;
}

.hamburger-btn:hover {
  background: #f3f4f6 !important;
  color: #374151 !important;
}

/* SVG Placeholder Section */
.navbar-svg-placeholder {
  display: flex;
  align-items: center;
  gap: 8px;
}

.svg-placeholder {
  width: 120px;
  height: 40px;
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #6b7280;
  font-size: 12px;
  font-weight: 500;
  font-family: 'Roboto', sans-serif;
}

/* Company Dropdown - Now in Right Section */
.navbar-right .company-dropdown {
  min-width: 280px;
  margin-right: 8px;
}

.navbar-right .company-dropdown .p-dropdown {
  background: rgba(255, 255, 255, 0.8) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 6px !important;
  height: 40px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
}

.navbar-right .company-dropdown .p-dropdown-label {
  color: #374151 !important;
  font-family: 'Roboto', sans-serif !important;
  font-size: 14px !important;
  font-weight: 500 !important;
}

.navbar-right .company-dropdown .p-dropdown-trigger {
  color: #6b7280 !important;
}

.navbar-right .company-dropdown .p-dropdown-panel {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(15px) !important;
  -webkit-backdrop-filter: blur(15px) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  border-radius: 6px !important;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15) !important;
}

.navbar-right .company-dropdown .p-dropdown-panel .p-dropdown-items .p-dropdown-item {
  color: #374151 !important;
  font-family: 'Roboto', sans-serif !important;
  font-size: 14px !important;
  padding: 8px 12px !important;
}

.navbar-right .company-dropdown .p-dropdown-panel .p-dropdown-items .p-dropdown-item:hover {
  background: rgba(37, 99, 235, 0.1) !important;
  color: #1e40af !important;
}





/* Navbar Action Buttons */
.navbar-action-btn {
  color: #6b7280 !important;
  background: rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
  -webkit-backdrop-filter: blur(10px) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  width: 40px !important;
  height: 40px !important;
  border-radius: 50% !important;
}

.navbar-action-btn:hover {
  background: rgba(255, 255, 255, 0.5) !important;
  color: #374151 !important;
}

.notification-wrapper {
  position: relative;
  display: inline-block;
}

.notification-btn {
  color: #ffffff !important;
  border: none !important;
  background: transparent !important;
  padding: 8px !important;
}

.notification-btn:hover {
  background: #e0e0e0 !important;
}

.notification-badge {
  position: absolute;
  top: -2px;
  right: -2px;
  background: #ef4444;
  color: white;
  border-radius: 50%;
  width: 18px;
  height: 18px;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  z-index: 1;
}

/* User Avatar Section */
.user-avatar-section {
  position: relative;
}

.user-avatar-btn {
  display: flex;
  align-items: center;
  padding: 0;
  border: none !important;
  background: transparent !important;
}

.user-avatar-btn:hover {
  background: transparent !important;
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: #1e40af;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
}

.user-initials {
  font-size: 14px;
  font-weight: 600;
  color: white;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  background: transparent !important;
}

.user-name {
  font-weight: 600;
  color: #111827 !important;
  line-height: 1.2;
  background: transparent !important;
}

.user-email {
  font-size: 12px;
  color: #6b7280 !important;
  line-height: 1.2;
  background: transparent !important;
}

.user-menu {
  min-width: 200px;
}

.user-role-item {
  background: #eff6ff;
  color: #1d4ed8;
  font-weight: 600;
}

/* ========================================
   DASHBOARD CONTAINER STYLES
   ======================================== */

.dashboard-container {
  max-width: 1400px;
  /* margin: 0 auto; */
  margin: 20px;
  padding: 0;
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
  padding: 0;
}

.welcome-section h1 {
  font-size: 28px;
  font-weight: 600;
  color: #ffff;
  margin: 0 0 8px 0;
}

.welcome-section p {
  font-size: 16px;
  color: #e5eeff;
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.company-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.company-label {
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  white-space: nowrap;
}

.company-dropdown {
  min-width: 280px;
}

.company-dropdown .p-dropdown-label {
  font-size: 14px;
  color: #374151;
}

/* Statistics Row */
.stats-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.stat-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e5e7eb;
}

.stat-card h3 {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  margin: 0 0 20px 0;
  text-align: center;
}

/* Active Users Card */
.active-users-card .chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.chart-container canvas {
  max-height: 120px !important;
}

.chart-legend {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 6px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.active1{
  background: #f97316;
}
.legend-color.active {
  background: #3b82f6;
}

.legend-color.inactive {
  background: #e5e7eb;
}

/* Project Conversation Card */
.project-conversation-card {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.circular-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.progress-circle {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: conic-gradient(#f97316 0deg 234deg, #e5e7eb 234deg 360deg);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.progress-circle::before {
  content: '';
  width: 80px;
  height: 80px;
  background: white;
  border-radius: 50%;
  position: absolute;
}

.progress-value {
  font-size: 24px;
  font-weight: 700;
  color: #111827;
  z-index: 1;
}

.progress-legend {
  display: flex;
  gap: 16px;
  font-size: 12px;
}

/* User Statistics Grid */
.user-stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.user-stat-card {
  /* background: #f8fafc; */
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  /* border: 1px solid #e5e7eb; */
}

.stat-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.stat-value {
  font-size: 32px;
  font-weight: 700;
  line-height: 1;
}

.stat-value.blue {
  color: #3b82f6;
}

.stat-value.green {
  color: #10b981;
}

.stat-value.purple {
  color: #8b5cf6;
}

.stat-value.orange {
  color: #f97316;
}

.stat-value.red {
  color: #ef4444;
}

/* ========================================
   TICKETS SECTION STYLES
   ======================================== */

.tickets-section {
  /* background: white; */
  border-radius: 12px;
  padding: 24px;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  /* border: 1px solid #e5e7eb; */
}

.tickets-header-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem 2rem;
  box-sizing: border-box;
  width: 100%;
  max-width: 100%;
  overflow-x: hidden;
  flex-wrap: wrap;
  gap: 12px;
}

.tickets-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.tickets-header h2 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.add-ticket-btn {
  background: #10b981 !important;
  border: none !important;
  width: 40px;
  height: 40px;
}

.add-ticket-btn:hover {
  background: #059669 !important;
}

/* Ticket Filter Cards */
.ticket-filters {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 16px;
  margin-bottom: 24px;
}

.filter-card {
  background: #f8fafc;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-card:hover {
  border-color: #d1d5db;
  background: #f3f4f6;
}

.filter-card.active {
  border-color: #3b82f6;
  background: #eff6ff;
}

.filter-label {
  font-size: 12px;
  color: #6b7280;
  margin-bottom: 8px;
  font-weight: 500;
}

.filter-value {
  font-size: 28px;
  font-weight: 700;
  line-height: 1;
}

/* Table Controls */
.table-controls {
  display: flex;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px; /* 16px gap between search container and filter controls */
  flex-wrap: nowrap;
  overflow-x: auto;
}

.search-container {
  position: relative;
  flex-shrink: 0;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  font-size: 14px;
}

.search-input {
  display: flex;
  width: 598px;
  height: 38px;
  padding: 9px 17px 9px 41px;
  align-items: center;
  flex-shrink: 0;
  border-radius: 8px;
  border: 1px solid #D1D5DB;
  background: #FFF;
  font-size: 14px !important;
}

.search-input:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 16px; /* 16px gap between filter dropdown and first date input */
  flex-shrink: 0;
}

/* 8px gap between the two date inputs (override the 16px gap) */
.filter-controls .date-btn + .date-btn {
  margin-left: -8px; /* This reduces the 16px gap to 8px between date inputs */
}

.filter-dropdown {
  width: 116px !important;
  height: 36px !important;
  border-radius: 8px !important;
  border: 1px solid #D1D5DB !important;
  background: #EFEFEF !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 9px 13px !important;
  box-sizing: border-box !important;
}

/* Ensure background covers the whole dropdown */
.filter-dropdown.p-dropdown {
  background: #EFEFEF !important;
  border: 1px solid #D1D5DB !important;
  border-radius: 8px !important;
  width: 116px !important;
  height: 36px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding: 9px 13px !important;
  box-sizing: border-box !important;
}

.filter-dropdown.p-dropdown .p-inputtext {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
  flex: 1 !important;
}

.filter-dropdown .p-dropdown-trigger {
  width: 16px !important;
  height: 16px !important;
  color: #000 !important;
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  margin: 0 !important;
}

.filter-dropdown .p-dropdown-trigger .p-icon {
  font-size: 12px !important;
  color: #000 !important;
  transform: none !important;
}

/* Ensure no rotation or transformation on any state */
.filter-dropdown .p-dropdown-trigger .p-icon,
.filter-dropdown.p-dropdown-open .p-dropdown-trigger .p-icon,
.filter-dropdown:hover .p-dropdown-trigger .p-icon,
.filter-dropdown:focus .p-dropdown-trigger .p-icon {
  transform: none !important;
  transition: none !important;
}



.filter-dropdown .p-dropdown-label {
  color: #000 !important;
  font-family: Roboto !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 400 !important;
  line-height: 16px !important;
  overflow: hidden !important;
  white-space: nowrap !important;
  text-overflow: ellipsis !important;
  flex: 1 !important;
  padding: 0 !important;
  margin: 0 !important;
  text-align: left !important;
}

.filter-dropdown .p-dropdown-label.p-placeholder {
  color: #666 !important;
  text-align: left !important;
}

/* Hide specific default PrimeReact dropdown icons */
.filter-dropdown .p-dropdown-trigger .pi-chevron-down,
.filter-dropdown .p-dropdown-trigger .pi-chevron-up,
.filter-dropdown .p-dropdown-trigger .pi-angle-down,
.filter-dropdown .p-dropdown-trigger .pi-angle-up,
.filter-dropdown .p-dropdown-trigger .pi-sort-down,
.filter-dropdown .p-dropdown-trigger .pi-sort-up {
  display: none !important;
  visibility: hidden !important;
}

/* Ensure our caret icons are visible and styled */
.filter-dropdown .p-dropdown-trigger .pi-caret-down,
.filter-dropdown .p-dropdown-trigger .pi-caret-up {
  display: inline-block !important;
  visibility: visible !important;
  color: #000 !important;
  font-size: 12px !important;
  width: 12px !important;
  height: 12px !important;
  line-height: 1 !important;
}

/* Override PrimeReact's default icon completely */
.filter-dropdown .p-dropdown-trigger {
  background-image: none !important;
}

.filter-dropdown .p-dropdown-trigger::before,
.filter-dropdown .p-dropdown-trigger::after {
  content: none !important;
  display: none !important;
}

/* Completely override PrimeReact dropdown icon */
.filter-dropdown.p-dropdown .p-dropdown-trigger {
  background: transparent !important;
  border: none !important;
  width: 16px !important;
  height: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
}

/* Force our custom icon to show */
.filter-dropdown .p-dropdown-trigger .p-icon {
  width: 12px !important;
  height: 12px !important;
  font-size: 12px !important;
  color: #000 !important;
  line-height: 1 !important;
  display: inline-block !important;
  visibility: visible !important;
}

/* Make sure the icon is always visible */
.filter-dropdown .p-dropdown-trigger {
  opacity: 1 !important;
  visibility: visible !important;
}

.filter-dropdown .p-dropdown-trigger * {
  opacity: 1 !important;
  visibility: visible !important;
}

/* Ensure trigger area is properly sized */
.filter-dropdown .p-dropdown-trigger {
  min-width: 16px !important;
  min-height: 16px !important;
}

/* Dynamic icon switching with CSS */
.filter-dropdown .p-dropdown-trigger::after {
  content: "▼" !important;
  display: block !important;
  color: #000 !important;
  font-size: 12px !important;
  line-height: 1 !important;
  position: absolute !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
}

/* When dropdown is open, change icon to caret-up */
.filter-dropdown.p-dropdown-open .p-dropdown-trigger::after,
.filter-dropdown[aria-expanded="true"] .p-dropdown-trigger::after {
  content: "▲" !important;
}

/* Hide PrimeReact icons completely and use our CSS icons */
.filter-dropdown .p-dropdown-trigger .p-icon {
  display: none !important;
}

/* Calendar Date Buttons */
.date-btn {
  width: 150px !important;
  height: 40px !important;
  position: relative !important;
}

.date-btn .p-calendar {
  width: 150px !important;
  height: 40px !important;
  position: relative !important;
}

.date-btn .p-calendar .p-inputtext {
  width: 150px !important;
  height: 40px !important;
  padding: 9px 40px 9px 13px !important;
  border-radius: 8px !important;
  border: 1px solid #D1D5DB !important;
  background: #FFF !important;
  color: #374151 !important;
  font-size: 14px !important;
  box-shadow: none !important;
  outline: none !important;
  box-sizing: border-box !important;
}

.date-btn .p-calendar .p-inputtext:focus {
  border-color: #D1D5DB !important;
  background: #FFF !important;
  box-shadow: none !important;
  outline: none !important;
}

.date-btn .p-calendar .p-inputtext:hover {
  border-color: #9ca3af !important;
  background: #f9fafb !important;
  box-shadow: none !important;
}

/* Calendar Button Styling */
.date-btn .p-calendar .p-button {
  position: absolute !important;
  right: 4px !important;
  top: 4px !important;
  bottom: 4px !important;
  width: 32px !important;
  height: 32px !important;
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
  padding: 0 !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 10 !important;
}

.date-btn .p-calendar .p-button:hover {
  background: transparent !important;
  border-radius: 4px !important;
  box-shadow: none !important;
}

.date-btn .p-calendar .p-button:focus {
  background: transparent !important;
  box-shadow: none !important;
  outline: none !important;
}

.date-btn .p-calendar .p-button:active {
  background: transparent !important;
  box-shadow: none !important;
}

/* Calendar Icon Styling - Complete Override */
.date-btn .p-calendar .p-button .pi-calendar,
.date-btn .p-calendar .p-button .p-icon,
.date-btn .p-calendar .p-button i,
.date-btn .p-calendar .p-button span {
  color: #374151 !important;
  font-size: 18px !important;
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
  background: transparent !important;
  background-color: transparent !important;
}

.date-btn .p-calendar .p-button .pi-calendar:before {
  content: "📅" !important;
  font-size: 18px !important;
  color: #374151 !important;
  background: transparent !important;
  background-color: transparent !important;
}

/* Force remove any PrimeReact button styling */
.date-btn .p-calendar .p-button,
.date-btn .p-calendar .p-button.p-button-icon-only,
.date-btn .p-calendar .p-button.p-component,
.date-btn .p-calendar .p-inputgroup-addon,
.date-btn .p-calendar .p-calendar-button {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.date-btn .p-calendar .p-button:hover,
.date-btn .p-calendar .p-button:focus,
.date-btn .p-calendar .p-button:active,
.date-btn .p-calendar .p-button.p-focus,
.date-btn .p-calendar .p-button.p-highlight {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.date-btn .p-button-icon {
  margin-left: auto !important;
  margin-right: 0 !important;
  order: 2;
}

.date-btn .p-button-label {
  order: 1;
  flex: 1;
  text-align: left;
}

.date-btn:hover {
  border-color: #9ca3af !important;
  background: #f9fafb !important;
  box-shadow: none !important;
}

.date-btn:focus {
  border-color: #D1D5DB !important;
  background: #FFF !important;
  box-shadow: none !important;
  outline: none !important;
}



/* View All Link Styling */
.view-all-container {
  text-align: center;
  margin-top: 16px;
}

.view-all-btn {
  color: #3b82f6 !important;
  font-weight: 500 !important;
  text-decoration: none !important;
}

.view-all-btn:hover {
  color: #2563eb !important;
  text-decoration: underline !important;
}

.view-all-link {
  color: #3b82f6;
  cursor: pointer;
  font-weight: 500;
  text-decoration: underline;
}

.view-all-link:hover {
  color: #2563eb;
}

/* Ticket ID Link Styling */
.ticket-id-link {
  color: #3b82f6;
  cursor: pointer;
  font-weight: 500;
  text-decoration: none;
  transition: color 0.2s ease;
}

.ticket-id-link:hover {
  color: #2563eb;
  text-decoration: underline;
}

/* View All Below Table Styling */
.view-all-below-table {
  text-align: center;
  margin-top: 16px;
  padding: 12px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}



/* Priority Badge Styling */
.priority-badge {
  display: flex;
  min-width: 60px;
  width: auto;
  height: 22px;
  padding: 4px 8px;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border-radius: 9999px;
  font-size: 11px;
  font-weight: 500;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

/* High Priority */
.priority-badge.high {
  background: #FEF2F2;
  color: #DC2626;
}

/* Medium Priority */
.priority-badge.medium {
  background: #FFF7ED;
  color: #EA580C;
}

/* Low Priority */
.priority-badge.low {
  background: #F0FDF4;
  color: #16A34A;
}

/* View All */
.view-all-container {
  text-align: center;
  margin-top: 16px;
}

.view-all-btn {
  color: #3b82f6 !important;
  font-weight: 500 !important;
  text-decoration: none !important;
}

.view-all-btn:hover {
  color: #1d4ed8 !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .stats-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .user-stats-grid {
    grid-template-columns: repeat(4, 1fr);
  }

  .ticket-filters {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .main-content.sidebar-expanded,
  .main-content.sidebar-collapsed {
    margin-left: 0;
  }

  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .table-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-input {
    width: 100% !important;
    max-width: 400px;
  }

  .filter-controls {
    justify-content: flex-start;
    gap: 12px;
  }

  .filter-controls {
    justify-content: center;
  }

  .ticket-filters {
    grid-template-columns: 1fr;
  }

  .user-stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* ========================================
   BUTTON LINK UTILITIES
   ======================================== */

.btn-link {
  background: none !important;
  border: none !important;
  padding: 0 !important;
  font-family: Inter, sans-serif !important;
  font-weight: 400 !important;
  font-size: 14px !important;
  line-height: 20px !important;
  color: #012169 !important;
  text-decoration: none !important;
  cursor: pointer !important;
}

.btn-link:hover {
  text-decoration: underline !important;
}

.btn-link:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
}

/* ========================================
   SUCCESS MESSAGE UTILITIES
   ======================================== */

.success-message {
  background-color: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.success-message .pi-check-circle {
  color: #16a34a;
}

.success-message-text {
  color: #15803d;
  font-size: 14px;
  font-family: Inter, sans-serif;
}

/* ========================================
   ADDITIONAL BACKGROUND COLORS
   ======================================== */

.bg-green-50 {
  background-color: #f0fdf4;
}

.border-green-200 {
  border-color: #bbf7d0;
}

.text-green-600 {
  color: #16a34a;
}

.text-green-700 {
  color: #15803d;
}

/* ========================================
   ADDITIONAL SPACING UTILITIES
   ======================================== */

.gap-2 { gap: 8px; }
.p-3 { padding: 12px; }

/* ========================================
   BORDER UTILITIES
   ======================================== */

.border {
  border-width: 1px;
  border-style: solid;
}

.rounded-lg {
  border-radius: 8px;
}

/* ========================================
   LANDING PAGE UTILITIES
   ======================================== */

/* User cards grid for landing page - 2x2 layout */
.user-cards-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 20px;
  /* margin-bottom: 32px; */
  width: 100%;
}

/* Mobile responsive - stack cards vertically */
@media (max-width: 768px) {
  .user-cards-grid {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, auto);
    gap: 16px;
  }
}

/* ========================================
   PASSWORD COMPLEXITY INDICATOR STYLES
   ======================================== */

/* Password Complexity Indicator Styles */
.password-complexity-indicator {
  padding: 6px 0;
}

.password-complexity-indicator.entering {
  animation: slideInFadeIn 0.3s ease-out forwards;
}

@keyframes slideInFadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
/* Mobile responsiveness */
@media (max-width: 768px) {
  .password-complexity-indicator {
    padding: 6px;
    font-size: 14px;
  }

  .password-requirements ul li {
    font-size: 13px;
  }
}

/* ========================================
   MANAGE USERS PAGE STYLES
   ======================================== */

/* Manage Users Styles */
.manage-users-container {
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #2563EB;
  color: white;
  padding: 16px 24px;
  /* border-radius: 8px; */
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.back-btn {
  color: white !important;
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  color: white;
}

.company-dropdown-header {
  min-width: 280px;
}

.company-dropdown-header .p-dropdown {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  color: white;
}

.company-dropdown-header .p-dropdown-label {
  color: white !important;
}

.header-right {
  display: flex;
  gap: 8px;
}

.header-right .p-button {
  color: white !important;
}

.controls-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  gap: 16px;
}

.search-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-container {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  color: #6B7280;
  z-index: 1;
}

.search-input-users {
  width: 300px;
  height: 38px;
  padding: 9px 13px 9px 40px;
  border-radius: 8px;
  border: 1px solid #D1D5DB;
  background: #FFF;
  font-size: 14px;
}

.filter-dropdown-users {
  width: 116px;
  height: 36px;
}

.filter-dropdown-users .p-dropdown {
  background: #EFEFEF;
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  height: 36px;
}

.date-input-users {
  width: 150px;
  height: 40px;
}

.date-input-users .p-inputtext {
  border-radius: 8px;
  border: 1px solid #D1D5DB;
  background: #FFF;
  height: 40px;
  padding: 9px 13px;
}

.action-controls {
  display: flex;
  gap: 12px;
}

.add-employee-btn {
  background: #2563EB !important;
  border: none !important;
  color: #FFF !important;
  text-align: center !important;
  font-family: Roboto !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 500 !important;
  line-height: 20px !important;
  padding: 8px 16px;
  border-radius: 6px;
}

.export-btn,
.import-btn {
  background: #2563EB !important;
  border: none !important;
  color: #FFF !important;
  text-align: center !important;
  font-family: Roboto !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 500 !important;
  line-height: 20px !important;
  padding: 8px 16px;
  border-radius: 6px;
}

.department-tabs {
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
  flex-wrap: wrap;
}

.department-tab {
  background: #F3F4F6 !important;
  border: 1px solid #E5E7EB !important;
  color: #6B7280 !important;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 400;
  transition: all 0.2s;
}

.department-tab.active {
  background: #2563EB !important;
  border: 1px solid #2563EB !important;
  color: #FFF !important;
  text-align: center !important;
  font-family: Roboto !important;
  font-size: 14px !important;
  font-style: normal !important;
  font-weight: 500 !important;
  line-height: 20px !important;
}

.department-tab:hover {
  background: #E5E7EB !important;
  color: #374151 !important;
}

.department-tab.active:hover {
  background: #1d4ed8 !important;
  color: #FFF !important;
}

.users-table-container {
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  padding: 16px 24px 0 24px;
}

.users-table {
  width: 100%;
}

.users-table .p-datatable-thead > tr > th {
  background: #F9FAFB;
  border-bottom: 1px solid #E5E7EB;
  padding: 12px;
  font-size: 12px;
  font-weight: 600;
  color: #6B7280;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  text-align: center;
}

.users-table .p-datatable-tbody > tr > td {
  padding: 12px;
  border-bottom: 1px solid #F3F4F6;
  font-size: 14px;
  color: #374151;
  text-align: center;
}

.users-table .p-datatable-tbody > tr:hover {
  background: #F9FAFB;
}

.status-toggle {
  width: 44px !important;
  height: 24px !important;
  border-radius: 12px !important;
}

.status-toggle.p-togglebutton-checked {
  background: #10B981 !important;
  border-color: #10B981 !important;
}

.status-toggle:not(.p-togglebutton-checked) {
  background: #E5E7EB !important;
  border-color: #E5E7EB !important;
}

.activity-link {
  color: #2563EB !important;
  text-decoration: none;
  font-size: 14px;
  padding: 0 !important;
}

.activity-link:hover {
  text-decoration: underline;
}

.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-buttons .p-button {
  /* width: 32px; */
  height: 32px;
  color: #6B7280 !important;
}

.action-buttons .p-button:hover {
  color: #374151 !important;
  background: #F3F4F6 !important;
}

/* Pagination styling */
.users-table-container .p-paginator {
  background: #F9FAFB;
  border-top: 1px solid #E5E7EB;
  padding: 12px 16px;
}

.users-table-container .p-paginator .p-paginator-pages .p-paginator-page {
  background: white;
  border: 1px solid #E5E7EB;
  color: #374151;
  margin: 0 2px;
  border-radius: 4px;
}

.users-table-container .p-paginator .p-paginator-pages .p-paginator-page.p-highlight {
  background: #4F46E5;
  border-color: #4F46E5;
  color: white;
}

/* Responsive design */
@media (max-width: 1200px) {
  .controls-section {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .search-controls {
    flex-wrap: wrap;
    gap: 12px;
  }

  .action-controls {
    justify-content: flex-end;
  }
}

@media (max-width: 768px) {
  .manage-users-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .header-left {
    justify-content: center;
  }

  .department-tabs {
    justify-content: center;
  }

  .search-input-users {
    width: 100%;
    min-width: 250px;
  }

  .users-table .p-datatable-thead > tr > th,
  .users-table .p-datatable-tbody > tr > td {
    padding: 8px;
    font-size: 12px;
  }
}

/* ========================================
   DATATABLE SORT ARROWS REMOVAL
   ======================================== */

/* Hide all sort icons and indicators globally */
.p-datatable .p-datatable-thead .p-sortable-column-icon,
.p-datatable .p-datatable-thead .p-sortable-column-badge,
.p-datatable .p-datatable-thead .p-column-header-content .pi-sort-alt,
.p-datatable .p-datatable-thead .p-column-header-content .pi-sort-amount-up,
.p-datatable .p-datatable-thead .p-column-header-content .pi-sort-amount-down,
.p-datatable .p-datatable-thead .p-column-header-content .pi-sort-up,
.p-datatable .p-datatable-thead .p-column-header-content .pi-sort-down {
  display: none !important;
}

/* Remove hover effects for sortable columns */
.p-datatable .p-datatable-thead .p-sortable-column:hover {
  background: #F9FAFB !important;
  cursor: default !important;
}

/* Force center alignment for header content */
.p-datatable .p-datatable-thead .p-column-header-content {
  justify-content: center !important;
}

/* Remove any cursor pointer for headers */
.p-datatable .p-datatable-thead th {
  cursor: default !important;
}

/* ========================================
   VIEW LOG BUTTON OVERRIDE
   ======================================== */

/* Force background color for View Log button with specific classes */
.p-button-link.activity-link.p-button.p-component,
.activity-link.p-button-link.p-button.p-component,
.p-button.p-component.p-button-link.activity-link {
  background: #DBEAFE !important;
  background-color: #DBEAFE !important;
  color: #1D4ED8 !important;
  text-align: center !important;
  font-family: Roboto !important;
  font-size: 12px !important;
  font-style: normal !important;
  font-weight: 500 !important;
  line-height: 16px !important;
  display: flex !important;
  width: 95.5px !important;
  height: 24px !important;
  padding: 4px 12px !important;
  align-items: center !important;
  flex-shrink: 0 !important;
  border-radius: 9999px !important;
  text-decoration: none !important;
  border: none !important;
  justify-content: center !important;
  box-sizing: border-box !important;
}

/* Hover state for View Log button */
.p-button-link.activity-link.p-button.p-component:hover,
.activity-link.p-button-link.p-button.p-component:hover,
.p-button.p-component.p-button-link.activity-link:hover {
  background: #BFDBFE !important;
  background-color: #BFDBFE !important;
  color: #1E40AF !important;
  text-decoration: none !important;
}

/* Override any PrimeReact button-link default styles */
.p-button-link.activity-link {
  background: #DBEAFE !important;
  background-color: #DBEAFE !important;
  color: #1D4ED8 !important;
}

.p-button-link.activity-link:hover {
  background: #BFDBFE !important;
  background-color: #BFDBFE !important;
  color: #1E40AF !important;
}

/* ========================================
   GLOBAL CALENDAR BUTTON OVERRIDE
   ======================================== */

/* Force override ALL PrimeReact calendar button backgrounds */
.p-calendar .p-button,
.p-calendar .p-button.p-button-icon-only,
.p-calendar .p-button.p-component,
.p-calendar .p-inputgroup-addon,
.p-calendar .p-calendar-button,
.p-calendar-w-btn .p-button,
.p-calendar-w-btn .p-button.p-button-icon-only {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

.p-calendar .p-button:hover,
.p-calendar .p-button:focus,
.p-calendar .p-button:active,
.p-calendar .p-button.p-focus,
.p-calendar .p-button.p-highlight,
.p-calendar-w-btn .p-button:hover,
.p-calendar-w-btn .p-button:focus,
.p-calendar-w-btn .p-button:active {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  box-shadow: none !important;
  outline: none !important;
}

/* ========================================
   DIRECT CALENDAR OVERRIDE - DASHBOARD
   ======================================== */

.date-btn.p-calendar {
  width: 150px !important;
  height: 40px !important;
  position: relative !important;
}

.date-btn.p-calendar .p-inputtext {
  width: 150px !important;
  height: 40px !important;
  padding: 9px 40px 9px 13px !important;
  border-radius: 8px !important;
  border: 1px solid #D1D5DB !important;
  background: #FFF !important;
  color: #374151 !important;
  font-size: 14px !important;
  box-sizing: border-box !important;
}

.date-btn.p-calendar .p-button {
  position: absolute !important;
  right: 8px !important;
  top: 8px !important;
  width: 24px !important;
  height: 24px !important;
  background: transparent !important;
  border: none !important;
  border-radius: 4px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.date-btn.p-calendar .p-button .pi-calendar:before {
  content: "📅" !important;
  font-size: 16px !important;
  color: #111827 !important;
}
.p-checkbox .p-checkbox-box{
  width: 17px !important;
    height: 17px !important;
}

/* Add this CSS globally (App.css or component-level CSS) */
.p-fileupload .p-fileupload-buttonbar {
  display: none !important; /* hides choose/upload/cancel bar */
}
.p-fileupload .p-fileupload-content {
  display: block !important; /* ensures content area is visible */
}
.p-fileupload-content{
  background: transparent;
  color: #000;
  border: none;
  text-align: center;
  line-height: 24px;
}

/*char related css */

.client-chart-box {
  /* background: white; */
  height: 337px;
  padding: 24px;
  border-radius: 12px;
 }

.client-chart-box .p-chart {
  height: 100%;
}

/* .p-datatable .p-datatable-thead > tr > th,
.p-datatable .p-datatable-tbody > tr > td {
  text-align: center !important;
  vertical-align: middle !important;
}

.p-datatable .p-datatable-thead > tr > th {
  justify-content: center !important;
}

.p-datatable .p-datatable-tbody > tr > td > * {
  margin: 0 auto;
  display: flex;
  justify-content: center;
}

.p-datatable .p-checkbox {
  display: inline-flex;
  vertical-align: middle;
} */

/* Ensure action buttons are properly centered */
.action-buttons {
  justify-content: center !important;
}

/*Dynamic form css*/

.content-section label{
  font-family: Roboto;
  font-weight: 500;
  font-size: 14px;
}

.content-section input::placeholder{
  font-family: Roboto;
  font-weight: 400;
  font-size: 12px;
}

.content-section .p-dropdown-label{
  font-family: Roboto;
  font-weight: 400;
  font-size: 12px;
}

.content-section small.text-color-secondary{
 
  font-family: Roboto;
  font-weight: 400;
  font-size: 12px;
}



/*Add User css*/

.custom-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 20px;
}

.custom-text-style {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}
/*title*/
.custom-title {
  margin: 0;
  color: #193276;
  font-size: 24px;
  font-weight: 600;
}
/*Customer css*/

.customer-header-gap{
  display: flex;
  align-items: center;
  justify-content: start;
  gap: 20px;
  flex-wrap: wrap;
  }

  .blue-text{
    color: #1E66F5; }

    /*Vendor*/

    .vendor-custom-title {
      margin: 0;
      color: #193276;
      font-size: 24px;
      font-weight: 600;
  }
  

  .vendor-custom-header{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom:20px;
    flex-wrap:wrap;
  }

/*New Ticket Dialog*/
 /* Container */
.status-group {
  display: flex;
  gap: 1rem; /* gap-4 in tailwind/primeflex */
}

/* Base button */
.status-btn {
  border-radius: 16px;
  padding: 0.4rem 1rem;
  font-size: 13px;
  font-weight: 400;
  cursor: pointer;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: #4b5563;
  background-color: rgba(255, 255, 255, 0.4);
  box-shadow: none;
}

/* Active state */
.status-btn.active {
  color: white;
  border: none;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-weight: 500;
}

/* Status-specific active colors */
.status-btn.draft.active {
  background-color: #3b82f6; /* Blue */
}

.status-btn.open.active {
  background-color: #f59e0b; /* Amber */
}

.status-btn.resolved.active {
  background-color: #10b981; /* Green */
}

/* Responsive tweaks */
@media screen and (max-width: 768px) {
  .status-btn {
    border-radius: 12px;
    padding: 0.3rem 0.75rem;
    font-size: 12px;
  }
}

@media screen and (max-width: 480px) {
  .status-btn {
    border-radius: 10px;
    padding: 0.25rem 0.5rem;
    font-size: 11px;
  }
}


/* Responsive dropdown style */
/* Ensure this targets native select with higher specificity and wins cascade */
select.responsive-dropdown,
.glass-dropdown.responsive-dropdown {
  width: 11.25rem !important;        /* 180px */
  min-width: 11.25rem !important;    /* 180px */
  max-width: 11.25rem !important;    /* 180px */
  font-size: 0.8125rem;              /* 13px */
  padding: 0.5rem 0.75rem;           /* 8px 12px */
  border-radius: 0.5rem;             /* 8px */
  border: 1px solid rgba(255, 255, 255, 0.3);
  background: white;
  color: #374151;
  box-sizing: border-box;
}

/* Tablet screens (~768px) */
@media (max-width: 768px) {
  select.responsive-dropdown,
  .glass-dropdown.responsive-dropdown {
    width: 9.375rem;       /* 150px */
    min-width: 9.375rem;
    max-width: 9.375rem;
  }
}

/* Mobile screens (~480px) */
@media (max-width: 480px) {
  select.responsive-dropdown,
  .glass-dropdown.responsive-dropdown {
    width: 7.5rem;         /* 120px */
    min-width: 7.5rem;
    max-width: 7.5rem;
  }
}

/* Base file upload container */
.file-upload-container {
  border: 2px dashed rgba(59, 130, 246, 0.3);
  border-radius: 0.75rem; /* 12px */
  padding: 2rem;
  text-align: center;
  background-color: rgba(59, 130, 246, 0.05);
  backdrop-filter: blur(10px);
  cursor: pointer;
  transition: all 0.3s ease;
}

/* Uploaded state */
.file-upload-container.uploaded {
  border: 2px solid rgba(16, 185, 129, 0.4);
  background-color: rgba(16, 185, 129, 0.05);
}

/* Icon styles */
.file-upload-icon {
  font-size: 2rem;
  display: block;
  margin-bottom: 0.75rem;
}

.file-upload-icon.uploaded {
  color: #10b981;
}

.file-upload-icon.default {
  color: #3b82f6;
}

/* File name */
.file-upload-name {
  margin: 0;
  color: #4b5563;
  font-size: 0.875rem; /* 14px */
  font-weight: 500;
}

/* File size */
.file-upload-size {
  margin: 0.25rem 0 0 0;
  color: #6b7280;
  font-size: 0.75rem; /* 12px */
}

/* Remove button */
.file-upload-remove {
  margin-top: 0.5rem;
  color: #ef4444;
}

/* ===============================
   NewTicketDialog Footer Bar
   =============================== */
.nt-footer-bar {
  padding: 1rem 1.5rem; /* 16px 24px */
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem; /* 12px */
  margin: 0.5rem 0 0 0; /* 8px top */
  border-radius: 0 0 16px 16px;
}

/* ===============================
   ManageApproval page styles (scoped, class-based)
   =============================== */
.ma-header-card {
  /* use with glass-card-global */
  margin: 1.5rem; /* 24px */
  margin-bottom: 1rem; /* 16px */
  padding: 1.5rem; /* 24px */
}

.ma-container {
  margin: 0 20px;
  width: calc(100% - 40px);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.ma-inner {
  padding: 2rem; /* 32px */
}

.approval-cards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem; /* 24px */
  margin-bottom: 2rem; /* 32px */
}

.ma-quick-actions { margin-bottom: 2rem; }
.ma-quick-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem; /* 16px */
}
.ma-quick-title { font-size: 18px; font-weight: 600; color: #374151; margin: 0; }
.ma-selected-count { font-size: 14px; color: #6b7280; }
.quick-actions-buttons { display: flex; gap: 0.75rem; }

.ma-activity-title { font-size: 18px; font-weight: 600; color: #374151; margin-bottom: 1rem; }
.ma-activity-list { display: flex; flex-direction: column; gap: 1rem; }
.ma-activity-item { display: flex; align-items: flex-start; gap: 0.75rem; }
.ma-activity-dot { width: 8px; height: 8px; border-radius: 50%; margin-top: 6px; flex-shrink: 0; }
.ma-activity-text { margin: 0; font-size: 14px; color: #374151; line-height: 1.5; }
.ma-activity-time { margin: 0.25rem 0 0 0; font-size: 12px; color: #6b7280; }

  /* ===============================
     Document Management (scoped)
     =============================== */
  .dm-fullpage { overflow: hidden; height: 100vh; padding: 0; }
  .dm-layout { height: 100%; }

  .dm-sidebar {
    width: 320px;
    height: 100vh;
    padding: 0;
    margin-right: 0;
    overflow: hidden;
    border-right: 1px solid rgba(0,0,0,0.1);
    display: flex;
    flex-direction: column;
  }

  /* ===============================
     Reports - Card (scoped)
     =============================== */
  .reports-card {
    width: 245.5px;
    height: 216px;
    text-align: center;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
  }
  @media (max-width: 768px) {
    .reports-card { width: 200px; height: 180px; }
  }
  @media (max-width: 480px) {
    .reports-card { width: 100%; height: 160px; }
  }

  .rc-icon { font-size: 2rem; color: white; margin-bottom: 0.5rem; }
  .rc-title { font-size: 14px; font-weight: 600; margin: 0; }
  .rc-count { font-size: 24px; font-weight: 700; margin: 0; }
  .rc-text-white { color: #ffffff; }

  /* Color variants (background intensified to 0.4) */
  .rc-blue { background-color: rgba(59, 130, 246, 0.4); }
  .rc-green { background-color: rgba(16, 185, 129, 0.4); }
  .rc-purple { background-color: rgba(139, 92, 246, 0.4); }
  .rc-orange { background-color: rgba(249, 115, 22, 0.4); }
  .rc-red { background-color: rgba(239, 68, 68, 0.4); }

  /* Text color helpers for title/count when needed */
  .rc-text-blue { color: #3b82f6; }
  .rc-text-green { color: #10b981; }
  .rc-text-purple { color: #8b5cf6; }
  .rc-text-orange { color: #f97316; }
  .rc-text-red { color: #ef4444; }

  /* ===============================
     Reports Page (scoped)
     =============================== */
  .reports-page { padding: 2rem; min-height: 100vh; width: 100%; box-sizing: border-box; }
  .reports-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; }
  .reports-title { margin: 0; font-size: 2rem; font-weight: 700; color: #1f2937; }
  .reports-actions { display: flex; gap: 1rem; }

  .reports-toolbar { display: flex; gap: 1rem; margin-bottom: 2rem; align-items: center; padding: 1.5rem; }
  .reports-search-wrap { flex: 1; max-width: 600px; }
  .reports-filter-wrap { min-width: 200px; }

  .reports-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 1.5rem; margin-bottom: 3rem; }

  .reports-summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; }
  .reports-summary-card { display: flex; align-items: center; gap: 1rem; padding: 1rem; }
  .reports-summary-icon { font-size: 1.5rem; }
  .reports-summary-count { font-size: 1.5rem; font-weight: 700; }
  .reports-summary-label { font-size: 0.875rem; color: #6b7280; }

  @media (max-width: 768px) {
    .reports-page { padding: 1rem; }
    .reports-title { font-size: 1.5rem; }
    .reports-toolbar { padding: 1rem; }
    .reports-search-wrap { max-width: 100%; }
  }

  .dm-sidebar-header {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    background-color: rgba(255,255,255,0.1);
    flex-shrink: 0;
  }
  .dm-sidebar-title { margin: 0; font-size: 16px; font-weight: 600; color: #374151; }

  .dm-category-list { padding: 0; flex: 1; overflow-y: auto; }
  .dm-category-section { border-bottom: 1px solid rgba(0,0,0,0.05); }
  .dm-category-row {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    background-color: transparent;
    transition: background-color 0.2s;
    border-left: 3px solid transparent;
  }
  .dm-category-row:hover { background-color: rgba(0,0,0,0.02); }
  .dm-category-row.selected { border-left-color: #4A90E2; }

  .dm-chevron { font-size: 12px; color: #6b7280; margin-right: 8px; transition: transform 0.2s; }
  .dm-chevron.expanded { transform: rotate(90deg); }

  .dm-category-label { font-size: 14px; color: #374151; font-weight: 400; }
  .dm-category-label.selected { font-weight: 600; }

  .dm-sublist { padding-left: 40px; padding-bottom: 8px; background-color: rgba(0,0,0,0.01); }
  .dm-sublist-inner { padding: 4px 0; font-size: 12px; color: #6b7280; }
  .dm-sublist-col { display: flex; flex-direction: column; gap: 4px; }
  .dm-subitem { display: flex; align-items: center; padding: 4px 0; cursor: pointer; }
  .dm-folder-icon { font-size: 12px; color: #f59e0b; margin-right: 8px; }

  .dm-main { display: flex; flex-direction: column; min-width: 0; overflow: hidden; height: 100vh; }
  .dm-empty { display: flex; align-items: center; justify-content: center; height: 100%; color: #6b7280; font-size: 18px; }

  @media (max-width: 768px) {
    .dm-sidebar { width: 260px; }
  }

  /* ===============================
     Document Management - DocHeader (scoped)
     =============================== */
  .dh-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 0.75rem; /* 12px */
    width: 100%;
    flex-wrap: wrap;
  }
  .dh-left { display: flex; align-items: center; gap: 0.75rem; flex-wrap: wrap; }
  .dh-showme { display: flex; align-items: center; gap: 0.5rem; }
  .dh-showme-label { font-size: 14px; font-weight: 500; color: #6b7280; white-space: nowrap; }

  .dh-right { display: flex; align-items: center; gap: 0.5rem; flex-wrap: wrap; }

  .dh-bottom { display: flex; align-items: center; gap: 0.5rem; flex-wrap: wrap; }

  /* ===============================
     Document Management - Quarter view (scoped)
     =============================== */
  .dmq-root { height: 100vh; display: flex; flex-direction: column; overflow: hidden; width: 100%; }
  .dmq-header { padding: 1rem 2rem; border-bottom: 1px solid rgba(0,0,0,0.1); flex-shrink: 0; display: flex; flex-direction: column; align-items: flex-start; width: 100%; box-sizing: border-box; }
  .dmq-title { margin: 0 0 1rem 0; font-size: 24px; font-weight: 600; color: #374151; }

  .dmq-content { flex: 1; padding: 0 2rem 1rem 2rem; overflow: hidden; display: flex; flex-direction: column; width: 100%; box-sizing: border-box; }
  .dmq-table-wrap { flex: 1; margin: 0; overflow: auto; display: flex; flex-direction: column; height: 100%; width: 100%; box-sizing: border-box; }
  .dt-full { width: 100%; }
  .col-half { width: 50%; }

  .dmq-quarter-cell { display: flex; align-items: center; gap: 8px; justify-content: center; }
  .dmq-folder-icon { color: #f59e0b; font-size: 16px; }
  .dmq-quarter-text { color: #2563eb; font-weight: 500; font-size: 14px; }
  .dmq-year-cell { display: flex; justify-content: center; align-items: center; }
  .dmq-year-text { color: #374151; font-size: 14px; }

  @media (max-width: 768px) {
    .dmq-header { padding: 0.75rem 1rem; }
    .dmq-content { padding: 0 1rem 0.75rem 1rem; }
  }

  /* ===============================
     Document Management - Date view (scoped)
     =============================== */
  .dmd-back { display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem; }
  .dmd-back-btn { background: none; border: none; color: #2563eb; cursor: pointer; font-size: 16px; padding: 4px; }
  .dmd-pdf-icon { color: #dc2626; font-size: 16px; }
  .dmd-docid-text { color: #2563eb; font-weight: 500; font-size: 14px; white-space: nowrap; }
  .dmd-center-cell { text-align: center; padding: 8px 0; }
  .dmd-price-text { color: #374151; font-size: 14px; font-weight: 500; }
  .dmd-file-text { color: #374151; font-size: 14px; }
  .col-third { width: 33%; }
  .col-third-34 { width: 34%; }

  /* ===============================
     Dynamic Form Management (scoped)
     =============================== */
  .dfm-page { padding: 2rem; min-height: 100vh; width: 100%; box-sizing: border-box; }
  .dfm-title { margin-bottom: 1.5rem; color: #193276; }

  .dfm-toolbar { display: flex; justify-content: space-between; align-items: center; margin-bottom: 2rem; padding: 1.5rem; gap: 1rem; }
  .dfm-toolbar-left { display: flex; gap: 2rem; align-items: center; flex: 1; flex-wrap: wrap; }
  .dfm-search-wrap { flex: 1; max-width: 400px; }
  .dfm-toolbar-right { display: flex; justify-content: flex-end; gap: 1rem; flex-wrap: wrap; }
  .dfm-filter-wrap { min-width: 150px; }

  .dfm-table-wrap { margin: 0; overflow: auto; display: flex; flex-direction: column; width: 100%; box-sizing: border-box; }

  .dfm-actions { display: flex; gap: 0.5rem; justify-content: center; }
  .dfm-action-icon { color: #6b7280; font-size: 16px; cursor: pointer; padding: 4px; }

  .col-15 { width: 15%; }
  .col-40 { width: 40%; }
  .col-20 { width: 20%; }
  .col-25 { width: 25%; }
  .text-center { text-align: center !important; }

  .dfm-pagination { display: flex; justify-content: space-between; align-items: center; padding: 1rem; border-top: 1px solid rgba(0, 0, 0, 0.1); background-color: rgba(255, 255, 255, 0.05); }

  @media (max-width: 768px) {
    .dfm-page { padding: 1rem; }
    .dfm-toolbar { padding: 1rem; }
    .dfm-search-wrap { max-width: 100%; }
  }

  /* ===============================
     Dashboard Header - Company dropdown panel tweak
     =============================== */
  /* Shift the company dropdown overlay slightly left to keep it within viewport */
  .company-dropdown-panel {
    transform: translateX(-12px);
  }

  @media (max-width: 576px) {
    .company-dropdown-panel {
      transform: translateX(-20px);
    }
  }


  .glass-table .p-datatable-header {
    background: transparent !important;
    border: none !important;
  }
  

  .glass-table.p-datatable-scrollable .p-datatable-wrapper > .p-datatable-table > .p-datatable-thead{
    background: transparent !important;
    border: none !important;

  }


  /* .new-ticket-sidebar {
    height: auto;
  } */

  /* ========================================
   PageHeader RESPONSIVE (scoped)
   ======================================== */
/* Tablet: tighten breadcrumb text and spacing */
@media (max-width: 992px) {
  .glass-header.page-header {
    height: auto;
  }
  .glass-header.page-header .glass-header-left span {
    font-size: 13px !important;
  }
}

/* Mobile: stack header content and make input/buttons comfortable */
@media (max-width: 768px) {
  .glass-header.page-header {
    flex-direction: column;
    align-items: flex-start;
    border-radius: 8px;
    gap: 8px;
    height: auto;
    padding: 8px 12px;
  }
  .glass-header.page-header .glass-header-left {
    padding: 8px 0;
    gap: 6px;
  }
  .glass-header.page-header .glass-header-left .glass-btn-text {
    padding: 4px 6px !important;
  }
  .glass-header.page-header .glass-header-left span {
    font-size: 12px !important;
    line-height: 18px !important;
  }
  .glass-header.page-header .glass-header-right {
    width: 100%;
    justify-content: flex-end;
    background: transparent;
    padding: 0;
  }
}

/* ===============================
   KeyData RESPONSIVE (scoped)
   =============================== */
   /* KeyData card styles - composes glassy-ui + global fonts */

.keydata-card {
  display: flex;
  width: 660px;
  height: 582px;
  padding: 1px;
  flex-direction: column;
  align-items: stretch; /* internal sections manage their own alignment */
  flex-shrink: 0;
  box-sizing: border-box;
  overflow: hidden;
}

/* Header area uses existing glass header, but align text style */
.keydata-header {
  border-radius: 16px 16px 0 0;
}

.keydata-star {
  color: #f59e0b; /* amber star */
  font-size: 18px;
}

.keydata-title {
  font-family: Inter, system-ui, -apple-system, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-weight: 600;
  font-size: 16px;
  color: var(--text-dark, #374151);
}

/* Body */
.keydata-body {
  /* layout spacing handled via PrimeFlex in JSX */
  display: block;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 0 0 16px 16px;
  box-sizing: border-box;
  height: 100%;
  overflow: auto;
}

/* Top section visuals only; layout is PrimeFlex */

.keydata-photo {
  width: 96px;
  height: 96px;
  border-radius: 8px;
  overflow: hidden;
  background: #e5e7eb;
  flex-shrink: 0;
}
.keydata-photo img { width: 100%; height: 100%; object-fit: cover; display: block; }
.keydata-photo-fallback {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #111827;
  font-weight: 600;
}

.keydata-person { display: flex; flex-direction: column; }
.keydata-label { font-size: 12px; color: #6b7280; }
.keydata-name { font-size: 16px; font-weight: 600; color: #111827; }
.keydata-country { font-size: 14px; color: #111827; }

/* Section blocks */
.keydata-section { display: flex; flex-direction: column; gap: 12px; }
.keydata-section-title { font-weight: 600; color: #111827; }

/* Chips row */
.keydata-chips { display: flex; flex-wrap: wrap; gap: 10px; }
.keydata-chip {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 6px 10px;
  border-radius: 9999px;
  background: #f3f4f6;
  color: #111827;
  border: 1px solid rgba(229, 231, 235, 0.7);
}
.chip-code {
  width: 24px; height: 24px; border-radius: 9999px; display: inline-flex; align-items: center; justify-content: center;
  font-size: 12px; font-weight: 600; color: #fff;
}
.chip-text { font-size: 13px; color: #374151; }

/* Color variants */
.badge-blue .chip-code { background: #3b82f6; }
.badge-green .chip-code { background: #10b981; }
.badge-purple .chip-code { background: #8b5cf6; }
.badge-pink .chip-code { background: #f472b6; }
.badge-red .chip-code { background: #ef4444; }
.badge-orange .chip-code { background: #f59e0b; }
.badge-teal .chip-code { background: #14b8a6; }
.badge-gray .chip-code { background: #6b7280; }

/* Sanctions header visuals only */
.source-badge {
  display: inline-flex; align-items: center; justify-content: center;
  width: 28px; height: 28px; border-radius: 9999px;
  background: #dbeafe; color: #1e40af; font-weight: 700; font-size: 12px;
}
.sanction-title { font-size: 16px; font-weight: 600; color: #111827; }

/* Documents list visuals */
.doc-row { padding: 12px 0; border-top: 1px solid rgba(229, 231, 235, 0.7); }
.doc-row:first-child { border-top: none; }

.doc-label { font-weight: 600; color: #111827; margin-bottom: 4px; }
.doc-meta { display: inline-flex; align-items: center; gap: 10px; color: #6b7280; font-size: 13px; }
.doc-number { color: #374151; }
.doc-country { display: inline-flex; align-items: center; gap: 6px; }
.doc-idtype { display: inline-flex; align-items: center; gap: 6px; }
.dot-sep { width: 1px; height: 14px; background: rgba(229, 231, 235, 0.9); display: inline-block; }

.doc-actions { display: inline-flex; gap: 8px; }
.icon-btn {
  display: inline-flex; align-items: center; justify-content: center;
  width: 28px; height: 28px; border-radius: 6px;
  background: rgba(255,255,255,0.9);
  border: 1px solid rgba(229, 231, 235, 0.8);
  cursor: pointer;
}
.icon-btn:hover { background: rgba(243,244,246,0.9); }
.icon-btn.view { color: #2563eb; }
.icon-btn.download { color: #10b981; }

/* Responsive rules */
@media (max-width: 992px) {
  .keydata-card { width: 100%; max-width: 660px; height: auto; }
}

@media (max-width: 768px) {
  .keydata-photo { width: 72px; height: 72px; }
  .keydata-body { padding: 16px; }
  .sanction-title { font-size: 15px; }
}

@media (max-width: 480px) {
  .keydata-card { width: 100%; height: auto; }
  .doc-actions { justify-content: flex-start; }
}

/* ========================================
   LEADS LIST STYLING
   ======================================== */

/* Leads controls container */
.leads-controls-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Leads filter section - horizontal layout */
.leads-filter-section {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  flex-wrap: wrap;
}

/* Search wrapper */
.leads-search-wrapper {
  flex: 0 0 auto;
}

/* Action buttons container */
.leads-action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

/* Responsive styling for leads list */
@media (max-width: 1200px) {
  .leads-filter-section {
    gap: 10px;
  }
  
  .leads-action-buttons {
    margin-left: 0;
  }
}

@media (max-width: 768px) {
  .leads-filter-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .leads-search-wrapper {
    width: 100%;
  }
  
  .leads-action-buttons {
    width: 100%;
    justify-content: space-between;
  }
}

/* ========================================
   SALES PIPELINE STYLING
   ======================================== */

/* Pipeline controls container */
.pipeline-controls-container {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

/* Pipeline filter section - horizontal layout */
.pipeline-filter-section {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  flex-wrap: wrap;
}

/* Search wrapper */
.pipeline-search-wrapper {
  flex: 0 0 auto;
}

/* Action buttons container */
.pipeline-action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: auto;
}

/* Stats Cards Container */
.pipeline-stats-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: 20px;
  margin: 24px 0;
  padding: 0 20px;
}

/* Stat Card Header */
.pipeline-stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

/* Stat Icon */
.pipeline-stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

/* Stat Count Badge */
.pipeline-stat-count {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Stat Body */
.pipeline-stat-body {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

/* Stat Title */
.pipeline-stat-title {
  font-size: 14px;
  color: #6B7280;
  font-weight: 500;
}

/* Stat Value */
.pipeline-stat-value {
  font-size: 24px;
  font-weight: 700;
  color: #1F2937;
}

/* Table Section */
.pipeline-table-section {
  padding: 0 20px 20px;
}

.pipeline-table-title {
  font-size: 18px;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 16px;
}

/* Responsive styling for pipeline */
@media (max-width: 1200px) {
  .pipeline-filter-section {
    gap: 10px;
  }
  
  .pipeline-action-buttons {
    margin-left: 0;
  }

  .pipeline-stats-container {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .pipeline-filter-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .pipeline-search-wrapper {
    width: 100%;
  }
  
  .pipeline-action-buttons {
    width: 100%;
    justify-content: space-between;
  }

  .pipeline-stats-container {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 0 12px;
  }

  .pipeline-stat-value {
    font-size: 20px;
  }

  .pipeline-table-section {
    padding: 0 12px 12px;
  }
}

@media (max-width: 480px) {
  .pipeline-stats-container {
    grid-template-columns: 1fr;
  }
}

/* ========================================
   EMPLOYEE DASHBOARD STYLING
   ======================================== */

/* Responsive grid for employee dashboard */
@media (max-width: 1200px) {
  .employee-dashboard-grid {
    grid-template-columns: repeat(4, 1fr) !important;
  }
}

@media (max-width: 992px) {
  .employee-dashboard-grid {
    grid-template-columns: repeat(3, 1fr) !important;
  }
}

@media (max-width: 768px) {
  .employee-dashboard-grid {
    grid-template-columns: repeat(2, 1fr) !important;
  }
}

@media (max-width: 480px) {
  .employee-dashboard-grid {
    grid-template-columns: 1fr !important;
  }
}


.white{
  color: white;
}


.psc{
  background: #000;
  color: #fff;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}


.scale-7{
  transform: scale(0.7);
}

/* ======================== */
/* Stepper css */
/* ======================== */

/* LeadDetailStepper.css */

/* Pull the circle up so it overlaps the steps baseline (same effect as marginTop: -25px) */
.stepper-item {
  /* margin-top: -25px; */
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 0.5rem;
}

/* Circle: size, border, centering */
.stepper-circle {
  width: 3rem;              /* 48px */
  height: 3rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  border: 1px solid var(--primary-color); /* uses theme var; change if missing */
  z-index: 1;
  transition: background-color 0.12s ease, color 0.12s ease, transform 0.12s ease;
}

/* Active state */
.stepper-circle--active {
  background-color: var(--primary-color);
  color: var(--surface-b);
  border-color: var(--primary-color);
}

/* Inactive state */
.stepper-circle--inactive {
  background-color: var(--surface-b);
  color: var(--text-color-secondary);
  border-color: var(--surface-b);
}

/* Slight hover lift for affordance */
.stepper-item:hover .stepper-circle {
  transform: translateY(-2px);
}

/* Label colors */
.stepper-label--active {
  /* font-size: 12px; */
  color: var(--primary-color);
}

.stepper-label--inactive {
  color: white; /* fallback if var not available */
}

/* Ensure icon size inside circle matches earlier `text-xl` */
.stepper-circle i {
  font-size: 1.25rem;
  line-height: 1;
}
