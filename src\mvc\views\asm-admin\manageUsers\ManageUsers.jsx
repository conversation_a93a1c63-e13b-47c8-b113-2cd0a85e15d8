import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { But<PERSON> } from "primereact/button";
import FromToDateInput from '@components/common/FromToDateInput'
import FilterBy from "@components/common/FilterBy";
import { Checkbox } from "primereact/checkbox";
import userController from "@controllers/user/userController";
// removed direct axios usage; using controller/service instead
import "../../../../styles/glassy/glassy-ui.css";
import GlassyWhiteButton from "@components/common/Buttons/GlassyWhiteButton";
import SearchInput from "@components/common/Input/SearchInput";
import ActionButton from "@components/common/Buttons/ActionButton";
import SwitchButton from "@components/common/Buttons/SwitchButton";
import GlassyTabs from "@components/common/Tab/GlassyTabs";
// import NotFoundComponent from "@Components/Common/NotFoundComponent";
import Paginator from "@components/common/Paginator/Paginator";
import { showToast } from "@utils/toast/toastUtils";
import { exportTableToCSV } from "@utils/csvExport";
// import { getAxiosInstance } from "@utils/Loader/axiosInstance";
import authHeader from "@services/AuthHeader";
import axios from "axios";

const ManageUsers = () => {
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);
  const [loading, setLoading] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [departmentStats, setDepartmentStats] = useState({});
  const [first, setFirst] = useState(0);
  const [rows, setRows] = useState(10);
  const [updatingUserId, setUpdatingUserId] = useState(null);

    // Pagination config
    const [paginatedUsers, setPaginatedUsers] = useState([]);
  
    const onPageChange = (page, paginatedData) => {
      console.log("Page changed to:", page, "Data:", paginatedData);
      setPaginatedUsers(paginatedData);
    };

  const loadUsers = async (department = "all", search = "") => {
    setLoading(true);
    try {
      let result;

      if (search.trim()) {
        // Search employee details by term
        result = await userController.searchEmployeeDetails(search);
      } else {
        // Get all employee details
        result = await userController.getEmployeeDetails();
      }

      if (result.success) {
        // Validate data structure
        const validatedUsers = Array.isArray(result.data) ? result.data : [];
        console.log("validatedUsers:", result);
        
        setUsers(validatedUsers);
        setTotalRecords(result.total || validatedUsers.length);

        // Compute department stats from returned users so we don't need a second API call
        const stats = {
          sales: 0,
          finance: 0,
          "fuel-supply": 0,
          operational: 0,
          marketing: 0,
          legal: 0,
        };

        setDepartmentStats(stats);
      } else {
        console.error("API Error:", result.error);
        setUsers([]);
        setTotalRecords(0);
      }
    } catch (error) {
      console.error("Error loading users:", error);
      setUsers([]);
      setTotalRecords(0);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    axios.get("/api/EmployeeDetails/getAll",{
      headers: authHeader()
    })
  
    return () => {
      
    }
  }, [])
  
  const handleStatusUpdate = async (userId, newStatusLabel) => {
    if (!userId) return;

    const payloadStatus = newStatusLabel === "Active" ? "ACTIVE" : "INACTIVE";
    try {
      setUpdatingUserId(userId);
      const result = await userController.updateUserStatus(
        userId,
        payloadStatus
      );

      if (result.success) {
        const updatedUsers = users.map((user) =>
          user.id === userId
            ? {
                ...user,
                status: payloadStatus === "ACTIVE",
                statusName: payloadStatus,
              }
            : user
        );
        setUsers(updatedUsers);
        showToast("success", "User status updated");
      } else {
        throw new Error(result.error || "Failed to update status");
      }
    } catch (err) {
      console.error("Error updating user status:", err);
      showToast("error", err.message || "Status update failed");
    } finally {
      setUpdatingUserId(null);
    }
  };

  // Load initial data when component mounts
  useEffect(() => {
    loadUsers();
  }, []);

  // Handle search functionality
  const handleSearch = () => {
    loadUsers("all", searchValue);
  };

  // Auto-search when search value changes (with debounce)
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (searchValue.trim()) {
        handleSearch();
      } else {
        loadUsers(); // Load all users when search is cleared
      }
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchValue]);

  // Template functions
  const checkboxTemplate = (rowData) => {
    return (
      <Checkbox
        checked={selectedUsers.includes(rowData)}
        onChange={(e) => {
          if (e.checked) {
            setSelectedUsers([...selectedUsers, rowData]);
          } else {
            setSelectedUsers(
              selectedUsers.filter((user) => user.id !== rowData.id)
            );
          }
        }}
      />
    );
  };

  const statusTemplate = (rowData) => (
    <SwitchButton
      checked={rowData?.statusName === "ACTIVE" || !!rowData?.status}
      onChange={(e) =>
        handleStatusUpdate(rowData.id, e.value ? "Active" : "Inactive")
        
      }
    
    />
  );

  const activityTemplate = (rowData) => {
    return (
      <div className="flex justify-content-center">
        <Button
          label="View Logs"
          className="glass-badge"
          onClick={() => console.log("View logs for:", rowData.id)}
        />
      </div>
    );
  };

  const actionTemplate = (rowData) => {
    return (
      <div className="action-buttons">
        <ActionButton
          action="update"
          onClick={() => {
            showToast(
              "info",
              `Editing ${rowData.firstName} ${rowData.lastName}`
            );
          }}
        />
      </div>
    );
  };

  const departmentTabs = [
    {
      label: `All Employees (${totalRecords || 0})`,
      value: "all",
      badge: totalRecords,
    },
    {
      label: `Sales (${departmentStats.sales || 0})`,
      value: "sales",
      badge: departmentStats.sales,
    },
    {
      label: `Finance (${departmentStats.finance || 0})`,
      value: "finance",
      badge: departmentStats.finance,
    },
    {
      label: `Fuel Supply (${departmentStats["fuel-supply"] || 0})`,
      value: "fuel-supply",
      badge: departmentStats["fuel-supply"],
    },
    {
      label: `Operational (${departmentStats.operational || 0})`,
      value: "operational",
      badge: departmentStats.operational,
    },
    {
      label: `Marketing (${departmentStats.marketing || 0})`,
      value: "marketing",
      badge: departmentStats.marketing,
    },
    {
      label: `Legal (${departmentStats.legal || 0})`,
      value: "legal",
      badge: departmentStats.legal,
    },
  ];

  const handleTabChange = (index, tab) => {
    setActiveTabIndex(index);
    if (index === 0) {
      loadUsers();
    } else {
      setUsers([]);
    }
  };

  // CSV Export functionality
  const handleExportCSV = () => {
    const columns = [
      { field: 'employeeCode', header: 'Employee ID' },
      { field: 'firstName', header: 'First Name' },
      { field: 'lastName', header: 'Last Name' },
      { field: 'role', header: 'Role' },
      { field: 'email', header: 'Email' },
      { field: 'statusName', header: 'Status' },
      { field: 'phone', header: 'Phone' },
      { field: 'userId', header: 'User ID' },
      { field: 'employeeId', header: 'Employee ID Internal' },
      { field: 'createdDate', header: 'Created Date' }
    ];
    
    exportTableToCSV(users, columns, 'manage_users');
  };

  return (
    <div className="glassy-page">
      {/* Content Above Table */}
      <div className="glass-content-section">
        {/* Controls */}
        <div className="flex flex-column md:flex-row justify-content-between align-items-center mb-4 gap-3 flex-wrap">
          <div className="flex align-items-center gap-3 flex-wrap">
            <SearchInput
              value={searchValue}
              onChange={setSearchValue}
              onSearch={handleSearch}
              placeholder="Search employees by name, email, or ID..."
            />

            <div className="glass-dropdown">
              <FilterBy
                value={null}
                options={[
                  { label: "All Users", value: "all" },
                  { label: "Active", value: "active" },
                  { label: "Inactive", value: "inactive" },
                  { label: "Recent", value: "recent" },
                ]}
                placeholder="Filter by"
              />
            </div>

            <FromToDateInput
              fromDate={fromDate}
              toDate={toDate}
              onFromDateChange={setFromDate}
              onToDateChange={setToDate}
            />
          </div>

          <div className="flex gap-2">
            <GlassyWhiteButton 
              label="Export" 
              icon="pi pi-download" 
              onClick={handleExportCSV}
            />
            <GlassyWhiteButton
              label="Import"
              icon="pi pi-upload"
              className="glass-btn-secondary"
              onClick={() => navigate("/manage-users/add-user")}
            />
          </div>
        </div>

        {/* Department Tabs */}
        <GlassyTabs
          tabs={departmentTabs}
          activeIndex={activeTabIndex}
          onTabChange={handleTabChange}
          variant="default"
          showBadges={false} // We show counts in labels instead
          hidePanels
        />
      </div>

      {/* Table Section */}
      <div className="glass-table-container">
        <DataTable
          value={paginatedUsers}
          selection={selectedUsers}
          onSelectionChange={(e) => setSelectedUsers(e.value)}
          className="glass-table"
          loading={loading}
          emptyMessage="No users found"
        >
          <Column body={checkboxTemplate} style={{ width: "3rem" }} />
          <Column field="employeeCode" header="Employee ID" sortable />
          <Column field="firstName" header="First Name" sortable />
          <Column field="lastName" header="Last Name" sortable />
          <Column field="role" header="Role" sortable />
          <Column field="email" header="Email" sortable />
          {/* <Column field="lastLoginDate" header="Last Login Date" sortable /> */}
          <Column header="Activity" body={activityTemplate} />
          <Column header="Status" body={statusTemplate} />
          <Column header="Action" body={actionTemplate} />
        </DataTable>

        {/* Custom Pagination */}
        <Paginator
          data={users}
          itemsPerPage={10}
          onPageChange={onPageChange}
        />
      </div>
    </div>
  );
};

export default ManageUsers;