// Friendly messages for login-related errors
export const loginStatusMessage = (status) => {
  switch (status) {
    case 400:
      return 'Invalid request. Please check your credentials and try again.';
    case 401:
      return 'Invalid username or password.';
    case 403:
      return 'Your account is disabled or not permitted to log in.';
    case 429:
      return 'Too many login attempts. Please wait a moment and try again.';
    case 423:
      return 'Your account is locked. Please contact support.';
    case 500:
      return 'Server error. Please try again later.';
    default:
      return 'Unable to log in. Please try again.';
  }
};

// Extract friendly message from axios-like error object for login flows
export const getFriendlyLoginError = (error) => {
  if (!error) return 'Unable to log in. Please try again.';

  // Prefer server-provided message
  const serverMessage = error?.response?.data?.message || error?.response?.data || null;
  if (serverMessage && typeof serverMessage === 'string' && serverMessage.trim()) {
    return serverMessage;
  }

  // Map status codes to friendly messages
  const status = error?.response?.status || error?.status;
  if (status) return loginStatusMessage(status);

  // Fallback
  return error.message || 'Unable to log in. Please try again.';
};

/**
 * Extract friendly message from a response-like payload (for successful HTTP responses
 * that contain error information) or from an error object.
 * Accepts objects like: { message, errorMessage, status }
 */
export const getFriendlyLoginMessage = (payload) => {
  if (!payload) return 'Unable to log in. Please try again.';

  if (typeof payload === 'string' && payload.trim()) return payload;

  if (payload.message && typeof payload.message === 'string') return payload.message;
  if (payload.errorMessage && typeof payload.errorMessage === 'string') return payload.errorMessage;

  const status = payload.status || payload?.response?.status;
  if (status) return loginStatusMessage(status);

  return payload.message || payload.errorMessage || payload?.response?.data || 'Unable to log in. Please try again.';
};

export default { loginStatusMessage, getFriendlyLoginError, getFriendlyLoginMessage };
