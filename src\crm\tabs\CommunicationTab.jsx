import React, { useState } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { Tag } from 'primereact/tag';
import { Timeline } from 'primereact/timeline';
import { Card } from 'primereact/card';

/**
 * CommunicationTab - Displays communication history and logs
 */
const CommunicationTab = ({ prospectId }) => {
  const [communications] = useState([
    {
      id: 1,
      date: '2024-01-20',
      time: '10:30 AM',
      type: 'Email',
      subject: 'Initial Contact',
      from: '<EMAIL>',
      to: '<EMAIL>',
      status: 'Sent',
      notes: 'Sent initial proposal and company introduction'
    },
    {
      id: 2,
      date: '2024-01-21',
      time: '02:15 PM',
      type: 'Phone Call',
      subject: 'Follow-up Discussion',
      from: '<PERSON> Hassan',
      to: 'Sales Team',
      status: 'Completed',
      notes: 'Discussed requirements and pricing'
    },
    {
      id: 3,
      date: '2024-01-22',
      time: '11:00 AM',
      type: 'Meeting',
      subject: 'Product Demo',
      from: 'Sales Team',
      to: 'Emirates Team',
      status: 'Scheduled',
      notes: 'Scheduled product demonstration'
    },
    {
      id: 4,
      date: '2024-01-23',
      time: '09:00 AM',
      type: 'Email',
      subject: 'Proposal Sent',
      from: '<EMAIL>',
      to: '<EMAIL>',
      status: 'Sent',
      notes: 'Sent detailed proposal with pricing'
    }
  ]);

  const timelineEvents = communications.map(comm => ({
    status: comm.subject,
    date: `${comm.date} ${comm.time}`,
    icon: comm.type === 'Email' ? 'pi pi-envelope' : comm.type === 'Phone Call' ? 'pi pi-phone' : 'pi pi-calendar',
    color: comm.status === 'Completed' ? '#22c55e' : comm.status === 'Sent' ? '#2196F3' : '#f59e0b',
    description: comm.notes
  }));

  const typeTemplate = (rowData) => {
    const iconMap = {
      'Email': 'pi-envelope',
      'Phone Call': 'pi-phone',
      'Meeting': 'pi-calendar'
    };
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
        <i className={`pi ${iconMap[rowData.type]}`}></i>
        <span>{rowData.type}</span>
      </div>
    );
  };

  const statusTemplate = (rowData) => {
    const severityMap = {
      'Sent': 'info',
      'Completed': 'success',
      'Scheduled': 'warning',
      'Pending': 'danger'
    };
    return <Tag value={rowData.status} severity={severityMap[rowData.status]} />;
  };

  const actionTemplate = (rowData) => {
    return (
      <div style={{ display: 'flex', gap: '0.5rem' }}>
        <Button
          icon="pi pi-eye"
          className="p-button-sm p-button-text p-button-info"
          tooltip="View"
        />
        <Button
          icon="pi pi-pencil"
          className="p-button-sm p-button-text p-button-secondary"
          tooltip="Edit"
        />
      </div>
    );
  };

  const customizedMarker = (item) => {
    return (
      <span 
        className="flex w-2rem h-2rem align-items-center justify-content-center text-white border-circle z-1 shadow-1"
        style={{ backgroundColor: item.color }}
      >
        <i className={item.icon}></i>
      </span>
    );
  };

  const customizedContent = (item) => {
    return (
      <Card title={item.status} subTitle={item.date}>
        <p>{item.description}</p>
      </Card>
    );
  };

  return (
    <div className="communication-tab">
      <div className="tab-header" style={{ marginBottom: '1.5rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h3>Communication History</h3>
        <Button label="Log Communication" icon="pi pi-plus" className="p-button-sm" />
      </div>

      {/* Timeline View */}
      <Card title="Communication Timeline" style={{ marginBottom: '1.5rem' }}>
        <Timeline 
          value={timelineEvents} 
          align="alternate" 
          className="customized-timeline"
          marker={customizedMarker}
          content={customizedContent}
        />
      </Card>

      {/* Table View */}
      <Card title="Communication Log">
        <DataTable 
          value={communications} 
          responsiveLayout="scroll"
          stripedRows
          showGridlines
          paginator
          rows={10}
        >
          <Column field="date" header="Date" sortable style={{ width: '120px' }} />
          <Column field="time" header="Time" sortable style={{ width: '100px' }} />
          <Column field="type" header="Type" body={typeTemplate} sortable style={{ width: '150px' }} />
          <Column field="subject" header="Subject" sortable />
          <Column field="from" header="From" sortable />
          <Column field="to" header="To" sortable />
          <Column field="status" header="Status" body={statusTemplate} sortable style={{ width: '120px' }} />
          <Column header="Actions" body={actionTemplate} style={{ width: '120px' }} />
        </DataTable>
      </Card>
    </div>
  );
};

export default CommunicationTab;

