import React, { useState } from 'react';
import { Card } from 'primereact/card';
import { Timeline } from 'primereact/timeline';
import { But<PERSON> } from 'primereact/button';
import { Tag } from 'primereact/tag';
import { Dropdown } from 'primereact/dropdown';
import { Calendar } from 'primereact/calendar';

const ActivityLogsTab = ({ leadId }) => {
  const [filterType, setFilterType] = useState(null);
  const [dateRange, setDateRange] = useState(null);

  const activities = [
    {
      id: 1,
      type: 'Email',
      action: 'Email Sent',
      description: 'Pricing proposal <NAME_EMAIL>',
      user: '<PERSON>',
      timestamp: '09:15 AM, Nov 26, 2024',
      status: 'completed',
      icon: 'pi pi-envelope',
      color: '#2196F3'
    },
    {
      id: 2,
      type: 'Call',
      action: 'Phone Call',
      description: 'Follow-up call with prospect. Duration: 15 minutes',
      user: '<PERSON>',
      timestamp: '02:30 PM, Nov 25, 2024',
      status: 'completed',
      icon: 'pi pi-phone',
      color: '#4CAF50'
    },
    {
      id: 3,
      type: 'Meeting',
      action: 'Meeting Scheduled',
      description: 'Product demo scheduled for Dec 1, 2024 at 10:00 AM',
      user: '<PERSON>',
      timestamp: '11:45 AM, Nov 25, 2024',
      status: 'scheduled',
      icon: 'pi pi-calendar',
      color: '#FF9800'
    },
    {
      id: 4,
      type: 'Note',
      action: 'Note Added',
      description: 'Initial contact made. Prospect interested in Jet Fuel supply',
      user: 'John Smith',
      timestamp: '10:08 AM, Nov 24, 2024',
      status: 'completed',
      icon: 'pi pi-file',
      color: '#9C27B0'
    },
    {
      id: 5,
      type: 'Status',
      action: 'Status Changed',
      description: 'Lead status changed from "New" to "Qualification"',
      user: 'System',
      timestamp: '09:00 AM, Nov 24, 2024',
      status: 'completed',
      icon: 'pi pi-sync',
      color: '#607D8B'
    },
    {
      id: 6,
      type: 'Document',
      action: 'Document Uploaded',
      description: 'Company profile and requirements document uploaded',
      user: 'Sarah Johnson',
      timestamp: '04:20 PM, Nov 23, 2024',
      status: 'completed',
      icon: 'pi pi-upload',
      color: '#00BCD4'
    },
    {
      id: 7,
      type: 'Task',
      action: 'Task Completed',
      description: 'Research on Mahan Air completed. Report generated.',
      user: 'Mark Stephen',
      timestamp: '11:30 AM, Nov 23, 2024',
      status: 'completed',
      icon: 'pi pi-check-circle',
      color: '#4CAF50'
    },
    {
      id: 8,
      type: 'Lead',
      action: 'Lead Created',
      description: 'New lead created from Event source',
      user: 'John Smith',
      timestamp: '09:00 AM, Nov 23, 2024',
      status: 'completed',
      icon: 'pi pi-plus-circle',
      color: '#2196F3'
    }
  ];

  const activityTypeOptions = [
    { label: 'All Activities', value: null },
    { label: 'Email', value: 'Email' },
    { label: 'Call', value: 'Call' },
    { label: 'Meeting', value: 'Meeting' },
    { label: 'Note', value: 'Note' },
    { label: 'Status', value: 'Status' },
    { label: 'Document', value: 'Document' },
    { label: 'Task', value: 'Task' }
  ];

  const customizedMarker = (item) => {
    return (
      <span
        className="activity-marker"
        style={{ backgroundColor: item.color }}
      >
        <i className={item.icon}></i>
      </span>
    );
  };

  const customizedContent = (item) => {
    return (
      <Card className="activity-card">
        <div className="activity-header">
          <div className="activity-title">
            <strong>{item.action}</strong>
            <Tag value={item.type} style={{ marginLeft: '0.5rem' }} />
          </div>
          <div className="activity-timestamp">
            <i className="pi pi-clock"></i>
            {item.timestamp}
          </div>
        </div>
        <div className="activity-description">
          {item.description}
        </div>
        <div className="activity-footer">
          <span className="activity-user">
            <i className="pi pi-user"></i>
            {item.user}
          </span>
          {item.status === 'scheduled' && (
            <Tag value="Scheduled" severity="warning" />
          )}
        </div>
      </Card>
    );
  };

  return (
    <div className="activity-logs-tab">
      <Card title="Activity Logs" className="activity-logs-card">
        {/* Filters */}
        <div className="activity-filters">
          <Dropdown
            value={filterType}
            options={activityTypeOptions}
            onChange={(e) => setFilterType(e.value)}
            placeholder="Filter by Type"
            className="filter-dropdown"
          />
          <Calendar
            value={dateRange}
            onChange={(e) => setDateRange(e.value)}
            selectionMode="range"
            placeholder="Select Date Range"
            className="filter-calendar"
            showIcon
          />
          <Button
            label="Export Logs"
            icon="pi pi-download"
            className="p-button-sm p-button-outlined"
          />
        </div>

        {/* Activity Timeline */}
        <div className="activity-timeline">
          <Timeline
            value={activities}
            align="left"
            className="customized-timeline"
            marker={customizedMarker}
            content={customizedContent}
          />
        </div>

        {/* Load More */}
        <div className="load-more-section">
          <Button
            label="Load More Activities"
            icon="pi pi-refresh"
            className="p-button-sm p-button-outlined"
          />
        </div>
      </Card>
    </div>
  );
};

export default ActivityLogsTab;

