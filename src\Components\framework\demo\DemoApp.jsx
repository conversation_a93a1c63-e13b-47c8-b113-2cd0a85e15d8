import React, { useState, useCallback } from 'react';
import { TabView, TabPanel } from 'primereact/tabview';
import { Card } from 'primereact/card';
import { Button } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { useRef } from 'react';

import DynamicComponent from '../core/DynamicComponent';
import componentRegistry from '../core/ComponentRegistry';
import ThemeSelector from '../theme/ThemeSelector';
import LanguageSelector from '../i18n/LanguageSelector';
import { useTheme } from '../theme/ThemeProvider';
import { useI18n } from '../i18n/I18nProvider';

const DemoApp = () => {
  const [activeIndex, setActiveIndex] = useState(0);
  const toast = useRef(null);
  const { currentTheme, isDarkMode } = useTheme();
  const { formatMessage, currentLocale } = useI18n();

  React.useEffect(() => {
    if (!componentRegistry.initialized) {
      componentRegistry.init();
    }
  }, []);

  const showToast = useCallback((severity, summary, detail) => {
    toast.current?.show({ severity, summary, detail });
  }, []);

  const formConfig = {
    fields: [
      {
        name: 'firstName',
        type: 'input-text',
        label: 'First Name',
        required: true,
        props: {
          placeholder: 'Enter your first name',
          maxLength: 50
        }
      },
      {
        name: 'lastName',
        type: 'input-text',
        label: 'Last Name',
        required: true,
        props: {
          placeholder: 'Enter your last name',
          maxLength: 50
        }
      },
      {
        name: 'email',
        type: 'input-text',
        label: 'Email Address',
        required: true,
        props: {
          placeholder: 'Enter your email address',
          keyfilter: 'email'
        }
      },
      {
        name: 'country',
        type: 'dropdown',
        label: 'Country',
        required: true,
        props: {
          options: [
            { label: 'United States', value: 'us' },
            { label: 'Canada', value: 'ca' },
            { label: 'United Kingdom', value: 'uk' },
            { label: 'Germany', value: 'de' },
            { label: 'France', value: 'fr' },
            { label: 'Japan', value: 'jp' },
            { label: 'Australia', value: 'au' }
          ],
          placeholder: 'Select your country',
          filter: true,
          showClear: true
        }
      },
      {
        name: 'interests',
        type: 'dropdown',
        label: 'Primary Interest',
        required: false,
        props: {
          options: [
            { label: 'Technology', value: 'tech' },
            { label: 'Business', value: 'business' },
            { label: 'Design', value: 'design' },
            { label: 'Marketing', value: 'marketing' },
            { label: 'Education', value: 'education' }
          ],
          placeholder: 'Select your primary interest'
        }
      }
    ],
    layout: {
      columns: 2,
      showProgress: true,
      showResetButton: true,
      submitButtonText: 'Submit Registration'
    }
  };

  const tableData = [
    {
      id: 1,
      name: 'John Doe',
      email: '<EMAIL>',
      country: 'United States',
      status: 'Active',
      joinDate: '2023-01-15',
      lastLogin: '2024-01-20'
    },
    {
      id: 2,
      name: 'Jane Smith',
      email: '<EMAIL>',
      country: 'Canada',
      status: 'Active',
      joinDate: '2023-03-22',
      lastLogin: '2024-01-19'
    },
    {
      id: 3,
      name: 'Bob Johnson',
      email: '<EMAIL>',
      country: 'United Kingdom',
      status: 'Inactive',
      joinDate: '2023-05-10',
      lastLogin: '2023-12-15'
    },
    {
      id: 4,
      name: 'Alice Brown',
      email: '<EMAIL>',
      country: 'Germany',
      status: 'Active',
      joinDate: '2023-07-08',
      lastLogin: '2024-01-21'
    },
    {
      id: 5,
      name: 'Charlie Wilson',
      email: '<EMAIL>',
      country: 'France',
      status: 'Pending',
      joinDate: '2024-01-01',
      lastLogin: null
    }
  ];

  const tableColumns = [
    { field: 'id', header: 'ID', sortable: true, filter: true },
    { field: 'name', header: 'Name', sortable: true, filter: true },
    { field: 'email', header: 'Email', sortable: true, filter: true },
    { field: 'country', header: 'Country', sortable: true, filter: true },
    { 
      field: 'status', 
      header: 'Status', 
      sortable: true, 
      filter: true,
      body: (rowData) => (
        <span className={`status-badge status-${rowData.status.toLowerCase()}`}>
          {rowData.status}
        </span>
      )
    },
    { field: 'joinDate', header: 'Join Date', sortable: true, filter: true },
    { field: 'lastLogin', header: 'Last Login', sortable: true, filter: true }
  ];

  const tableConfig = {
    pagination: {
      enabled: true,
      rows: 5,
      rowsPerPageOptions: [5, 10, 25]
    },
    sorting: {
      enabled: true,
      defaultSortField: 'name',
      defaultSortOrder: 1
    },
    filtering: {
      enabled: true
    },
    globalFilter: {
      enabled: true,
      placeholder: 'Search users...',
      fields: ['name', 'email', 'country', 'status']
    },
    selection: {
      mode: 'multiple'
    },
    export: {
      enabled: true
    },
    actions: [
      {
        name: 'edit',
        label: 'Edit',
        icon: 'pi pi-pencil',
        severity: 'info',
        onClick: (rowData) => {
          showToast('info', 'Edit User', `Editing user: ${rowData.name}`);
        }
      },
      {
        name: 'delete',
        label: 'Delete',
        icon: 'pi pi-trash',
        severity: 'danger',
        onClick: (rowData) => {
          showToast('warn', 'Delete User', `Delete user: ${rowData.name}`);
        },
        disabled: (rowData) => rowData.status === 'Active'
      }
    ],
    rowHover: true,
    stripedRows: true,
    showGridlines: true,
    responsiveLayout: 'scroll'
  };

  const handleFormSubmit = useCallback((formData) => {
    console.log('Form submitted:', formData);
    showToast('success', 'Form Submitted', 'Registration completed successfully!');
  }, [showToast]);

  const handleFormError = useCallback((error) => {
    console.error('Form error:', error);
    showToast('error', 'Form Error', error.message || 'An error occurred');
  }, [showToast]);

  const handleTableEvent = useCallback((eventName, eventData) => {
    console.log('Table event:', eventName, eventData);
    
    switch (eventName) {
      case 'selectionChange':
        showToast('info', 'Selection Changed', `Selected ${eventData.selection?.length || 0} rows`);
        break;
      case 'rowClick':
        showToast('info', 'Row Clicked', `Clicked on: ${eventData.row.name}`);
        break;
      case 'export':
        showToast('success', 'Export', 'Data exported successfully');
        break;
      default:
        break;
    }
  }, [showToast]);

  const registryStats = componentRegistry.getStats();

  return (
    <div className="demo-app">
      <Toast ref={toast} />
      
      <div className="demo-header">
        <h1>Dynamic Component Framework Demo</h1>
        <p>
          A comprehensive React component framework built on PrimeReact that allows you to create 
          dynamic UIs through JSON configuration.
        </p>
        
        <div className="demo-stats">
          <div className="stat-item">
            <span className="stat-value">{registryStats.totalComponents}</span>
            <span className="stat-label">Components</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{registryStats.categories}</span>
            <span className="stat-label">Categories</span>
          </div>
          <div className="stat-item">
            <span className="stat-value">{registryStats.totalPlugins}</span>
            <span className="stat-label">Plugins</span>
          </div>
        </div>
      </div>

      <TabView activeIndex={activeIndex} onTabChange={(e) => setActiveIndex(e.index)}>
        <TabPanel header="Dynamic Form" leftIcon="pi pi-list">
          <Card title="User Registration Form" className="demo-card">
            <p className="demo-description">
              This form is generated entirely from JSON configuration. It includes validation, 
              progress tracking, and automatic field management.
            </p>
            
            <DynamicComponent
              type="form"
              config={{
                config: formConfig,
                initialData: {
                  firstName: '',
                  lastName: '',
                  email: '',
                  country: null,
                  interests: null
                },
                onSubmit: handleFormSubmit,
                onError: handleFormError
              }}
              debug={true}
            />
          </Card>
        </TabPanel>

        <TabPanel header="Dynamic DataTable" leftIcon="pi pi-table">
          <Card title="User Management Table" className="demo-card">
            <p className="demo-description">
              This data table supports sorting, filtering, pagination, row selection, and custom actions.
              All features are configurable through JSON.
            </p>
            
            <DynamicComponent
              type="datatable"
              config={{
                data: tableData,
                columns: tableColumns,
                config: tableConfig,
                onEvent: handleTableEvent
              }}
              debug={true}
            />
          </Card>
        </TabPanel>

        <TabPanel header="Theme & Language" leftIcon="pi pi-palette">
          <Card title="Theme and Internationalization Settings" className="demo-card">
            <p className="demo-description">
              Configure themes and languages for the framework. Changes are applied globally and persist across sessions.
            </p>

            <div className="settings-grid" style={{ display: 'grid', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))', gap: '2rem', marginTop: '2rem' }}>
              <Card title="Theme Settings" className="settings-card">
                <div className="setting-info" style={{ marginBottom: '1rem' }}>
                  <p><strong>Current Theme:</strong> {currentTheme}</p>
                  <p><strong>Mode:</strong> {isDarkMode ? 'Dark' : 'Light'}</p>
                </div>

                <div className="theme-selectors" style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  <div>
                    <h4>Dropdown Selector</h4>
                    <ThemeSelector
                      variant="dropdown"
                      showLabel={true}
                      showDarkModeToggle={true}
                      showCustomThemeButton={true}
                      onThemeChange={(theme) => showToast('info', 'Theme Changed', `Switched to ${theme}`)}
                    />
                  </div>

                  <div>
                    <h4>Button Selector</h4>
                    <ThemeSelector
                      variant="buttons"
                      showLabel={true}
                      showDarkModeToggle={true}
                      showCustomThemeButton={true}
                    />
                  </div>

                  <div>
                    <h4>Grid Selector</h4>
                    <ThemeSelector
                      variant="grid"
                      showLabel={true}
                    />
                  </div>
                </div>
              </Card>

              <Card title="Language Settings" className="settings-card">
                <div className="setting-info" style={{ marginBottom: '1rem' }}>
                  <p><strong>Current Language:</strong> {currentLocale}</p>
                  <p><strong>Available Languages:</strong> 11</p>
                </div>

                <div className="language-selectors" style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  <div>
                    <h4>Dropdown Selector</h4>
                    <LanguageSelector
                      variant="dropdown"
                      showLabel={true}
                      showFlag={true}
                      showNativeName={true}
                      onLanguageChange={(locale) => showToast('info', 'Language Changed', `Switched to ${locale}`)}
                    />
                  </div>

                  <div>
                    <h4>Button Selector</h4>
                    <LanguageSelector
                      variant="buttons"
                      showLabel={true}
                      showFlag={true}
                    />
                  </div>
                </div>
              </Card>

              <Card title="Localized Content Demo" className="settings-card">
                <div className="localized-demo" style={{ display: 'flex', flexDirection: 'column', gap: '1rem' }}>
                  <p><strong>Formatted Messages:</strong></p>
                  <ul style={{ listStyle: 'none', padding: 0 }}>
                    <li>✓ {formatMessage({ id: 'common.loading', defaultMessage: 'Loading...' })}</li>
                    <li>✓ {formatMessage({ id: 'common.success', defaultMessage: 'Success' })}</li>
                    <li>✓ {formatMessage({ id: 'common.error', defaultMessage: 'Error' })}</li>
                    <li>✓ {formatMessage({ id: 'form.required', defaultMessage: 'This field is required' })}</li>
                    <li>✓ {formatMessage({ id: 'datatable.noRecords', defaultMessage: 'No records found' })}</li>
                  </ul>

                  <p><strong>Dynamic Components with I18n:</strong></p>
                  <DynamicComponent
                    type="button"
                    config={{
                      label: formatMessage({ id: 'common.save', defaultMessage: 'Save' }),
                      icon: 'pi pi-save',
                      severity: 'primary',
                      onClick: () => showToast('success',
                        formatMessage({ id: 'common.success', defaultMessage: 'Success' }),
                        formatMessage({ id: 'demo.componentShowcase', defaultMessage: 'Component Showcase' })
                      )
                    }}
                  />
                </div>
              </Card>
            </div>
          </Card>
        </TabPanel>

        <TabPanel header="All Components" leftIcon="pi pi-th-large">
          <Card title="Component Showcase" className="demo-card">
            <p className="demo-description">
              Explore all available components in the framework with live examples.
            </p>

            <div className="component-showcase">
              <DynamicComponent
                type="panel"
                config={{
                  header: "Form Components",
                  toggleable: true,
                  content: [
                    {
                      type: "card",
                      config: {
                        title: "Input Components",
                        content: [
                          {
                            type: "input-text",
                            config: {
                              placeholder: "Text Input",
                              label: "Name",
                              floatLabel: true
                            }
                          },
                          {
                            type: "calendar",
                            config: {
                              placeholder: "Select Date",
                              showIcon: true,
                              dateFormat: "mm/dd/yy"
                            }
                          },
                          {
                            
                            type: "dropdown",
                            config: {
                              placeholder: "Select Option",
                              options: [
                                { label: "Option 1", value: "opt1" },
                                { label: "Option 2", value: "opt2" },
                                { label: "Option 3", value: "opt3" }
                              ],
                              filter: true
                            }
                          },
                          {
                            type: "multiselect",
                            config: {
                              placeholder: "Select Multiple",
                              options: [
                                { label: "Item 1", value: "item1" },
                                { label: "Item 2", value: "item2" },
                                { label: "Item 3", value: "item3" },
                                { label: "Item 4", value: "item4" }
                              ],
                              display: "chip"
                            }
                          },
                          {
                            type: "checkbox",
                            config: {
                              label: "Accept Terms",
                              labelPosition: "right"
                            }
                          }
                        ]
                      }
                    }
                  ]
                }}
                debug={true}
              />

              <DynamicComponent
                type="panel"
                config={{
                  header: "Button Components",
                  toggleable: true,
                  content: [
                    {
                      type: "card",
                      config: {
                        title: "Button Variants",
                        content: [
                          {
                            type: "button",
                            config: {
                              label: "Primary",
                              severity: "primary",
                              onClick: () => showToast('info', 'Button Clicked', 'Primary button was clicked')
                            }
                          },
                          {
                            type: "button",
                            config: {
                              label: "Secondary",
                              severity: "secondary",
                              variant: "outlined",
                              onClick: () => showToast('info', 'Button Clicked', 'Secondary button was clicked')
                            }
                          },
                          {
                            type: "button",
                            config: {
                              label: "Success",
                              severity: "success",
                              icon: "pi pi-check",
                              onClick: () => showToast('success', 'Success', 'Success button clicked')
                            }
                          },
                          {
                            type: "button",
                            config: {
                              label: "Danger",
                              severity: "danger",
                              icon: "pi pi-times",
                              variant: "text",
                              onClick: () => showToast('error', 'Error', 'Danger button clicked')
                            }
                          }
                        ]
                      }
                    }
                  ]
                }}
                debug={true}
              />

              <DynamicComponent
                type="panel"
                config={{
                  header: "Layout Components",
                  toggleable: true,
                  content: [
                    {
                      type: "card",
                      config: {
                        title: "Nested Layout Example",
                        content: [
                          {
                            type: "panel",
                            config: {
                              header: "Nested Panel",
                              toggleable: true,
                              size: "small",
                              content: [
                                {
                                  type: "card",
                                  config: {
                                    title: "Nested Card",
                                    subTitle: "Card inside a panel",
                                    size: "small",
                                    content: [
                                      {
                                        type: "input-text",
                                        config: {
                                          placeholder: "Nested input"
                                        }
                                      }
                                    ]
                                  }
                                }
                              ]
                            }
                          }
                        ]
                      }
                    }
                  ]
                }}
                debug={true}
              />

              <DynamicComponent
                type="panel"
                config={{
                  header: "Message Components",
                  toggleable: true,
                  content: [
                    {
                      type: "card",
                      config: {
                        title: "Message Examples",
                        content: [
                          {
                            type: "message",
                            config: {
                              severity: "info",
                              text: "This is an information message"
                            }
                          },
                          {
                            type: "message",
                            config: {
                              severity: "success",
                              text: "Operation completed successfully"
                            }
                          },
                          {
                            type: "message",
                            config: {
                              severity: "warn",
                              text: "This is a warning message"
                            }
                          },
                          {
                            type: "message",
                            config: {
                              severity: "error",
                              text: "An error occurred"
                            }
                          }
                        ]
                      }
                    }
                  ]
                }}
                debug={true}
              />

              <DynamicComponent
                type="panel"
                config={{
                  header: "Miscellaneous Components",
                  toggleable: true,
                  content: [
                    {
                      type: "card",
                      config: {
                        title: "Progress Indicators",
                        content: [
                          {
                            type: "progressbar",
                            config: {
                              value: 75,
                              showValue: true,
                              label: "Loading Progress"
                            }
                          },
                          {
                            type: "progressbar",
                            config: {
                              mode: "indeterminate",
                              label: "Processing..."
                            }
                          }
                        ]
                      }
                    }
                  ]
                }}
                debug={true}
              />
            </div>
          </Card>
        </TabPanel>

        <TabPanel header="Component Registry" leftIcon="pi pi-cog">
          <Card title="Component Registry Information" className="demo-card">
            <p className="demo-description">
              The component registry manages all available components and their configurations.
            </p>
            
            <div className="registry-info">
              <h3>Registered Components</h3>
              <div className="component-list">
                {componentRegistry.getAll().map(comp => (
                  <div key={comp.name} className="component-item">
                    <div className="component-header">
                      <i className={comp.icon} />
                      <strong>{comp.name}</strong>
                      <span className="component-category">{comp.category}</span>
                    </div>
                    <p className="component-description">{comp.description}</p>
                    <div className="component-tags">
                      {comp.tags.map(tag => (
                        <span key={tag} className="tag">{tag}</span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </TabPanel>

        <TabPanel header="JSON Configuration" leftIcon="pi pi-code">
          <Card title="Configuration Examples" className="demo-card">
            <p className="demo-description">
              Here are the JSON configurations used to generate the components above.
            </p>
            
            <div className="config-examples">
              <h3>Form Configuration</h3>
              <pre className="config-code">
                {JSON.stringify(formConfig, null, 2)}
              </pre>
              
              <h3>DataTable Configuration</h3>
              <pre className="config-code">
                {JSON.stringify({ columns: tableColumns, config: tableConfig }, null, 2)}
              </pre>
            </div>
          </Card>
        </TabPanel>
      </TabView>
    </div>
  );
};

export default DemoApp;
