import React, { useState, useCallback, useMemo } from 'react';
import PropTypes from 'prop-types';
import { MultiSelect } from 'primereact/multiselect';
import FloatLabel from './FloatLabel';

const DynamicMultiSelect = ({
  value = [],
  onChange,
  onBlur,
  onFocus,
  onShow,
  onHide,
  onSelectAll,
  options = [],
  optionLabel = 'label',
  optionValue = 'value',
  optionDisabled = 'disabled',
  optionGroupLabel = 'label',
  optionGroupChildren = 'items',
  placeholder = 'Select options',
  disabled = false,
  required = false,
  filter = false,
  filterBy = 'label',
  filterPlaceholder = 'Search...',
  filterMatchMode = 'contains',
  showClear = false,
  showSelectAll = true,
  selectAllLabel = 'Select All',
  maxSelectedLabels = 3,
  selectedItemsLabel = '{0} items selected',
  autoFocus = false,
  className = '',
  style = {},
  panelClassName = '',
  panelStyle = {},
  scrollHeight = '200px',
  id,
  name,
  label,
  floatLabel = false,
  emptyMessage = 'No options available',
  emptyFilterMessage = 'No results found',
  virtualScrollerOptions,
  tooltip,
  size = 'normal',
  variant = 'outlined',
  display = 'comma', // comma, chip
  itemTemplate,
  selectedItemTemplate,
  headerTemplate,
  footerTemplate,
  onEvent,
  debug = false,
  ...props
}) => {
  const [focused, setFocused] = useState(false);
  const [panelVisible, setPanelVisible] = useState(false);

  // Process options to ensure consistent format
  const processedOptions = useMemo(() => {
    if (!Array.isArray(options)) {
      return [];
    }

    return options.map(option => {
      // Handle string options
      if (typeof option === 'string' || typeof option === 'number') {
        return { label: String(option), value: option };
      }

      // Handle object options
      if (typeof option === 'object' && option !== null) {
        return {
          ...option,
          label: option[optionLabel] || option.label || String(option.value || option),
          value: option[optionValue] !== undefined ? option[optionValue] : option.value,
          disabled: option[optionDisabled] || option.disabled || false
        };
      }

      return { label: String(option), value: option };
    });
  }, [options, optionLabel, optionValue, optionDisabled]);

  const handleChange = useCallback((e) => {
    const newValue = e.value || [];
    
    if (debug) {
      console.log('DynamicMultiSelect change:', { name, value: newValue, selectedOptions: e.selectedOption });
    }

    if (onChange) {
      onChange(newValue, e);
    }

    if (onEvent) {
      onEvent('change', { 
        value: newValue, 
        name, 
        selectedOptions: e.selectedOption,
        event: e 
      });
    }
  }, [onChange, onEvent, name, debug]);

  const handleBlur = useCallback((e) => {
    setFocused(false);
    
    if (onBlur) {
      onBlur(e);
    }

    if (onEvent) {
      onEvent('blur', { value, name, event: e });
    }
  }, [onBlur, onEvent, value, name]);

  const handleFocus = useCallback((e) => {
    setFocused(true);
    
    if (onFocus) {
      onFocus(e);
    }

    if (onEvent) {
      onEvent('focus', { value, name, event: e });
    }
  }, [onFocus, onEvent, value, name]);

  const handleShow = useCallback((e) => {
    setPanelVisible(true);
    
    if (onShow) {
      onShow(e);
    }

    if (onEvent) {
      onEvent('show', { value, name, event: e });
    }
  }, [onShow, onEvent, value, name]);

  const handleHide = useCallback((e) => {
    setPanelVisible(false);
    
    if (onHide) {
      onHide(e);
    }

    if (onEvent) {
      onEvent('hide', { value, name, event: e });
    }
  }, [onHide, onEvent, value, name]);

  const handleSelectAll = useCallback((e) => {
    if (onSelectAll) {
      onSelectAll(e);
    }

    if (onEvent) {
      onEvent('selectAll', { 
        value: e.value, 
        name, 
        checked: e.checked,
        event: e 
      });
    }
  }, [onSelectAll, onEvent, name]);

  // Build CSS classes
  const multiSelectClasses = [
    'dynamic-multiselect',
    `dynamic-multiselect--${size}`,
    `dynamic-multiselect--${variant}`,
    `dynamic-multiselect--${display}`,
    focused ? 'dynamic-multiselect--focused' : '',
    panelVisible ? 'dynamic-multiselect--open' : '',
    disabled ? 'dynamic-multiselect--disabled' : '',
    required ? 'dynamic-multiselect--required' : '',
    className
  ].filter(Boolean).join(' ');

  // Find selected options for display
  const selectedOptions = useMemo(() => {
    if (!Array.isArray(value)) return [];
    return processedOptions.filter(option => value.includes(option.value));
  }, [processedOptions, value]);

  // Custom item template
  const defaultItemTemplate = useCallback((option) => {
    if (itemTemplate && typeof itemTemplate === 'function') {
      return itemTemplate(option);
    }

    return (
      <div className="multiselect-item">
        <span className="multiselect-item__label">{option.label}</span>
        {option.description && (
          <small className="multiselect-item__description">{option.description}</small>
        )}
      </div>
    );
  }, [itemTemplate]);

  // Custom selected item template
  const defaultSelectedItemTemplate = useCallback((option) => {
    if (selectedItemTemplate && typeof selectedItemTemplate === 'function') {
      return selectedItemTemplate(option);
    }

    return option?.label;
  }, [selectedItemTemplate]);

  // MultiSelect props
  const multiSelectProps = {
    id: id || name,
    name,
    value: value || [],
    options: processedOptions,
    onChange: handleChange,
    onBlur: handleBlur,
    onFocus: handleFocus,
    onShow: handleShow,
    onHide: handleHide,
    onSelectAll: handleSelectAll,
    placeholder,
    disabled,
    required,
    filter,
    filterBy,
    filterPlaceholder,
    filterMatchMode,
    showClear,
    showSelectAll,
    selectAllLabel,
    maxSelectedLabels,
    selectedItemsLabel,
    autoFocus,
    className: multiSelectClasses,
    style,
    panelClassName,
    panelStyle,
    scrollHeight,
    emptyMessage,
    emptyFilterMessage,
    virtualScrollerOptions,
    tooltip,
    display,
    itemTemplate: defaultItemTemplate,
    selectedItemTemplate: defaultSelectedItemTemplate,
    headerTemplate,
    footerTemplate,
    'aria-label': label || placeholder,
    'aria-required': required,
    'data-component-type': 'multiselect',
    'data-field-name': name,
    ...props
  };

  // Remove undefined props
  Object.keys(multiSelectProps).forEach(key => {
    if (multiSelectProps[key] === undefined) {
      delete multiSelectProps[key];
    }
  });

  const renderMultiSelect = () => <MultiSelect {...multiSelectProps} />;

  const renderWithFloatLabel = (multiSelect) => {
    if (!floatLabel || !label) {
      return multiSelect;
    }

    return (
      <FloatLabel>
        {multiSelect}
        <label htmlFor={id || name}>{label}</label>
      </FloatLabel>
    );
  };

  let finalMultiSelect = renderMultiSelect();
  finalMultiSelect = renderWithFloatLabel(finalMultiSelect);

  return (
    <div className="dynamic-multiselect-wrapper">
      {debug && (
        <div className="dynamic-multiselect__debug">
          <small>
            Selected: {Array.isArray(value) ? value.length : 0} / {processedOptions.length} | 
            Display: {display} | 
            Panel: {panelVisible ? 'Open' : 'Closed'}
          </small>
        </div>
      )}
      {finalMultiSelect}
    </div>
  );
};

DynamicMultiSelect.propTypes = {
  value: PropTypes.array,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onFocus: PropTypes.func,
  onShow: PropTypes.func,
  onHide: PropTypes.func,
  onSelectAll: PropTypes.func,
  options: PropTypes.array,
  optionLabel: PropTypes.string,
  optionValue: PropTypes.string,
  optionDisabled: PropTypes.string,
  optionGroupLabel: PropTypes.string,
  optionGroupChildren: PropTypes.string,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  filter: PropTypes.bool,
  filterBy: PropTypes.string,
  filterPlaceholder: PropTypes.string,
  filterMatchMode: PropTypes.string,
  showClear: PropTypes.bool,
  showSelectAll: PropTypes.bool,
  selectAllLabel: PropTypes.string,
  maxSelectedLabels: PropTypes.number,
  selectedItemsLabel: PropTypes.string,
  autoFocus: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object,
  panelClassName: PropTypes.string,
  panelStyle: PropTypes.object,
  scrollHeight: PropTypes.string,
  id: PropTypes.string,
  name: PropTypes.string,
  label: PropTypes.string,
  floatLabel: PropTypes.bool,
  emptyMessage: PropTypes.string,
  emptyFilterMessage: PropTypes.string,
  virtualScrollerOptions: PropTypes.object,
  tooltip: PropTypes.string,
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  variant: PropTypes.oneOf(['outlined', 'filled']),
  display: PropTypes.oneOf(['comma', 'chip']),
  itemTemplate: PropTypes.func,
  selectedItemTemplate: PropTypes.func,
  headerTemplate: PropTypes.func,
  footerTemplate: PropTypes.func,
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicMultiSelect;
