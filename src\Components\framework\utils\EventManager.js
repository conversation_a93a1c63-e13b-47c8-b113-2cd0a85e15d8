

class EventManager {
  constructor() {
    this.listeners = new Map();
    this.history = [];
    this.maxHistorySize = 1000;
    this.debug = false;
  }

  on(eventName, callback, options = {}) {
    if (!this.listeners.has(eventName)) {
      this.listeners.set(eventName, []);
    }

    const listener = {
      callback,
      once: options.once || false,
      priority: options.priority || 0,
      id: this.generateId(),
      context: options.context || null
    };

    const listeners = this.listeners.get(eventName);
    listeners.push(listener);

    listeners.sort((a, b) => b.priority - a.priority);

    if (this.debug) {
      console.log(`EventManager: Added listener for '${eventName}'`, listener);
    }

    return () => this.off(eventName, listener.id);
  }

  once(eventName, callback, options = {}) {
    return this.on(eventName, callback, { ...options, once: true });
  }

  off(eventName, listenerId) {
    if (!this.listeners.has(eventName)) {
      return;
    }

    const listeners = this.listeners.get(eventName);
    const index = listeners.findIndex(l => l.id === listenerId);
    
    if (index !== -1) {
      listeners.splice(index, 1);
      
      if (listeners.length === 0) {
        this.listeners.delete(eventName);
      }

      if (this.debug) {
        console.log(`EventManager: Removed listener for '${eventName}'`, listenerId);
      }
    }
  }

  removeAllListeners(eventName) {
    if (eventName) {
      this.listeners.delete(eventName);
    } else {
      this.listeners.clear();
    }

    if (this.debug) {
      console.log(`EventManager: Removed all listeners${eventName ? ` for '${eventName}'` : ''}`);
    }
  }

  emit(eventName, data = null, options = {}) {
    const eventData = {
      name: eventName,
      data,
      timestamp: new Date().toISOString(),
      id: this.generateId(),
      source: options.source || 'unknown'
    };

    this.addToHistory(eventData);

    if (this.debug) {
      console.log(`EventManager: Emitting '${eventName}'`, eventData);
    }

    const listeners = this.listeners.get(eventName) || [];
    const results = [];

    for (const listener of listeners) {
      try {
        const result = listener.callback.call(listener.context, eventData);
        results.push(result);

        if (listener.once) {
          this.off(eventName, listener.id);
        }

        if (options.stopPropagation && result === false) {
          break;
        }
      } catch (error) {
        console.error(`EventManager: Error in listener for '${eventName}':`, error);
        
        this.emit('error', {
          originalEvent: eventName,
          error: error.message,
          stack: error.stack,
          listener: listener.id
        });
      }
    }

    return results;
  }

  async emitAsync(eventName, data = null, options = {}) {
    const eventData = {
      name: eventName,
      data,
      timestamp: new Date().toISOString(),
      id: this.generateId(),
      source: options.source || 'unknown'
    };

    this.addToHistory(eventData);

    if (this.debug) {
      console.log(`EventManager: Emitting async '${eventName}'`, eventData);
    }

    const listeners = this.listeners.get(eventName) || [];
    const results = [];

    for (const listener of listeners) {
      try {
        const result = await Promise.resolve(listener.callback.call(listener.context, eventData));
        results.push(result);

        if (listener.once) {
          this.off(eventName, listener.id);
        }

        if (options.stopPropagation && result === false) {
          break;
        }
      } catch (error) {
        console.error(`EventManager: Error in async listener for '${eventName}':`, error);
        
        this.emit('error', {
          originalEvent: eventName,
          error: error.message,
          stack: error.stack,
          listener: listener.id
        });
      }
    }

    return results;
  }

  hasListeners(eventName) {
    return this.listeners.has(eventName) && this.listeners.get(eventName).length > 0;
  }

  getListenerCount(eventName) {
    return this.listeners.has(eventName) ? this.listeners.get(eventName).length : 0;
  }

  getEventNames() {
    return Array.from(this.listeners.keys());
  }

  addToHistory(eventData) {
    this.history.push(eventData);
    
    if (this.history.length > this.maxHistorySize) {
      this.history.shift();
    }
  }

  getHistory(eventName = null, limit = null) {
    let history = this.history;
    
    if (eventName) {
      history = history.filter(event => event.name === eventName);
    }
    
    if (limit) {
      history = history.slice(-limit);
    }
    
    return history;
  }

  clearHistory() {
    this.history = [];
  }

  generateId() {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  setDebug(enabled) {
    this.debug = enabled;
  }

  getStats() {
    const eventNames = this.getEventNames();
    const totalListeners = eventNames.reduce((sum, name) => sum + this.getListenerCount(name), 0);
    
    return {
      totalEvents: eventNames.length,
      totalListeners,
      historySize: this.history.length,
      maxHistorySize: this.maxHistorySize,
      eventBreakdown: eventNames.reduce((acc, name) => {
        acc[name] = this.getListenerCount(name);
        return acc;
      }, {})
    };
  }

  namespace(namespace) {
    return {
      on: (eventName, callback, options) => 
        this.on(`${namespace}:${eventName}`, callback, options),
      once: (eventName, callback, options) => 
        this.once(`${namespace}:${eventName}`, callback, options),
      off: (eventName, listenerId) => 
        this.off(`${namespace}:${eventName}`, listenerId),
      emit: (eventName, data, options) => 
        this.emit(`${namespace}:${eventName}`, data, options),
      emitAsync: (eventName, data, options) => 
        this.emitAsync(`${namespace}:${eventName}`, data, options),
      hasListeners: (eventName) => 
        this.hasListeners(`${namespace}:${eventName}`),
      getListenerCount: (eventName) => 
        this.getListenerCount(`${namespace}:${eventName}`)
    };
  }

  destroy() {
    this.removeAllListeners();
    this.clearHistory();
    
    if (this.debug) {
      console.log('EventManager: Destroyed');
    }
  }
}

export default EventManager;
