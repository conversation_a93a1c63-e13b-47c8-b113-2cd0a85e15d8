import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { InputTextarea } from 'primereact/inputtextarea';
import FloatLabel from './FloatLabel';

const DynamicTextarea = ({
  value = '',
  onChange,
  onBlur,
  onFocus,
  onKeyDown,
  onInput,
  placeholder = '',
  disabled = false,
  readOnly = false,
  required = false,
  rows = 3,
  cols = 30,
  autoResize = false,
  maxLength,
  className = '',
  style = {},
  id,
  name,
  label,
  floatLabel = false,
  tooltip,
  size = 'normal', // small, normal, large
  variant = 'outlined', // outlined, filled
  onEvent,
  debug = false,
  ...props
}) => {
  const [focused, setFocused] = useState(false);
  const [charCount, setCharCount] = useState(value?.length || 0);

  const handleChange = useCallback((e) => {
    const newValue = e.target.value;
    setCharCount(newValue.length);
    
    if (debug) {
      console.log('DynamicTextarea change:', { name, value: newValue, length: newValue.length });
    }

    if (onChange) {
      onChange(newValue, e);
    }

    if (onEvent) {
      onEvent('change', { value: newValue, name, length: newValue.length, event: e });
    }
  }, [onChange, onEvent, name, debug]);

  const handleFocus = useCallback((e) => {
    setFocused(true);

    if (debug) {
      console.log('DynamicTextarea focus:', { name });
    }

    if (onFocus) {
      onFocus(e);
    }

    if (onEvent) {
      onEvent('focus', { name, event: e });
    }
  }, [onFocus, onEvent, name, debug]);

  const handleBlur = useCallback((e) => {
    setFocused(false);

    if (debug) {
      console.log('DynamicTextarea blur:', { name, value });
    }

    if (onBlur) {
      onBlur(e);
    }

    if (onEvent) {
      onEvent('blur', { name, value, event: e });
    }
  }, [onBlur, onEvent, name, value, debug]);

  const handleKeyDown = useCallback((e) => {
    if (debug) {
      console.log('DynamicTextarea keydown:', { name, key: e.key });
    }

    if (onKeyDown) {
      onKeyDown(e);
    }

    if (onEvent) {
      onEvent('keydown', { name, key: e.key, event: e });
    }
  }, [onKeyDown, onEvent, name, debug]);

  const handleInput = useCallback((e) => {
    if (debug) {
      console.log('DynamicTextarea input:', { name, value: e.target.value });
    }

    if (onInput) {
      onInput(e);
    }

    if (onEvent) {
      onEvent('input', { name, value: e.target.value, event: e });
    }
  }, [onInput, onEvent, name, debug]);

  // Build class names
  const textareaClasses = [
    'dynamic-textarea',
    `dynamic-textarea--${size}`,
    `dynamic-textarea--${variant}`,
    focused && 'dynamic-textarea--focused',
    disabled && 'dynamic-textarea--disabled',
    readOnly && 'dynamic-textarea--readonly',
    required && 'dynamic-textarea--required',
    autoResize && 'dynamic-textarea--autoresize',
    className
  ].filter(Boolean).join(' ');

  // Textarea props
  const textareaProps = {
    id,
    name,
    value,
    onChange: handleChange,
    onFocus: handleFocus,
    onBlur: handleBlur,
    onKeyDown: handleKeyDown,
    onInput: handleInput,
    placeholder,
    disabled,
    readOnly,
    required,
    rows,
    cols,
    autoResize,
    maxLength,
    className: textareaClasses,
    style,
    tooltip,
    'data-component-type': 'textarea',
    'data-textarea-size': size,
    'data-textarea-variant': variant,
    ...props
  };

  // Remove undefined props
  Object.keys(textareaProps).forEach(key => {
    if (textareaProps[key] === undefined) {
      delete textareaProps[key];
    }
  });

  if (debug) {
    console.log('DynamicTextarea render:', {
      name,
      value,
      rows,
      cols,
      autoResize,
      maxLength,
      charCount
    });
  }

  const renderTextarea = () => (
    <>
      <InputTextarea {...textareaProps} />
      {maxLength && (
        <small className="dynamic-textarea-counter">
          {charCount} / {maxLength}
        </small>
      )}
    </>
  );

  // Render with float label if specified
  if (floatLabel && label) {
    return (
      <div className="dynamic-textarea-wrapper">
        <FloatLabel>
          {renderTextarea()}
          <label htmlFor={id}>{label}</label>
        </FloatLabel>
      </div>
    );
  }

  // Render with regular label if provided
  if (label) {
    return (
      <div className="dynamic-textarea-wrapper">
        <label htmlFor={id} className="dynamic-textarea-label">
          {label}
          {required && <span className="dynamic-textarea-required">*</span>}
        </label>
        {renderTextarea()}
      </div>
    );
  }

  return renderTextarea();
};

DynamicTextarea.propTypes = {
  value: PropTypes.string,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onFocus: PropTypes.func,
  onKeyDown: PropTypes.func,
  onInput: PropTypes.func,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  readOnly: PropTypes.bool,
  required: PropTypes.bool,
  rows: PropTypes.number,
  cols: PropTypes.number,
  autoResize: PropTypes.bool,
  maxLength: PropTypes.number,
  className: PropTypes.string,
  style: PropTypes.object,
  id: PropTypes.string,
  name: PropTypes.string,
  label: PropTypes.string,
  floatLabel: PropTypes.bool,
  tooltip: PropTypes.string,
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  variant: PropTypes.oneOf(['outlined', 'filled']),
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

DynamicTextarea.defaultProps = {
  value: '',
  placeholder: '',
  disabled: false,
  readOnly: false,
  required: false,
  rows: 3,
  cols: 30,
  autoResize: false,
  className: '',
  style: {},
  floatLabel: false,
  size: 'normal',
  variant: 'outlined',
  debug: false
};

export default DynamicTextarea;

