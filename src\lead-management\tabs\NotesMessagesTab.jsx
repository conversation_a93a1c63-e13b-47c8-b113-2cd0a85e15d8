import React, { useState, useRef } from 'react';
import { Card } from 'primereact/card';
import { <PERSON><PERSON> } from 'primereact/button';
import { InputTextarea } from 'primereact/inputtextarea';
import { Avatar } from 'primereact/avatar';
import { Divider } from 'primereact/divider';
import { Toast } from 'primereact/toast';
import GlassyWhiteButton from '../../components/common/GlassyWhiteButton';
import GlassyBlueButton from '../../components/common/GlassyBlueButton';

const NotesMessagesTab = ({ leadId }) => {
  const toast = useRef(null);
  const [newMessage, setNewMessage] = useState('');
  const [messages] = useState([
    {
      id: 1,
      user: '<PERSON>',
      userInitials: 'JS',
      role: 'Sales Rep',
      message: 'Initial contact made with the prospect. They showed interest in our Jet Fuel supply services.',
      timestamp: '10:08 11/24',
      type: 'note'
    },
    {
      id: 2,
      user: '<PERSON>',
      userInitials: 'SJ',
      role: 'Account Manager',
      message: 'Follow-up call scheduled for next week. Need to prepare pricing proposal.',
      timestamp: '14:30 11/25',
      type: 'note'
    },
    {
      id: 3,
      user: '<PERSON>',
      userInitials: 'JS',
      role: 'Sales Rep',
      message: 'Sent pricing proposal via email. Waiting for their feedback on the terms.',
      timestamp: '09:15 11/26',
      type: 'message'
    },
    {
      id: 4,
      user: 'Mark Stephen',
      userInitials: 'MS',
      role: 'Manager',
      message: 'Great progress! Make sure to follow up within 48 hours if no response.',
      timestamp: '11:20 11/26',
      type: 'note'
    }
  ]);

  const handleSendMessage = () => {
    if (newMessage.trim()) {
      toast.current.show({
        severity: 'success',
        summary: 'Message Sent',
        detail: 'Your message has been added successfully',
        life: 3000
      });
      setNewMessage('');
    }
  };

  const getAvatarColor = (index) => {
    const colors = ['#2196F3', '#4CAF50', '#FF9800', '#9C27B0', '#F44336'];
    return colors[index % colors.length];
  };

  return (
    <div className="notes-messages-tab">
      <Toast ref={toast} />
      
      <Card title="Notes & Messages" className="notes-messages-card">
        {/* Message Input */}
        <div className="message-input-section">
          <InputTextarea
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Write a note or message..."
            rows={3}
            className="message-input"
          />
          <div className="message-actions">
            <GlassyBlueButton
              label="Add Note"
              icon="pi pi-file"
              className="p-button-sm"
              onClick={handleSendMessage}
            />
            <GlassyWhiteButton
              label="Send Message"
              icon="pi pi-send"
              className="p-button-sm p-button-success"
              onClick={handleSendMessage}
            />
          </div>
        </div>

        <Divider />

        {/* Messages Thread */}
        <div className="messages-thread">
          <h4>Conversation History</h4>
          {messages.map((msg, index) => (
            <div key={msg.id} className="message-item">
              <div className="message-header">
                <Avatar
                  label={msg.userInitials}
                  size="normal"
                  shape="circle"
                  style={{ backgroundColor: getAvatarColor(index), color: '#ffffff' }}
                />
                <div className="message-meta">
                  <div className="message-user">
                    <strong>{msg.user}</strong>
                    <span className="user-role">({msg.role})</span>
                  </div>
                  <div className="message-timestamp">
                    <i className="pi pi-clock"></i>
                    {msg.timestamp}
                  </div>
                </div>
                <div className="message-type">
                  {msg.type === 'note' ? (
                    <i className="pi pi-file" title="Note"></i>
                  ) : (
                    <i className="pi pi-envelope" title="Message"></i>
                  )}
                </div>
              </div>
              <div className="message-content">
                <p>{msg.message}</p>
              </div>
              {index < messages.length - 1 && <Divider />}
            </div>
          ))}
        </div>

        {/* Quick Actions */}
        <div className="quick-actions">
          <GlassyWhiteButton
            label="View All Messages"
            icon="pi pi-list"
            className="p-button-sm p-button-outlined"
          />
          <GlassyWhiteButton
            label="Export Thread"
            icon="pi pi-download"
            className="p-button-sm p-button-outlined"
          />
        </div>
      </Card>
    </div>
  );
};

export default NotesMessagesTab;

