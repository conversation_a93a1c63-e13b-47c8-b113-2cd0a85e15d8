.service-orders-view {
  padding: 1.5rem;
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.service-orders-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  background: white;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.service-orders-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
}

.header-actions {
  display: flex;
  gap: 1rem;
  align-items: center;
}

/* Date Header */
.date-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 1rem 1.5rem;
  border-radius: 8px;
  margin-bottom: 1rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.date-info h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #2c3e50;
}

.stats {
  display: flex;
  gap: 1.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.stats span {
  display: flex;
  align-items: center;
}

/* Service Orders List */
.service-orders-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.service-order-card {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.service-order-card .p-card-body {
  padding: 0;
}

.service-order-card .p-card-content {
  padding: 0;
}

/* Flight Header */
.flight-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.flight-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.flight-info .time {
  font-weight: 600;
  font-size: 1rem;
  color: #2c3e50;
}

.flight-info .flight-number {
  font-weight: 600;
  font-size: 1.1rem;
  color: #3498db;
}

.flight-info .airline {
  color: #6c757d;
  font-size: 0.875rem;
}

.flight-info .route {
  display: flex;
  align-items: center;
}

.flight-actions {
  display: flex;
  gap: 0.5rem;
}

/* Flight Details Bar */
.flight-details-bar {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.75rem 1.5rem;
  background: white;
  border-bottom: 1px solid #e9ecef;
  font-size: 0.875rem;
  overflow-x: auto;
}

.flight-details-bar span {
  white-space: nowrap;
  color: #495057;
}

.flight-details-bar .status-badge {
  padding: 0.25rem 0.5rem;
  background: #e9ecef;
  border-radius: 4px;
  font-weight: 500;
}

.flight-details-bar .departure-time {
  color: #e74c3c;
  font-weight: 600;
}

.flight-details-bar .progress {
  font-weight: 600;
  color: #3498db;
}

/* Progress Bar */
.progress-bar-container {
  position: relative;
  padding: 1rem 1.5rem;
  background: white;
}

.progress-bar {
  position: relative;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  overflow: hidden;
}

.progress-fill {
  position: absolute;
  top: 0;
  height: 100%;
  transition: width 0.3s ease;
}

.progress-fill.catering {
  background: linear-gradient(90deg, #27ae60 0%, #2ecc71 100%);
  left: 0;
}

.progress-fill.migration {
  background: linear-gradient(90deg, #e74c3c 0%, #c0392b 100%);
}

.progress-markers {
  position: absolute;
  top: 0.75rem;
  left: 1.5rem;
  right: 1.5rem;
  height: 8px;
}

.marker {
  position: absolute;
  transform: translateX(-50%);
  color: #3498db;
  font-size: 1rem;
}

.marker.catering-marker {
  color: #27ae60;
}

.marker.migration-marker {
  color: #e74c3c;
}

/* Expandable Sections */
.expandable-sections {
  border-top: 1px solid #e9ecef;
}

.expandable-sections .p-tabview {
  background: #f8f9fa;
}

.expandable-sections .p-tabview-nav {
  background: white;
  border-bottom: 1px solid #e9ecef;
}

.expandable-sections .p-tabview-panels {
  background: #f8f9fa;
  padding: 0;
}

.tab-content {
  padding: 1.5rem;
}

/* Catering Section */
.catering-section {
  padding: 1rem;
}

.catering-orders {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

.catering-order {
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1rem;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding-bottom: 0.75rem;
  border-bottom: 1px solid #e9ecef;
}

.order-header h4 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
}

.order-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #6c757d;
}

.order-details {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.order-field {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.order-field label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
}

.order-section {
  margin-bottom: 1rem;
}

.order-section label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #495057;
}

.order-footer {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e9ecef;
}

.expenses-section {
  display: flex;
  gap: 1rem;
  padding-top: 0.75rem;
  border-top: 1px solid #e9ecef;
}

/* File Upload Custom */
.file-upload-custom .p-fileupload-choose {
  width: 100%;
  justify-content: center;
  background: #f8f9fa;
  border: 2px dashed #dee2e6;
  color: #6c757d;
}

.file-upload-custom .p-fileupload-choose:hover {
  background: #e9ecef;
  border-color: #adb5bd;
}

/* Responsive */
@media (max-width: 1200px) {
  .catering-orders {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .service-orders-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .header-actions {
    width: 100%;
    flex-wrap: wrap;
  }

  .header-actions > * {
    flex: 1;
    min-width: 150px;
  }

  .flight-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .flight-details-bar {
    flex-wrap: wrap;
  }

  .order-details {
    grid-template-columns: 1fr;
  }
}

