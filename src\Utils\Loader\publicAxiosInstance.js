

import axios from 'axios';


let publicAxiosInstance = null;

export const configurePublicAxios = (setLoading) => {
  publicAxiosInstance = axios.create();

  let activeRequests = 0;

  publicAxiosInstance.interceptors.request.use(
    (config) => {
      activeRequests++;
      setLoading(true);
      return config;
    },
    (error) => {
      activeRequests--;
      if (activeRequests === 0) setLoading(false);
      return Promise.reject(error);
    }
  );

  publicAxiosInstance.interceptors.response.use(
    (response) => {
      activeRequests--;
      if (activeRequests === 0) setLoading(false);
      return response;
    },
    (error) => {
      activeRequests--;
      if (activeRequests === 0) setLoading(false);

      if (error?.response?.status === 401) {
        // showToast('Unauthorized access. Please log in again.');

      }

      return Promise.reject(error);
    }
  );
};


export const getPublicAxiosInstance = () => {
  if (!publicAxiosInstance) throw new Error('getPublicAxiosInstance instance not configured');
  return publicAxiosInstance;
};
 