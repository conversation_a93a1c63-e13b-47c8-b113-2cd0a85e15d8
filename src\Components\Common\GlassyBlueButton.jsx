import React from "react";
import { Button } from "primereact/button";

export default function GlassyBlueButton({
  label,
  icon,
  severity = "primary",
  loading = false,
  onClick,
  className = ""
}) {
  const baseStyle = "glass-btn-primary";
  return (
    <Button
      label={label}
      icon={icon}
      severity={severity}
      loading={loading}
      onClick={onClick}
      className={`${baseStyle} ${className}`}
    />
  );
}