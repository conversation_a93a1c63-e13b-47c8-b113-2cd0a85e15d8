import React, { useState, useRef } from 'react';
import { Card } from 'primereact/card';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { TabView, TabPanel } from 'primereact/tabview';
import { FileUpload } from 'primereact/fileupload';
import { Toast } from 'primereact/toast';
import { Badge } from 'primereact/badge';
import './ServiceOrdersView.css';

/**
 * ServiceOrdersView - Flight Service Orders Management
 */
const ServiceOrdersView = () => {
  const toast = useRef(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedDate, setSelectedDate] = useState('Today');
  const [selectedAirport, setSelectedAirport] = useState('HTC');
  const [activeTab, setActiveTab] = useState(0);

  const dateOptions = [
    { label: 'Today', value: 'Today' },
    { label: 'Tomorrow', value: 'Tomorrow' },
    { label: 'This Week', value: 'This Week' },
    { label: 'This Month', value: 'This Month' }
  ];

  const airportOptions = [
    { label: 'HTC', value: 'HTC' },
    { label: 'DXB', value: 'DXB' },
    { label: 'AUH', value: 'AUH' },
    { label: 'SHJ', value: 'SHJ' }
  ];

  const serviceOrders = [
    {
      id: 1,
      date: 'Tue 5 Aug 2025',
      time: '14:00',
      flightNumber: 'VG5373',
      airline: 'ASM ATR',
      route: { from: 'DMDW', to: 'DBSV' },
      aircraft: 'A-4',
      tail: 'ISD',
      executList: 'EXECULIST MIDDLE EAST',
      status: 'NA',
      service: 'TBD',
      catering: 'Ok',
      migration: 'Ok',
      time2: '14:00+',
      type: 'T/I',
      passengers: 'N',
      departure: '3:33 DEPARTURE',
      progress: 0,
      cateringStatus: 'pending',
      migrationStatus: 'completed'
    },
    {
      id: 2,
      date: 'Tue 5 Aug 2025',
      time: '14:45',
      flightNumber: 'ASU38I',
      airline: 'Aeroplane-XX',
      route: { from: 'OBSV', to: 'DBSV' },
      aircraft: 'A-4',
      tail: 'ISD',
      executList: 'EXECULIST MIDDLE EAST',
      status: 'NR',
      service: 'TBD',
      catering: 'Ok',
      migration: 'Ok',
      time2: '14:00+',
      type: 'T/I',
      passengers: 'N',
      departure: '2:33 DEPARTURE',
      progress: 0,
      cateringStatus: 'in-progress',
      migrationStatus: 'pending'
    }
  ];

  const stats = {
    total: 8,
    active: 4,
    nouns: 1,
    completed: 3
  };

  const showToast = (severity, summary, detail) => {
    toast.current?.show({ severity, summary, detail, life: 3000 });
  };

  const handleAddServiceOrder = () => {
    showToast('info', 'Add Service Order', 'Opening service order form...');
  };

  const handleSendCatering = (orderId, direction) => {
    showToast('success', 'Catering Order', `Sending ${direction} catering order for flight ${orderId}`);
  };

  const handleSendMigration = (orderId, direction) => {
    showToast('success', 'Migration Order', `Sending ${direction} migration order for flight ${orderId}`);
  };

  return (
    <div className="service-orders-view">
      <Toast ref={toast} />

      {/* Header */}
      <div className="service-orders-header">
        <h2>Service Orders</h2>
        <div className="header-actions">
          <span className="p-input-icon-left">
            <i className="pi pi-search" />
            <InputText
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              placeholder="Search flights, tails, customers..."
              style={{ width: '300px' }}
            />
          </span>
          <Dropdown
            value={selectedDate}
            options={dateOptions}
            onChange={(e) => setSelectedDate(e.value)}
            style={{ width: '150px' }}
          />
          <Dropdown
            value={selectedAirport}
            options={airportOptions}
            onChange={(e) => setSelectedAirport(e.value)}
            style={{ width: '100px' }}
          />
          <Button
            icon="pi pi-filter"
            label="Filters"
            className="p-button-outlined"
          />
          <Button
            icon="pi pi-plus"
            label="Add Flight/Service Order"
            onClick={handleAddServiceOrder}
          />
        </div>
      </div>

      {/* Date Header with Stats */}
      <div className="date-header">
        <div className="date-info">
          <h3>Tue 5 Aug 2025</h3>
          <div className="stats">
            <span>Total: {stats.total}</span>
            <span>Active: {stats.active}</span>
            <span>Nouns: {stats.nouns}</span>
            <span>Completed: {stats.completed}</span>
          </div>
        </div>
        <Button icon="pi pi-refresh" className="p-button-text p-button-rounded" />
      </div>

      {/* Service Orders List */}
      <div className="service-orders-list">
        {serviceOrders.map((order) => (
          <Card key={order.id} className="service-order-card">
            {/* Flight Header */}
            <div className="flight-header">
              <div className="flight-info">
                <span className="time">{order.time}</span>
                <span className="flight-number">{order.flightNumber}</span>
                <span className="airline">{order.airline}</span>
                <span className="route">
                  <Badge value={order.route.from} severity="info" />
                  <i className="pi pi-arrow-right" style={{ margin: '0 0.5rem' }} />
                  <Badge value={order.route.to} severity="info" />
                </span>
              </div>
              <div className="flight-actions">
                <Button icon="pi pi-check-circle" className="p-button-text p-button-rounded" />
                <Button icon="pi pi-copy" className="p-button-text p-button-rounded" />
                <Button icon="pi pi-ellipsis-v" className="p-button-text p-button-rounded" />
              </div>
            </div>

            {/* Flight Details Bar */}
            <div className="flight-details-bar">
              <span>A-4</span>
              <span>ISD</span>
              <span>EXECULIST MIDDLE EAST</span>
              <span className="status-badge">{order.status}</span>
              <span className="status-badge">{order.service}</span>
              <span>TBD</span>
              <span>Ok</span>
              <span>Ok</span>
              <span>{order.time2}</span>
              <span>{order.type}</span>
              <span>{order.passengers}</span>
              <span className="departure-time">{order.departure}</span>
              <span className="progress">{order.progress}/0</span>
            </div>

            {/* Progress Bar */}
            <div className="progress-bar-container">
              <div className="progress-bar">
                <div 
                  className="progress-fill catering" 
                  style={{ width: order.cateringStatus === 'completed' ? '50%' : '25%' }}
                />
                <div 
                  className="progress-fill migration" 
                  style={{ width: order.migrationStatus === 'completed' ? '50%' : '0%', left: '50%' }}
                />
              </div>
              <div className="progress-markers">
                <div className="marker catering-marker" style={{ left: '25%' }}>
                  <i className="pi pi-circle-fill" />
                </div>
                <div className="marker migration-marker" style={{ left: '75%' }}>
                  <i className="pi pi-circle-fill" />
                </div>
              </div>
            </div>

            {/* Expandable Sections */}
            <div className="expandable-sections">
              <TabView activeIndex={activeTab} onTabChange={(e) => setActiveTab(e.index)}>
                {/* Hotels Tab */}
                <TabPanel header="Hotels">
                  <div className="tab-content">
                    <p>Hotel information will be displayed here</p>
                  </div>
                </TabPanel>

                {/* Catering Tab */}
                <TabPanel header={<span>Catering <Badge value="2" severity="info" /></span>}>
                  <div className="catering-section">
                    <div className="catering-orders">
                      {/* DMDW Catering */}
                      <div className="catering-order">
                        <div className="order-header">
                          <h4>Catering_a → DMDW</h4>
                          <div className="order-actions">
                            <span>by</span>
                            <Button icon="pi pi-star" className="p-button-text p-button-sm" />
                            <span>Aviation Service I</span>
                            <Button icon="pi pi-times" className="p-button-text p-button-sm" />
                            <Button icon="pi pi-bars" className="p-button-text p-button-sm" />
                            <Button icon="pi pi-plus" className="p-button-text p-button-sm" />
                          </div>
                        </div>
                        <div className="order-details">
                          <div className="order-field">
                            <label>NR</label>
                            <Dropdown placeholder="Select" style={{ width: '100%' }} />
                          </div>
                          <div className="order-field">
                            <label>Service</label>
                            <Dropdown value="TBD" options={[{ label: 'TBD', value: 'TBD' }]} style={{ width: '100%' }} />
                          </div>
                          <div className="order-field">
                            <label>For</label>
                            <Dropdown value="Pax" options={[{ label: 'Pax', value: 'Pax' }]} style={{ width: '100%' }} />
                          </div>
                        </div>
                        <div className="order-section">
                          <label>Order</label>
                          <InputText placeholder="Enter order details" style={{ width: '100%' }} />
                        </div>
                        <div className="order-section">
                          <label>Pax Remarks</label>
                          <InputText placeholder="Enter remarks" style={{ width: '100%' }} />
                        </div>
                        <div className="order-section">
                          <label>Confirmation</label>
                          <FileUpload
                            mode="basic"
                            name="confirmation"
                            accept="*/*"
                            maxFileSize={10000000}
                            chooseLabel="Drop your file(s) here or browse"
                            className="file-upload-custom"
                          />
                        </div>
                        <div className="order-footer">
                          <Button label="Send" className="p-button-sm" onClick={() => handleSendCatering(order.id, 'DMDW')} />
                          <Button label="Cancel" className="p-button-sm p-button-secondary" />
                          <Button label="Send Quote Only" className="p-button-sm p-button-outlined" />
                        </div>
                        <div className="expenses-section">
                          <Button label="+ Expenses (0)" className="p-button-text p-button-sm" />
                          <Button label="+ Add Expense" className="p-button-text p-button-sm" />
                        </div>
                      </div>

                      {/* DEBK Catering */}
                      <div className="catering-order">
                        <div className="order-header">
                          <h4>Catering → DEBK</h4>
                          <div className="order-actions">
                            <span>by</span>
                            <Button icon="pi pi-star" className="p-button-text p-button-sm" />
                            <span>Aviation Service I</span>
                            <Button icon="pi pi-times" className="p-button-text p-button-sm" />
                            <Button icon="pi pi-bars" className="p-button-text p-button-sm" />
                            <Button icon="pi pi-plus" className="p-button-text p-button-sm" />
                          </div>
                        </div>
                        <div className="order-details">
                          <div className="order-field">
                            <label>NR</label>
                            <Dropdown placeholder="Select" style={{ width: '100%' }} />
                          </div>
                          <div className="order-field">
                            <label>Service</label>
                            <Dropdown value="TBD" options={[{ label: 'TBD', value: 'TBD' }]} style={{ width: '100%' }} />
                          </div>
                          <div className="order-field">
                            <label>For</label>
                            <Dropdown value="Pax" options={[{ label: 'Pax', value: 'Pax' }]} style={{ width: '100%' }} />
                          </div>
                        </div>
                        <div className="order-section">
                          <label>Order</label>
                          <InputText placeholder="Enter order details" style={{ width: '100%' }} />
                        </div>
                        <div className="order-section">
                          <label>Pax Remarks</label>
                          <InputText placeholder="Enter remarks" style={{ width: '100%' }} />
                        </div>
                        <div className="order-section">
                          <label>Confirmation</label>
                          <FileUpload
                            mode="basic"
                            name="confirmation"
                            accept="*/*"
                            maxFileSize={10000000}
                            chooseLabel="Drop your file(s) here or browse"
                            className="file-upload-custom"
                          />
                        </div>
                        <div className="order-footer">
                          <Button label="Send" className="p-button-sm" onClick={() => handleSendCatering(order.id, 'DEBK')} />
                          <Button label="Cancel" className="p-button-sm p-button-secondary" />
                          <Button label="Send Quote Only" className="p-button-sm p-button-outlined" />
                        </div>
                        <div className="expenses-section">
                          <Button label="+ Expenses (0)" className="p-button-text p-button-sm" />
                          <Button label="+ Add Expense" className="p-button-text p-button-sm" />
                        </div>
                      </div>
                    </div>
                  </div>
                </TabPanel>

                {/* Customers / Migration Tab */}
                <TabPanel header="Customers / Migration">
                  <div className="tab-content">
                    <p>Customer and migration information will be displayed here</p>
                  </div>
                </TabPanel>
              </TabView>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default ServiceOrdersView;

