import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Card } from 'primereact/card';
import { Tag } from 'primereact/tag';
import CRMNavigation from '../crm/CRMNavigation';

const LeadManagementRoot = () => {
  const navigate = useNavigate();
  const [globalFilter, setGlobalFilter] = useState('');
  const [selectedLeads, setSelectedLeads] = useState([]);

  const leads = [
    {
      id: 'ASK-000001',
      company: 'Mahan Air',
      contact: '<PERSON>',
      email: '<EMAIL>',
      phone: '+98 21 4884 5000',
      status: 'Introduction',
      priority: 'Medium',
      revenue: '$7000',
      source: 'Event',
      rating: 2,
      createdDate: '2024-01-15'
    },
    {
      id: 'ASK-000002',
      company: 'Emirates Airlines',
      contact: '<PERSON>',
      email: '<EMAIL>',
      phone: '+971 4 214 4444',
      status: 'Qualification',
      priority: 'High',
      revenue: '$15000',
      source: 'Website',
      rating: 4,
      createdDate: '2024-01-20'
    },
    {
      id: 'ASK-000003',
      company: 'Qatar Airways',
      contact: 'Ahmed Ali',
      email: '<EMAIL>',
      phone: '+974 4023 0000',
      status: 'Proposal',
      priority: 'High',
      revenue: '$25000',
      source: 'Referral',
      rating: 5,
      createdDate: '2024-02-01'
    },
    {
      id: 'ASK-000004',
      company: 'Etihad Airways',
      contact: 'Michael Brown',
      email: '<EMAIL>',
      phone: '+971 2 599 0000',
      status: 'Negotiation',
      priority: 'Medium',
      revenue: '$12000',
      source: 'Cold Call',
      rating: 3,
      createdDate: '2024-02-10'
    },
    {
      id: 'ASK-000005',
      company: 'Turkish Airlines',
      contact: 'Ayse Yilmaz',
      email: '<EMAIL>',
      phone: '+90 ************',
      status: 'Introduction',
      priority: 'Low',
      revenue: '$5000',
      source: 'Event',
      rating: 2,
      createdDate: '2024-02-15'
    }
  ];

  const statusOptions = [
    { label: 'All Status', value: null },
    { label: 'Introduction', value: 'Introduction' },
    { label: 'Qualification', value: 'Qualification' },
    { label: 'Proposal', value: 'Proposal' },
    { label: 'Negotiation', value: 'Negotiation' }
  ];

  const priorityOptions = [
    { label: 'All Priority', value: null },
    { label: 'Low', value: 'Low' },
    { label: 'Medium', value: 'Medium' },
    { label: 'High', value: 'High' }
  ];

  const handleRowClick = (rowData) => {
    navigate(`/lead/${rowData.id}`);
  };

  const companyTemplate = (rowData) => {
    return (
      <div className="company-cell">
        <strong>{rowData.company}</strong>
        <div className="text-sm text-gray-600">{rowData.id}</div>
      </div>
    );
  };

  const contactTemplate = (rowData) => {
    return (
      <div>
        <div>{rowData.contact}</div>
        <div className="text-sm text-gray-600">{rowData.email}</div>
      </div>
    );
  };

  const statusTemplate = (rowData) => {
    const severityMap = {
      'Introduction': 'info',
      'Qualification': 'warning',
      'Proposal': 'success',
      'Negotiation': 'danger'
    };
    return <Tag value={rowData.status} severity={severityMap[rowData.status]} />;
  };

  const priorityTemplate = (rowData) => {
    const severityMap = {
      'Low': 'success',
      'Medium': 'warning',
      'High': 'danger'
    };
    return <Tag value={rowData.priority} severity={severityMap[rowData.priority]} />;
  };

  const ratingTemplate = (rowData) => {
    return (
      <div className="rating-stars">
        {[...Array(5)].map((_, i) => (
          <i
            key={i}
            className={`pi ${i < rowData.rating ? 'pi-star-fill' : 'pi-star'}`}
            style={{ color: i < rowData.rating ? '#ffc107' : '#dee2e6' }}
          ></i>
        ))}
      </div>
    );
  };

  const actionTemplate = (rowData) => {
    return (
      <div className="action-buttons">
        <Button
          icon="pi pi-eye"
          className="p-button-rounded p-button-text p-button-sm"
          onClick={() => handleRowClick(rowData)}
          tooltip="View Details"
        />
        <Button
          icon="pi pi-pencil"
          className="p-button-rounded p-button-text p-button-sm"
          tooltip="Edit"
        />
        <Button
          icon="pi pi-trash"
          className="p-button-rounded p-button-text p-button-danger p-button-sm"
          tooltip="Delete"
        />
      </div>
    );
  };

  const header = (
    <div className="table-header">
      <div className="header-left">
        <h2>Lead Management</h2>
        <span className="lead-count">{leads.length} Leads</span>
      </div>
      <div className="header-right">
        <span className="p-input-icon-left">
          <i className="pi pi-search" />
          <InputText
            type="search"
            value={globalFilter}
            onChange={(e) => setGlobalFilter(e.target.value)}
            placeholder="Search leads..."
          />
        </span>
        <Dropdown
          options={statusOptions}
          placeholder="Filter by Status"
          className="ml-2"
        />
        <Dropdown
          options={priorityOptions}
          placeholder="Filter by Priority"
          className="ml-2"
        />
        <Button
          label="New Lead"
          icon="pi pi-plus"
          className="p-button-success ml-2"
          onClick={() => navigate('/lead/new')}
        />
      </div>
    </div>
  );

  const statsCards = (
    <div className="stats-grid">
      <div className="stat-card">
        <div className="stat-content">
          <div className="stat-icon" style={{ backgroundColor: '#E3F2FD' }}>
            <i className="pi pi-users" style={{ color: '#2196F3' }}></i>
          </div>
          <div className="stat-details">
            <div className="stat-value">{leads.length}</div>
            <div className="stat-label">Total Leads</div>
          </div>
        </div>
      </div>

      <div className="stat-card">
        <div className="stat-content">
          <div className="stat-icon" style={{ backgroundColor: '#FFF3E0' }}>
            <i className="pi pi-clock" style={{ color: '#FF9800' }}></i>
          </div>
          <div className="stat-details">
            <div className="stat-value">
              {leads.filter(l => l.status === 'Introduction').length}
            </div>
            <div className="stat-label">New Leads</div>
          </div>
        </div>
      </div>

      <div className="stat-card">
        <div className="stat-content">
          <div className="stat-icon" style={{ backgroundColor: '#E8F5E9' }}>
            <i className="pi pi-check-circle" style={{ color: '#4CAF50' }}></i>
          </div>
          <div className="stat-details">
            <div className="stat-value">
              {leads.filter(l => l.status === 'Proposal' || l.status === 'Negotiation').length}
            </div>
            <div className="stat-label">In Progress</div>
          </div>
        </div>
      </div>

      <div className="stat-card">
        <div className="stat-content">
          <div className="stat-icon" style={{ backgroundColor: '#F3E5F5' }}>
            <i className="pi pi-dollar" style={{ color: '#9C27B0' }}></i>
          </div>
          <div className="stat-details">
            <div className="stat-value">
              ${leads.reduce((sum, l) => sum + parseInt(l.revenue.replace(/[$,]/g, '')), 0).toLocaleString()}
            </div>
            <div className="stat-label">Total Revenue</div>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="lead-management-root">
      <CRMNavigation />

      <div className="page-header">
        <h1>CRM - Lead Management</h1>
        <p>Manage your leads and track opportunities</p>
      </div>

      {statsCards}

      <Card className="leads-table-card">
        <DataTable
          value={leads}
          selection={selectedLeads}
          onSelectionChange={(e) => setSelectedLeads(e.value)}
          dataKey="id"
          paginator
          rows={10}
          rowsPerPageOptions={[5, 10, 25, 50]}
          globalFilter={globalFilter}
          header={header}
          emptyMessage="No leads found"
          className="leads-datatable"
          onRowClick={(e) => handleRowClick(e.data)}
          rowHover
        >
          <Column selectionMode="multiple" headerStyle={{ width: '3rem' }} />
          <Column field="company" header="Company" body={companyTemplate} sortable />
          <Column field="contact" header="Contact" body={contactTemplate} sortable />
          <Column field="phone" header="Phone" sortable />
          <Column field="status" header="Status" body={statusTemplate} sortable />
          <Column field="priority" header="Priority" body={priorityTemplate} sortable />
          <Column field="revenue" header="Revenue" sortable />
          <Column field="source" header="Source" sortable />
          <Column field="rating" header="Rating" body={ratingTemplate} sortable />
          <Column field="createdDate" header="Created" sortable />
          <Column body={actionTemplate} headerStyle={{ width: '10rem' }} />
        </DataTable>
      </Card>
    </div>
  );
};

export default LeadManagementRoot;

