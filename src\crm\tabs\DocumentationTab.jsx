import React, { useState } from 'react';
import { But<PERSON> } from 'primereact/button';
import '../../styles/glassy/glassy-ui.css';
import '../../styles/glassy/global.css';
import GlassyBlueButton from '../../components/common/GlassyBlueButton';
import GlassyWhiteButton from '../../components/common/GlassyWhiteButton';

/**
 * DocumentationTab - Displays and manages documents with glassy UI
 * Clean table layout matching the provided design
 */
const DocumentationTab = ({ prospectId }) => {
  const [documents] = useState([
    {
      id: 1,
      documentName: 'Proof of Business Address',
      expiryDate: '25/07/2026',
      hasDocument: true
    },
    {
      id: 2,
      documentName: 'D-U-N-S Number',
      expiryDate: '25/07/2026',
      hasDocument: true
    },
    {
      id: 3,
      documentName: 'Letter of Authorization (LOA)',
      expiryDate: '25/07/2026',
      hasDocument: true
    },
    {
      id: 4,
      documentName: 'Power of Attorney',
      expiryDate: '25/07/2026',
      hasDocument: true
    }
  ]);

  const handleView = (doc) => {
    console.log('View document:', doc.documentName);
  };

  const handleDownload = (doc) => {
    console.log('Download document:', doc.documentName);
  };

  const handleDelete = (doc) => {
    console.log('Delete document:', doc.documentName);
  };

  return (
    <div className="documentation-tab" style={{ padding: '1.5rem' }}>
      {/* Header */}
      <div className="tab-header" style={{ marginBottom: '1.5rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <h2 className="text-900" style={{ margin: 0, fontWeight: 600, fontSize: '1.25rem' }}>Documentation</h2>
        <GlassyBlueButton 
          label="Upload" 
          icon="pi pi-upload"
          className="p-button-sm" 
          severity="info"
          style={{ fontWeight: 500 }}
        />
      </div>

      {/* Documents Table */}
      <div className="glass-card-global" style={{ padding: 0, overflow: 'hidden' }}>
        {/* Table Header */}
        <div 
          style={{ 
            display: 'grid', 
            gridTemplateColumns: '2fr 1fr 1fr 1fr',
            gap: '1rem',
            padding: '1rem 1.5rem',
            backgroundColor: '#f9fafb',
            borderBottom: '1px solid #e5e7eb',
            fontWeight: 600,
            fontSize: '0.875rem',
            color: '#6b7280'
          }}
        >
          <div>Document Name</div>
          <div>Expiry Date</div>
          <div>Document</div>
          <div>Action</div>
        </div>

        {/* Table Body */}
        <div>
          {documents.map((doc, index) => (
            <div 
              key={doc.id}
              style={{ 
                display: 'grid', 
                gridTemplateColumns: '2fr 1fr 1fr 1fr',
                gap: '1rem',
                padding: '1.25rem 1.5rem',
                borderBottom: index < documents.length - 1 ? '1px solid #e5e7eb' : 'none',
                alignItems: 'center',
                backgroundColor: 'white',
                transition: 'background-color 0.2s'
              }}
              onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f9fafb'}
              onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'white'}
            >
              {/* Document Name */}
              <div style={{ color: '#111827', fontWeight: 500 }}>
                {doc.documentName}
              </div>

              {/* Expiry Date */}
              <div style={{ color: '#6b7280', fontSize: '0.875rem' }}>
                {doc.expiryDate}
              </div>

              {/* Document Icon */}
              <div>
                {doc.hasDocument && (
                  <i 
                    className="pi pi-file" 
                    style={{ 
                      fontSize: '1.25rem', 
                      color: '#6b7280',
                      cursor: 'pointer'
                    }}
                    title="Document available"
                  />
                )}
              </div>

              {/* Actions */}
              <div style={{ display: 'flex', gap: '0.75rem', alignItems: 'center' }}>
                <button
                  onClick={() => handleView(doc)}
                  style={{
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    padding: '0.5rem',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'background-color 0.2s'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                  title="View"
                >
                  <i className="pi pi-eye" style={{ fontSize: '1rem', color: '#6b7280' }} />
                </button>

                <button
                  onClick={() => handleDownload(doc)}
                  style={{
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    padding: '0.5rem',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'background-color 0.2s'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#f3f4f6'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                  title="Download"
                >
                  <i className="pi pi-download" style={{ fontSize: '1rem', color: '#6b7280' }} />
                </button>

                <button
                  onClick={() => handleDelete(doc)}
                  style={{
                    background: 'none',
                    border: 'none',
                    cursor: 'pointer',
                    padding: '0.5rem',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    transition: 'background-color 0.2s'
                  }}
                  onMouseEnter={(e) => e.currentTarget.style.backgroundColor = '#fee2e2'}
                  onMouseLeave={(e) => e.currentTarget.style.backgroundColor = 'transparent'}
                  title="Delete"
                >
                  <i className="pi pi-trash" style={{ fontSize: '1rem', color: '#ef4444' }} />
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default DocumentationTab;

