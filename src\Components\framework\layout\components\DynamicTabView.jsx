import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { TabView, TabPanel } from 'primereact/tabview';
import DynamicComponent from '../../core/DynamicComponent';

/**
 * DynamicTabView - TabView component for the dynamic framework
 */
const DynamicTabView = ({
  tabs = [],
  activeIndex = 0,
  onTabChange,
  scrollable = false,
  className = '',
  style = {},
  id,
  children,
  onEvent,
  debug = false,
  ...props
}) => {
  const handleTabChange = useCallback((e) => {
    if (debug) {
      console.log('DynamicTabView: Tab changed', e);
    }

    if (onTabChange) {
      onTabChange(e);
    }

    if (onEvent) {
      onEvent('tabChange', { index: e.index });
    }
  }, [onTabChange, onEvent, debug]);

  const tabViewProps = {
    activeIndex,
    onTabChange: handleTabChange,
    scrollable,
    className: `dynamic-tabview ${className}`,
    style,
    id,
    ...props
  };

  if (debug) {
    console.log('DynamicTabView: Rendering with props', tabViewProps);
  }

  const renderTabContent = useCallback((tab) => {
    // If tab has children, render them
    if (tab.children) {
      return tab.children;
    }

    // If tab has content array, render dynamic components
    if (Array.isArray(tab.content) && tab.content.length > 0) {
      return (
        <div className="tab-dynamic-content">
          {tab.content.map((item, index) => (
            <DynamicComponent
              key={index}
              type={item.type}
              config={item.config}
              data={item.data}
              onEvent={onEvent}
              debug={debug}
            />
          ))}
        </div>
      );
    }

    // If tab has single content object, render it
    if (tab.content && typeof tab.content === 'object' && tab.content.type) {
      return (
        <DynamicComponent
          type={tab.content.type}
          config={tab.content.config}
          data={tab.content.data}
          onEvent={onEvent}
          debug={debug}
        />
      );
    }

    // If tab has text content, render it
    if (tab.content && typeof tab.content === 'string') {
      return <div className="tab-text-content">{tab.content}</div>;
    }

    return null;
  }, [onEvent, debug]);

  return (
    <div className="dynamic-tabview-wrapper">
      {debug && (
        <div className="dynamic-tabview__debug">
          <small>
            Active: {activeIndex} | 
            Tabs: {tabs.length} | 
            Scrollable: {scrollable ? 'Yes' : 'No'}
          </small>
        </div>
      )}
      <TabView {...tabViewProps}>
        {children || tabs.map((tab, index) => (
          <TabPanel
            key={index}
            header={tab.header}
            leftIcon={tab.leftIcon}
            rightIcon={tab.rightIcon}
            disabled={tab.disabled}
            closable={tab.closable}
            headerClassName={tab.headerClassName}
            headerStyle={tab.headerStyle}
            contentClassName={tab.contentClassName}
            contentStyle={tab.contentStyle}
          >
            {renderTabContent(tab)}
          </TabPanel>
        ))}
      </TabView>
    </div>
  );
};

DynamicTabView.propTypes = {
  tabs: PropTypes.arrayOf(PropTypes.shape({
    header: PropTypes.string.isRequired,
    leftIcon: PropTypes.string,
    rightIcon: PropTypes.string,
    disabled: PropTypes.bool,
    closable: PropTypes.bool,
    content: PropTypes.oneOfType([
      PropTypes.string,
      PropTypes.array,
      PropTypes.object
    ]),
    children: PropTypes.node,
    headerClassName: PropTypes.string,
    headerStyle: PropTypes.object,
    contentClassName: PropTypes.string,
    contentStyle: PropTypes.object
  })),
  activeIndex: PropTypes.number,
  onTabChange: PropTypes.func,
  scrollable: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object,
  id: PropTypes.string,
  children: PropTypes.node,
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicTabView;

