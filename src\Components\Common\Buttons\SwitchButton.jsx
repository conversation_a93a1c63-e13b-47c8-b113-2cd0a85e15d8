// components/common/SwitchButton.jsx
import React from "react";
import PropTypes from "prop-types";
import { InputSwitch } from "primereact/inputswitch";

export default function SwitchButton({
  checked,
  onChange,
  className = "",
  ...props
}) {
    const baseStyle = "glass-toggle";
  return (
    <InputSwitch
      checked={checked}
      onChange={(e) => onChange(e.value)}
      className={`${baseStyle} ${className}`}
      {...props}
    />
  );
}

SwitchButton.propTypes = {
  checked: PropTypes.bool.isRequired,
  onChange: PropTypes.func.isRequired,
  className: PropTypes.string,
};
