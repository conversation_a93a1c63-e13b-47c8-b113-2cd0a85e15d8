
import componentRegistry from './ComponentRegistry';

import DynamicInputText from '../forms/components/DynamicInputText';
import DynamicDropdown from '../forms/components/DynamicDropdown';
import DynamicCalendar from '../forms/components/DynamicCalendar';
import DynamicCheckbox from '../forms/components/DynamicCheckbox';
import DynamicMultiSelect from '../forms/components/DynamicMultiSelect';
import DynamicRating from '../forms/components/DynamicRating';
import DynamicTextarea from '../forms/components/DynamicTextarea';

import DynamicButton from '../buttons/components/DynamicButton';

import DynamicPanel from '../layout/components/DynamicPanel';
import DynamicCard from '../layout/components/DynamicCard';

import DynamicDialog from '../overlay/components/DynamicDialog';

import DynamicMessage from '../messages/components/DynamicMessage';
import DynamicToast from '../messages/components/DynamicToast';

import DynamicMenu from '../navigation/components/DynamicMenu';
import DynamicNavbar from '../navigation/DynamicNavbar';
import DynamicSteps from '../navigation/components/DynamicSteps';

import DynamicTabView from '../layout/components/DynamicTabView';
import DynamicTag from '../display/components/DynamicTag';

import ThemeSelector from '../theme/ThemeSelector';

import LanguageSelector from '../i18n/LanguageSelector';

import DynamicProgressBar from '../misc/components/DynamicProgressBar';

import DynamicDataTable from '../data/DynamicDataTable';
import DynamicServiceOrderTable from '../data/DynamicServiceOrderTable';
import CustomDataTable from '../../custom/CustomDataTable';

import DynamicForm from '../forms/DynamicForm';

export const registerDefaultComponents = () => {
  componentRegistry.register('input-text', {
    component: DynamicInputText,
    category: 'form',
    description: 'Text input field with validation and formatting options',
    props: {
      placeholder: { type: 'string', default: '' },
      maxLength: { type: 'number' },
      required: { type: 'boolean', default: false },
      disabled: { type: 'boolean', default: false },
      readOnly: { type: 'boolean', default: false }
    },
    defaultProps: {
      size: 'normal',
      variant: 'outlined',
      autoComplete: 'off'
    },
    validation: {
      type: 'object',
      properties: {
        value: { type: 'string' },
        maxLength: { type: 'number', minimum: 1 },
        minLength: { type: 'number', minimum: 0 }
      }
    },
    tags: ['input', 'text', 'form', 'validation'],
    icon: 'pi pi-pencil',
    examples: [
      {
        name: 'Basic Text Input',
        config: {
          placeholder: 'Enter your name',
          required: true
        }
      },
      {
        name: 'Email Input',
        config: {
          placeholder: 'Enter email address',
          keyfilter: 'email',
          required: true
        }
      }
    ]
  });

  componentRegistry.register('dropdown', {
    component: DynamicDropdown,
    category: 'form',
    description: 'Dropdown selection component with filtering and custom templates',
    props: {
      options: { type: 'array', default: [] },
      placeholder: { type: 'string', default: 'Select an option' },
      filter: { type: 'boolean', default: false },
      showClear: { type: 'boolean', default: false },
      required: { type: 'boolean', default: false },
      disabled: { type: 'boolean', default: false }
    },
    defaultProps: {
      size: 'normal',
      variant: 'outlined',
      filterMatchMode: 'contains'
    },
    validation: {
      type: 'object',
      properties: {
        options: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              label: { type: 'string' },
              value: { type: 'any' }
            }
          }
        }
      }
    },
    tags: ['dropdown', 'select', 'form', 'options'],
    icon: 'pi pi-chevron-down',
    examples: [
      {
        name: 'Basic Dropdown',
        config: {
          options: [
            { label: 'Option 1', value: 'opt1' },
            { label: 'Option 2', value: 'opt2' },
            { label: 'Option 3', value: 'opt3' }
          ],
          placeholder: 'Choose an option'
        }
      },
      {
        name: 'Filterable Dropdown',
        config: {
          options: [
            { label: 'New York', value: 'ny' },
            { label: 'Los Angeles', value: 'la' },
            { label: 'Chicago', value: 'chi' },
            { label: 'Houston', value: 'hou' }
          ],
          filter: true,
          showClear: true,
          placeholder: 'Select a city'
        }
      }
    ]
  });

  componentRegistry.register('calendar', {
    component: DynamicCalendar,
    category: 'form',
    description: 'Date and time picker with multiple selection modes',
    props: {
      placeholder: { type: 'string', default: 'Select date' },
      dateFormat: { type: 'string', default: 'mm/dd/yy' },
      selectionMode: { type: 'string', default: 'single' },
      showTime: { type: 'boolean', default: false },
      showIcon: { type: 'boolean', default: true },
      required: { type: 'boolean', default: false },
      disabled: { type: 'boolean', default: false }
    },
    defaultProps: {
      size: 'normal',
      variant: 'outlined'
    },
    tags: ['calendar', 'date', 'time', 'picker', 'form'],
    icon: 'pi pi-calendar',
    examples: [
      {
        name: 'Basic Date Picker',
        config: {
          placeholder: 'Select a date',
          dateFormat: 'mm/dd/yy'
        }
      },
      {
        name: 'Date Time Picker',
        config: {
          placeholder: 'Select date and time',
          showTime: true,
          hourFormat: '24'
        }
      }
    ]
  });

  componentRegistry.register('rating', {
    component: DynamicRating,
    category: 'form',
    description: 'Star rating component for user feedback and ratings',
    props: {
      stars: { type: 'number', default: 5 },
      cancel: { type: 'boolean', default: true },
      disabled: { type: 'boolean', default: false },
      readOnly: { type: 'boolean', default: false }
    },
    defaultProps: {
      size: 'normal',
      variant: 'default',
      stars: 5,
      cancel: true
    },
    tags: ['rating', 'stars', 'feedback', 'form'],
    icon: 'pi pi-star',
    examples: [
      {
        name: 'Basic Rating',
        config: {
          stars: 5,
          cancel: true
        }
      },
      {
        name: 'Read-only Rating',
        config: {
          value: 4,
          stars: 5,
          cancel: false,
          readOnly: true
        }
      }
    ]
  });

  componentRegistry.register('textarea', {
    component: DynamicTextarea,
    category: 'form',
    description: 'Multi-line text input with auto-resize and character counting',
    props: {
      placeholder: { type: 'string', default: '' },
      rows: { type: 'number', default: 3 },
      cols: { type: 'number', default: 30 },
      autoResize: { type: 'boolean', default: false },
      maxLength: { type: 'number' },
      required: { type: 'boolean', default: false },
      disabled: { type: 'boolean', default: false },
      readOnly: { type: 'boolean', default: false }
    },
    defaultProps: {
      size: 'normal',
      variant: 'outlined',
      rows: 3,
      autoResize: false
    },
    tags: ['textarea', 'text', 'multiline', 'form'],
    icon: 'pi pi-align-left',
    examples: [
      {
        name: 'Basic Textarea',
        config: {
          placeholder: 'Enter your message...',
          rows: 3
        }
      },
      {
        name: 'Auto-resize Textarea',
        config: {
          placeholder: 'Type here...',
          autoResize: true,
          maxLength: 500
        }
      }
    ]
  });

  componentRegistry.register('checkbox', {
    component: DynamicCheckbox,
    category: 'form',
    description: 'Checkbox input for boolean values',
    props: {
      checked: { type: 'boolean', default: false },
      label: { type: 'string' },
      labelPosition: { type: 'string', default: 'right' },
      required: { type: 'boolean', default: false },
      disabled: { type: 'boolean', default: false }
    },
    defaultProps: {
      size: 'normal',
      variant: 'outlined'
    },
    tags: ['checkbox', 'boolean', 'form', 'input'],
    icon: 'pi pi-check-square',
    examples: [
      {
        name: 'Basic Checkbox',
        config: {
          label: 'Accept terms and conditions',
          required: true
        }
      }
    ]
  });

  componentRegistry.register('multiselect', {
    component: DynamicMultiSelect,
    category: 'form',
    description: 'Multiple selection component with filtering',
    props: {
      options: { type: 'array', default: [] },
      placeholder: { type: 'string', default: 'Select options' },
      filter: { type: 'boolean', default: false },
      showClear: { type: 'boolean', default: false },
      showSelectAll: { type: 'boolean', default: true },
      display: { type: 'string', default: 'comma' },
      required: { type: 'boolean', default: false },
      disabled: { type: 'boolean', default: false }
    },
    defaultProps: {
      size: 'normal',
      variant: 'outlined'
    },
    tags: ['multiselect', 'multiple', 'form', 'options'],
    icon: 'pi pi-list',
    examples: [
      {
        name: 'Basic MultiSelect',
        config: {
          options: [
            { label: 'Option 1', value: 'opt1' },
            { label: 'Option 2', value: 'opt2' },
            { label: 'Option 3', value: 'opt3' }
          ],
          placeholder: 'Choose options'
        }
      }
    ]
  });

  componentRegistry.register('button', {
    component: DynamicButton,
    category: 'button',
    description: 'Configurable button with multiple variants and states',
    props: {
      label: { type: 'string' },
      icon: { type: 'string' },
      type: { type: 'string', default: 'button' },
      severity: { type: 'string', default: 'primary' },
      variant: { type: 'string', default: 'filled' },
      size: { type: 'string', default: 'normal' },
      disabled: { type: 'boolean', default: false },
      loading: { type: 'boolean', default: false }
    },
    defaultProps: {
      type: 'button',
      severity: 'primary',
      variant: 'filled'
    },
    tags: ['button', 'action', 'click', 'submit'],
    icon: 'pi pi-circle',
    examples: [
      {
        name: 'Primary Button',
        config: {
          label: 'Click Me',
          severity: 'primary'
        }
      },
      {
        name: 'Icon Button',
        config: {
          label: 'Save',
          icon: 'pi pi-save',
          severity: 'success'
        }
      }
    ]
  });

  componentRegistry.register('panel', {
    component: DynamicPanel,
    category: 'layout',
    description: 'Collapsible panel container with header and footer',
    props: {
      header: { type: 'string' },
      toggleable: { type: 'boolean', default: false },
      collapsed: { type: 'boolean', default: false },
      size: { type: 'string', default: 'normal' },
      variant: { type: 'string', default: 'default' },
      bordered: { type: 'boolean', default: true },
      shadow: { type: 'boolean', default: false }
    },
    defaultProps: {
      size: 'normal',
      variant: 'default'
    },
    tags: ['panel', 'container', 'layout', 'collapsible'],
    icon: 'pi pi-window-maximize',
    examples: [
      {
        name: 'Basic Panel',
        config: {
          header: 'Panel Title',
          content: [
            {
              type: 'input-text',
              config: { placeholder: 'Enter text...' }
            }
          ]
        }
      }
    ]
  });

  componentRegistry.register('card', {
    component: DynamicCard,
    category: 'layout',
    description: 'Card container with header, body, and footer sections',
    props: {
      title: { type: 'string' },
      subTitle: { type: 'string' },
      size: { type: 'string', default: 'normal' },
      variant: { type: 'string', default: 'default' },
      bordered: { type: 'boolean', default: true },
      shadow: { type: 'boolean', default: true },
      hoverable: { type: 'boolean', default: false },
      clickable: { type: 'boolean', default: false }
    },
    defaultProps: {
      size: 'normal',
      variant: 'default'
    },
    tags: ['card', 'container', 'layout'],
    icon: 'pi pi-id-card',
    examples: [
      {
        name: 'Basic Card',
        config: {
          title: 'Card Title',
          subTitle: 'Card subtitle',
          content: [
            {
              type: 'input-text',
              config: { placeholder: 'Card content...' }
            }
          ]
        }
      }
    ]
  });

  componentRegistry.register('datatable', {
    component: DynamicDataTable,
    category: 'data',
    description: 'Advanced data table with sorting, filtering, pagination, and editing',
    props: {
      data: { type: 'array', default: [] },
      columns: { type: 'array', default: [] },
      config: { type: 'object', default: {} }
    },
    defaultProps: {
      loading: false,
      debug: false
    },
    validation: {
      type: 'object',
      properties: {
        data: { type: 'array' },
        columns: {
          type: 'array',
          items: {
            type: 'object',
            required: ['field'],
            properties: {
              field: { type: 'string' },
              header: { type: 'string' },
              sortable: { type: 'boolean' },
              filter: { type: 'boolean' }
            }
          }
        }
      }
    },
    tags: ['table', 'data', 'grid', 'sorting', 'filtering', 'pagination'],
    icon: 'pi pi-table',
    examples: [
      {
        name: 'Basic Data Table',
        config: {
          data: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'Active' },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'Inactive' }
          ],
          columns: [
            { field: 'id', header: 'ID' },
            { field: 'name', header: 'Name' },
            { field: 'email', header: 'Email' },
            { field: 'status', header: 'Status' }
          ],
          config: {
            pagination: { enabled: true, rows: 10 },
            sorting: { enabled: true },
            filtering: { enabled: true }
          }
        }
      }
    ]
  });

  componentRegistry.register('form', {
    component: DynamicForm,
    category: 'form',
    description: 'Dynamic form builder with validation and submission handling',
    props: {
      config: { type: 'object', required: true },
      initialData: { type: 'object', default: {} }
    },
    defaultProps: {
      disabled: false,
      loading: false,
      debug: false
    },
    validation: {
      type: 'object',
      required: ['config'],
      properties: {
        config: {
          type: 'object',
          required: ['fields'],
          properties: {
            fields: {
              type: 'array',
              minItems: 1,
              items: {
                type: 'object',
                required: ['name', 'type'],
                properties: {
                  name: { type: 'string' },
                  type: { type: 'string' },
                  label: { type: 'string' },
                  required: { type: 'boolean' }
                }
              }
            }
          }
        }
      }
    },
    tags: ['form', 'builder', 'validation', 'dynamic'],
    icon: 'pi pi-list',
    examples: [
      {
        name: 'Contact Form',
        config: {
          config: {
            fields: [
              {
                name: 'name',
                type: 'input-text',
                label: 'Full Name',
                required: true,
                props: { placeholder: 'Enter your full name' }
              },
              {
                name: 'email',
                type: 'input-text',
                label: 'Email Address',
                required: true,
                props: { placeholder: 'Enter your email', keyfilter: 'email' }
              },
              {
                name: 'subject',
                type: 'dropdown',
                label: 'Subject',
                required: true,
                props: {
                  options: [
                    { label: 'General Inquiry', value: 'general' },
                    { label: 'Support', value: 'support' },
                    { label: 'Sales', value: 'sales' }
                  ],
                  placeholder: 'Select a subject'
                }
              }
            ],
            layout: {
              columns: 1,
              showProgress: true
            }
          }
        }
      }
    ]
  });

  componentRegistry.register('dialog', {
    component: DynamicDialog,
    category: 'overlay',
    description: 'Modal dialog with customizable content and actions',
    props: {
      visible: { type: 'boolean', default: false },
      header: { type: 'string' },
      modal: { type: 'boolean', default: true },
      resizable: { type: 'boolean', default: true },
      draggable: { type: 'boolean', default: true },
      closable: { type: 'boolean', default: true },
      maximizable: { type: 'boolean', default: false },
      size: { type: 'string', default: 'normal' },
      position: { type: 'string', default: 'center' }
    },
    defaultProps: {
      size: 'normal',
      variant: 'default'
    },
    tags: ['dialog', 'modal', 'overlay', 'popup'],
    icon: 'pi pi-window-maximize',
    examples: [
      {
        name: 'Basic Dialog',
        config: {
          header: 'Dialog Title',
          visible: true,
          content: [
            {
              type: 'input-text',
              config: { placeholder: 'Enter text...' }
            }
          ]
        }
      }
    ]
  });

  componentRegistry.register('message', {
    component: DynamicMessage,
    category: 'message',
    description: 'Display messages with different severity levels',
    props: {
      severity: { type: 'string', default: 'info' },
      text: { type: 'string' },
      closable: { type: 'boolean', default: false },
      sticky: { type: 'boolean', default: true },
      life: { type: 'number', default: 3000 },
      autoHide: { type: 'boolean', default: false },
      showIcon: { type: 'boolean', default: true }
    },
    defaultProps: {
      severity: 'info',
      size: 'normal'
    },
    tags: ['message', 'notification', 'alert'],
    icon: 'pi pi-info-circle',
    examples: [
      {
        name: 'Info Message',
        config: {
          severity: 'info',
          text: 'This is an information message'
        }
      },
      {
        name: 'Success Message',
        config: {
          severity: 'success',
          text: 'Operation completed successfully'
        }
      }
    ]
  });

  componentRegistry.register('toast', {
    component: DynamicToast,
    category: 'message',
    description: 'Toast notifications that appear temporarily',
    props: {
      position: { type: 'string', default: 'top-right' },
      baseZIndex: { type: 'number', default: 0 },
      autoZIndex: { type: 'boolean', default: true }
    },
    defaultProps: {
      position: 'top-right'
    },
    tags: ['toast', 'notification', 'popup'],
    icon: 'pi pi-bell',
    examples: [
      {
        name: 'Toast Container',
        config: {
          position: 'top-right'
        }
      }
    ]
  });

  componentRegistry.register('menu', {
    component: DynamicMenu,
    category: 'navigation',
    description: 'Menu component with hierarchical items',
    props: {
      model: { type: 'array', default: [] },
      popup: { type: 'boolean', default: false },
      size: { type: 'string', default: 'normal' },
      variant: { type: 'string', default: 'default' },
      orientation: { type: 'string', default: 'vertical' }
    },
    defaultProps: {
      size: 'normal',
      variant: 'default'
    },
    tags: ['menu', 'navigation', 'items'],
    icon: 'pi pi-bars',
    examples: [
      {
        name: 'Basic Menu',
        config: {
          model: [
            { label: 'Home', icon: 'pi pi-home' },
            { label: 'About', icon: 'pi pi-info' },
            { label: 'Contact', icon: 'pi pi-envelope' }
          ]
        }
      }
    ]
  });

  componentRegistry.register('progressbar', {
    component: DynamicProgressBar,
    category: 'misc',
    description: 'Progress indicator with determinate and indeterminate modes',
    props: {
      value: { type: 'number', default: 0 },
      showValue: { type: 'boolean', default: true },
      unit: { type: 'string', default: '%' },
      mode: { type: 'string', default: 'determinate' },
      size: { type: 'string', default: 'normal' },
      variant: { type: 'string', default: 'default' },
      animated: { type: 'boolean', default: false },
      striped: { type: 'boolean', default: false }
    },
    defaultProps: {
      mode: 'determinate',
      size: 'normal'
    },
    tags: ['progress', 'loading', 'indicator'],
    icon: 'pi pi-spinner',
    examples: [
      {
        name: 'Progress Bar',
        config: {
          value: 75,
          showValue: true,
          label: 'Loading...'
        }
      },
      {
        name: 'Indeterminate Progress',
        config: {
          mode: 'indeterminate',
          label: 'Processing...'
        }
      }
    ]
  });

  componentRegistry.register('theme-selector', {
    component: ThemeSelector,
    category: 'theme',
    description: 'Theme selector component with multiple variants',
    props: {
      showLabel: { type: 'boolean', default: true },
      showDarkModeToggle: { type: 'boolean', default: true },
      showCustomThemeButton: { type: 'boolean', default: true },
      variant: { type: 'string', default: 'dropdown' },
      size: { type: 'string', default: 'normal' }
    },
    defaultProps: {
      variant: 'dropdown',
      size: 'normal'
    },
    tags: ['theme', 'selector', 'ui'],
    icon: 'pi pi-palette',
    examples: [
      {
        name: 'Dropdown Theme Selector',
        config: {
          variant: 'dropdown',
          showLabel: true,
          showDarkModeToggle: true
        }
      }
    ]
  });

  componentRegistry.register('language-selector', {
    component: LanguageSelector,
    category: 'i18n',
    description: 'Language selector component for internationalization',
    props: {
      showLabel: { type: 'boolean', default: true },
      showFlag: { type: 'boolean', default: true },
      showNativeName: { type: 'boolean', default: true },
      variant: { type: 'string', default: 'dropdown' },
      size: { type: 'string', default: 'normal' }
    },
    defaultProps: {
      variant: 'dropdown',
      size: 'normal'
    },
    tags: ['language', 'i18n', 'selector'],
    icon: 'pi pi-globe',
    examples: [
      {
        name: 'Dropdown Language Selector',
        config: {
          variant: 'dropdown',
          showLabel: true,
          showFlag: true
        }
      }
    ]
  });

  componentRegistry.register('navbar', {
    component: DynamicNavbar,
    category: 'navigation',
    description: 'Dynamic navigation bar with theme and language support',
    props: {
      brandText: { type: 'string', default: 'Dynamic Framework' },
      brandIcon: { type: 'string', default: 'pi pi-bolt' },
      showThemeSelector: { type: 'boolean', default: true },
      showLanguageSelector: { type: 'boolean', default: true },
      showUserMenu: { type: 'boolean', default: true },
      variant: { type: 'string', default: 'default' },
      position: { type: 'string', default: 'static' }
    },
    defaultProps: {
      variant: 'default',
      position: 'static'
    },
    tags: ['navbar', 'navigation', 'menu'],
    icon: 'pi pi-bars',
    examples: [
      {
        name: 'Basic Navbar',
        config: {
          brandText: 'My App',
          showThemeSelector: true,
          showLanguageSelector: true
        }
      }
    ]
  });

  componentRegistry.register('steps', {
    component: DynamicSteps,
    category: 'navigation',
    description: 'Wizard steps component for multi-step processes',
    props: {
      model: { type: 'array', default: [] },
      activeIndex: { type: 'number', default: 0 },
      readOnly: { type: 'boolean', default: false }
    },
    defaultProps: {
      activeIndex: 0,
      readOnly: false
    },
    tags: ['steps', 'wizard', 'navigation', 'progress'],
    icon: 'pi pi-list',
    examples: [
      {
        name: 'Basic Steps',
        config: {
          model: [
            { label: 'Step 1', icon: 'pi pi-user' },
            { label: 'Step 2', icon: 'pi pi-calendar' },
            { label: 'Step 3', icon: 'pi pi-check' }
          ],
          activeIndex: 0
        }
      }
    ]
  });

  componentRegistry.register('tabview', {
    component: DynamicTabView,
    category: 'layout',
    description: 'Tabbed interface for organizing content',
    props: {
      tabs: { type: 'array', default: [] },
      activeIndex: { type: 'number', default: 0 },
      scrollable: { type: 'boolean', default: false }
    },
    defaultProps: {
      activeIndex: 0,
      scrollable: false
    },
    tags: ['tabs', 'tabview', 'layout', 'navigation'],
    icon: 'pi pi-folder',
    examples: [
      {
        name: 'Basic TabView',
        config: {
          tabs: [
            { header: 'Tab 1', content: 'Content 1' },
            { header: 'Tab 2', content: 'Content 2' },
            { header: 'Tab 3', content: 'Content 3' }
          ]
        }
      }
    ]
  });

  componentRegistry.register('tag', {
    component: DynamicTag,
    category: 'display',
    description: 'Tag component for labels and status indicators',
    props: {
      value: { type: 'string', default: '' },
      severity: { type: 'string', default: 'info' },
      rounded: { type: 'boolean', default: false },
      icon: { type: 'string', default: null }
    },
    defaultProps: {
      severity: 'info',
      rounded: false
    },
    tags: ['tag', 'label', 'badge', 'status'],
    icon: 'pi pi-tag',
    examples: [
      {
        name: 'Basic Tag',
        config: {
          value: 'New',
          severity: 'success'
        }
      },
      {
        name: 'Rounded Tag',
        config: {
          value: 'Premium',
          severity: 'warning',
          rounded: true
        }
      }
    ]
  });

  componentRegistry.register('custom-datatable', {
    component: CustomDataTable,
    category: 'data',
    description: 'Custom data table with advanced features including sorting, filtering, pagination, row expansion, and editing',
    props: {
      data: { type: 'array', default: [] },
      columns: { type: 'array', default: [] },
      config: { type: 'object', default: {} }
    },
    defaultProps: {
      loading: false,
      debug: false
    },
    validation: {
      type: 'object',
      properties: {
        data: { type: 'array' },
        columns: {
          type: 'array',
          items: {
            type: 'object',
            required: ['field'],
            properties: {
              field: { type: 'string' },
              header: { type: 'string' },
              sortable: { type: 'boolean' },
              filter: { type: 'boolean' }
            }
          }
        }
      }
    },
    tags: ['table', 'data', 'grid', 'custom', 'sorting', 'filtering', 'pagination', 'expandable'],
    icon: 'pi pi-table',
    examples: [
      {
        name: 'Basic Custom DataTable',
        config: {
          data: [
            { id: 1, name: 'John Doe', email: '<EMAIL>', status: 'Active' },
            { id: 2, name: 'Jane Smith', email: '<EMAIL>', status: 'Inactive' }
          ],
          columns: [
            { field: 'id', header: 'ID' },
            { field: 'name', header: 'Name' },
            { field: 'email', header: 'Email' },
            { field: 'status', header: 'Status' }
          ],
          config: {
            pagination: { enabled: true, rows: 10 },
            sorting: { enabled: true },
            filtering: { enabled: true },
            globalFilter: { enabled: true }
          }
        }
      },
      {
        name: 'Advanced Custom DataTable',
        config: {
          data: [
            { id: 1, name: 'Product A', price: 99.99, stock: 50, active: true },
            { id: 2, name: 'Product B', price: 149.99, stock: 30, active: true },
            { id: 3, name: 'Product C', price: 79.99, stock: 0, active: false }
          ],
          columns: [
            { field: 'id', header: 'ID', sortable: true },
            { field: 'name', header: 'Product Name', sortable: true, filter: true },
            { field: 'price', header: 'Price', sortable: true },
            { field: 'stock', header: 'Stock', sortable: true },
            { field: 'active', header: 'Active', sortable: true }
          ],
          config: {
            pagination: { enabled: true, rows: 10 },
            sorting: { enabled: true },
            filtering: { enabled: true },
            globalFilter: { enabled: true, placeholder: 'Search products...' },
            export: { enabled: true },
            selection: { mode: 'multiple' },
            showGridlines: true,
            stripedRows: true
          }
        }
      }
    ]
  });

  componentRegistry.register('service-order-table', {
    component: DynamicServiceOrderTable,
    category: 'data',
    description: 'Service order table with row grouping, expandable rows with tabs, status bars, and clickable status badges',
    props: {
      data: { type: 'array', default: [], required: true },
      title: { type: 'string', default: 'Service Orders' },
      columns: { type: 'array', default: [] },
      serviceFields: { type: 'array', default: [], description: 'Array of field names for service columns (e.g., ["transport", "hotels", "catering"])' },
      groupBy: { type: 'object', default: null },
      statusBar: { type: 'object', default: { enabled: false } },
      expansion: { type: 'object', default: { enabled: false } },
      header: { type: 'object', default: { enabled: true } },
      filters: { type: 'array', default: [] },
      actions: { type: 'object', default: { enabled: false } },
      onEvent: { type: 'function', default: null }
    },
    defaultProps: {
      title: 'Service Orders',
      statusBar: { enabled: true },
      expansion: { enabled: true },
      header: { enabled: true }
    },
    validation: {
      type: 'object',
      required: ['data'],
      properties: {
        data: { type: 'array' },
        columns: {
          type: 'array',
          items: {
            type: 'object',
            required: ['field', 'header'],
            properties: {
              field: { type: 'string' },
              header: { type: 'string' },
              type: { type: 'string' },
              style: { type: 'object' }
            }
          }
        }
      }
    },
    tags: ['table', 'service-order', 'expandable', 'status-bar', 'grouping', 'tabs'],
    icon: 'pi pi-list',
    examples: [
      {
        name: 'Service Order Table',
        config: {
          title: 'Service Orders',
          data: [
            {
              id: 1,
              date: 'Tue 5 Aug 2025',
              time: '14:00',
              flightNumber: 'VG5373',
              registration: 'UK-73',
              route: { from: 'DMDW', to: 'DRSV' },
              crew: 4,
              pax: 140,
              transport: 'NA',
              hotels: 'NR',
              catering: 'TBD',
              statusBar: { progress: 75, color: 'green' }
            }
          ],
          columns: [
            { field: 'time', header: 'Time', type: 'composite' },
            { field: 'route', header: 'Route', type: 'route' },
            { field: 'crew', header: 'Crew' },
            { field: 'pax', header: 'Pax' },
            { field: 'transport', header: 'Transport', type: 'status' },
            { field: 'hotels', header: 'Hotels', type: 'status' },
            { field: 'catering', header: 'Catering', type: 'status' }
          ],
          serviceFields: ['transport', 'hotels', 'catering', 'customs', 'migration', 'permits'],
          groupBy: { field: 'date' },
          statusBar: { enabled: true, field: 'statusBar', idField: 'id' },
          expansion: { enabled: true, tabs: [] }
        }
      }
    ]
  });

  console.log('Default components registered successfully');
};

export const getComponentSchemas = () => {
  return {
    'input-text': {
      type: 'object',
      properties: {
        value: { type: 'string' },
        placeholder: { type: 'string' },
        maxLength: { type: 'number', minimum: 1 },
        required: { type: 'boolean' },
        disabled: { type: 'boolean' },
        readOnly: { type: 'boolean' }
      }
    },
    'dropdown': {
      type: 'object',
      properties: {
        value: { type: 'any' },
        options: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              label: { type: 'string' },
              value: { type: 'any' }
            }
          }
        },
        placeholder: { type: 'string' },
        filter: { type: 'boolean' },
        showClear: { type: 'boolean' },
        required: { type: 'boolean' },
        disabled: { type: 'boolean' }
      }
    },
    'datatable': {
      type: 'object',
      properties: {
        data: { type: 'array' },
        columns: {
          type: 'array',
          items: {
            type: 'object',
            required: ['field'],
            properties: {
              field: { type: 'string' },
              header: { type: 'string' },
              sortable: { type: 'boolean' },
              filter: { type: 'boolean' }
            }
          }
        },
        config: { type: 'object' }
      }
    },
    'custom-datatable': {
      type: 'object',
      properties: {
        data: { type: 'array' },
        columns: {
          type: 'array',
          items: {
            type: 'object',
            required: ['field'],
            properties: {
              field: { type: 'string' },
              header: { type: 'string' },
              sortable: { type: 'boolean' },
              filter: { type: 'boolean' }
            }
          }
        },
        config: { type: 'object' }
      }
    },
    'form': {
      type: 'object',
      required: ['config'],
      properties: {
        config: {
          type: 'object',
          required: ['fields'],
          properties: {
            fields: {
              type: 'array',
              minItems: 1,
              items: {
                type: 'object',
                required: ['name', 'type'],
                properties: {
                  name: { type: 'string' },
                  type: { type: 'string' },
                  label: { type: 'string' },
                  required: { type: 'boolean' }
                }
              }
            }
          }
        },
        initialData: { type: 'object' }
      }
    }
  };
};


export const registerComponentSchemas = () => {
  const ConfigValidator = require('./ConfigValidator').default;
  const schemas = getComponentSchemas();
  
  Object.entries(schemas).forEach(([name, schema]) => {
    ConfigValidator.registerSchema(name, schema);
  });
  
  console.log('Component schemas registered successfully');
};

export default {
  registerDefaultComponents,
  getComponentSchemas,
  registerComponentSchemas
};
