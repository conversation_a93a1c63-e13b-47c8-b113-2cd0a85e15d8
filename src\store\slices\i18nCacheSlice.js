import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export const cacheI18nData = createAsyncThunk(
  'i18nCache/cacheI18nData',
  async ({ locale, dataType, data, options = {} }, { dispatch }) => {
    const {
      ttl = 60 * 60 * 1000, // 1 hour default
      tags = [],
      priority = 1,
      persistent = true
    } = options;

    const cacheKey = `i18n:${locale}:${dataType}`;
    const now = Date.now();

    const entry = {
      data,
      locale,
      dataType,
      createdAt: now,
      lastAccessed: now,
      expiresAt: now + ttl,
      tags: [...tags, `locale:${locale}`, `type:${dataType}`],
      priority,
      persistent,
      size: JSON.stringify(data).length,
      version: 1
    };

    dispatch(i18nCacheSlice.actions.set({ key: cacheKey, entry }));
    
    return { key: cacheKey, data, locale, dataType };
  }
);

export const getI18nData = createAsyncThunk(
  'i18nCache/getI18nData',
  async ({ locale, dataType }, { dispatch, getState }) => {
    const cacheKey = `i18n:${locale}:${dataType}`;
    const state = getState();
    const entry = state.i18nCache.entries[cacheKey];
    const now = Date.now();

    if (!entry) {
      dispatch(i18nCacheSlice.actions.recordMiss({ key: cacheKey }));
      return null;
    }

    // Check if expired
    if (entry.expiresAt && entry.expiresAt <= now) {
      dispatch(i18nCacheSlice.actions.delete({ key: cacheKey }));
      dispatch(i18nCacheSlice.actions.recordMiss({ key: cacheKey, reason: 'expired' }));
      return null;
    }

    // Update last accessed
    dispatch(i18nCacheSlice.actions.updateLastAccessed({ key: cacheKey, timestamp: now }));
    dispatch(i18nCacheSlice.actions.recordHit({ key: cacheKey }));

    return { key: cacheKey, data: entry.data, locale, dataType };
  }
);

export const invalidateI18nCache = createAsyncThunk(
  'i18nCache/invalidateLocale',
  async ({ locale, dataTypes = [] }, { dispatch, getState }) => {
    const state = getState();
    const entries = state.i18nCache.entries;
    const keysToInvalidate = [];

    Object.keys(entries).forEach(key => {
      const entry = entries[key];
      
      if (entry.locale === locale) {
        if (dataTypes.length === 0 || dataTypes.includes(entry.dataType)) {
          keysToInvalidate.push(key);
        }
      }
    });

    keysToInvalidate.forEach(key => {
      dispatch(i18nCacheSlice.actions.delete({ key }));
    });

    return { locale, dataTypes, invalidatedKeys: keysToInvalidate };
  }
);

export const preloadLocaleData = createAsyncThunk(
  'i18nCache/preloadLocale',
  async ({ locale, dataTypes = ['messages', 'formats', 'plurals'] }, { dispatch }) => {
    const results = [];
    
    for (const dataType of dataTypes) {
      try {
        let data;
        
        switch (dataType) {
          case 'messages':
            // Load translation messages
            data = await import(`../../locales/${locale}.json`);
            break;
          case 'formats':
            // Load locale-specific formats
            data = {
              date: getDateFormats(locale),
              number: getNumberFormats(locale),
              currency: getCurrencyFormats(locale)
            };
            break;
          case 'plurals':
            // Load plural rules
            data = getPluralRules(locale);
            break;
          default:
            console.warn(`Unknown i18n data type: ${dataType}`);
            continue;
        }

        await dispatch(cacheI18nData({
          locale,
          dataType,
          data: data.default || data,
          options: { persistent: true }
        }));

        results.push({ locale, dataType, success: true });
      } catch (error) {
        console.error(`Failed to preload ${dataType} for ${locale}:`, error);
        results.push({ locale, dataType, success: false, error: error.message });
      }
    }

    return { locale, results };
  }
);

const initialState = {
  entries: {},
  analytics: {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalRequests: 0,
    localeCount: 0,
    dataTypeStats: {}
  },
  config: {
    maxEntries: 200,
    defaultTTL: 60 * 60 * 1000, // 1 hour
    maxLocalesInCache: 10,
    preloadCommonLocales: true,
    strategy: 'locale-priority'
  },
  loading: {},
  errors: {}
};

const i18nCacheSlice = createSlice({
  name: 'i18nCache',
  initialState,
  reducers: {
    set: (state, action) => {
      const { key, entry } = action.payload;
      
      // Apply eviction strategy if needed
      if (Object.keys(state.entries).length >= state.config.maxEntries) {
        state.entries = applyI18nEvictionStrategy(state.entries, state.config.strategy, state.config.maxEntries);
      }
      
      state.entries[key] = entry;
      
      // Update analytics
      updateI18nAnalytics(state);
    },

    delete: (state, action) => {
      const { key } = action.payload;
      delete state.entries[key];
      delete state.loading[key];
      delete state.errors[key];
      
      // Update analytics
      updateI18nAnalytics(state);
    },

    clear: (state) => {
      state.entries = {};
      state.loading = {};
      state.errors = {};
      state.analytics.localeCount = 0;
      state.analytics.dataTypeStats = {};
    },

    updateLastAccessed: (state, action) => {
      const { key, timestamp } = action.payload;
      if (state.entries[key]) {
        state.entries[key].lastAccessed = timestamp;
      }
    },

    recordHit: (state, action) => {
      const { key } = action.payload;
      state.analytics.hits++;
      state.analytics.totalRequests++;
      state.analytics.hitRate = (state.analytics.hits / state.analytics.totalRequests) * 100;
      
      // Update data type stats
      const entry = state.entries[key];
      if (entry && entry.dataType) {
        if (!state.analytics.dataTypeStats[entry.dataType]) {
          state.analytics.dataTypeStats[entry.dataType] = { hits: 0, misses: 0 };
        }
        state.analytics.dataTypeStats[entry.dataType].hits++;
      }
    },

    recordMiss: (state, action) => {
      const { key, reason } = action.payload;
      state.analytics.misses++;
      state.analytics.totalRequests++;
      state.analytics.hitRate = (state.analytics.hits / state.analytics.totalRequests) * 100;
      
      // Try to extract data type from key for stats
      const keyParts = key.split(':');
      if (keyParts.length >= 3) {
        const dataType = keyParts[2];
        if (!state.analytics.dataTypeStats[dataType]) {
          state.analytics.dataTypeStats[dataType] = { hits: 0, misses: 0 };
        }
        state.analytics.dataTypeStats[dataType].misses++;
      }
    },

    cleanupExpired: (state) => {
      const now = Date.now();
      const validEntries = {};
      
      Object.entries(state.entries).forEach(([key, entry]) => {
        if (!entry.expiresAt || entry.expiresAt > now) {
          validEntries[key] = entry;
        }
      });
      
      state.entries = validEntries;
      updateI18nAnalytics(state);
    },

    cleanupLocale: (state, action) => {
      const { locale } = action.payload;
      const validEntries = {};
      
      Object.entries(state.entries).forEach(([key, entry]) => {
        if (entry.locale !== locale) {
          validEntries[key] = entry;
        }
      });
      
      state.entries = validEntries;
      updateI18nAnalytics(state);
    },

    updateConfig: (state, action) => {
      state.config = { ...state.config, ...action.payload };
    },

    setLoading: (state, action) => {
      const { key, loading } = action.payload;
      if (loading) {
        state.loading[key] = true;
      } else {
        delete state.loading[key];
      }
    },

    setError: (state, action) => {
      const { key, error } = action.payload;
      if (error) {
        state.errors[key] = error;
      } else {
        delete state.errors[key];
      }
    },

    importEntries: (state, action) => {
      const entries = action.payload;
      const now = Date.now();
      
      // Filter out expired entries during import
      Object.entries(entries).forEach(([key, entry]) => {
        if (!entry.expiresAt || entry.expiresAt > now) {
          state.entries[key] = entry;
        }
      });
      
      updateI18nAnalytics(state);
    }
  },

  extraReducers: (builder) => {
    builder
      .addCase(cacheI18nData.pending, (state, action) => {
        const { locale, dataType } = action.meta.arg;
        const cacheKey = `i18n:${locale}:${dataType}`;
        state.loading[cacheKey] = true;
        delete state.errors[cacheKey];
      })
      .addCase(cacheI18nData.fulfilled, (state, action) => {
        const { key } = action.payload;
        delete state.loading[key];
      })
      .addCase(cacheI18nData.rejected, (state, action) => {
        const { locale, dataType } = action.meta.arg;
        const cacheKey = `i18n:${locale}:${dataType}`;
        delete state.loading[cacheKey];
        state.errors[cacheKey] = action.error.message;
      })
      .addCase(preloadLocaleData.pending, (state, action) => {
        const { locale } = action.meta.arg;
        state.loading[`preload:${locale}`] = true;
      })
      .addCase(preloadLocaleData.fulfilled, (state, action) => {
        const { locale } = action.payload;
        delete state.loading[`preload:${locale}`];
      })
      .addCase(preloadLocaleData.rejected, (state, action) => {
        const { locale } = action.meta.arg;
        delete state.loading[`preload:${locale}`];
        state.errors[`preload:${locale}`] = action.error.message;
      });
  }
});

function updateI18nAnalytics(state) {
  const uniqueLocales = new Set();
  
  Object.values(state.entries).forEach(entry => {
    if (entry.locale) {
      uniqueLocales.add(entry.locale);
    }
  });
  
  state.analytics.localeCount = uniqueLocales.size;
}

function applyI18nEvictionStrategy(entries, strategy, maxEntries) {
  const entryCount = Object.keys(entries).length;
  if (entryCount <= maxEntries) return entries;

  switch (strategy) {
    case 'locale-priority': {
      // Prioritize common locales
      const commonLocales = ['en', 'es', 'fr', 'de', 'zh', 'ja'];
      const sortedEntries = Object.entries(entries)
        .sort(([, a], [, b]) => {
          const aCommon = commonLocales.includes(a.locale);
          const bCommon = commonLocales.includes(b.locale);
          
          if (aCommon && !bCommon) return -1;
          if (!aCommon && bCommon) return 1;
          
          // If both are common or both are uncommon, sort by last accessed
          return (b.lastAccessed || 0) - (a.lastAccessed || 0);
        });

      const newEntries = {};
      sortedEntries.slice(0, maxEntries).forEach(([key, entry]) => {
        newEntries[key] = entry;
      });

      return newEntries;
    }

    case 'data-type-priority': {
      // Prioritize certain data types
      const priorityTypes = ['messages', 'formats'];
      const sortedEntries = Object.entries(entries)
        .sort(([, a], [, b]) => {
          const aPriority = priorityTypes.indexOf(a.dataType) !== -1 ? 10 : (a.priority || 1);
          const bPriority = priorityTypes.indexOf(b.dataType) !== -1 ? 10 : (b.priority || 1);
          return bPriority - aPriority;
        });

      const newEntries = {};
      sortedEntries.slice(0, maxEntries).forEach(([key, entry]) => {
        newEntries[key] = entry;
      });

      return newEntries;
    }

    default:
      return applyI18nEvictionStrategy(entries, 'locale-priority', maxEntries);
  }
}

function getDateFormats(locale) {
  return {
    short: new Intl.DateTimeFormat(locale, { dateStyle: 'short' }),
    medium: new Intl.DateTimeFormat(locale, { dateStyle: 'medium' }),
    long: new Intl.DateTimeFormat(locale, { dateStyle: 'long' }),
    full: new Intl.DateTimeFormat(locale, { dateStyle: 'full' })
  };
}

function getNumberFormats(locale) {
  return {
    decimal: new Intl.NumberFormat(locale),
    percent: new Intl.NumberFormat(locale, { style: 'percent' }),
    scientific: new Intl.NumberFormat(locale, { notation: 'scientific' })
  };
}

function getCurrencyFormats(locale) {
  const currencies = ['USD', 'EUR', 'GBP', 'JPY', 'CNY'];
  const formats = {};
  
  currencies.forEach(currency => {
    formats[currency] = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency
    });
  });
  
  return formats;
}

function getPluralRules(locale) {
  return {
    cardinal: new Intl.PluralRules(locale),
    ordinal: new Intl.PluralRules(locale, { type: 'ordinal' })
  };
}

// Selectors
export const selectI18nCacheEntry = (state, locale, dataType) => {
  const key = `i18n:${locale}:${dataType}`;
  return state.i18nCache.entries[key];
};

export const selectI18nCacheEntries = (state) => state.i18nCache.entries;
export const selectI18nCacheAnalytics = (state) => state.i18nCache.analytics;
export const selectI18nCacheConfig = (state) => state.i18nCache.config;
export const selectI18nCacheLoading = (state, locale, dataType) => {
  const key = `i18n:${locale}:${dataType}`;
  return state.i18nCache.loading[key] || false;
};
export const selectI18nCacheError = (state, locale, dataType) => {
  const key = `i18n:${locale}:${dataType}`;
  return state.i18nCache.errors[key];
};

export const selectLocaleData = (state, locale) => {
  return Object.entries(state.i18nCache.entries)
    .filter(([, entry]) => entry.locale === locale)
    .reduce((acc, [key, entry]) => {
      acc[entry.dataType] = entry;
      return acc;
    }, {});
};

export const selectI18nDataTypeStats = (state) => state.i18nCache.analytics.dataTypeStats;

export const {
  set,
  delete: deleteEntry,
  clear,
  updateLastAccessed,
  recordHit,
  recordMiss,
  cleanupExpired,
  cleanupLocale,
  updateConfig,
  setLoading,
  setError,
  importEntries
} = i18nCacheSlice.actions;

export default i18nCacheSlice.reducer;
