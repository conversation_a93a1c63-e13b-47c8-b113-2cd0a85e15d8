import authHeader from "../AuthHeader";
import { getAxiosInstance } from "@utils/loader/axiosInstance.js";

/**
 * Get users by role
 * @param {string} roleName - Role name (default: 'asm_employee')
 * @returns {Promise} - Promise resolving to user data
 */
const getUsersByRole = async (roleName = "asm_employee") => {
  try {
    const axios = getAxiosInstance();
    const headers = {};

    // Add authorization header if available
    const token = authHeader();
    if (token) {
      headers["Authorization"] = `Bearer ${token}`;
    }

    const response = await axios.get(`/api/userrole/${roleName}/users`, {
      headers,
    });

    // Transform the API response to match the expected format
    if (response.data && response.data.isSuccess) {
      // const transformedUsers = transformUsersData(response.data.data);

      return {
        success: true,
        data: response.data.data,
        total: response.data.data.totalCount.length,
        statusCode: response.data.statusCode,
      };
    } else {
      throw new Error("API response indicates failure");
    }
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

/**
 * Transform API response data to match the expected user format
 * @param {Array} apiData - Raw API response data
 * @returns {Array} - Transformed user data
 */
const transformUsersData = (apiData) => {
  apiData = apiData?.users || [];
  if (!Array.isArray(apiData)) {
    return [];
  }
  return apiData.map((item, index) => {
    const user = item.user || {};

    return {
      id: item.userId || `USER-${index + 1}`,
      employeeCode: `ASME-${String(item.userId || index + 1).padStart(6, "0")}`,
      firstName: extractFirstName(user.username),
      lastName: extractLastName(user.username),
      email: user.username || "",
      role: item.roleKey ,
      status: user.statusName === "ACTIVE",
      statusName: user.statusName || "INACTIVE",
      activity: "View Logs",
      lastLoginDate: formatDate(new Date()),
      department: getDepartmentFromRole(item.roleName),
    };
  });
};

/**
 * Extract first name from username/email
 * @param {string} username - Username or email
 * @returns {string} - First name
 */
const extractFirstName = (username) => {
  if (!username) return "Unknown";

  // If it's an email, extract the part before @
  const emailPart = username.includes("@") ? username.split("@")[0] : username;

  // Split by dots or underscores and take the first part
  const parts = emailPart.split(/[._]/);
  return parts[0] ? capitalizeFirstLetter(parts[0]) : "Unknown";
};

/**
 * Extract last name from username/email
 * @param {string} username - Username or email
 * @returns {string} - Last name
 */
const extractLastName = (username) => {
  if (!username) return "";

  // If it's an email, extract the part before @
  const emailPart = username.includes("@") ? username.split("@")[0] : username;

  // Split by dots or underscores and take the second part
  const parts = emailPart.split(/[._]/);
  return parts[1] ? capitalizeFirstLetter(parts[1]) : "";
};

/**
 * Capitalize first letter of a string
 * @param {string} str - String to capitalize
 * @returns {string} - Capitalized string
 */
const capitalizeFirstLetter = (str) => {
  return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
};

/**
 * Get department based on role name
 * @param {string} roleName - Role name
 * @returns {string} - Department name
 */
const getDepartmentFromRole = (roleName) => {
  const roleMapping = {
    asm_employee: "Aviation Services",
    admin: "Administration",
    manager: "Management",
    hr: "Human Resources",
    finance: "Finance",
  };

  return roleMapping[roleName] || "General";
};

/**
 * Format date to readable string
 * @param {Date} date - Date object
 * @returns {string} - Formatted date string
 */
const formatDate = (date) => {
  if (!date) return "";
  return new Date(date).toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  });
};

/**
 * Search users by term (can be extended for server-side search)
 * @param {string} searchTerm - Search term
 * @param {string} roleName - Role name to filter by
 * @returns {Promise} - Promise resolving to filtered user data
 */
const searchUsers = async (searchTerm, roleName = "asm_employee") => {
  try {
    // For now, we'll get all users and filter client-side
    // In a real implementation, you might want to add server-side search
    const result = await getUsersByRole(roleName);

    if (!result.success) {
      return result;
    }

    // Filter users based on search term
    const filteredUsers = result.data.filter((user) => {
      const searchLower = searchTerm.toLowerCase();
      return (
        user.firstName.toLowerCase().includes(searchLower) ||
        user.lastName.toLowerCase().includes(searchLower) ||
        user.email.toLowerCase().includes(searchLower) ||
        user.employeeCode.toLowerCase().includes(searchLower)
      );
    });

    return {
      success: true,
      data: filteredUsers,
      total: filteredUsers.length,
      statusCode: result.statusCode,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

const updateUserStatus = async (userId, newStatus) => {
  try {
    const axios = getAxiosInstance();
    const headers = {};
    const token = authHeader();
    if (token) headers["Authorization"] = `Bearer ${token}`;

    const response = await axios.put(
      `/api/userstatus/${userId}`,
      { newStatus },
      { headers }
    );

    if (response.data && response.data.isSuccess) {
      return { success: true, data: response.data };
    }

    return {
      success: false,
      error: response.data?.message || "Failed to update status",
    };
  } catch (error) {
    return {
      success: false,
      error: error.response?.data?.message || error.message,
    };
  }
};

/**
 * Get all employee details
 * @returns {Promise} - Promise resolving to employee data
 */
const getEmployeeDetails = async () => {
  try {
    // Use the configured axios instance which should have the correct base URL
    const axios = getAxiosInstance(); // need auth token

    const response = await axios.get('/api/EmployeeDetails/getAll');

    // Transform the API response to match the expected format
    if (response.data && response.data.statusCode === 200) {
      return {
        success: true,
        data: response.data.data,
        total: response.data.data?.length || 0,
        statusCode: response.data.statusCode,
      };
    } else {
      throw new Error(`API response indicates failure: ${response.data?.message || 'Unknown error'}`);
    }
  } catch (error) {
    console.error('Error in getEmployeeDetails:', error);
    console.error('Full error details:', {
      message: error.message,
      status: error.status,
      response: error.response?.data,
      stack: error.stack
    });
    
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

/**
 * Search employee details by term
 * @param {string} searchTerm - Search term
 * @returns {Promise} - Promise resolving to filtered employee data
 */
const searchEmployeeDetails = async (searchTerm) => {
  try {
    const result = await getEmployeeDetails();

    if (!result.success) {
      return result;
    }

    // Filter employees based on search term
    const filteredEmployees = result.data.filter((employee) => {
      const searchLower = searchTerm.toLowerCase();
      return (
        employee.firstName?.toLowerCase().includes(searchLower) ||
        employee.lastName?.toLowerCase().includes(searchLower) ||
        employee.email?.toLowerCase().includes(searchLower) ||
        employee.employeeId?.toString().includes(searchLower)
      );
    });

    return {
      success: true,
      data: filteredEmployees,
      total: filteredEmployees.length,
      statusCode: result.statusCode,
    };
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

const UserService = {
  getUsersByRole,
  searchUsers,
  updateUserStatus,
  getEmployeeDetails,
  searchEmployeeDetails,
};

export default UserService;
