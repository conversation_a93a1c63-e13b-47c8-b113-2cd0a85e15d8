/**
 * Utility functions for CSV export functionality
 */

/**
 * Convert array of objects to CSV string
 * @param {Array} data - Array of objects to convert
 * @param {Array} columns - Array of column definitions with field and header properties
 * @returns {string} - CSV string
 */
export const convertToCSV = (data, columns) => {
  if (!data || data.length === 0) {
    return '';
  }

  // Create header row
  const headers = columns.map(col => col.header || col.field);
  const csvHeaders = headers.join(',');

  // Create data rows
  const csvRows = data.map(row => {
    return columns.map(col => {
      let value = row[col.field] || '';
      
      // Handle special cases
      if (value === null || value === undefined) {
        value = '';
      }
      
      // Convert to string and escape quotes
      value = String(value).replace(/"/g, '""');
      
      // Wrap in quotes if contains comma, newline, or quote
      if (value.includes(',') || value.includes('\n') || value.includes('"')) {
        value = `"${value}"`;
      }
      
      return value;
    }).join(',');
  });

  return [csvHeaders, ...csvRows].join('\n');
};

/**
 * Download CSV file
 * @param {string} csvContent - CSV content string
 * @param {string} filename - Name of the file to download
 */
export const downloadCSV = (csvContent, filename) => {
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }
};

/**
 * Export data table to CSV
 * @param {Array} data - Data array to export
 * @param {Array} columns - Column definitions
 * @param {string} filename - Filename for the CSV file
 */
export const exportTableToCSV = (data, columns, filename) => {
  try {
    if (!data || data.length === 0) {
      alert('No data available to export');
      return;
    }

    const csvContent = convertToCSV(data, columns);
    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
    const fullFilename = `${filename}_${timestamp}.csv`;
    
    downloadCSV(csvContent, fullFilename);
  } catch (error) {
    console.error('Error exporting CSV:', error);
    alert('Error occurred while exporting data');
  }
};

/**
 * Get current date timestamp for filename
 * @returns {string} - Formatted timestamp
 */
export const getTimestamp = () => {
  return new Date().toISOString().slice(0, 19).replace(/:/g, '-');
};
