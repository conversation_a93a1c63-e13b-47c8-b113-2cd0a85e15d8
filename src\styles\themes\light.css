/* Light Theme */
[data-theme="light"] {
  /* Primary Theme Colors */
  --theme-primary: var(--color-primary-600);
  --theme-primary-hover: var(--color-primary-700);
  --theme-primary-active: var(--color-primary-800);
  --theme-primary-light: var(--color-primary-100);
  --theme-primary-dark: var(--color-primary-900);

  /* Secondary Theme Colors */
  --theme-secondary: var(--color-secondary-500);
  --theme-secondary-hover: var(--color-secondary-600);
  --theme-secondary-active: var(--color-secondary-700);
  --theme-secondary-light: var(--color-secondary-100);
  --theme-secondary-dark: var(--color-secondary-800);

  /* Background Colors */
  --theme-bg-primary: var(--color-white);
  --theme-bg-secondary: var(--color-gray-50);
  --theme-bg-tertiary: var(--color-gray-100);
  --theme-bg-accent: var(--color-primary-50);
  --theme-bg-overlay: rgba(0, 0, 0, 0.5);
  --theme-bg-modal: var(--color-white);
  --theme-bg-card: var(--color-white);
  --theme-bg-input: var(--color-white);
  --theme-bg-disabled: var(--color-gray-100);

  /* Text Colors */
  --theme-text-primary: var(--color-gray-900);
  --theme-text-secondary: var(--color-gray-700);
  --theme-text-tertiary: var(--color-gray-500);
  --theme-text-accent: var(--color-primary-600);
  --theme-text-inverse: var(--color-white);
  --theme-text-disabled: var(--color-gray-400);
  --theme-text-placeholder: var(--color-gray-400);
  --theme-text-link: var(--color-primary-600);
  --theme-text-link-hover: var(--color-primary-700);

  /* Border Colors */
  --theme-border-primary: var(--color-gray-200);
  --theme-border-secondary: var(--color-gray-300);
  --theme-border-accent: var(--color-primary-300);
  --theme-border-focus: var(--color-primary-500);
  --theme-border-error: var(--color-error-500);
  --theme-border-success: var(--color-success-500);
  --theme-border-warning: var(--color-warning-500);
  --theme-border-info: var(--color-info-500);

  /* Status Colors */
  --theme-success: var(--color-success-600);
  --theme-success-bg: var(--color-success-50);
  --theme-success-border: var(--color-success-200);
  --theme-success-text: var(--color-success-800);

  --theme-warning: var(--color-warning-600);
  --theme-warning-bg: var(--color-warning-50);
  --theme-warning-border: var(--color-warning-200);
  --theme-warning-text: var(--color-warning-800);

  --theme-error: var(--color-error-600);
  --theme-error-bg: var(--color-error-50);
  --theme-error-border: var(--color-error-200);
  --theme-error-text: var(--color-error-800);

  --theme-info: var(--color-info-600);
  --theme-info-bg: var(--color-info-50);
  --theme-info-border: var(--color-info-200);
  --theme-info-text: var(--color-info-800);

  /* Component Specific Colors */
  --theme-navbar-bg: var(--color-white);
  --theme-navbar-text: var(--color-gray-700);
  --theme-navbar-border: var(--color-gray-200);

  --theme-sidebar-bg: var(--color-gray-50);
  --theme-sidebar-text: var(--color-gray-700);
  --theme-sidebar-border: var(--color-gray-200);
  --theme-sidebar-hover: var(--color-gray-100);
  --theme-sidebar-active: var(--color-primary-100);

  --theme-footer-bg: var(--color-gray-100);
  --theme-footer-text: var(--color-gray-600);
  --theme-footer-border: var(--color-gray-200);

  --theme-button-primary-bg: var(--color-primary-600);
  --theme-button-primary-text: var(--color-white);
  --theme-button-primary-hover: var(--color-primary-700);
  --theme-button-primary-active: var(--color-primary-800);
  --theme-button-primary-disabled: var(--color-gray-300);

  --theme-button-secondary-bg: var(--color-white);
  --theme-button-secondary-text: var(--color-gray-700);
  --theme-button-secondary-border: var(--color-gray-300);
  --theme-button-secondary-hover: var(--color-gray-50);
  --theme-button-secondary-active: var(--color-gray-100);

  --theme-input-bg: var(--color-white);
  --theme-input-text: var(--color-gray-900);
  --theme-input-border: var(--color-gray-300);
  --theme-input-focus: var(--color-primary-500);
  --theme-input-placeholder: var(--color-gray-400);
  --theme-input-disabled: var(--color-gray-100);

  --theme-table-bg: var(--color-white);
  --theme-table-header-bg: var(--color-gray-50);
  --theme-table-border: var(--color-gray-200);
  --theme-table-hover: var(--color-gray-50);
  --theme-table-stripe: var(--color-gray-25);

  --theme-card-bg: var(--color-white);
  --theme-card-border: var(--color-gray-200);
  --theme-card-shadow: var(--shadow-sm);
  --theme-card-hover-shadow: var(--shadow-md);

  --theme-modal-bg: var(--color-white);
  --theme-modal-overlay: rgba(0, 0, 0, 0.5);
  --theme-modal-border: var(--color-gray-200);

  --theme-dropdown-bg: var(--color-white);
  --theme-dropdown-border: var(--color-gray-200);
  --theme-dropdown-shadow: var(--shadow-lg);
  --theme-dropdown-hover: var(--color-gray-50);

  --theme-tooltip-bg: var(--color-gray-900);
  --theme-tooltip-text: var(--color-white);
  --theme-tooltip-shadow: var(--shadow-lg);

  --theme-progress-bg: var(--color-gray-200);
  --theme-progress-fill: var(--color-primary-600);

  --theme-switch-bg: var(--color-gray-200);
  --theme-switch-thumb: var(--color-white);
  --theme-switch-active: var(--color-primary-600);

  --theme-checkbox-bg: var(--color-white);
  --theme-checkbox-border: var(--color-gray-300);
  --theme-checkbox-checked: var(--color-primary-600);
  --theme-checkbox-checkmark: var(--color-white);

  --theme-radio-bg: var(--color-white);
  --theme-radio-border: var(--color-gray-300);
  --theme-radio-checked: var(--color-primary-600);

  --theme-slider-track: var(--color-gray-200);
  --theme-slider-thumb: var(--color-primary-600);
  --theme-slider-fill: var(--color-primary-600);

  /* Scrollbar Colors */
  --theme-scrollbar-track: var(--color-gray-100);
  --theme-scrollbar-thumb: var(--color-gray-300);
  --theme-scrollbar-thumb-hover: var(--color-gray-400);

  /* Focus Ring */
  --theme-focus-ring: 0 0 0 3px rgba(59, 130, 246, 0.1);
  --theme-focus-ring-offset: 2px;

  /* Elevation Shadows */
  --theme-elevation-1: var(--shadow-sm);
  --theme-elevation-2: var(--shadow-base);
  --theme-elevation-3: var(--shadow-md);
  --theme-elevation-4: var(--shadow-lg);
  --theme-elevation-5: var(--shadow-xl);

  /* Code Syntax Highlighting */
  --theme-code-bg: var(--color-gray-100);
  --theme-code-text: var(--color-gray-800);
  --theme-code-keyword: var(--color-primary-600);
  --theme-code-string: var(--color-success-600);
  --theme-code-comment: var(--color-gray-500);
  --theme-code-number: var(--color-warning-600);
  --theme-code-function: var(--color-info-600);

  /* Selection Colors */
  --theme-selection-bg: rgba(59, 130, 246, 0.2);
  --theme-selection-text: var(--color-gray-900);

  /* Print Styles */
  --theme-print-bg: var(--color-white);
  --theme-print-text: var(--color-black);
  --theme-print-border: var(--color-gray-400);
}

/* Light theme specific adjustments */
[data-theme="light"] {
  color-scheme: light;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  [data-theme="light"] {
    --theme-border-primary: var(--color-gray-400);
    --theme-border-secondary: var(--color-gray-500);
    --theme-text-secondary: var(--color-gray-800);
    --theme-text-tertiary: var(--color-gray-600);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  [data-theme="light"] {
    --transition-all: none;
    --transition-colors: none;
    --transition-opacity: none;
    --transition-shadow: none;
    --transition-transform: none;
  }
}
