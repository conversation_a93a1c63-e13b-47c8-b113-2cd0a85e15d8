import React, { useState, useRef } from 'react';
import { Steps } from 'primereact/steps';
import { TabView, TabPanel } from 'primereact/tabview';
import { But<PERSON> } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { Card } from 'primereact/card';
import DynamicComponent from '../components/framework/core/DynamicComponent';
import './LeadManagement.css';

/**
 * LeadManagementDynamic - Lead Management page built using the Dynamic Component Framework
 * This demonstrates how to build complex forms using JSON configuration
 */
const LeadManagementDynamic = () => {
  const toast = useRef(null);
  const [activeStep, setActiveStep] = useState(0);
  const [activeTabIndex, setActiveTabIndex] = useState(0);

  // Form state
  const [formData, setFormData] = useState({
    company: 'Mahan Air',
    contact: '<PERSON>',
    email: '<EMAIL>',
    phone: '+98 21 4884 5000',
    rating: 2,
    opportunity: '1 Ton Jet Fuel',
    expectedRevenue: '$7000',
    priority: 'Medium',
    leadSource: 'Event',
    tags: '',
    salesPerson: '<PERSON>',
    leadStatus: 'Introduction',
    blacklistLead: 'No',
    reason: '',
    streetAddress: '',
    city: '',
    stateProvince: '',
    zipPostalCode: '',
    country: null,
    addressType: 'Business'
  });

  // Wizard steps
  const steps = [
    { label: 'New Lead', icon: 'pi pi-user' },
    { label: 'Opportunity', icon: 'pi pi-briefcase' },
    { label: 'Won / Lost', icon: 'pi pi-check-circle' }
  ];

  // Form configuration for General section - Column 1
  const generalFormColumn1Config = {
    fields: [
      {
        name: 'company',
        type: 'input-text',
        label: 'Company',
        required: false,
        props: {
          placeholder: 'Enter company name'
        },
        helpText: '(New prospect can be created by typing name only if not available for selection, later users can fill complete details)'
      },
      {
        name: 'email',
        type: 'input-text',
        label: 'Email',
        required: false,
        props: {
          placeholder: 'Enter email',
          keyfilter: 'email'
        }
      },
      {
        name: 'phone',
        type: 'input-text',
        label: 'Phone',
        required: false,
        props: {
          placeholder: 'Enter phone number'
        }
      },
      {
        name: 'rating',
        type: 'rating',
        label: 'Ratings',
        required: false,
        props: {
          cancel: false,
          stars: 5
        }
      }
    ],
    layout: {
      columns: 1,
      showProgress: false,
      showSubmitButton: false
    }
  };

  // Form configuration for General section - Column 2
  const generalFormColumn2Config = {
    fields: [
      {
        name: 'contact',
        type: 'input-text',
        label: 'Contact',
        required: false,
        props: {
          placeholder: 'Enter contact name'
        },
        helpText: '(New Contact to be created by typing name only if not available for selection, later users can fill other details)'
      },
      {
        name: 'priority',
        type: 'dropdown',
        label: 'Priority',
        required: false,
        props: {
          options: [
            { label: 'Low', value: 'Low' },
            { label: 'Medium', value: 'Medium' },
            { label: 'High', value: 'High' }
          ],
          placeholder: 'Select priority'
        }
      },
      {
        name: 'leadSource',
        type: 'dropdown',
        label: 'Lead Source',
        required: false,
        props: {
          options: [
            { label: 'Event', value: 'Event' },
            { label: 'Website', value: 'Website' },
            { label: 'Referral', value: 'Referral' },
            { label: 'Cold Call', value: 'Cold Call' }
          ],
          placeholder: 'Select lead source'
        }
      },
      {
        name: 'tags',
        type: 'input-text',
        label: 'Tags',
        required: false,
        props: {
          placeholder: 'Enter tags...'
        },
        helpText: '(New tags can be created if not available from this screen and add it to the master)'
      }
    ],
    layout: {
      columns: 1,
      showProgress: false,
      showSubmitButton: false
    }
  };

  // Form configuration for General section - Column 3
  const generalFormColumn3Config = {
    fields: [
      {
        name: 'opportunity',
        type: 'input-text',
        label: 'Opportunity',
        required: false,
        props: {
          placeholder: 'Enter opportunity'
        },
        helpText: '(To create an Opportunity from Lead, Prospect or Customer Details needs to be completed)'
      },
      {
        name: 'expectedRevenue',
        type: 'input-text',
        label: 'Expected Revenue',
        required: false,
        props: {
          placeholder: 'Enter expected revenue'
        },
        helpText: '(The expected revenue value will be overridden based on an opportunity when created)'
      },
      {
        name: 'salesPerson',
        type: 'dropdown',
        label: 'Sales Person',
        required: false,
        props: {
          options: [
            { label: 'John Doe', value: 'John Doe' },
            { label: 'Jane Smith', value: 'Jane Smith' },
            { label: 'Mike Johnson', value: 'Mike Johnson' }
          ],
          placeholder: 'Select sales person'
        }
      },
      {
        name: 'blacklistLead',
        type: 'dropdown',
        label: 'Blacklist Lead',
        required: false,
        props: {
          options: [
            { label: 'Yes', value: 'Yes' },
            { label: 'No', value: 'No' }
          ],
          placeholder: 'Select option'
        }
      },
      {
        name: 'reason',
        type: 'textarea',
        label: 'Reason',
        required: false,
        props: {
          placeholder: 'Enter reason...',
          rows: 3
        },
        helpText: '(Reason mandatory if blacklisted)'
      }
    ],
    layout: {
      columns: 1,
      showProgress: false,
      showSubmitButton: false
    }
  };

  // Address form configuration
  const addressFormConfig = {
    fields: [
      {
        name: 'streetAddress',
        type: 'input-text',
        label: 'Street Address',
        required: false,
        props: {
          placeholder: 'Enter street address'
        }
      },
      {
        name: 'city',
        type: 'input-text',
        label: 'City',
        required: false,
        props: {
          placeholder: 'Enter city'
        }
      },
      {
        name: 'stateProvince',
        type: 'input-text',
        label: 'State/Province',
        required: false,
        props: {
          placeholder: 'Enter state/province'
        }
      },
      {
        name: 'zipPostalCode',
        type: 'input-text',
        label: 'ZIP/Postal Code',
        required: false,
        props: {
          placeholder: 'Enter ZIP code'
        }
      },
      {
        name: 'country',
        type: 'dropdown',
        label: 'Country',
        required: false,
        props: {
          options: [
            { label: 'United States', value: 'US' },
            { label: 'United Kingdom', value: 'UK' },
            { label: 'Canada', value: 'CA' },
            { label: 'Iran', value: 'IR' },
            { label: 'UAE', value: 'AE' }
          ],
          placeholder: 'Select Country'
        }
      },
      {
        name: 'addressType',
        type: 'dropdown',
        label: 'Address Type',
        required: false,
        props: {
          options: [
            { label: 'Business', value: 'Business' },
            { label: 'Residential', value: 'Residential' },
            { label: 'Billing', value: 'Billing' },
            { label: 'Shipping', value: 'Shipping' }
          ],
          placeholder: 'Select address type'
        }
      }
    ],
    layout: {
      columns: 2,
      showProgress: false,
      showSubmitButton: false
    }
  };

  const handleFormChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const showToast = (severity, summary, detail) => {
    toast.current.show({ severity, summary, detail, life: 3000 });
  };

  return (
    <div className="lead-management">
      <Toast ref={toast} />

      {/* Header */}
      <div className="lead-header">
        <div className="lead-breadcrumb">
          <Button icon="pi pi-arrow-left" text className="p-button-sm" />
          <span className="breadcrumb-text">Pipeline / Mahan Air - ASK-000001</span>
        </div>
        <div className="company-selector">
          <label>Company</label>
          <DynamicComponent
            type="dropdown"
            config={{
              value: 'Aviation Services Management, Dubai',
              options: [{ label: 'Aviation Services Management, Dubai', value: 'Aviation Services Management, Dubai' }],
              className: 'company-dropdown'
            }}
          />
        </div>
      </div>

      {/* Wizard Steps */}
      <div className="wizard-steps">
        <Steps
          model={steps}
          activeIndex={activeStep}
          onSelect={(e) => setActiveStep(e.index)}
          readOnly={false}
        />
      </div>

      {/* Action Buttons */}
      <div className="action-buttons">
        <Button
          label="Edit"
          icon="pi pi-pencil"
          className="p-button-sm"
          onClick={() => showToast('info', 'Edit Mode', 'Editing lead information')}
        />
        <Button
          label="Enrich Data"
          icon="pi pi-database"
          className="p-button-sm"
          onClick={() => showToast('info', 'Enrich Data', 'Enriching lead data...')}
        />
        <Button
          label="Create Opportunity"
          icon="pi pi-plus"
          className="p-button-sm p-button-success"
          onClick={() => showToast('success', 'Opportunity Created', 'Opportunity has been created successfully')}
        />
        <Button
          label="Mark as Won"
          icon="pi pi-check"
          className="p-button-sm p-button-success"
          onClick={() => showToast('success', 'Marked as Won', 'Lead has been marked as won')}
        />
        <Button
          label="Mark as Lost"
          icon="pi pi-times"
          className="p-button-sm p-button-danger"
          onClick={() => showToast('warn', 'Marked as Lost', 'Lead has been marked as lost')}
        />
        <div className="activities-section">
          <i className="pi pi-clock"></i>
          <span>Activities</span>
          <span className="activity-count">3/5</span>
        </div>
        <Button icon="pi pi-trash" className="p-button-sm p-button-danger p-button-text" />
      </div>

      {/* Main Content - Using Dynamic Forms */}
      <div className="lead-content">
        <Card title="General" className="general-section">
          <div className="form-grid">
            {/* Column 1 - Using Dynamic Form */}
            <div className="form-column">
              <DynamicComponent
                type="form"
                config={{
                  config: generalFormColumn1Config,
                  initialData: formData,
                  onChange: (data) => setFormData(prev => ({ ...prev, ...data }))
                }}
              />
            </div>

            {/* Column 2 - Using Dynamic Form */}
            <div className="form-column">
              <DynamicComponent
                type="form"
                config={{
                  config: generalFormColumn2Config,
                  initialData: formData,
                  onChange: (data) => setFormData(prev => ({ ...prev, ...data }))
                }}
              />
            </div>

            {/* Column 3 - Using Dynamic Form */}
            <div className="form-column">
              <DynamicComponent
                type="form"
                config={{
                  config: generalFormColumn3Config,
                  initialData: formData,
                  onChange: (data) => setFormData(prev => ({ ...prev, ...data }))
                }}
              />

              {/* Lead Status with custom rendering */}
              <div className="field">
                <label htmlFor="leadStatus">Lead Status</label>
                <div className="lead-status-field">
                  <Button
                    icon="pi pi-chevron-left"
                    className="p-button-sm p-button-text"
                  />
                  <div className="status-badge">
                    {formData.leadStatus}
                  </div>
                  <Button
                    icon="pi pi-chevron-right"
                    className="p-button-sm p-button-text"
                  />
                </div>
                <small className="field-hint">
                  (The statuses can be inserted in the Lead status master, status will suggest next status values)
                </small>
              </div>
            </div>
          </div>
        </Card>

        {/* TabView Section */}
        <div className="tabs-section">
          <TabView activeIndex={activeTabIndex} onTabChange={(e) => setActiveTabIndex(e.index)}>
            <TabPanel header="Address">
              <Card title="Address Information" className="address-section">
                <div className="address-header">
                  <Button
                    label="New Address"
                    icon="pi pi-plus"
                    className="p-button-sm p-button-primary"
                    onClick={() => showToast('info', 'New Address', 'Adding new address')}
                  />
                </div>

                {/* Address Form - Using Dynamic Form */}
                <DynamicComponent
                  type="form"
                  config={{
                    config: addressFormConfig,
                    initialData: formData,
                    onChange: (data) => setFormData(prev => ({ ...prev, ...data }))
                  }}
                />
              </Card>
            </TabPanel>

            <TabPanel header="Contacts">
              <Card title="Contact Information">
                <p>Contact information will be displayed here.</p>
              </Card>
            </TabPanel>

            <TabPanel header="Compliance">
              <Card title="Compliance Information">
                <p>Compliance information will be displayed here.</p>
              </Card>
            </TabPanel>

            <TabPanel header="Notes & Messages">
              <Card title="Notes & Messages">
                <p>Notes and messages will be displayed here.</p>
              </Card>
            </TabPanel>

            <TabPanel header="Logs">
              <Card title="Activity Logs">
                <p>Activity logs will be displayed here.</p>
              </Card>
            </TabPanel>

            <TabPanel header="Documentation">
              <Card title="Documentation">
                <p>Documentation will be displayed here.</p>
              </Card>
            </TabPanel>
          </TabView>
        </div>
      </div>

      {/* Footer */}
      <div className="lead-footer">
        <span className="footer-info">
          <i className="pi pi-circle-fill" style={{ color: 'green', fontSize: '0.5rem' }}></i>
          Mark Stephen
        </span>
        <Button label="New Message" className="p-button-sm p-button-text" />
      </div>
    </div>
  );
};

export default LeadManagementDynamic;
