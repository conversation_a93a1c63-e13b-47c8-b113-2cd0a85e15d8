import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Calendar } from 'primereact/calendar';
import FloatLabel from './FloatLabel';

const DynamicCalendar = ({
  value,
  onChange,
  onBlur,
  onFocus,
  onSelect,
  onViewDateChange,
  onShow,
  onHide,
  placeholder = 'Select date',
  disabled = false,
  required = false,
  readOnly = false,
  showIcon = true,
  icon = 'pi pi-calendar',
  dateFormat = 'mm/dd/yy',
  selectionMode = 'single', // single, multiple, range
  numberOfMonths = 1,
  view = 'date', // date, month, year
  touchUI = false,
  showTime = false,
  timeOnly = false,
  hourFormat = '24', // 12, 24
  stepHour = 1,
  stepMinute = 1,
  stepSecond = 1,
  showSeconds = false,
  showMillisec = false,
  showButtonBar = false,
  todayButtonClassName = 'p-button-text',
  clearButtonClassName = 'p-button-text',
  minDate,
  maxDate,
  disabledDates = [],
  disabledDays = [],
  inline = false,
  showWeek = false,
  locale = 'en',
  className = '',
  style = {},
  inputStyle = {},
  panelStyle = {},
  panelClassName = '',
  id,
  name,
  label,
  floatLabel = false,
  tooltip,
  size = 'normal',
  variant = 'outlined',
  monthNavigator = false,
  yearNavigator = false,
  yearRange = '1900:2030',
  onEvent,
  debug = false,
  ...props
}) => {
  const [focused, setFocused] = useState(false);
  const [panelVisible, setPanelVisible] = useState(false);

  const handleChange = useCallback((e) => {
    const newValue = e.value;
    
    if (debug) {
      console.log('DynamicCalendar change:', { name, value: newValue });
    }

    if (onChange) {
      onChange(newValue, e);
    }

    if (onEvent) {
      onEvent('change', { value: newValue, name, event: e });
    }
  }, [onChange, onEvent, name, debug]);

  const handleBlur = useCallback((e) => {
    setFocused(false);
    
    if (onBlur) {
      onBlur(e);
    }

    if (onEvent) {
      onEvent('blur', { value, name, event: e });
    }
  }, [onBlur, onEvent, value, name]);

  const handleFocus = useCallback((e) => {
    setFocused(true);
    
    if (onFocus) {
      onFocus(e);
    }

    if (onEvent) {
      onEvent('focus', { value, name, event: e });
    }
  }, [onFocus, onEvent, value, name]);

  const handleSelect = useCallback((e) => {
    if (onSelect) {
      onSelect(e);
    }

    if (onEvent) {
      onEvent('select', { value: e.value, name, event: e });
    }
  }, [onSelect, onEvent, name]);

  const handleShow = useCallback((e) => {
    setPanelVisible(true);
    
    if (onShow) {
      onShow(e);
    }

    if (onEvent) {
      onEvent('show', { value, name, event: e });
    }
  }, [onShow, onEvent, value, name]);

  const handleHide = useCallback((e) => {
    setPanelVisible(false);
    
    if (onHide) {
      onHide(e);
    }

    if (onEvent) {
      onEvent('hide', { value, name, event: e });
    }
  }, [onHide, onEvent, value, name]);

  const handleViewDateChange = useCallback((e) => {
    if (onViewDateChange) {
      onViewDateChange(e);
    }

    if (onEvent) {
      onEvent('viewDateChange', { value: e.value, name, event: e });
    }
  }, [onViewDateChange, onEvent, name]);

  // Build CSS classes
  const calendarClasses = [
    'dynamic-calendar',
    `dynamic-calendar--${size}`,
    `dynamic-calendar--${variant}`,
    focused ? 'dynamic-calendar--focused' : '',
    panelVisible ? 'dynamic-calendar--open' : '',
    disabled ? 'dynamic-calendar--disabled' : '',
    required ? 'dynamic-calendar--required' : '',
    inline ? 'dynamic-calendar--inline' : '',
    className
  ].filter(Boolean).join(' ');

  // Calendar props
  const calendarProps = {
    id: id || name,
    name,
    value,
    onChange: handleChange,
    onBlur: handleBlur,
    onFocus: handleFocus,
    onSelect: handleSelect,
    onViewDateChange: handleViewDateChange,
    onShow: handleShow,
    onHide: handleHide,
    placeholder,
    disabled,
    required,
    readOnly,
    showIcon,
    icon,
    dateFormat,
    selectionMode,
    numberOfMonths,
    view,
    touchUI,
    showTime,
    timeOnly,
    hourFormat,
    stepHour,
    stepMinute,
    stepSecond,
    showSeconds,
    showMillisec,
    showButtonBar,
    todayButtonClassName,
    clearButtonClassName,
    minDate,
    maxDate,
    disabledDates,
    disabledDays,
    inline,
    showWeek,
    locale,
    className: calendarClasses,
    style,
    inputStyle,
    panelStyle,
    panelClassName,
    tooltip,
    monthNavigator,
    yearNavigator,
    yearRange,
    'aria-label': label || placeholder,
    'aria-required': required,
    'data-component-type': 'calendar',
    'data-field-name': name,
    ...props
  };

  // Remove undefined props
  Object.keys(calendarProps).forEach(key => {
    if (calendarProps[key] === undefined) {
      delete calendarProps[key];
    }
  });

  const renderCalendar = () => <Calendar {...calendarProps} />;

  const renderWithFloatLabel = (calendar) => {
    if (!floatLabel || !label || inline) {
      return calendar;
    }

    return (
      <FloatLabel>
        {calendar}
        <label htmlFor={id || name}>{label}</label>
      </FloatLabel>
    );
  };

  let finalCalendar = renderCalendar();
  finalCalendar = renderWithFloatLabel(finalCalendar);

  return (
    <div className="dynamic-calendar-wrapper">
      {debug && (
        <div className="dynamic-calendar__debug">
          <small>
            Value: {value ? (Array.isArray(value) ? `${value.length} dates` : value.toString()) : 'None'} | 
            Mode: {selectionMode} | 
            Panel: {panelVisible ? 'Open' : 'Closed'}
          </small>
        </div>
      )}
      {finalCalendar}
    </div>
  );
};

DynamicCalendar.propTypes = {
  value: PropTypes.oneOfType([PropTypes.instanceOf(Date), PropTypes.array]),
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onFocus: PropTypes.func,
  onSelect: PropTypes.func,
  onViewDateChange: PropTypes.func,
  onShow: PropTypes.func,
  onHide: PropTypes.func,
  placeholder: PropTypes.string,
  disabled: PropTypes.bool,
  required: PropTypes.bool,
  readOnly: PropTypes.bool,
  showIcon: PropTypes.bool,
  icon: PropTypes.string,
  dateFormat: PropTypes.string,
  selectionMode: PropTypes.oneOf(['single', 'multiple', 'range']),
  numberOfMonths: PropTypes.number,
  view: PropTypes.oneOf(['date', 'month', 'year']),
  touchUI: PropTypes.bool,
  showTime: PropTypes.bool,
  timeOnly: PropTypes.bool,
  hourFormat: PropTypes.oneOf(['12', '24']),
  stepHour: PropTypes.number,
  stepMinute: PropTypes.number,
  stepSecond: PropTypes.number,
  showSeconds: PropTypes.bool,
  showMillisec: PropTypes.bool,
  showButtonBar: PropTypes.bool,
  todayButtonClassName: PropTypes.string,
  clearButtonClassName: PropTypes.string,
  minDate: PropTypes.instanceOf(Date),
  maxDate: PropTypes.instanceOf(Date),
  disabledDates: PropTypes.array,
  disabledDays: PropTypes.array,
  inline: PropTypes.bool,
  showWeek: PropTypes.bool,
  locale: PropTypes.string,
  className: PropTypes.string,
  style: PropTypes.object,
  inputStyle: PropTypes.object,
  panelStyle: PropTypes.object,
  panelClassName: PropTypes.string,
  id: PropTypes.string,
  name: PropTypes.string,
  label: PropTypes.string,
  floatLabel: PropTypes.bool,
  tooltip: PropTypes.string,
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  variant: PropTypes.oneOf(['outlined', 'filled']),
  monthNavigator: PropTypes.bool,
  yearNavigator: PropTypes.bool,
  yearRange: PropTypes.string,
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicCalendar;
