import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export const cacheThemeData = createAsyncThunk(
  'themeCache/cacheThemeData',
  async ({ themeId, dataType, data, options = {} }, { dispatch }) => {
    const {
      ttl = 24 * 60 * 60 * 1000, // 24 hours default
      tags = [],
      priority = 1,
      persistent = true
    } = options;

    const cacheKey = `theme:${themeId}:${dataType}`;
    const now = Date.now();

    const entry = {
      data,
      themeId,
      dataType,
      createdAt: now,
      lastAccessed: now,
      expiresAt: now + ttl,
      tags: [...tags, `theme:${themeId}`, `type:${dataType}`],
      priority,
      persistent,
      size: JSON.stringify(data).length,
      version: 1
    };

    dispatch(themeCacheSlice.actions.set({ key: cacheKey, entry }));
    
    return { key: cacheKey, data, themeId, dataType };
  }
);

export const getThemeData = createAsyncThunk(
  'themeCache/getThemeData',
  async ({ themeId, dataType }, { dispatch, getState }) => {
    const cacheKey = `theme:${themeId}:${dataType}`;
    const state = getState();
    const entry = state.themeCache.entries[cacheKey];
    const now = Date.now();

    if (!entry) {
      dispatch(themeCacheSlice.actions.recordMiss({ key: cacheKey }));
      return null;
    }

    // Check if expired
    if (entry.expiresAt && entry.expiresAt <= now) {
      dispatch(themeCacheSlice.actions.delete({ key: cacheKey }));
      dispatch(themeCacheSlice.actions.recordMiss({ key: cacheKey, reason: 'expired' }));
      return null;
    }

    // Update last accessed
    dispatch(themeCacheSlice.actions.updateLastAccessed({ key: cacheKey, timestamp: now }));
    dispatch(themeCacheSlice.actions.recordHit({ key: cacheKey }));

    return { key: cacheKey, data: entry.data, themeId, dataType };
  }
);

export const invalidateThemeCache = createAsyncThunk(
  'themeCache/invalidateTheme',
  async ({ themeId, dataTypes = [] }, { dispatch, getState }) => {
    const state = getState();
    const entries = state.themeCache.entries;
    const keysToInvalidate = [];

    Object.keys(entries).forEach(key => {
      const entry = entries[key];
      
      if (entry.themeId === themeId) {
        if (dataTypes.length === 0 || dataTypes.includes(entry.dataType)) {
          keysToInvalidate.push(key);
        }
      }
    });

    keysToInvalidate.forEach(key => {
      dispatch(themeCacheSlice.actions.delete({ key }));
    });

    return { themeId, dataTypes, invalidatedKeys: keysToInvalidate };
  }
);

const initialState = {
  entries: {},
  analytics: {
    hits: 0,
    misses: 0,
    hitRate: 0,
    totalRequests: 0,
    themeCount: 0,
    dataTypeStats: {}
  },
  config: {
    maxEntries: 50,
    defaultTTL: 24 * 60 * 60 * 1000, // 24 hours
    maxThemesInCache: 20,
    persistCustomThemes: true,
    strategy: 'theme-priority'
  },
  loading: {},
  errors: {}
};

const themeCacheSlice = createSlice({
  name: 'themeCache',
  initialState,
  reducers: {
    set: (state, action) => {
      const { key, entry } = action.payload;
      
      // Apply eviction strategy if needed
      if (Object.keys(state.entries).length >= state.config.maxEntries) {
        state.entries = applyThemeEvictionStrategy(state.entries, state.config.strategy, state.config.maxEntries);
      }
      
      state.entries[key] = entry;
      
      // Update analytics
      updateThemeAnalytics(state);
    },

    delete: (state, action) => {
      const { key } = action.payload;
      delete state.entries[key];
      delete state.loading[key];
      delete state.errors[key];
      
      // Update analytics
      updateThemeAnalytics(state);
    },

    clear: (state) => {
      state.entries = {};
      state.loading = {};
      state.errors = {};
      state.analytics.themeCount = 0;
      state.analytics.dataTypeStats = {};
    },

    updateLastAccessed: (state, action) => {
      const { key, timestamp } = action.payload;
      if (state.entries[key]) {
        state.entries[key].lastAccessed = timestamp;
      }
    },

    recordHit: (state, action) => {
      const { key } = action.payload;
      state.analytics.hits++;
      state.analytics.totalRequests++;
      state.analytics.hitRate = (state.analytics.hits / state.analytics.totalRequests) * 100;
      
      // Update data type stats
      const entry = state.entries[key];
      if (entry && entry.dataType) {
        if (!state.analytics.dataTypeStats[entry.dataType]) {
          state.analytics.dataTypeStats[entry.dataType] = { hits: 0, misses: 0 };
        }
        state.analytics.dataTypeStats[entry.dataType].hits++;
      }
    },

    recordMiss: (state, action) => {
      const { key, reason } = action.payload;
      state.analytics.misses++;
      state.analytics.totalRequests++;
      state.analytics.hitRate = (state.analytics.hits / state.analytics.totalRequests) * 100;
      
      // Try to extract data type from key for stats
      const keyParts = key.split(':');
      if (keyParts.length >= 3) {
        const dataType = keyParts[2];
        if (!state.analytics.dataTypeStats[dataType]) {
          state.analytics.dataTypeStats[dataType] = { hits: 0, misses: 0 };
        }
        state.analytics.dataTypeStats[dataType].misses++;
      }
    },

    cleanupExpired: (state) => {
      const now = Date.now();
      const validEntries = {};
      
      Object.entries(state.entries).forEach(([key, entry]) => {
        if (!entry.expiresAt || entry.expiresAt > now) {
          validEntries[key] = entry;
        }
      });
      
      state.entries = validEntries;
      updateThemeAnalytics(state);
    },

    cleanupTheme: (state, action) => {
      const { themeId } = action.payload;
      const validEntries = {};
      
      Object.entries(state.entries).forEach(([key, entry]) => {
        if (entry.themeId !== themeId) {
          validEntries[key] = entry;
        }
      });
      
      state.entries = validEntries;
      updateThemeAnalytics(state);
    },

    updateConfig: (state, action) => {
      state.config = { ...state.config, ...action.payload };
    },

    setLoading: (state, action) => {
      const { key, loading } = action.payload;
      if (loading) {
        state.loading[key] = true;
      } else {
        delete state.loading[key];
      }
    },

    setError: (state, action) => {
      const { key, error } = action.payload;
      if (error) {
        state.errors[key] = error;
      } else {
        delete state.errors[key];
      }
    },

    importEntries: (state, action) => {
      const entries = action.payload;
      const now = Date.now();
      
      // Filter out expired entries during import
      Object.entries(entries).forEach(([key, entry]) => {
        if (!entry.expiresAt || entry.expiresAt > now) {
          state.entries[key] = entry;
        }
      });
      
      updateThemeAnalytics(state);
    }
  },

  extraReducers: (builder) => {
    builder
      .addCase(cacheThemeData.pending, (state, action) => {
        const { themeId, dataType } = action.meta.arg;
        const cacheKey = `theme:${themeId}:${dataType}`;
        state.loading[cacheKey] = true;
        delete state.errors[cacheKey];
      })
      .addCase(cacheThemeData.fulfilled, (state, action) => {
        const { key } = action.payload;
        delete state.loading[key];
      })
      .addCase(cacheThemeData.rejected, (state, action) => {
        const { themeId, dataType } = action.meta.arg;
        const cacheKey = `theme:${themeId}:${dataType}`;
        delete state.loading[cacheKey];
        state.errors[cacheKey] = action.error.message;
      })
      .addCase(getThemeData.fulfilled, (state, action) => {
        // Data retrieval completed
      })
      .addCase(invalidateThemeCache.fulfilled, (state, action) => {
        updateThemeAnalytics(state);
      });
  }
});

function updateThemeAnalytics(state) {
  const uniqueThemes = new Set();
  
  Object.values(state.entries).forEach(entry => {
    if (entry.themeId) {
      uniqueThemes.add(entry.themeId);
    }
  });
  
  state.analytics.themeCount = uniqueThemes.size;
}

function applyThemeEvictionStrategy(entries, strategy, maxEntries) {
  const entryCount = Object.keys(entries).length;
  if (entryCount <= maxEntries) return entries;

  switch (strategy) {
    case 'theme-priority': {
      // Prioritize built-in themes over custom themes
      const builtInThemes = ['light', 'dark', 'auto'];
      const sortedEntries = Object.entries(entries)
        .sort(([, a], [, b]) => {
          const aBuiltIn = builtInThemes.includes(a.themeId);
          const bBuiltIn = builtInThemes.includes(b.themeId);
          
          if (aBuiltIn && !bBuiltIn) return -1;
          if (!aBuiltIn && bBuiltIn) return 1;
          
          // If both are built-in or both are custom, sort by last accessed
          return (b.lastAccessed || 0) - (a.lastAccessed || 0);
        });

      const newEntries = {};
      sortedEntries.slice(0, maxEntries).forEach(([key, entry]) => {
        newEntries[key] = entry;
      });

      return newEntries;
    }

    case 'data-type-priority': {
      // Prioritize certain data types
      const priorityTypes = ['config', 'variables', 'assets'];
      const sortedEntries = Object.entries(entries)
        .sort(([, a], [, b]) => {
          const aPriority = priorityTypes.indexOf(a.dataType) !== -1 ? 10 : (a.priority || 1);
          const bPriority = priorityTypes.indexOf(b.dataType) !== -1 ? 10 : (b.priority || 1);
          return bPriority - aPriority;
        });

      const newEntries = {};
      sortedEntries.slice(0, maxEntries).forEach(([key, entry]) => {
        newEntries[key] = entry;
      });

      return newEntries;
    }

    case 'persistent-first': {
      // Keep persistent entries first
      const sortedEntries = Object.entries(entries)
        .sort(([, a], [, b]) => {
          if (a.persistent && !b.persistent) return -1;
          if (!a.persistent && b.persistent) return 1;
          return (b.lastAccessed || 0) - (a.lastAccessed || 0);
        });

      const newEntries = {};
      sortedEntries.slice(0, maxEntries).forEach(([key, entry]) => {
        newEntries[key] = entry;
      });

      return newEntries;
    }

    default:
      return applyThemeEvictionStrategy(entries, 'theme-priority', maxEntries);
  }
}

// Selectors
export const selectThemeCacheEntry = (state, themeId, dataType) => {
  const key = `theme:${themeId}:${dataType}`;
  return state.themeCache.entries[key];
};

export const selectThemeCacheEntries = (state) => state.themeCache.entries;
export const selectThemeCacheAnalytics = (state) => state.themeCache.analytics;
export const selectThemeCacheConfig = (state) => state.themeCache.config;
export const selectThemeCacheLoading = (state, themeId, dataType) => {
  const key = `theme:${themeId}:${dataType}`;
  return state.themeCache.loading[key] || false;
};
export const selectThemeCacheError = (state, themeId, dataType) => {
  const key = `theme:${themeId}:${dataType}`;
  return state.themeCache.errors[key];
};

export const selectThemeData = (state, themeId) => {
  return Object.entries(state.themeCache.entries)
    .filter(([, entry]) => entry.themeId === themeId)
    .reduce((acc, [key, entry]) => {
      acc[entry.dataType] = entry;
      return acc;
    }, {});
};

export const selectThemeDataTypeStats = (state) => state.themeCache.analytics.dataTypeStats;

export const {
  set,
  delete: deleteEntry,
  clear,
  updateLastAccessed,
  recordHit,
  recordMiss,
  cleanupExpired,
  cleanupTheme,
  updateConfig,
  setLoading,
  setError,
  importEntries
} = themeCacheSlice.actions;

export default themeCacheSlice.reducer;
