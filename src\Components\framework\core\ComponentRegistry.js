

class ComponentRegistry {
  constructor() {
    this.components = new Map();
    this.plugins = new Map();
    this.hooks = new Map();
    this.filters = new Map();
    this.middleware = [];
    this.initialized = false;
  }

  init() {
    if (this.initialized) return;
    
    this.registerDefaultComponents();
    this.registerDefaultHooks();
    this.initialized = true;
    
    console.log('ComponentRegistry initialized with', this.components.size, 'components');
  }

  register(name, config) {
    if (!name || !config) {
      throw new Error('Component name and config are required');
    }

    if (!config.component) {
      throw new Error('Component config must include a component property');
    }

    const componentConfig = {
      name,
      component: config.component,
      category: config.category || 'general',
      description: config.description || '',
      props: config.props || {},
      defaultProps: config.defaultProps || {},
      validation: config.validation || {},
      dependencies: config.dependencies || [],
      version: config.version || '1.0.0',
      author: config.author || 'Unknown',
      tags: config.tags || [],
      icon: config.icon || 'pi pi-box',
      preview: config.preview || null,
      examples: config.examples || [],
      documentation: config.documentation || '',
      hooks: config.hooks || {},
      filters: config.filters || {},
      middleware: config.middleware || [],
      isPlugin: config.isPlugin || false,
      pluginId: config.pluginId || null,
      registeredAt: new Date().toISOString()
    };

    this.validateDependencies(componentConfig.dependencies);

    this.registerComponentHooks(name, componentConfig.hooks);
    this.registerComponentFilters(name, componentConfig.filters);

    if (componentConfig.middleware.length > 0) {
      this.middleware.push(...componentConfig.middleware);
    }

    this.components.set(name, componentConfig);
    
    this.emit('component:registered', { name, config: componentConfig });
    
    console.log(`Component '${name}' registered successfully`);
    return componentConfig;
  }

  unregister(name) {
    if (!this.components.has(name)) {
      console.warn(`Component '${name}' not found in registry`);
      return false;
    }

    const config = this.components.get(name);
    
    this.unregisterComponentHooks(name);
    this.unregisterComponentFilters(name);
    
    this.middleware = this.middleware.filter(m => m.componentName !== name);
    
    this.components.delete(name);
    
    this.emit('component:unregistered', { name, config });
    
    console.log(`Component '${name}' unregistered successfully`);
    return true;
  }

  get(name) {
    return this.components.get(name) || null;
  }

  getAll(category = null) {
    const components = Array.from(this.components.values());
    
    if (category) {
      return components.filter(comp => comp.category === category);
    }
    
    return components;
  }

  getByTags(tags) {
    if (!Array.isArray(tags)) {
      tags = [tags];
    }
    
    return Array.from(this.components.values()).filter(comp => 
      tags.some(tag => comp.tags.includes(tag))
    );
  }

  search(query) {
    const searchTerm = query.toLowerCase();
    
    return Array.from(this.components.values()).filter(comp => 
      comp.name.toLowerCase().includes(searchTerm) ||
      comp.description.toLowerCase().includes(searchTerm) ||
      comp.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    );
  }

  has(name) {
    return this.components.has(name);
  }

  getCategories() {
    const categories = new Set();
    this.components.forEach(comp => categories.add(comp.category));
    return Array.from(categories).sort();
  }

  registerPlugin(pluginId, plugin) {
    if (!pluginId || !plugin) {
      throw new Error('Plugin ID and configuration are required');
    }

    const pluginConfig = {
      id: pluginId,
      name: plugin.name || pluginId,
      version: plugin.version || '1.0.0',
      description: plugin.description || '',
      author: plugin.author || 'Unknown',
      components: plugin.components || [],
      hooks: plugin.hooks || {},
      filters: plugin.filters || {},
      init: plugin.init || (() => {}),
      destroy: plugin.destroy || (() => {}),
      dependencies: plugin.dependencies || [],
      registeredAt: new Date().toISOString()
    };

    this.validateDependencies(pluginConfig.dependencies);

    pluginConfig.components.forEach(comp => {
      this.register(comp.name, {
        ...comp,
        isPlugin: true,
        pluginId: pluginId
      });
    });

    this.registerComponentHooks(pluginId, pluginConfig.hooks);
    this.registerComponentFilters(pluginId, pluginConfig.filters);

    this.plugins.set(pluginId, pluginConfig);

    try {
      pluginConfig.init();
      console.log(`Plugin '${pluginId}' registered and initialized successfully`);
    } catch (error) {
      console.error(`Error initializing plugin '${pluginId}':`, error);
    }

    this.emit('plugin:registered', { pluginId, config: pluginConfig });

    return pluginConfig;
  }

  unregisterPlugin(pluginId) {
    if (!this.plugins.has(pluginId)) {
      console.warn(`Plugin '${pluginId}' not found in registry`);
      return false;
    }

    const plugin = this.plugins.get(pluginId);

    try {
      plugin.destroy();
    } catch (error) {
      console.error(`Error destroying plugin '${pluginId}':`, error);
    }

    Array.from(this.components.values())
      .filter(comp => comp.pluginId === pluginId)
      .forEach(comp => this.unregister(comp.name));

    this.unregisterComponentHooks(pluginId);
    this.unregisterComponentFilters(pluginId);

    this.plugins.delete(pluginId);

    this.emit('plugin:unregistered', { pluginId, config: plugin });

    console.log(`Plugin '${pluginId}' unregistered successfully`);
    return true;
  }

  validateDependencies(dependencies) {
    const missing = dependencies.filter(dep => !this.components.has(dep));
    if (missing.length > 0) {
      console.warn('Missing dependencies:', missing);
    }
  }

  registerComponentHooks(componentName, hooks) {
    Object.entries(hooks).forEach(([hookName, handler]) => {
      if (!this.hooks.has(hookName)) {
        this.hooks.set(hookName, []);
      }
      this.hooks.get(hookName).push({ componentName, handler });
    });
  }

  unregisterComponentHooks(componentName) {
    this.hooks.forEach((handlers, hookName) => {
      this.hooks.set(hookName, handlers.filter(h => h.componentName !== componentName));
    });
  }

  registerComponentFilters(componentName, filters) {
    Object.entries(filters).forEach(([filterName, handler]) => {
      if (!this.filters.has(filterName)) {
        this.filters.set(filterName, []);
      }
      this.filters.get(filterName).push({ componentName, handler });
    });
  }

  unregisterComponentFilters(componentName) {
    this.filters.forEach((handlers, filterName) => {
      this.filters.set(filterName, handlers.filter(h => h.componentName !== filterName));
    });
  }

  emit(hookName, data) {
    if (this.hooks.has(hookName)) {
      this.hooks.get(hookName).forEach(({ handler }) => {
        try {
          handler(data);
        } catch (error) {
          console.error(`Error executing hook '${hookName}':`, error);
        }
      });
    }
  }

  applyFilters(filterName, value, context = {}) {
    if (!this.filters.has(filterName)) {
      return value;
    }

    return this.filters.get(filterName).reduce((filteredValue, { handler }) => {
      try {
        return handler(filteredValue, context);
      } catch (error) {
        console.error(`Error applying filter '${filterName}':`, error);
        return filteredValue;
      }
    }, value);
  }

  registerDefaultComponents() {
    try {
      const { registerDefaultComponents } = require('./DefaultComponents');
      registerDefaultComponents();
      console.log('Default components registered successfully');
    } catch (error) {
      console.warn('Failed to register default components:', error.message);
    }
  }

  registerDefaultHooks() {
    this.hooks.set('component:beforeRender', []);
    this.hooks.set('component:afterRender', []);
    this.hooks.set('component:beforeMount', []);
    this.hooks.set('component:afterMount', []);
    this.hooks.set('component:beforeUnmount', []);
    this.hooks.set('component:afterUnmount', []);
    this.hooks.set('form:beforeSubmit', []);
    this.hooks.set('form:afterSubmit', []);
    this.hooks.set('form:beforeValidation', []);
    this.hooks.set('form:afterValidation', []);
  }

  getStats() {
    const categories = this.getCategories();
    const plugins = Array.from(this.plugins.values());
    
    return {
      totalComponents: this.components.size,
      totalPlugins: this.plugins.size,
      categories: categories.length,
      categoryBreakdown: categories.reduce((acc, cat) => {
        acc[cat] = this.getAll(cat).length;
        return acc;
      }, {}),
      pluginComponents: Array.from(this.components.values()).filter(c => c.isPlugin).length,
      hooks: this.hooks.size,
      filters: this.filters.size,
      middleware: this.middleware.length
    };
  }

  export() {
    return {
      components: Array.from(this.components.entries()),
      plugins: Array.from(this.plugins.entries()),
      hooks: Array.from(this.hooks.entries()),
      filters: Array.from(this.filters.entries()),
      middleware: this.middleware,
      exportedAt: new Date().toISOString()
    };
  }

  import(data) {
    if (!data || typeof data !== 'object') {
      throw new Error('Invalid import data');
    }

    this.components.clear();
    this.plugins.clear();
    this.hooks.clear();
    this.filters.clear();
    this.middleware = [];

    if (data.components) {
      data.components.forEach(([name, config]) => {
        this.components.set(name, config);
      });
    }

    if (data.plugins) {
      data.plugins.forEach(([id, config]) => {
        this.plugins.set(id, config);
      });
    }

    if (data.hooks) {
      data.hooks.forEach(([name, handlers]) => {
        this.hooks.set(name, handlers);
      });
    }

    if (data.filters) {
      data.filters.forEach(([name, handlers]) => {
        this.filters.set(name, handlers);
      });
    }

    if (data.middleware) {
      this.middleware = [...data.middleware];
    }

    console.log('Registry imported successfully');
  }
}

const componentRegistry = new ComponentRegistry();

export default componentRegistry;
