import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Rating } from 'primereact/rating';

const DynamicRating = ({
  value = null,
  onChange,
  onBlur,
  onFocus,
  disabled = false,
  readOnly = false,
  stars = 5,
  cancel = true,
  iconOnClass = 'pi pi-star-fill',
  iconOffClass = 'pi pi-star',
  iconCancelClass = 'pi pi-ban',
  className = '',
  style = {},
  id,
  name,
  label,
  tooltip,
  size = 'normal', 
  variant = 'default',
  onEvent,
  debug = false,
  ...props
}) => {
  const [focused, setFocused] = useState(false);

  const handleChange = useCallback((e) => {
    const newValue = e.value;
    
    if (debug) {
      console.log('DynamicRating change:', { name, value: newValue });
    }

    if (onChange) {
      onChange(newValue, e);
    }

    if (onEvent) {
      onEvent('change', { value: newValue, name, event: e });
    }
  }, [onChange, onEvent, name, debug]);

  const handleFocus = useCallback((e) => {
    setFocused(true);

    if (debug) {
      console.log('DynamicRating focus:', { name });
    }

    if (onFocus) {
      onFocus(e);
    }

    if (onEvent) {
      onEvent('focus', { name, event: e });
    }
  }, [onFocus, onEvent, name, debug]);

  const handleBlur = useCallback((e) => {
    setFocused(false);

    if (debug) {
      console.log('DynamicRating blur:', { name, value });
    }

    if (onBlur) {
      onBlur(e);
    }

    if (onEvent) {
      onEvent('blur', { name, value, event: e });
    }
  }, [onBlur, onEvent, name, value, debug]);

  // Build class names
  const ratingClasses = [
    'dynamic-rating',
    `dynamic-rating--${size}`,
    `dynamic-rating--${variant}`,
    focused && 'dynamic-rating--focused',
    disabled && 'dynamic-rating--disabled',
    readOnly && 'dynamic-rating--readonly',
    className
  ].filter(Boolean).join(' ');

  // Rating props
  const ratingProps = {
    id,
    name,
    value,
    onChange: handleChange,
    onFocus: handleFocus,
    onBlur: handleBlur,
    disabled,
    readOnly,
    stars,
    cancel,
    iconOnClass,
    iconOffClass,
    iconCancelClass,
    className: ratingClasses,
    style,
    tooltip,
    'data-component-type': 'rating',
    'data-rating-size': size,
    'data-rating-variant': variant,
    ...props
  };

  // Remove undefined props
  Object.keys(ratingProps).forEach(key => {
    if (ratingProps[key] === undefined) {
      delete ratingProps[key];
    }
  });

  if (debug) {
    console.log('DynamicRating render:', {
      name,
      value,
      stars,
      cancel,
      disabled,
      readOnly
    });
  }

  // Render with label if provided
  if (label) {
    return (
      <div className="dynamic-rating-wrapper">
        <label htmlFor={id} className="dynamic-rating-label">
          {label}
        </label>
        <Rating {...ratingProps} />
      </div>
    );
  }

  return <Rating {...ratingProps} />;
};

DynamicRating.propTypes = {
  value: PropTypes.number,
  onChange: PropTypes.func,
  onBlur: PropTypes.func,
  onFocus: PropTypes.func,
  disabled: PropTypes.bool,
  readOnly: PropTypes.bool,
  stars: PropTypes.number,
  cancel: PropTypes.bool,
  iconOnClass: PropTypes.string,
  iconOffClass: PropTypes.string,
  iconCancelClass: PropTypes.string,
  className: PropTypes.string,
  style: PropTypes.object,
  id: PropTypes.string,
  name: PropTypes.string,
  label: PropTypes.string,
  tooltip: PropTypes.string,
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  variant: PropTypes.string,
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

DynamicRating.defaultProps = {
  value: null,
  disabled: false,
  readOnly: false,
  stars: 5,
  cancel: true,
  iconOnClass: 'pi pi-star-fill',
  iconOffClass: 'pi pi-star',
  iconCancelClass: 'pi pi-ban',
  className: '',
  style: {},
  size: 'normal',
  variant: 'default',
  debug: false
};

export default DynamicRating;

