import BaseController from './BaseController.js';
import CacheService from '../services/CacheService.js';
import { getCacheAnalytics, resetCacheAnalytics } from '../../store/middleware/analyticsMiddleware.js';
import { getCacheStorageStats, clearAllPersistedCaches } from '../../store/middleware/persistenceMiddleware.js';

class CacheController extends BaseController {
  constructor() {
    super();
    
    this.state = {
      isInitialized: false,
      cacheStats: null,
      analytics: null,
      storageStats: null,
      monitoringEnabled: true,
      autoCleanupEnabled: true,
      cleanupInterval: null,
      lastCleanup: null,
      cacheStrategies: [],
      activeCaches: new Set(),
      errors: {}
    };
    
    this.cleanupIntervalMs = 5 * 60 * 1000; 
    this.monitoringIntervalMs = 30 * 1000; 
    this.monitoringInterval = null;
  }

  async initialize() {
    try {
      this.setState({ loading: true });
      
      if (!CacheService.store) {
        throw new Error('CacheService not initialized with store');
      }
      
      const strategies = CacheService.getStrategies();
      this.setState({ cacheStrategies: strategies });
      
      if (this.state.monitoringEnabled) {
        this.startMonitoring();
      }
      
      if (this.state.autoCleanupEnabled) {
        this.startAutoCleanup();
      }
      
      await this.refreshStats();
      
      this.setState({ 
        isInitialized: true,
        loading: false 
      });
      
      this.notifyListeners('initialized', { controller: 'CacheController' });
      
    } catch (error) {
      this.setState({ 
        error: error.message,
        loading: false 
      });
      throw error;
    }
  }

  async get(cacheType, key, options = {}) {
    try {
      this.setState({ loading: true });
      
      const data = await CacheService.get(cacheType, key, options);
      
      this.setState({ loading: false });
      this.notifyListeners('get', { cacheType, key, data, found: !!data });
      
      return data;
    } catch (error) {
      this.setState({ 
        error: error.message,
        loading: false 
      });
      throw error;
    }
  }

  async set(cacheType, key, data, options = {}) {
    try {
      this.setState({ loading: true });
      
      const success = await CacheService.set(cacheType, key, data, options);
      
      if (success) {
        this.state.activeCaches.add(cacheType);
      }
      
      this.setState({ loading: false });
      this.notifyListeners('set', { cacheType, key, success });
      
      return success;
    } catch (error) {
      this.setState({ 
        error: error.message,
        loading: false 
      });
      throw error;
    }
  }

  async delete(cacheType, key) {
    try {
      this.setState({ loading: true });
      
      const success = await CacheService.delete(cacheType, key);
      
      this.setState({ loading: false });
      this.notifyListeners('delete', { cacheType, key, success });
      
      return success;
    } catch (error) {
      this.setState({ 
        error: error.message,
        loading: false 
      });
      throw error;
    }
  }

  async clear(cacheType = null) {
    try {
      this.setState({ loading: true });
      
      const success = await CacheService.clear(cacheType);
      
      if (success && !cacheType) {
        this.state.activeCaches.clear();
      } else if (success && cacheType) {
        this.state.activeCaches.delete(cacheType);
      }
      
      this.setState({ loading: false });
      this.notifyListeners('clear', { cacheType, success });
      
      return success;
    } catch (error) {
      this.setState({ 
        error: error.message,
        loading: false 
      });
      throw error;
    }
  }

  async invalidateByTags(tags, cacheType = null) {
    try {
      this.setState({ loading: true });
      
      const success = await CacheService.invalidateByTags(tags, cacheType);
      
      this.setState({ loading: false });
      this.notifyListeners('invalidate', { tags, cacheType, success });
      
      return success;
    } catch (error) {
      this.setState({ 
        error: error.message,
        loading: false 
      });
      throw error;
    }
  }

  async applyStrategy(cacheType, strategyName, maxEntries) {
    try {
      this.setState({ loading: true });
      
      const success = CacheService.applyStrategy(cacheType, strategyName, maxEntries);
      
      this.setState({ loading: false });
      this.notifyListeners('strategyApplied', { cacheType, strategyName, maxEntries, success });
      
      return success;
    } catch (error) {
      this.setState({ 
        error: error.message,
        loading: false 
      });
      throw error;
    }
  }

  getCacheStats(cacheType = null) {
    return CacheService.getStats(cacheType);
  }

  getAnalytics() {
    return getCacheAnalytics();
  }

  async getStorageStats() {
    return await getCacheStorageStats();
  }

  async refreshStats() {
    try {
      const [cacheStats, analytics, storageStats] = await Promise.all([
        this.getCacheStats(),
        this.getAnalytics(),
        this.getStorageStats()
      ]);
      
      this.setState({
        cacheStats,
        analytics,
        storageStats
      });
      
      this.notifyListeners('statsRefreshed', { cacheStats, analytics, storageStats });
      
      return { cacheStats, analytics, storageStats };
    } catch (error) {
      console.error('Failed to refresh cache stats:', error);
      this.setState({ error: error.message });
      throw error;
    }
  }

  startMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    
    this.monitoringInterval = setInterval(async () => {
      try {
        await this.refreshStats();
        
        this.checkCacheHealth();
      } catch (error) {
        console.error('Cache monitoring error:', error);
      }
    }, this.monitoringIntervalMs);
    
    this.setState({ monitoringEnabled: true });
    this.notifyListeners('monitoringStarted', { interval: this.monitoringIntervalMs });
  }

  stopMonitoring() {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    this.setState({ monitoringEnabled: false });
    this.notifyListeners('monitoringStopped');
  }

  startAutoCleanup() {
    if (this.state.cleanupInterval) {
      clearInterval(this.state.cleanupInterval);
    }
    
    const cleanupInterval = setInterval(async () => {
      try {
        await this.performCleanup();
      } catch (error) {
        console.error('Auto cleanup error:', error);
      }
    }, this.cleanupIntervalMs);
    
    this.setState({ 
      autoCleanupEnabled: true,
      cleanupInterval 
    });
    
    this.notifyListeners('autoCleanupStarted', { interval: this.cleanupIntervalMs });
  }

  stopAutoCleanup() {
    if (this.state.cleanupInterval) {
      clearInterval(this.state.cleanupInterval);
      this.setState({ cleanupInterval: null });
    }
    
    this.setState({ autoCleanupEnabled: false });
    this.notifyListeners('autoCleanupStopped');
  }

  async performCleanup() {
    try {
      const cacheTypes = ['cache', 'userCache', 'apiCache', 'themeCache', 'i18nCache'];
      const cleanupResults = {};
      
      for (const cacheType of cacheTypes) {
        const beforeStats = this.getCacheStats(cacheType);
        
        await CacheService.store.dispatch({
          type: `${cacheType}/cleanupExpired`
        });
        
        const afterStats = this.getCacheStats(cacheType);
        
        cleanupResults[cacheType] = {
          before: beforeStats?.entryCount || 0,
          after: afterStats?.entryCount || 0,
          removed: (beforeStats?.entryCount || 0) - (afterStats?.entryCount || 0)
        };
      }
      
      this.setState({ lastCleanup: Date.now() });
      this.notifyListeners('cleanupCompleted', { results: cleanupResults });
      
      return cleanupResults;
    } catch (error) {
      console.error('Cache cleanup failed:', error);
      throw error;
    }
  }

  checkCacheHealth() {
    const analytics = this.state.analytics;
    if (!analytics) return;
    
    const issues = [];
    
    if (analytics.summary.hitRate < 70) {
      issues.push({
        type: 'performance',
        severity: 'warning',
        message: `Low cache hit rate: ${analytics.summary.hitRate.toFixed(1)}%`
      });
    }
    
    if (analytics.summary.errorRate > 5) {
      issues.push({
        type: 'reliability',
        severity: 'error',
        message: `High error rate: ${analytics.summary.errorRate.toFixed(1)}%`
      });
    }
    
    const storageStats = this.state.storageStats;
    if (storageStats && storageStats.totalSize > 50 * 1024 * 1024) { 
      issues.push({
        type: 'memory',
        severity: 'warning',
        message: `High memory usage: ${(storageStats.totalSize / 1024 / 1024).toFixed(1)}MB`
      });
    }
    
    if (issues.length > 0) {
      this.notifyListeners('healthIssues', { issues });
    }
  }

  async exportCache(cacheType = null) {
    try {
      const exportData = CacheService.exportCache(cacheType);
      
      this.notifyListeners('cacheExported', { cacheType, size: JSON.stringify(exportData).length });
      
      return exportData;
    } catch (error) {
      this.setState({ error: error.message });
      throw error;
    }
  }

  async importCache(cacheData, cacheType = null) {
    try {
      this.setState({ loading: true });
      
      const success = await CacheService.importCache(cacheData, cacheType);
      
      this.setState({ loading: false });
      this.notifyListeners('cacheImported', { cacheType, success });
      
      return success;
    } catch (error) {
      this.setState({ 
        error: error.message,
        loading: false 
      });
      throw error;
    }
  }

  resetAnalytics() {
    resetCacheAnalytics();
    this.notifyListeners('analyticsReset');
  }

  clearPersistedCaches() {
    clearAllPersistedCaches();
    this.notifyListeners('persistedCachesCleared');
  }

  getCacheConfig() {
    return {
      cleanupInterval: this.cleanupIntervalMs,
      monitoringInterval: this.monitoringIntervalMs,
      monitoringEnabled: this.state.monitoringEnabled,
      autoCleanupEnabled: this.state.autoCleanupEnabled,
      strategies: this.state.cacheStrategies,
      activeCaches: Array.from(this.state.activeCaches)
    };
  }

  updateConfig(config) {
    if (config.cleanupInterval) {
      this.cleanupIntervalMs = config.cleanupInterval;
      if (this.state.autoCleanupEnabled) {
        this.startAutoCleanup(); 
      }
    }
    
    if (config.monitoringInterval) {
      this.monitoringIntervalMs = config.monitoringInterval;
      if (this.state.monitoringEnabled) {
        this.startMonitoring(); 
      }
    }
    
    if (typeof config.monitoringEnabled === 'boolean') {
      if (config.monitoringEnabled) {
        this.startMonitoring();
      } else {
        this.stopMonitoring();
      }
    }
    
    if (typeof config.autoCleanupEnabled === 'boolean') {
      if (config.autoCleanupEnabled) {
        this.startAutoCleanup();
      } else {
        this.stopAutoCleanup();
      }
    }
    
    this.notifyListeners('configUpdated', { config });
  }

  destroy() {
    this.stopMonitoring();
    this.stopAutoCleanup();
    super.destroy();
  }
}

const cacheController = new CacheController();

export default cacheController;
