import CryptoJS from 'crypto-js';
import DOMPurify from 'dompurify';

class SecurityService {
  constructor() {
    this.encryptionKey = import.meta.env?.VITE_ENCRYPTION_KEY || 'default-dev-key-change-in-production';
    this.csrfTokens = new Map();
    this.rateLimitMap = new Map();
    this.initializeSecurity();
  }

  initializeSecurity() {
    DOMPurify.addHook('uponSanitizeElement', (node, data) => {
      if (data.tagName === 'script' || data.tagName === 'iframe' || data.tagName === 'object') {
        return false;
      }
    });

    DOMPurify.addHook('uponSanitizeAttribute', (node, data) => {
      const dangerousAttrs = ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur'];
      if (dangerousAttrs.includes(data.attrName.toLowerCase())) {
        return false;
      }

      if ((data.attrName === 'href' || data.attrName === 'src') &&
          data.attrValue && data.attrValue.toLowerCase().startsWith('javascript:')) {
        return false;
      }
    });
  }

  sanitizeHTML(html, options = {}) {
    const defaultOptions = {
      ALLOWED_TAGS: ['p', 'br', 'strong', 'em', 'ul', 'ol', 'li', 'a', 'span', 'div'],
      ALLOWED_ATTR: ['href', 'target', 'rel', 'class', 'id'],
      FORBID_TAGS: ['script', 'style', 'iframe', 'frame', 'object', 'embed'],
      FORBID_ATTR: ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur', 'style']
    };

    const sanitizeOptions = { ...defaultOptions, ...options };
    return DOMPurify.sanitize(html, sanitizeOptions);
  }
 
  validateInput(input, rules) {
    const defaultRules = {
      maxLength: 255,
      allowedChars: /^[a-zA-Z0-9\s\-_@.]+$/,
      preventXSS: true
    };

    const validationRules = { ...defaultRules, ...rules };

    if (input.length > validationRules.maxLength) {
      throw new Error('Input too long');
    }

    if (validationRules.preventXSS && /<script|javascript:|on\w+=/i.test(input)) {
      throw new Error('XSS attempt detected');
    }

    if (validationRules.allowedChars && !validationRules.allowedChars.test(input)) {
      throw new Error('Invalid characters detected');
    }

    return true;
  }

  encryptData(data) {
    try {
      return CryptoJS.AES.encrypt(JSON.stringify(data), this.encryptionKey).toString();
    } catch (error) {
      console.error('Encryption failed:', error);
      return null;
    }
  }

  decryptData(encryptedData) {
    try {
      const bytes = CryptoJS.AES.decrypt(encryptedData, this.encryptionKey);
      return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    } catch (error) {
      console.error('Decryption failed:', error);
      return null;
    }
  }

  generateCSRFToken(sessionId = 'default') {
    const token = CryptoJS.SHA256(Date.now() + Math.random().toString() + sessionId).toString();
    const expiry = Date.now() + (30 * 60 * 1000); 

    this.csrfTokens.set(token, { sessionId, expiry });

    this.cleanupExpiredTokens();

    return token;
  }

  validateCSRFToken(token, sessionId = 'default') {
    const tokenData = this.csrfTokens.get(token);

    if (!tokenData) {
      return false;
    }

    if (Date.now() > tokenData.expiry) {
      this.csrfTokens.delete(token);
      return false;
    }

    if (tokenData.sessionId !== sessionId) {
      return false;
    }

    return true;
  }

  cleanupExpiredTokens() {
    const now = Date.now();
    for (const [token, data] of this.csrfTokens.entries()) {
      if (now > data.expiry) {
        this.csrfTokens.delete(token);
      }
    }
  }

  checkRateLimit(identifier, maxRequests = 100, windowMs = 60000) {
    const now = Date.now();
    const windowStart = now - windowMs;

    if (!this.rateLimitMap.has(identifier)) {
      this.rateLimitMap.set(identifier, []);
    }

    const requests = this.rateLimitMap.get(identifier);

    const validRequests = requests.filter(timestamp => timestamp > windowStart);

    if (validRequests.length >= maxRequests) {
      return false; 
    }

    validRequests.push(now);
    this.rateLimitMap.set(identifier, validRequests);

    return true;
  }

  generateSecureRandom(length = 32) {
    const array = new Uint8Array(length);
    if (window.crypto && window.crypto.getRandomValues) {
      window.crypto.getRandomValues(array);
    } else {
      for (let i = 0; i < length; i++) {
        array[i] = Math.floor(Math.random() * 256);
      }
    }
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }

  handleCSPViolation(violationReport) {
    console.warn('CSP Violation:', violationReport);

    const incident = {
      type: 'csp_violation',
      timestamp: new Date().toISOString(),
      blockedURI: violationReport.blockedURI,
      violatedDirective: violationReport.violatedDirective,
      originalPolicy: violationReport.originalPolicy,
      userAgent: navigator.userAgent
    };

    this.reportSecurityIncident(incident);
  }

  reportSecurityIncident(incident) {
    console.error('Security Incident:', incident);

    if (import.meta.env?.MODE === 'development') {
      const incidents = JSON.parse(localStorage.getItem('security_incidents') || '[]');
      incidents.push(incident);
      localStorage.setItem('security_incidents', JSON.stringify(incidents.slice(-100))); 
    }
  }

  validateSecureHeaders(response) {
    const requiredHeaders = [
      'x-content-type-options',
      'x-frame-options',
      'x-xss-protection',
      'strict-transport-security'
    ];

    const missingHeaders = requiredHeaders.filter(header =>
      !response.headers.has(header)
    );

    if (missingHeaders.length > 0) {
      this.reportSecurityIncident({
        type: 'missing_security_headers',
        timestamp: new Date().toISOString(),
        missingHeaders,
        url: response.url
      });
    }

    return missingHeaders.length === 0;
  }
}

export default new SecurityService();