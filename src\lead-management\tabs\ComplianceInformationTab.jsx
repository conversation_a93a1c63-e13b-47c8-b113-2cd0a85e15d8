import React, { useState } from 'react';
import { Card } from 'primereact/card';
import { Button } from 'primereact/button';
import { ProgressBar } from 'primereact/progressbar';
import { Tag } from 'primereact/tag';
import { Divider } from 'primereact/divider';
import { Panel } from 'primereact/panel';

const ComplianceInformationTab = ({ leadId }) => {
  const [complianceData] = useState({
    overallScore: 100,
    verificationStatus: 'Verified',
    lastVerified: '12/08/24',
    documents: [
      { name: 'KYC Document', status: 'Verified', date: '12/08/24' },
      { name: 'Tax ID Verification', status: 'Verified', date: '12/08/24' },
      { name: 'Business License', status: 'Verified', date: '12/08/24' }
    ],
    keyData: {
      matchScore: '100%',
      verified: 'Verified',
      keyData: 'Obtained'
    }
  });

  const getStatusSeverity = (status) => {
    switch (status) {
      case 'Verified':
        return 'success';
      case 'Pending':
        return 'warning';
      case 'Failed':
        return 'danger';
      default:
        return 'info';
    }
  };

  return (
    <div className="compliance-information-tab">
      <Card title="Compliance Information" className="">
        {/* Overall Compliance Score */}
        <div className="compliance-section">
          <h4>Overall Compliance Score</h4>
          <div className="score-display">
            <div className="score-circle">
              <span className="score-value">{complianceData.overallScore}%</span>
              <span className="score-label">Match Score</span>
            </div>
            <div className="score-details">
              <div className="score-item">
                <i className="pi pi-check-circle" style={{ color: '#4CAF50' }}></i>
                <span>Verified</span>
              </div>
              <div className="score-item">
                <i className="pi pi-calendar"></i>
                <span>Last Verified: {complianceData.lastVerified}</span>
              </div>
            </div>
          </div>
        </div>

        <Divider />

        {/* Document Verification Status */}
        <div className="compliance-section">
          <h4>Document Verification</h4>
          <div className="document-list">
            {complianceData.documents.map((doc, index) => (
              <div key={index} className="document-item">
                <div className="document-info">
                  <i className="pi pi-file" style={{ fontSize: '1.5rem', color: '#2196F3' }}></i>
                  <div className="document-details">
                    <div className="document-name">{doc.name}</div>
                    <div className="document-date">Verified on: {doc.date}</div>
                  </div>
                </div>
                <Tag value={doc.status} severity={getStatusSeverity(doc.status)} />
              </div>
            ))}
          </div>
        </div>

        <Divider />

        {/* Key Data Section */}
        <div className="compliance-section">
          <h4>Key Data</h4>
          <div className="key-data-grid">
            <div className="key-data-item">
              <label>Match Score</label>
              <div className="key-data-value">
                <ProgressBar value={100} showValue={false} style={{ height: '10px' }} />
                <span>{complianceData.keyData.matchScore}</span>
              </div>
            </div>
            <div className="key-data-item">
              <label>Verification Status</label>
              <Tag value={complianceData.keyData.verified} severity="success" />
            </div>
            <div className="key-data-item">
              <label>Key Data Status</label>
              <Tag value={complianceData.keyData.keyData} severity="info" />
            </div>
          </div>
        </div>

        <Divider />

        {/* Actions */}
        <div className="compliance-actions">
          <Button 
            label="Re-verify Documents" 
            icon="pi pi-refresh" 
            className="p-button-sm"
          />
          <Button 
            label="Upload Document" 
            icon="pi pi-upload" 
            className="p-button-sm p-button-success"
          />
          <Button 
            label="View Full Report" 
            icon="pi pi-file-pdf" 
            className="p-button-sm p-button-outlined"
          />
        </div>
      </Card>
    </div>
  );
};

export default ComplianceInformationTab;

