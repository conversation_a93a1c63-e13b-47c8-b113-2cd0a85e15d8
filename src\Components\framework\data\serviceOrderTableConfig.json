{"title": "Service Orders", "groupBy": {"field": "date", "actions": {"icon": "pi pi-cog"}}, "groupStatistics": [{"name": "Total", "type": "count"}, {"name": "Active", "type": "countIf", "condition": "row => ['TBD', 'NR'].includes(row.catering) || ['TBD', 'NR'].includes(row.hotels)"}, {"name": "Issues", "type": "countIf", "condition": "row => row.transport === 'NR' || row.permits === 'NR'"}, {"name": "Completed", "type": "countIf", "condition": "row => row.catering === 'Ok' && row.hotels === 'Ok' && row.transport === 'Ok'"}], "statusBar": {"enabled": true, "field": "statusBar", "idField": "id"}, "expansion": {"enabled": true, "tabs": [{"header": "Transport", "icon": "pi pi-car"}, {"header": "Hotels", "icon": "pi pi-building"}, {"header": "Catering", "icon": "pi pi-shopping-cart"}, {"header": "Customs", "icon": "pi pi-shield"}, {"header": "Migration", "icon": "pi pi-users"}, {"header": "Permits", "icon": "pi pi-file"}]}, "header": {"enabled": true, "search": {"enabled": true, "placeholder": "Search flights, tails, customers..."}, "actions": [{"name": "addServiceOrder", "icon": "pi pi-plus", "label": "Add Flight/Service Order", "className": "p-button-primary p-button-sm"}]}, "filters": [{"name": "date", "placeholder": "Today", "options": [{"label": "Today", "value": "today"}, {"label": "Tomorrow", "value": "tomorrow"}, {"label": "This Week", "value": "week"}, {"label": "This Month", "value": "month"}]}, {"name": "timezone", "placeholder": "UTC", "options": [{"label": "UTC", "value": "UTC"}, {"label": "Local", "value": "local"}, {"label": "EST", "value": "EST"}, {"label": "PST", "value": "PST"}]}], "columns": [{"field": "time", "header": "Time/Flight/Customer", "type": "composite", "className": "time-cell", "fields": [{"field": "time", "className": "time-main"}, {"field": "flightNumber", "className": "time-sub"}, {"field": "registration", "className": "time-detail"}], "style": {"minWidth": "150px"}}, {"field": "route", "header": "Route", "type": "route", "style": {"width": "120px"}}, {"field": "crew", "header": "Crew", "style": {"width": "70px"}}, {"field": "pax", "header": "Pax", "style": {"width": "70px"}}, {"field": "fao", "header": "FAO", "style": {"width": "80px"}}, {"field": "transport", "header": "Transport", "type": "status", "clickable": true, "tabIndex": 0, "tabName": "Transport", "style": {"width": "100px"}}, {"field": "hotels", "header": "Hotels", "type": "status", "clickable": true, "tabIndex": 1, "tabName": "Hotels", "style": {"width": "100px"}}, {"field": "catering", "header": "Catering", "type": "status", "clickable": true, "tabIndex": 2, "tabName": "Catering", "style": {"width": "100px"}}, {"field": "customs", "header": "Customs", "type": "status", "clickable": true, "tabIndex": 3, "tabName": "Customs", "style": {"width": "100px"}}, {"field": "migration", "header": "Migration", "type": "status", "clickable": true, "tabIndex": 4, "tabName": "Migration", "style": {"width": "100px"}}, {"field": "slotPPR", "header": "Slot PPR", "style": {"width": "100px"}}, {"field": "permits", "header": "Permits", "type": "status", "clickable": true, "tabIndex": 5, "tabName": "Permits", "style": {"width": "100px"}}, {"field": "fuel", "header": "FH, WB8", "type": "object", "className": "fuel-cell", "displayFields": [{"field": "price", "className": "fuel-price"}, {"field": "unit", "className": "fuel-unit"}], "style": {"width": "100px"}}, {"field": "tasks", "header": "Tasks", "style": {"width": "70px"}}], "actions": {"enabled": true, "buttons": [{"name": "approve", "icon": "pi pi-check", "className": "p-button-rounded p-button-text p-button-sm", "tooltip": "Approve"}, {"name": "copy", "icon": "pi pi-copy", "className": "p-button-rounded p-button-text p-button-sm", "tooltip": "Copy"}, {"name": "more", "icon": "pi pi-ellipsis-v", "className": "p-button-rounded p-button-text p-button-sm", "tooltip": "More options"}], "style": {"width": "120px"}}}