import React, { useState, useRef } from 'react';
import { Steps } from 'primereact/steps';
import { TabView, TabPanel } from 'primereact/tabview';
import { Button } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { InputTextarea } from 'primereact/inputtextarea';
import { Rating } from 'primereact/rating';
import { Toast } from 'primereact/toast';
import { Card } from 'primereact/card';
import './LeadManagement.css';

const LeadManagement = () => {
  const toast = useRef(null);
  const [activeStep, setActiveStep] = useState(0);
  const [activeTabIndex, setActiveTabIndex] = useState(0);

  // Form state
  const [formData, setFormData] = useState({
    company: 'Mahan Air',
    contact: '<PERSON>',
    email: '<EMAIL>',
    phone: '+98 21 4884 5000',
    rating: 2,
    opportunity: '1 Ton Jet Fuel',
    expectedRevenue: '$7000',
    priority: 'Medium',
    leadSource: 'Event',
    tags: '',
    leadStatus: 'Introduction',
    blacklistLead: 'No',
    reason: '',
    // Address fields
    streetAddress: '',
    city: '',
    stateProvince: '',
    zipPostalCode: '',
    country: null,
    addressType: 'Business'
  });

  // Wizard steps
  const steps = [
    { label: 'New Lead', icon: 'pi pi-user' },
    { label: 'Opportunity', icon: 'pi pi-briefcase' },
    { label: 'Won / Lost', icon: 'pi pi-check-circle' }
  ];

  // Dropdown options
  const priorityOptions = [
    { label: 'Low', value: 'Low' },
    { label: 'Medium', value: 'Medium' },
    { label: 'High', value: 'High' }
  ];

  const leadSourceOptions = [
    { label: 'Event', value: 'Event' },
    { label: 'Website', value: 'Website' },
    { label: 'Referral', value: 'Referral' },
    { label: 'Cold Call', value: 'Cold Call' }
  ];

  const leadStatusOptions = [
    { label: 'Introduction', value: 'Introduction' },
    { label: 'Qualification', value: 'Qualification' },
    { label: 'Proposal', value: 'Proposal' },
    { label: 'Negotiation', value: 'Negotiation' }
  ];

  const blacklistOptions = [
    { label: 'Yes', value: 'Yes' },
    { label: 'No', value: 'No' }
  ];

  const countryOptions = [
    { label: 'United States', value: 'US' },
    { label: 'United Kingdom', value: 'UK' },
    { label: 'Canada', value: 'CA' },
    { label: 'Iran', value: 'IR' },
    { label: 'UAE', value: 'AE' }
  ];

  const addressTypeOptions = [
    { label: 'Business', value: 'Business' },
    { label: 'Residential', value: 'Residential' },
    { label: 'Billing', value: 'Billing' },
    { label: 'Shipping', value: 'Shipping' }
  ];

  const salesPersonOptions = [
    { label: 'John Doe', value: 'John Doe' },
    { label: 'Jane Smith', value: 'Jane Smith' },
    { label: 'Mike Johnson', value: 'Mike Johnson' }
  ];

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleEnrichData = () => {
    toast.current.show({
      severity: 'info',
      summary: 'Enrich Data',
      detail: 'Enriching lead data...',
      life: 3000
    });
  };

  const handleCreateOpportunity = () => {
    toast.current.show({
      severity: 'success',
      summary: 'Opportunity Created',
      detail: 'Opportunity has been created successfully',
      life: 3000
    });
  };

  const handleMarkAsWon = () => {
    toast.current.show({
      severity: 'success',
      summary: 'Marked as Won',
      detail: 'Lead has been marked as won',
      life: 3000
    });
  };

  const handleMarkAsLost = () => {
    toast.current.show({
      severity: 'warn',
      summary: 'Marked as Lost',
      detail: 'Lead has been marked as lost',
      life: 3000
    });
  };

  const handleEdit = () => {
    toast.current.show({
      severity: 'info',
      summary: 'Edit Mode',
      detail: 'Editing lead information',
      life: 3000
    });
  };

  const handleNewAddress = () => {
    toast.current.show({
      severity: 'info',
      summary: 'New Address',
      detail: 'Adding new address',
      life: 3000
    });
  };

  return (
    <div className="lead-management">
      <Toast ref={toast} />
      
      {/* Header */}
      <div className="lead-header">
        <div className="lead-breadcrumb">
          <Button icon="pi pi-arrow-left" text className="p-button-sm" />
          <span className="breadcrumb-text">Pipeline / Mahan Air - ASK-000001</span>
        </div>
        <div className="company-selector">
          <label>Company</label>
          <Dropdown 
            value="Aviation Services Management, Dubai" 
            options={[{ label: 'Aviation Services Management, Dubai', value: 'Aviation Services Management, Dubai' }]}
            className="company-dropdown"
          />
        </div>
      </div>

      {/* Wizard Steps */}
      <div className="wizard-steps">
        <Steps 
          model={steps} 
          activeIndex={activeStep} 
          onSelect={(e) => setActiveStep(e.index)}
          readOnly={false}
        />
      </div>

      {/* Action Buttons */}
      <div className="action-buttons">
        <Button label="Edit" icon="pi pi-pencil" className="p-button-sm" onClick={handleEdit} />
        <Button label="Enrich Data" icon="pi pi-database" className="p-button-sm" onClick={handleEnrichData} />
        <Button label="Create Opportunity" icon="pi pi-plus" className="p-button-sm p-button-success" onClick={handleCreateOpportunity} />
        <Button label="Mark as Won" icon="pi pi-check" className="p-button-sm p-button-success" onClick={handleMarkAsWon} />
        <Button label="Mark as Lost" icon="pi pi-times" className="p-button-sm p-button-danger" onClick={handleMarkAsLost} />
        <div className="activities-section">
          <i className="pi pi-clock"></i>
          <span>Activities</span>
          <span className="activity-count">3/5</span>
        </div>
        <Button icon="pi pi-trash" className="p-button-sm p-button-danger p-button-text" />
      </div>

      {/* Main Content */}
      <div className="lead-content">
        <Card title="General" className="general-section">
          <div className="form-grid">
            {/* Left Column */}
            <div className="form-column">
              <div className="field">
                <label htmlFor="company">Company</label>
                <InputText 
                  id="company" 
                  value={formData.company}
                  onChange={(e) => handleInputChange('company', e.target.value)}
                />
                <small className="field-hint">
                  (New prospect can be created by typing name only if not available for selection, later users can fill complete details)
                </small>
              </div>

              <div className="field">
                <label htmlFor="email">Email</label>
                <InputText 
                  id="email" 
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                />
              </div>

              <div className="field">
                <label htmlFor="phone">Phone</label>
                <InputText 
                  id="phone" 
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                />
              </div>

              <div className="field">
                <label htmlFor="ratings">Ratings</label>
                <Rating 
                  value={formData.rating} 
                  onChange={(e) => handleInputChange('rating', e.value)}
                  cancel={false}
                  stars={5}
                />
              </div>
            </div>

            {/* Right Column */}
            <div className="form-column">
              <div className="field">
                <label htmlFor="contact">Contact</label>
                <InputText 
                  id="contact" 
                  value={formData.contact}
                  onChange={(e) => handleInputChange('contact', e.target.value)}
                />
                <small className="field-hint">
                  (New Contact to be created by typing name only if not available for selection, later users can fill other details)
                </small>
              </div>

              <div className="field">
                <label htmlFor="priority">Priority</label>
                <Dropdown 
                  id="priority"
                  value={formData.priority}
                  options={priorityOptions}
                  onChange={(e) => handleInputChange('priority', e.value)}
                  placeholder="Select priority"
                />
              </div>

              <div className="field">
                <label htmlFor="leadSource">Lead Source</label>
                <Dropdown 
                  id="leadSource"
                  value={formData.leadSource}
                  options={leadSourceOptions}
                  onChange={(e) => handleInputChange('leadSource', e.value)}
                  placeholder="Select lead source"
                />
              </div>

              <div className="field">
                <label htmlFor="tags">Tags</label>
                <InputText 
                  id="tags" 
                  value={formData.tags}
                  onChange={(e) => handleInputChange('tags', e.target.value)}
                  placeholder="Enter tags..."
                />
                <small className="field-hint">
                  (New tags can be created if not available from this screen and add it to the master)
                </small>
              </div>
            </div>

            {/* Third Column */}
            <div className="form-column">
              <div className="field">
                <label htmlFor="opportunity">Opportunity</label>
                <InputText
                  id="opportunity"
                  value={formData.opportunity}
                  onChange={(e) => handleInputChange('opportunity', e.target.value)}
                />
                <small className="field-hint">
                  (To create an Opportunity from Lead, Prospect or Customer Details needs to be completed)
                </small>
              </div>

              <div className="field">
                <label htmlFor="expectedRevenue">Expected Revenue</label>
                <InputText
                  id="expectedRevenue"
                  value={formData.expectedRevenue}
                  onChange={(e) => handleInputChange('expectedRevenue', e.target.value)}
                />
                <small className="field-hint text-warning">
                  (The expected revenue value will be overridden based on an opportunity when created)
                </small>
              </div>

              <div className="field">
                <label htmlFor="salesPerson">Sales Person</label>
                <Dropdown
                  id="salesPerson"
                  value={formData.salesPerson}
                  options={salesPersonOptions}
                  onChange={(e) => handleInputChange('salesPerson', e.value)}
                  placeholder="Select sales person"
                />
              </div>

              <div className="field">
                <label htmlFor="leadStatus">Lead Status</label>
                <div className="lead-status-field">
                  <Button
                    icon="pi pi-chevron-left"
                    className="p-button-sm p-button-text"
                  />
                  <div className="status-badge">
                    {formData.leadStatus}
                  </div>
                  <Button
                    icon="pi pi-chevron-right"
                    className="p-button-sm p-button-text"
                  />
                </div>
                <small className="field-hint">
                  (The statuses can be inserted in the Lead status master, status will suggest next status values)
                </small>
              </div>

              <div className="field">
                <label htmlFor="blacklistLead">Blacklist Lead</label>
                <Dropdown
                  id="blacklistLead"
                  value={formData.blacklistLead}
                  options={blacklistOptions}
                  onChange={(e) => handleInputChange('blacklistLead', e.value)}
                  placeholder="Select option"
                />
              </div>

              <div className="field">
                <label htmlFor="reason">Reason</label>
                <InputTextarea
                  id="reason"
                  value={formData.reason}
                  onChange={(e) => handleInputChange('reason', e.target.value)}
                  placeholder="Enter reason..."
                  rows={3}
                />
                <small className="field-hint text-warning">
                  (Reason mandatory if blacklisted)
                </small>
              </div>
            </div>
          </div>
        </Card>

        {/* TabView Section */}
        <div className="tabs-section">
          <TabView activeIndex={activeTabIndex} onTabChange={(e) => setActiveTabIndex(e.index)}>
            <TabPanel header="Address">
              <Card title="Address Information" className="address-section">
                <div className="address-header">
                  <Button
                    label="New Address"
                    icon="pi pi-plus"
                    className="p-button-sm p-button-primary"
                    onClick={handleNewAddress}
                  />
                </div>

                <div className="address-form-grid">
                  <div className="address-row">
                    <div className="field">
                      <label htmlFor="streetAddress">Street Address</label>
                      <InputText
                        id="streetAddress"
                        value={formData.streetAddress}
                        onChange={(e) => handleInputChange('streetAddress', e.target.value)}
                        placeholder="Enter street address"
                      />
                    </div>

                    <div className="field">
                      <label htmlFor="city">City</label>
                      <InputText
                        id="city"
                        value={formData.city}
                        onChange={(e) => handleInputChange('city', e.target.value)}
                        placeholder="Enter city"
                      />
                    </div>
                  </div>

                  <div className="address-row">
                    <div className="field">
                      <label htmlFor="stateProvince">State/Province</label>
                      <InputText
                        id="stateProvince"
                        value={formData.stateProvince}
                        onChange={(e) => handleInputChange('stateProvince', e.target.value)}
                        placeholder="Enter state/province"
                      />
                    </div>

                    <div className="field">
                      <label htmlFor="zipPostalCode">ZIP/Postal Code</label>
                      <InputText
                        id="zipPostalCode"
                        value={formData.zipPostalCode}
                        onChange={(e) => handleInputChange('zipPostalCode', e.target.value)}
                        placeholder="Enter ZIP code"
                      />
                    </div>
                  </div>

                  <div className="address-row">
                    <div className="field">
                      <label htmlFor="country">Country</label>
                      <Dropdown
                        id="country"
                        value={formData.country}
                        options={countryOptions}
                        onChange={(e) => handleInputChange('country', e.value)}
                        placeholder="Select Country"
                      />
                    </div>

                    <div className="field">
                      <label htmlFor="addressType">Address Type</label>
                      <Dropdown
                        id="addressType"
                        value={formData.addressType}
                        options={addressTypeOptions}
                        onChange={(e) => handleInputChange('addressType', e.value)}
                        placeholder="Select address type"
                      />
                    </div>
                  </div>
                </div>
              </Card>
            </TabPanel>

            <TabPanel header="Contacts">
              <Card title="Contact Information">
                <p>Contact information will be displayed here.</p>
              </Card>
            </TabPanel>

            <TabPanel header="Compliance">
              <Card title="Compliance Information">
                <p>Compliance information will be displayed here.</p>
              </Card>
            </TabPanel>

            <TabPanel header="Notes & Messages">
              <Card title="Notes & Messages">
                <p>Notes and messages will be displayed here.</p>
              </Card>
            </TabPanel>

            <TabPanel header="Logs">
              <Card title="Activity Logs">
                <p>Activity logs will be displayed here.</p>
              </Card>
            </TabPanel>

            <TabPanel header="Documentation">
              <Card title="Documentation">
                <p>Documentation will be displayed here.</p>
              </Card>
            </TabPanel>
          </TabView>
        </div>
      </div>

      {/* Footer */}
      <div className="lead-footer">
        <span className="footer-info">
          <i className="pi pi-circle-fill" style={{ color: 'green', fontSize: '0.5rem' }}></i>
          Mark Stephen
        </span>
        <Button label="New Message" className="p-button-sm p-button-text" />
      </div>
    </div>
  );
};

export default LeadManagement;
