/* Theme Selector Styles */

/* Theme Selector Container */
.theme-selector {
  position: relative;
  margin-left: 1rem;
}

/* Dropdown Styling */
.theme-dropdown {
  min-width: 180px;
  border-radius: 6px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.theme-dropdown .p-dropdown-label {
  display: flex;
  align-items: center;
  padding: 0.5rem;
}

.theme-dropdown .p-dropdown-trigger {
  color: var(--text-color, #1e293b);
}

.theme-dropdown.p-focus {
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
  border-color: var(--accent-color, #3b82f6);
}

/* Theme Item in Dropdown */
.theme-item {
  display: flex;
  align-items: center;
  padding: 0.75rem 0.5rem;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.theme-item:hover {
  background-color: var(--table-row-hover, #f8fafc);
}

.p-dropdown-item.p-highlight .theme-item {
  background-color: rgba(59, 130, 246, 0.1);
}

.theme-color-sample {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  margin-right: 0.75rem;
  border: 1px solid rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.theme-item:hover .theme-color-sample {
  transform: scale(1.1);
}

.theme-item-text {
  display: flex;
  flex-direction: column;
}

.theme-item-name {
  font-weight: 500;
  margin-bottom: 0.25rem;
  color: var(--header-color, #0f172a);
}

.theme-item-description {
  font-size: 0.8rem;
  color: var(--subheader-color, #64748b);
  line-height: 1.2;
}

/* Selected Theme Value */
.theme-value {
  display: flex;
  align-items: center;
  padding: 0.25rem 0;
}

.theme-value .theme-color-sample {
  width: 20px;
  height: 20px;
  margin-right: 0.5rem;
}

.theme-value span {
  font-weight: 500;
  color: var(--text-color, #1e293b);
}

/* Theme Variables */
:root {
  /* These variables will be overridden by theme selection */
  /* Default values are set to match the current CSS structure */
  --app-background: linear-gradient(to bottom, #ffffff 0%, #fffbeb 50%, #fff8e1 100%);
  --content-background: #ffffff;
  --text-color: #1e293b;
  --card-background: #ffffff;
  --card-border: 1px solid #e2e8f0;
  --header-color: #0f172a;
  --subheader-color: #334155;
  --accent-color: #E2085D;
  --table-header-bg: #f0f5ff;
  --table-header-color: #667085;
  --table-row-bg: #ffffff;
  --table-row-color: #262626;
  --table-row-hover: #f8fafc;
  --table-border: #e4e7ec;
  --input-background: #ffffff;
  --input-border: #e4e7ec;
  --input-text: #262626;
  --button-primary-bg: #E2085D;
  --button-primary-text: #ffffff;
  --button-secondary-bg: #f1f5f9;
  --button-secondary-text: #334155;
  --shadow-color: rgba(0, 0, 0, 0.1);
}

/* Default theme - preserves current CSS structure */
.theme-default .app-container {
  background: linear-gradient(to bottom, #ffffff 0%, #fffbeb 50%, #fff8e1 100%);
}

/* Apply CSS Variables to Elements */

/* App Container - Only apply to non-default themes */
.theme-dark .app-container,
.theme-light-blue .app-container,
.theme-corporate .app-container,
.theme-nature .app-container,
.theme-purple .app-container {
  background: var(--app-background) !important;
}

/* Dynamic Theme - Special handling for background images */
.theme-dynamic .app-container {
  background-image: var(--dynamic-background-image) !important;
  background-size: cover !important;
  background-position: center !important;
  background-repeat: no-repeat !important;
  position: relative !important;
}

/* Add overlay to dynamic theme */
.theme-dynamic .app-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--app-background-overlay);
  z-index: 0;
}

/* Ensure content is above the overlay */
.theme-dynamic .content-container {
  position: relative;
  z-index: 1;
}

/* Content Container */
.content-container {
  background-color: var(--content-background);
  color: var(--text-color);
}

/* Page Content */
.page-content {
  background-color: var(--card-background);
  border: var(--card-border);
  color: var(--text-color);
}

/* Headers */
h1, h2, h3, h4, h5, h6 {
  color: var(--header-color);
}

/* DataTable Styling */
.p-datatable .p-datatable-thead > tr > th {
  background-color: var(--table-header-bg) !important;
  color: var(--table-header-color) !important;
  border-color: var(--table-border) !important;
}

.p-datatable .p-datatable-tbody > tr > td {
  background-color: var(--table-row-bg);
  color: var(--table-row-color);
  border-color: var(--table-border);
}

.p-datatable .p-datatable-tbody > tr:hover {
  background-color: var(--table-row-hover) !important;
}

/* Transparent DataTable for Dynamic Theme */
.theme-dynamic .p-datatable {
  background-color: transparent;
}

.theme-dynamic .p-datatable .p-datatable-thead > tr > th {
  background-color: rgba(0, 0, 0, 0.3) !important;
  color: white !important;
  border-color: rgba(255, 255, 255, 0.2) !important;
}

.theme-dynamic .p-datatable .p-datatable-tbody > tr > td {
  background-color: rgba(255, 255, 255, 0.1);
  color: white;
  border-color: rgba(255, 255, 255, 0.1);
}

.theme-dynamic .p-datatable .p-datatable-tbody > tr:hover > td {
  background-color: rgba(255, 255, 255, 0.2) !important;
}

.theme-dynamic .p-paginator {
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  border-color: rgba(255, 255, 255, 0.2);
}

.theme-dynamic .p-paginator .p-paginator-element {
  color: white;
}

/* Input Styling */
.p-inputtext {
  background-color: var(--input-background);
  border-color: var(--input-border);
  color: var(--input-text);
}

/* Transparent Input for Dynamic Theme */
.theme-dynamic .p-inputtext,
.theme-dynamic .p-dropdown,
.theme-dynamic .p-multiselect,
.theme-dynamic .p-password,
.theme-dynamic .p-inputnumber,
.theme-dynamic .p-inputtextarea {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: var(--card-backdrop-filter);
  -webkit-backdrop-filter: var(--card-backdrop-filter);
}

.theme-dynamic .p-inputtext:enabled:focus,
.theme-dynamic .p-dropdown:enabled:focus,
.theme-dynamic .p-multiselect:enabled:focus,
.theme-dynamic .p-password:enabled:focus,
.theme-dynamic .p-inputnumber:enabled:focus,
.theme-dynamic .p-inputtextarea:enabled:focus {
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
}

.theme-dynamic .p-dropdown-panel,
.theme-dynamic .p-multiselect-panel {
  background-color: rgba(0, 0, 0, 0.8);
  backdrop-filter: var(--dialog-backdrop-filter);
  -webkit-backdrop-filter: var(--dialog-backdrop-filter);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-dynamic .p-dropdown-panel .p-dropdown-items .p-dropdown-item,
.theme-dynamic .p-multiselect-panel .p-multiselect-items .p-multiselect-item {
  color: white;
}

.theme-dynamic .p-dropdown-panel .p-dropdown-items .p-dropdown-item:hover,
.theme-dynamic .p-multiselect-panel .p-multiselect-items .p-multiselect-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* Button Styling */
.p-button.p-component:not(.p-button-outlined):not(.p-button-text):not(.p-button-link) {
  /* background-color: var(--button-primary-bg, #E2085D);
  color: black; */
}

.p-button.p-component:not(.p-button-outlined):not(.p-button-text):not(.p-button-link):hover {
  /* background-color: var(--button-primary-bg-hover, #c0074f);
  color: black; */
}

.p-button.p-component.p-button-outlined {
  /* color: var(--button-primary-bg, #E2085D);
  border-color: var(--button-primary-bg, #E2085D); */
}

.p-button.p-component.p-button-outlined:hover {
  background-color: rgba(226, 8, 93, 0.04);
}

/* Transparent Buttons for Dynamic Theme */
.theme-dynamic .p-button.p-component:not(.p-button-outlined):not(.p-button-text):not(.p-button-link) {
  background-color: rgba(59, 130, 246, 0.6);
  color: white;
  backdrop-filter: var(--card-backdrop-filter);
  -webkit-backdrop-filter: var(--card-backdrop-filter);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.theme-dynamic .p-button.p-component:not(.p-button-outlined):not(.p-button-text):not(.p-button-link):hover {
  background-color: rgba(59, 130, 246, 0.8);
  transform: translateY(-2px);
  box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.theme-dynamic .p-button.p-component.p-button-outlined {
  color: white;
  border-color: rgba(255, 255, 255, 0.4);
  background-color: rgba(255, 255, 255, 0.05);
}

.theme-dynamic .p-button.p-component.p-button-outlined:hover {
  background-color: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.6);
}

/* Card Styling */
.p-card {
  background-color: var(--card-background);
  color: var(--text-color);
  border: var(--card-border);
}

.p-card .p-card-title {
  color: var(--header-color);
}

.p-card .p-card-subtitle {
  color: var(--subheader-color);
}

/* Transparent Card for Dynamic Theme */
.theme-dynamic .p-card {
  background-color: var(--card-background);
  backdrop-filter: var(--card-backdrop-filter);
  -webkit-backdrop-filter: var(--card-backdrop-filter);
  border: var(--card-border);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
}

/* Navbar Styling */
.navbar {
  background-color: var(--card-background);
  border-bottom: var(--card-border);
}

/* Transparent Navbar for Dynamic Theme */
.theme-dynamic .navbar-container {
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: var(--card-backdrop-filter);
  -webkit-backdrop-filter: var(--card-backdrop-filter);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 30px rgba(0, 0, 0, 0.1);
  z-index: 100;
}

/* Menu items in dynamic theme */
.theme-dynamic .menu-item {
  color: white;
  background-color: transparent;
  transition: background-color 0.3s ease;
}

.theme-dynamic .menu-item:hover,
.theme-dynamic .menu-item.active {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Sidebar Styling */
.sidebar {
  background-color: var(--card-background);
  border-right: var(--card-border);
}

.sidebar .menu-item {
  color: var(--text-color);
}

.sidebar .menu-item:hover {
  background-color: var(--table-row-hover);
}

.sidebar .menu-item.selected {
  background-color: var(--accent-color, #E2085D);
  color: white;
}

/* Transparent Sidebar for Dynamic Theme */
.theme-dynamic .sidebar {
  background-color: rgba(0, 0, 0, 0.3);
  backdrop-filter: var(--card-backdrop-filter);
  -webkit-backdrop-filter: var(--card-backdrop-filter);
  border-right: 1px solid rgba(255, 255, 255, 0.2);
}

.theme-dynamic .sidebar .menu-item {
  color: white;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.theme-dynamic .sidebar .menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.theme-dynamic .sidebar .menu-item.selected {
  background-color: rgba(59, 130, 246, 0.5);
  color: white;
}

/* Form Elements */
.form-group label {
  color: var(--text-color);
}

/* Dropdown Styling */
.p-dropdown {
  background-color: var(--input-background);
  border-color: var(--input-border);
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item {
  color: var(--text-color);
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item:hover {
  background-color: var(--table-row-hover);
}

/* Dialog Styling */
.p-dialog .p-dialog-header {
  background-color: var(--card-background);
  color: var(--header-color);
  border-bottom: var(--card-border);
}

.p-dialog .p-dialog-content {
  background-color: var(--card-background);
  color: var(--text-color);
}

/* Transparent Dialog for Dynamic Theme */
.theme-dynamic .p-dialog .p-dialog-header,
.theme-dynamic .p-dialog .p-dialog-content,
.theme-dynamic .p-dialog .p-dialog-footer {
  background-color: var(--dialog-background);
  backdrop-filter: var(--dialog-backdrop-filter);
  -webkit-backdrop-filter: var(--dialog-backdrop-filter);
  border-color: rgba(255, 255, 255, 0.2);
  color: var(--text-color);
}

/* Sidebar Styling for Dynamic Theme */
.theme-dynamic .p-sidebar .p-sidebar-header,
.theme-dynamic .p-sidebar .p-sidebar-content {
  background-color: var(--sidebar-background);
  backdrop-filter: var(--card-backdrop-filter);
  -webkit-backdrop-filter: var(--card-backdrop-filter);
  border-color: rgba(255, 255, 255, 0.2);
  color: var(--text-color);
}

/* Accordion Styling */
.p-accordion .p-accordion-header .p-accordion-header-link {
  background-color: var(--table-header-bg);
  color: var(--header-color);
  border-color: var(--table-border);
}

.p-accordion .p-accordion-content {
  background-color: var(--card-background);
  color: var(--text-color);
  border-color: var(--table-border);
}

/* Transparent Accordion for Dynamic Theme */
.theme-dynamic .p-accordion .p-accordion-header .p-accordion-header-link {
  background-color: rgba(0, 0, 0, 0.3);
  color: white;
  border-color: rgba(255, 255, 255, 0.2);
  backdrop-filter: var(--card-backdrop-filter);
  -webkit-backdrop-filter: var(--card-backdrop-filter);
}

.theme-dynamic .p-accordion .p-accordion-content {
  background-color: rgba(0, 0, 0, 0.2);
  color: white;
  border-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: var(--card-backdrop-filter);
  -webkit-backdrop-filter: var(--card-backdrop-filter);
}

/* Toast Styling */
.p-toast .p-toast-message {
  background-color: var(--card-background);
  color: var(--text-color);
  border-color: var(--table-border);
}

/* Calendar Styling */
.p-calendar .p-inputtext {
  background-color: var(--input-background);
  color: var(--input-text);
}

.p-calendar .p-datepicker {
  background-color: var(--card-background);
  color: var(--text-color);
}

.p-datepicker .p-datepicker-header {
  background-color: var(--table-header-bg);
  color: var(--header-color);
}

.p-datepicker table td > span {
  color: var(--text-color);
}

.p-datepicker table td > span.p-highlight {
  background-color: var(--accent-color);
  color: white;
}

/* Checkbox Styling */
.p-checkbox .p-checkbox-box {
  background-color: var(--input-background);
  border-color: var(--input-border);
}

.p-checkbox .p-checkbox-box.p-highlight {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}

/* Radio Button Styling */
.p-radiobutton .p-radiobutton-box {
  background-color: var(--input-background);
  border-color: var(--input-border);
}

.p-radiobutton .p-radiobutton-box.p-highlight {
  background-color: var(--accent-color);
  border-color: var(--accent-color);
}



.p-highlight>.p-checkbox-box{
  background-color: var(--accent-color) !important;
  border-color: var(--accent-color) !important;
}