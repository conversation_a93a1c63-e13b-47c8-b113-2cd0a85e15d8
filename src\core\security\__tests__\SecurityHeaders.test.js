import SecurityHeaders from '../SecurityHeaders.js';

// Mock logger
jest.mock('../../logging/logger.js', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    security: jest.fn(),
    debug: jest.fn()
  }
}));

// Mock DOM APIs
Object.defineProperty(window, 'location', {
  writable: true,
  value: {
    href: 'https://example.com',
    origin: 'https://example.com'
  }
});

Object.defineProperty(window, 'navigator', {
  writable: true,
  value: {
    userAgent: 'Mozilla/5.0 (Test Browser)'
  }
});

// Mock fetch
global.fetch = jest.fn();

describe('SecurityHeaders', () => {
  beforeEach(() => {
    // Reset SecurityHeaders state
    SecurityHeaders.headers.clear();
    SecurityHeaders.violations = [];
    
    // Clear all mocks
    jest.clearAllMocks();
    
    // Reset environment
    process.env.NODE_ENV = 'test';
    
    // Reinitialize headers
    SecurityHeaders.initializeHeaders();
  });

  describe('initializeHeaders', () => {
    it('should initialize security headers', () => {
      expect(SecurityHeaders.headers.size).toBeGreaterThan(0);
      expect(SecurityHeaders.headers.has('Content-Security-Policy')).toBe(true);
      expect(SecurityHeaders.headers.has('X-Content-Type-Options')).toBe(true);
      expect(SecurityHeaders.headers.has('X-Frame-Options')).toBe(true);
    });

    it('should include HSTS in production', () => {
      process.env.NODE_ENV = 'production';
      SecurityHeaders.initializeHeaders();
      
      expect(SecurityHeaders.headers.has('Strict-Transport-Security')).toBe(true);
    });

    it('should not include HSTS in development', () => {
      process.env.NODE_ENV = 'development';
      SecurityHeaders.initializeHeaders();
      
      expect(SecurityHeaders.headers.has('Strict-Transport-Security')).toBe(false);
    });

    it('should configure CSP for development', () => {
      process.env.NODE_ENV = 'development';
      SecurityHeaders.initializeHeaders();
      
      const csp = SecurityHeaders.headers.get('Content-Security-Policy');
      expect(csp).toContain("'unsafe-eval'");
      expect(csp).toContain('ws:');
    });
  });

  describe('handleCSPViolation', () => {
    it('should handle CSP violation events', () => {
      const violationEvent = {
        blockedURI: 'https://evil.com/script.js',
        violatedDirective: 'script-src',
        originalPolicy: "default-src 'self'",
        disposition: 'enforce',
        documentURI: 'https://example.com',
        referrer: '',
        statusCode: 200
      };

      SecurityHeaders.handleCSPViolation(violationEvent);

      expect(SecurityHeaders.violations).toHaveLength(1);
      expect(SecurityHeaders.violations[0].type).toBe('csp-violation');
      expect(SecurityHeaders.violations[0].blockedURI).toBe('https://evil.com/script.js');
    });

    it('should send violation reports', () => {
      const violationEvent = {
        blockedURI: 'https://evil.com/script.js',
        violatedDirective: 'script-src',
        originalPolicy: "default-src 'self'",
        disposition: 'enforce',
        documentURI: 'https://example.com',
        referrer: '',
        statusCode: 200
      };

      fetch.mockResolvedValueOnce({ ok: true });

      SecurityHeaders.handleCSPViolation(violationEvent);

      expect(fetch).toHaveBeenCalledWith('/api/csp-report', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: expect.stringContaining('csp-violation')
      });
    });
  });

  describe('checkViolationThreshold', () => {
    it('should trigger alert when violation threshold exceeded', () => {
      const dispatchEventSpy = jest.spyOn(window, 'dispatchEvent').mockImplementation();

      // Add multiple violations
      for (let i = 0; i < 12; i++) {
        SecurityHeaders.violations.push({
          type: 'csp-violation',
          timestamp: new Date().toISOString(),
          blockedURI: `https://evil${i}.com/script.js`
        });
      }

      SecurityHeaders.checkViolationThreshold();

      expect(dispatchEventSpy).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'securityAlert',
          detail: expect.objectContaining({
            type: 'csp_violations',
            severity: 'high'
          })
        })
      );

      dispatchEventSpy.mockRestore();
    });

    it('should not trigger alert below threshold', () => {
      const dispatchEventSpy = jest.spyOn(window, 'dispatchEvent').mockImplementation();

      // Add few violations
      for (let i = 0; i < 5; i++) {
        SecurityHeaders.violations.push({
          type: 'csp-violation',
          timestamp: new Date().toISOString(),
          blockedURI: `https://evil${i}.com/script.js`
        });
      }

      SecurityHeaders.checkViolationThreshold();

      expect(dispatchEventSpy).not.toHaveBeenCalled();

      dispatchEventSpy.mockRestore();
    });
  });

  describe('getCSRFToken', () => {
    it('should get CSRF token from meta tag', () => {
      // Mock meta tag
      const metaTag = document.createElement('meta');
      metaTag.name = 'csrf-token';
      metaTag.content = 'test-csrf-token';
      document.head.appendChild(metaTag);

      const token = SecurityHeaders.getCSRFToken();

      expect(token).toBe('test-csrf-token');

      // Cleanup
      document.head.removeChild(metaTag);
    });

    it('should get CSRF token from cookie', () => {
      // Mock document.cookie
      Object.defineProperty(document, 'cookie', {
        writable: true,
        value: 'csrf_token=cookie-csrf-token; other=value'
      });

      const token = SecurityHeaders.getCSRFToken();

      expect(token).toBe('cookie-csrf-token');
    });

    it('should get CSRF token from localStorage', () => {
      // Mock localStorage
      const localStorageMock = {
        getItem: jest.fn(() => 'storage-csrf-token')
      };
      Object.defineProperty(window, 'localStorage', {
        value: localStorageMock
      });

      const token = SecurityHeaders.getCSRFToken();

      expect(token).toBe('storage-csrf-token');
    });

    it('should return null if no token found', () => {
      // Clear all sources
      Object.defineProperty(document, 'cookie', {
        writable: true,
        value: ''
      });
      
      const localStorageMock = {
        getItem: jest.fn(() => null)
      };
      Object.defineProperty(window, 'localStorage', {
        value: localStorageMock
      });

      const token = SecurityHeaders.getCSRFToken();

      expect(token).toBeNull();
    });
  });

  describe('validateResponseHeaders', () => {
    it('should validate response with all required headers', () => {
      const mockResponse = {
        headers: new Map([
          ['x-content-type-options', 'nosniff'],
          ['x-frame-options', 'DENY'],
          ['x-xss-protection', '1; mode=block']
        ]),
        url: 'https://example.com'
      };

      mockResponse.headers.has = jest.fn((header) => 
        mockResponse.headers.get(header) !== undefined
      );

      const isValid = SecurityHeaders.validateResponseHeaders(mockResponse);

      expect(isValid).toBe(true);
    });

    it('should detect missing security headers', () => {
      const mockResponse = {
        headers: new Map([
          ['x-content-type-options', 'nosniff']
        ]),
        url: 'https://example.com'
      };

      mockResponse.headers.has = jest.fn((header) => 
        mockResponse.headers.get(header) !== undefined
      );

      const { logger } = require('../../logging/logger.js');
      
      const isValid = SecurityHeaders.validateResponseHeaders(mockResponse);

      expect(isValid).toBe(false);
      expect(logger.security).toHaveBeenCalledWith('missing_security_headers', {
        url: 'https://example.com',
        missingHeaders: ['x-frame-options', 'x-xss-protection']
      });
    });
  });

  describe('getHeadersForServer', () => {
    it('should return headers object for server implementation', () => {
      const headers = SecurityHeaders.getHeadersForServer();

      expect(headers).toBeInstanceOf(Object);
      expect(headers['Content-Security-Policy']).toBeDefined();
      expect(headers['X-Content-Type-Options']).toBe('nosniff');
    });
  });

  describe('getExpressMiddleware', () => {
    it('should return Express middleware function', () => {
      const middleware = SecurityHeaders.getExpressMiddleware();

      expect(typeof middleware).toBe('function');
      expect(middleware.length).toBe(3); // req, res, next
    });

    it('should set headers in Express middleware', () => {
      const middleware = SecurityHeaders.getExpressMiddleware();
      const mockRes = {
        setHeader: jest.fn()
      };
      const mockNext = jest.fn();

      middleware({}, mockRes, mockNext);

      expect(mockRes.setHeader).toHaveBeenCalledTimes(SecurityHeaders.headers.size);
      expect(mockNext).toHaveBeenCalled();
    });
  });

  describe('getNginxConfig', () => {
    it('should generate Nginx configuration', () => {
      const config = SecurityHeaders.getNginxConfig();

      expect(config).toContain('add_header Content-Security-Policy');
      expect(config).toContain('server_tokens off');
      expect(config).toContain('more_clear_headers Server');
    });
  });

  describe('getSecurityStatus', () => {
    it('should return security status', () => {
      SecurityHeaders.violations.push({
        type: 'csp-violation',
        timestamp: new Date().toISOString()
      });

      const status = SecurityHeaders.getSecurityStatus();

      expect(status).toEqual({
        headersConfigured: SecurityHeaders.headers.size,
        violationsCount: 1,
        recentViolations: 1,
        environment: process.env.NODE_ENV
      });
    });
  });

  describe('cleanupViolations', () => {
    it('should remove old violations', () => {
      // Add old violation
      SecurityHeaders.violations.push({
        type: 'csp-violation',
        timestamp: new Date(Date.now() - 25 * 60 * 60 * 1000).toISOString() // 25 hours ago
      });

      // Add recent violation
      SecurityHeaders.violations.push({
        type: 'csp-violation',
        timestamp: new Date().toISOString()
      });

      SecurityHeaders.cleanupViolations();

      expect(SecurityHeaders.violations).toHaveLength(1);
    });
  });
});
