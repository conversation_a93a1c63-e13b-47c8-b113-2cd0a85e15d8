import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { PrimeReactProvider } from 'primereact/api';

// PrimeReact CSS imports
import 'primereact/resources/themes/lara-light-indigo/theme.css'; // or any other theme
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';
import 'primeflex/primeflex.css'; // PrimeFlex CSS
// import './index.css';
import App from './App.jsx';
import { Provider } from 'react-redux';

// Optional: Configure PrimeReact default settings
const value = {
  ripple: true, // Enable ripple effect
  inputStyle: 'outlined', // or 'filled' or 'outlined'
  locale: 'en', // Default locale
};

createRoot(document.getElementById('root')).render(
  // <StrictMode>
    <PrimeReactProvider value={value}>
      <App />
    </PrimeReactProvider>
  // </StrictMode>
);
