

class DataTransformer {
  constructor() {
    this.transformers = new Map();
    this.initializeDefaultTransformers();
  }

  register(name, transformer) {
    if (typeof transformer !== 'function') {
      throw new Error('Transformer must be a function');
    }
    
    this.transformers.set(name, transformer);
  }

  transform(data, transformer) {
    if (!transformer) {
      return data;
    }

    if (typeof transformer === 'string') {
      const transformerFn = this.transformers.get(transformer);
      if (!transformerFn) {
        console.warn(`Transformer '${transformer}' not found`);
        return data;
      }
      return transformerFn(data);
    }

    if (typeof transformer === 'object') {
      return this.applyTransformerConfig(data, transformer);
    }

    if (typeof transformer === 'function') {
      return transformer(data);
    }

    return data;
  }

  applyTransformerConfig(data, config) {
    let result = data;

    if (config.transformers && Array.isArray(config.transformers)) {
      result = config.transformers.reduce((acc, transformerName) => {
        return this.transform(acc, transformerName);
      }, result);
    }

    if (config.transformer) {
      result = this.transform(result, config.transformer);
    }

    if (config.mapping) {
      result = this.applyMapping(result, config.mapping);
    }

    if (config.filter) {
      result = this.applyFilter(result, config.filter);
    }

    if (config.sort) {
      result = this.applySort(result, config.sort);
    }

    if (config.groupBy) {
      result = this.applyGrouping(result, config.groupBy);
    }

    if (config.pagination) {
      result = this.applyPagination(result, config.pagination);
    }

    return result;
  }

  applyMapping(data, mapping) {
    if (!data || !mapping) {
      return data;
    }

    if (Array.isArray(data)) {
      return data.map(item => this.mapObject(item, mapping));
    }

    if (typeof data === 'object') {
      return this.mapObject(data, mapping);
    }

    return data;
  }

  mapObject(obj, mapping) {
    const result = {};

    Object.entries(mapping).forEach(([newKey, oldKey]) => {
      if (typeof oldKey === 'string') {
        result[newKey] = this.getNestedValue(obj, oldKey);
      } else if (typeof oldKey === 'function') {
        result[newKey] = oldKey(obj);
      } else if (typeof oldKey === 'object' && oldKey.field) {
        let value = this.getNestedValue(obj, oldKey.field);
        
        if (oldKey.transform) {
          value = this.transform(value, oldKey.transform);
        }
        
        if (oldKey.default !== undefined && (value === null || value === undefined)) {
          value = oldKey.default;
        }
        
        result[newKey] = value;
      }
    });

    return result;
  }

  getNestedValue(obj, path) {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  applyFilter(data, filter) {
    if (!Array.isArray(data)) {
      return data;
    }

    if (typeof filter === 'function') {
      return data.filter(filter);
    }

    if (typeof filter === 'object') {
      return data.filter(item => {
        return Object.entries(filter).every(([key, value]) => {
          const itemValue = this.getNestedValue(item, key);
          
          if (typeof value === 'object' && value.operator) {
            return this.applyFilterOperator(itemValue, value.value, value.operator);
          }
          
          return itemValue === value;
        });
      });
    }

    return data;
  }

  applyFilterOperator(itemValue, filterValue, operator) {
    switch (operator) {
      case 'eq': return itemValue === filterValue;
      case 'ne': return itemValue !== filterValue;
      case 'gt': return itemValue > filterValue;
      case 'gte': return itemValue >= filterValue;
      case 'lt': return itemValue < filterValue;
      case 'lte': return itemValue <= filterValue;
      case 'contains': return String(itemValue).includes(String(filterValue));
      case 'startsWith': return String(itemValue).startsWith(String(filterValue));
      case 'endsWith': return String(itemValue).endsWith(String(filterValue));
      case 'in': return Array.isArray(filterValue) && filterValue.includes(itemValue);
      case 'notIn': return Array.isArray(filterValue) && !filterValue.includes(itemValue);
      default: return true;
    }
  }

  applySort(data, sort) {
    if (!Array.isArray(data)) {
      return data;
    }

    const sortedData = [...data];

    if (typeof sort === 'string') {
      return sortedData.sort((a, b) => {
        const aVal = this.getNestedValue(a, sort);
        const bVal = this.getNestedValue(b, sort);
        return aVal > bVal ? 1 : aVal < bVal ? -1 : 0;
      });
    }

    if (typeof sort === 'object') {
      return sortedData.sort((a, b) => {
        const aVal = this.getNestedValue(a, sort.field);
        const bVal = this.getNestedValue(b, sort.field);
        const direction = sort.direction === 'desc' ? -1 : 1;
        
        if (sort.type === 'number') {
          return (Number(aVal) - Number(bVal)) * direction;
        }
        
        if (sort.type === 'date') {
          return (new Date(aVal) - new Date(bVal)) * direction;
        }
        
        return String(aVal).localeCompare(String(bVal)) * direction;
      });
    }

    return sortedData;
  }

  applyGrouping(data, groupBy) {
    if (!Array.isArray(data)) {
      return data;
    }

    const groups = {};

    data.forEach(item => {
      let key;
      
      if (typeof groupBy === 'string') {
        key = this.getNestedValue(item, groupBy);
      } else if (typeof groupBy === 'function') {
        key = groupBy(item);
      } else {
        key = 'default';
      }

      if (!groups[key]) {
        groups[key] = [];
      }
      
      groups[key].push(item);
    });

    return groups;
  }

  applyPagination(data, pagination) {
    if (!Array.isArray(data)) {
      return data;
    }

    const { page = 1, pageSize = 10 } = pagination;
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    
    return {
      data: data.slice(startIndex, endIndex),
      pagination: {
        page,
        pageSize,
        total: data.length,
        totalPages: Math.ceil(data.length / pageSize),
        hasNext: endIndex < data.length,
        hasPrev: page > 1
      }
    };
  }

  initializeDefaultTransformers() {
    this.register('arrayToObject', (data) => {
      if (!Array.isArray(data)) return data;
      return data.reduce((acc, item, index) => {
        acc[index] = item;
        return acc;
      }, {});
    });

    this.register('objectToArray', (data) => {
      if (typeof data !== 'object' || data === null) return data;
      return Object.entries(data).map(([key, value]) => ({ key, value }));
    });

    this.register('flatten', (data) => {
      if (!Array.isArray(data)) return data;
      return data.flat(Infinity);
    });

    this.register('unique', (data) => {
      if (!Array.isArray(data)) return data;
      return [...new Set(data)];
    });

    this.register('uppercase', (data) => {
      if (typeof data === 'string') return data.toUpperCase();
      return data;
    });

    this.register('lowercase', (data) => {
      if (typeof data === 'string') return data.toLowerCase();
      return data;
    });

    this.register('capitalize', (data) => {
      if (typeof data === 'string') {
        return data.charAt(0).toUpperCase() + data.slice(1).toLowerCase();
      }
      return data;
    });

    this.register('toNumber', (data) => {
      const num = Number(data);
      return isNaN(num) ? data : num;
    });

    this.register('toString', (data) => {
      return String(data);
    });

    this.register('toDate', (data) => {
      const date = new Date(data);
      return isNaN(date.getTime()) ? data : date;
    });

    this.register('formatDate', (data) => {
      const date = new Date(data);
      return isNaN(date.getTime()) ? data : date.toLocaleDateString();
    });

    this.register('parseJSON', (data) => {
      if (typeof data === 'string') {
        try {
          return JSON.parse(data);
        } catch (error) {
          console.warn('Failed to parse JSON:', error);
          return data;
        }
      }
      return data;
    });

    this.register('stringifyJSON', (data) => {
      try {
        return JSON.stringify(data);
      } catch (error) {
        console.warn('Failed to stringify JSON:', error);
        return data;
      }
    });
  }

  getTransformers() {
    return Array.from(this.transformers.keys());
  }

  hasTransformer(name) {
    return this.transformers.has(name);
  }
}

const dataTransformer = new DataTransformer();

export default dataTransformer;
