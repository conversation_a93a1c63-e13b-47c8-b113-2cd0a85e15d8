import React from "react";
import asmLogo from "@assets/Images/authencation/asm-logo.png";

const AuthHeader = ({ 
  title, 
  subtitle, 
  showLogo = true, 
  className = "",
  icon="" 
}) => {
  return (
    <div className={`auth-header ${className}`}>
      {/* Back Button */}

      {/* Logo Section */}
      {showLogo && (
        <div className="logo-section text-center">
          <img src={asmLogo} alt="ASM" className="logo-image" />
        </div>
      )}

      {icon && (
        icon
      )}

      {/* Title and Subtitle */}
      {(title || subtitle) && (
        <div className="text-center">
          {title && (
            <h2 className="text-2xl font-semibold text-gray-600 mb-2">
              {title}
            </h2>
          )}
          {subtitle && (
            <p className="text-sm text-gray-500">
              {subtitle}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default AuthHeader;
