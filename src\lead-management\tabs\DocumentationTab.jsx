import React, { useState, useRef } from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { But<PERSON> } from 'primereact/button';
import { Card } from 'primereact/card';
import { FileUpload } from 'primereact/fileupload';
import { Tag } from 'primereact/tag';
import { Toast } from 'primereact/toast';
import { ProgressBar } from 'primereact/progressbar';

const DocumentationTab = ({ leadId }) => {
  const toast = useRef(null);
  const fileUploadRef = useRef(null);
  const [selectedDocuments, setSelectedDocuments] = useState([]);

  const documents = [
    {
      id: 1,
      fileName: 'Proof of Delivery.pdf',
      fileType: 'PDF',
      fileSize: '2.5 MB',
      uploadedBy: '<PERSON>',
      uploadDate: '11/23/2024',
      category: 'Delivery',
      status: 'Approved'
    },
    {
      id: 2,
      fileName: 'KYC Document.pdf',
      fileType: 'PDF',
      fileSize: '1.8 MB',
      uploadedBy: '<PERSON>',
      uploadDate: '11/22/2024',
      category: 'Compliance',
      status: 'Approved'
    },
    {
      id: 3,
      fileName: 'Letter of the Airline.pdf',
      fileType: 'PDF',
      fileSize: '890 KB',
      uploadedBy: 'Mark Stephen',
      uploadDate: '11/20/2024',
      category: 'Legal',
      status: 'Pending'
    },
    {
      id: 4,
      fileName: 'Power of Attorney.pdf',
      fileType: 'PDF',
      fileSize: '1.2 MB',
      uploadedBy: 'John Smith',
      uploadDate: '11/18/2024',
      category: 'Legal',
      status: 'Approved'
    }
  ];

  const onUpload = () => {
    toast.current.show({
      severity: 'success',
      summary: 'Success',
      detail: 'File uploaded successfully',
      life: 3000
    });
  };

  const fileNameTemplate = (rowData) => {
    const getFileIcon = (type) => {
      switch (type) {
        case 'PDF':
          return 'pi pi-file-pdf';
        case 'DOC':
        case 'DOCX':
          return 'pi pi-file-word';
        case 'XLS':
        case 'XLSX':
          return 'pi pi-file-excel';
        default:
          return 'pi pi-file';
      }
    };

    return (
      <div className="file-name-cell">
        <i className={getFileIcon(rowData.fileType)} style={{ fontSize: '1.5rem', color: '#F44336' }}></i>
        <div className="file-details">
          <div className="file-name">{rowData.fileName}</div>
          <div className="file-size">{rowData.fileSize}</div>
        </div>
      </div>
    );
  };

  const categoryTemplate = (rowData) => {
    const getCategorySeverity = (category) => {
      switch (category) {
        case 'Compliance':
          return 'success';
        case 'Legal':
          return 'warning';
        case 'Delivery':
          return 'info';
        default:
          return null;
      }
    };

    return <Tag value={rowData.category} severity={getCategorySeverity(rowData.category)} />;
  };

  const statusTemplate = (rowData) => {
    const severity = rowData.status === 'Approved' ? 'success' : 'warning';
    return <Tag value={rowData.status} severity={severity} />;
  };

  const uploadInfoTemplate = (rowData) => {
    return (
      <div className="upload-info">
        <div><i className="pi pi-user"></i> {rowData.uploadedBy}</div>
        <div><i className="pi pi-calendar"></i> {rowData.uploadDate}</div>
      </div>
    );
  };

  const actionTemplate = (rowData) => {
    return (
      <div className="action-buttons">
        <Button
          icon="pi pi-eye"
          className="p-button-rounded p-button-text p-button-sm"
          tooltip="View"
          onClick={() => {
            toast.current.show({
              severity: 'info',
              summary: 'View Document',
              detail: `Opening ${rowData.fileName}`,
              life: 3000
            });
          }}
        />
        <Button
          icon="pi pi-download"
          className="p-button-rounded p-button-text p-button-sm"
          tooltip="Download"
          onClick={() => {
            toast.current.show({
              severity: 'success',
              summary: 'Download Started',
              detail: `Downloading ${rowData.fileName}`,
              life: 3000
            });
          }}
        />
        <Button
          icon="pi pi-trash"
          className="p-button-rounded p-button-text p-button-danger p-button-sm"
          tooltip="Delete"
          onClick={() => {
            toast.current.show({
              severity: 'warn',
              summary: 'Delete Document',
              detail: `Are you sure you want to delete ${rowData.fileName}?`,
              life: 3000
            });
          }}
        />
      </div>
    );
  };

  const header = (
    <div className="table-header">
      <h3>Documentation</h3>
      <div className="header-actions">
        <Button
          label="Upload Document"
          icon="pi pi-upload"
          className="p-button-sm p-button-success"
          onClick={() => fileUploadRef.current.choose()}
        />
      </div>
    </div>
  );

  return (
    <div className="documentation-tab">
      <Toast ref={toast} />
      
      <Card className="documentation-card">
        {/* File Upload Section */}
        <div className="upload-section" style={{ marginBottom: '1rem' }}>
          <FileUpload
            ref={fileUploadRef}
            name="documents[]"
            multiple
            accept="application/pdf,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document"
            maxFileSize={10000000}
            onUpload={onUpload}
            emptyTemplate={
              <p className="upload-placeholder">
                Drag and drop files here or click to browse
              </p>
            }
            chooseLabel="Select Files"
            uploadLabel="Upload"
            cancelLabel="Cancel"
            className="custom-file-upload"
          />
        </div>

        {/* Documents Table */}
        <DataTable
          value={documents}
          selection={selectedDocuments}
          onSelectionChange={(e) => setSelectedDocuments(e.value)}
          dataKey="id"
          header={header}
          emptyMessage="No documents found"
          className="documents-datatable"
        >
          <Column selectionMode="multiple" headerStyle={{ width: '3rem' }} />
          <Column field="fileName" header="File Name" body={fileNameTemplate} sortable />
          <Column field="category" header="Category" body={categoryTemplate} sortable />
          <Column field="uploadInfo" header="Uploaded By" body={uploadInfoTemplate} />
          <Column field="status" header="Status" body={statusTemplate} sortable />
          <Column body={actionTemplate} headerStyle={{ width: '10rem' }} />
        </DataTable>

        {/* Document Statistics */}
        <div className="document-stats">
          <div className="stat-item">
            <i className="pi pi-file"></i>
            <span>Total Documents: {documents.length}</span>
          </div>
          <div className="stat-item">
            <i className="pi pi-check-circle"></i>
            <span>Approved: {documents.filter(d => d.status === 'Approved').length}</span>
          </div>
          <div className="stat-item">
            <i className="pi pi-clock"></i>
            <span>Pending: {documents.filter(d => d.status === 'Pending').length}</span>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default DocumentationTab;

