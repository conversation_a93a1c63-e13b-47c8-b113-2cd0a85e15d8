import React from 'react';
import { Dropdown } from 'primereact/dropdown';

const CompanyDetailsCard = ({ 
  customerName, 
  registeredOfficeAddress, 
  operationalBase,
  language,
  languages,
  onLanguageChange 
}) => {
  return (
    <div className="glass-card-doc-management p-4">
      {/* <div style={{ marginBottom: '1rem' }}>
        <label style={{ 
          display: 'block', 
          fontSize: '0.875rem', 
          fontWeight: '500', 
          color: '#6B7280',
          marginBottom: '0.5rem'
        }}>
          Language
        </label>
        <Dropdown
          value={language}
          options={languages}
          onChange={onLanguageChange}
          placeholder="Select language"
          className="w-full"
        />
      </div> */}

      <div style={{ 
        backgroundColor: '#EFF6FF', 
        padding: '1rem', 
        borderRadius: '0.5rem',
        marginTop: '1.5rem'
      }}>
        <h4 style={{ 
          margin: 0, 
          marginBottom: '1rem', 
          fontSize: '1rem', 
          fontWeight: '600',
          color: '#1E40AF'
        }}>
          Company Details
        </h4>
        
        <div style={{ display: 'flex', flexDirection: 'column', gap: '0.75rem' }}>
          <div>
            <div style={{ fontSize: '0.875rem', color: '#2563EB', fontWeight: '500', marginBottom: '0.25rem' }}>
              Customer Name:
            </div>
            <div style={{ fontSize: '0.875rem', color: '#111827' }}>
              {customerName}
            </div>
          </div>

          <div>
            <div style={{ fontSize: '0.875rem', color: '#2563EB', fontWeight: '500', marginBottom: '0.25rem' }}>
              Registered Office Address:
            </div>
            <div style={{ fontSize: '0.875rem', color: '#111827' }}>
              {registeredOfficeAddress}
            </div>
          </div>

          <div>
            <div style={{ fontSize: '0.875rem', color: '#2563EB', fontWeight: '500', marginBottom: '0.25rem' }}>
              Operational Base:
            </div>
            <div style={{ fontSize: '0.875rem', color: '#111827' }}>
              {operationalBase}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyDetailsCard;
