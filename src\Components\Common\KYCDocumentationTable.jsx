import React from 'react';
import { DataTable } from 'primereact/datatable';
import { Column } from 'primereact/column';
import { Button } from 'primereact/button';
import GlassyBadge from './GlassyBadge';

const KYCDocumentationTable = ({ documents }) => {
  const statusBodyTemplate = (rowData) => {
    let className = 'badge-success';
    if (rowData.status === 'Pending') {
      className = 'badge-warning';
    } else if (rowData.status === 'Verified') {
      className = 'badge-success';
    }
    
    return (
      <div style={{ display: 'flex', justifyContent: 'center' }}>
        <GlassyBadge 
          label={rowData.status} 
          className={className}
        />
      </div>
    );
  };

  const actionBodyTemplate = (rowData) => {
    return (
      <div style={{ display: 'flex', gap: '0.5rem' }}>
        <Button
          icon="pi pi-eye"
          className="p-button-sm p-button-text p-button-secondary"
          tooltip="View"
        />
        <Button
          icon="pi pi-download"
          className="p-button-sm p-button-text p-button-secondary"
          tooltip="Download"
        />
      </div>
    );
  };

  return (
    <div className="">
      <div className="tab-header" style={{ marginBottom: '1.5rem', display: 'flex', justifyContent: 'space-between', alignItems: 'center', color: 'black' }}>
        <h3>KYC Documentation</h3>
      </div>

      <div className="">
        <DataTable 
          value={documents} 
          className="glass-table"
          responsiveLayout="scroll"
          stripedRows
          showGridlines
        style={{ fontSize: '0.875rem' }}
        >
          <Column 
            field="documentType" 
            header="Document Type" 
            sortable
            style={{ minWidth: '150px', textAlign: 'center' }}
          />
          <Column 
            field="status" 
            header="Status" 
            body={statusBodyTemplate} 
            sortable
            style={{ minWidth: '120px', textAlign: 'center' }}
          />
          <Column 
            field="uploadDate" 
            header="Upload Date" 
            sortable
            style={{ minWidth: '120px', textAlign: 'center' }}
          />
          <Column 
            field="verifiedBy" 
            header="Verified By" 
            sortable
            style={{ minWidth: '150px', textAlign: 'center' }}
          />
          <Column 
            header="Action" 
            body={actionBodyTemplate}
            style={{ width: '100px', textAlign: 'center' }}
          />
        </DataTable>
      </div>
    </div>
  );
};

export default KYCDocumentationTable;
