import React, { useState, useRef, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Menu } from 'primereact/menu';
import { Button } from 'primereact/button';
import { Dropdown } from 'primereact/dropdown';
import { useAuthContext } from '@contexts/AuthContext';
import AuthService from '@services/auth/authService';
import { showToast } from '@utils/toast/toastUtils';
import asmLogo from '@assets/images/dashboard/asm-logo.svg';

const DashboardNavbar = ({ onMenuToggle, isCollapsed }) => {
  const navigate = useNavigate();
  const menuRef = useRef(null);
  const { user,roles, logout } = useAuthContext();
  const [isLoggingOut, setIsLoggingOut] = useState(false);
  const [selectedCompany, setSelectedCompany] = useState('Aviation Services Management, Dubai');  
const roleDisplayMap = {
    asm_admin: 'Asm Admin',
    asm_employee: 'Asm Employee',
    client: 'Client',
    client_employee: 'Client Employee',
};
  const handleLogout = async () => {
    try {
      setIsLoggingOut(true);
      const storedUser = JSON.parse(localStorage.getItem('user') || '{}');
      const { refreshToken = '', accessToken = '' } = storedUser;

      await AuthService.logoutApi(refreshToken, accessToken);
      showToast('success', 'Logged out successfully');
      logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
      // showToast('error', 'Logout failed, but you have been logged out locally');
      logout();
      navigate('/login');
    } finally {
      setIsLoggingOut(false);
    }
  };

  // Company options for dropdown
  const companyOptions = [
    { label: 'Aviation Services Management, Dubai', value: 'Aviation Services Management, Dubai' },
    { label: 'Emirates Airlines, UAE', value: 'Emirates Airlines, UAE' },
    { label: 'Qatar Airways, Doha', value: 'Qatar Airways, Doha' },
    { label: 'Etihad Airways, Abu Dhabi', value: 'Etihad Airways, Abu Dhabi' },
    { label: 'Flydubai, Dubai', value: 'Flydubai, Dubai' },
    { label: 'Air Arabia, Sharjah', value: 'Air Arabia, Sharjah' }
  ];

  const userMenuItems = [
    { label: roleDisplayMap[roles?.[0]], icon: 'pi pi-user', className: 'user-role-item' },
    { separator: true },
    { label: 'Profile', icon: 'pi pi-user', command: () => navigate('/profile'), disabled: isLoggingOut },
    { label: 'Settings', icon: 'pi pi-cog', command: () => navigate('/settings'), disabled: isLoggingOut },
    {
      label: isLoggingOut ? 'Logging out...' : 'Logout',
      icon: isLoggingOut ? 'pi pi-spin pi-spinner' : 'pi pi-sign-out',
      command: handleLogout,
      disabled: isLoggingOut
    }
  ];

  return (
    <div className="dashboard-navbar p-0">
      <div className="navbar-content flex align-items-center justify-content-between gap-1 px-1 py-1 md:gap-3 md:px-3 overflow-hidden w-full">
        {/* Left side - Logo + Hamburger */}
        <div className="navbar-left flex align-items-center gap-2 pl-0">
          {/* Mobile/Tablet: Sidebar toggle (hamburger) */}
          <Button
            icon="pi pi-bars"
            className="p-button-text p-button-rounded navbar-menu-btn p-1 m-0 block lg:hidden"
            aria-label="Toggle sidebar"
            onClick={() => onMenuToggle && onMenuToggle()}
          />
          <div className="navbar-svg-placeholder flex align-items-center ml-0">
            {/* SVG will be added here later */}
              <img src={asmLogo} alt="Logo" srcSet="" className="h-1_5rem md:h-2rem" />
          </div>
        </div>

        {/* Center - Empty for now */}
        <div className="navbar-center"></div>

        {/* Right side - Company Dropdown, Actions and User */}
        <div className="navbar-right flex align-items-center gap-1 md:gap-3 min-w-0 flex-nowrap justify-content-end overflow-hidden max-w-full">
          {/* Company Dropdown (responsive width with truncation) */}
          <div className="company-dropdown flex-1 min-w-0 w-8rem sm:w-13rem md:w-14rem lg:w-20rem">
            <Dropdown
              value={selectedCompany}
              options={companyOptions}
              onChange={(e) => setSelectedCompany(e.value)}
              className="w-full overflow-hidden white-space-nowrap text-overflow-ellipsis text-xs md:text-sm"
              panelClassName="w-15rem sm:w-14rem md:w-16rem"
              placeholder="Select Company"
            />
          </div>

          {/* Info Icon */}
          <Button
            icon="pi pi-info-circle"
            className="p-button-rounded p-button-text navbar-action-btn p-0 md:p-2 flex-shrink-0"
          />

          {/* Notification Bell */}
          <div className="notification-wrapper flex-shrink-0">
            <Button
              icon="pi pi-bell"
              className="p-button-rounded p-button-text navbar-action-btn p-0 md:p-2 flex-shrink-0"
            />
            <span className="notification-badge hidden sm:inline-block text-center">10</span>
          </div>

          {/* User Avatar */}
          <div className="user-avatar-section flex-shrink-0">
            <Button
              className="user-avatar-btn p-0 md:p-2"
              onClick={(e) => menuRef.current.toggle(e)}
            >
              <div className="user-avatar w-1_75rem h-1_75rem md:w-2_5rem md:h-2_5rem flex align-items-center justify-content-center">
                <span className="user-initials text-2xs md:text-sm">JD</span>
              </div>
            </Button>

            <Menu model={userMenuItems} popup ref={menuRef} className="user-menu" />
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardNavbar;
