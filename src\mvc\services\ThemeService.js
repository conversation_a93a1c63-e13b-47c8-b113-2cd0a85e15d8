import { logger } from '../../core/logging/logger.js';

class ThemeService {
  constructor() {
    this.currentTheme = this.getUserTheme();
    this.availableThemes = ['light', 'dark', 'auto', 'custom'];
    this.customThemes = new Map();
    this.listeners = new Set();
    this.mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    
    this.initializeTheme();
    this.setupMediaQueryListener();
  }

  initializeTheme() {
    try {
      this.applyTheme(this.currentTheme);
      this.loadCustomThemes();
      logger.info('Theme service initialized', { theme: this.currentTheme });
    } catch (error) {
      logger.error('Failed to initialize theme service', { error: error.message });
      this.currentTheme = 'light';
      this.applyTheme('light');
    }
  }

  getUserTheme() {
    const savedTheme = localStorage.getItem('userTheme');
    if (savedTheme && this.availableThemes.includes(savedTheme)) {
      return savedTheme;
    }

    const urlParams = new URLSearchParams(window.location.search);
    const urlTheme = urlParams.get('theme');
    if (urlTheme && this.availableThemes.includes(urlTheme)) {
      return urlTheme;
    }

    return 'auto';
  }

  setupMediaQueryListener() {
    this.mediaQuery.addEventListener('change', (e) => {
      if (this.currentTheme === 'auto') {
        this.applySystemTheme();
      }
    });
  }

  applyTheme(theme) {
    const resolvedTheme = this.resolveTheme(theme);
    
    document.documentElement.setAttribute('data-theme', resolvedTheme);
    
    this.updateMetaThemeColor(resolvedTheme);
    
    if (theme === 'custom') {
      this.applyCustomTheme();
    }
    
    logger.info(`Applied theme: ${theme} (resolved: ${resolvedTheme})`);
  }

  resolveTheme(theme) {
    if (theme === 'auto') {
      return this.mediaQuery.matches ? 'dark' : 'light';
    }
    return theme;
  }

  applySystemTheme() {
    const systemTheme = this.mediaQuery.matches ? 'dark' : 'light';
    document.documentElement.setAttribute('data-theme', systemTheme);
    this.updateMetaThemeColor(systemTheme);
    this.notifyListeners(this.currentTheme, systemTheme);
  }

  updateMetaThemeColor(theme) {
    let themeColor = '#ffffff';
    
    if (theme === 'dark') {
      themeColor = '#111827'; 
    }
    
    let metaThemeColor = document.querySelector('meta[name="theme-color"]');
    if (!metaThemeColor) {
      metaThemeColor = document.createElement('meta');
      metaThemeColor.name = 'theme-color';
      document.head.appendChild(metaThemeColor);
    }
    metaThemeColor.content = themeColor;
  }

  changeTheme(theme) {
    if (!this.availableThemes.includes(theme) && !this.customThemes.has(theme)) {
      logger.warn(`Invalid theme: ${theme}`);
      return false;
    }

    const oldTheme = this.currentTheme;
    this.currentTheme = theme;
    
    this.applyTheme(theme);
    
    if (theme !== 'auto') {
      localStorage.setItem('userTheme', theme);
    } else {
      localStorage.removeItem('userTheme');
    }
    
    this.notifyListeners(theme, oldTheme);
    
    logger.info(`Theme changed from ${oldTheme} to ${theme}`);
    return true;
  }

  toggleTheme() {
    const currentResolved = this.resolveTheme(this.currentTheme);
    const newTheme = currentResolved === 'light' ? 'dark' : 'light';
    return this.changeTheme(newTheme);
  }

  createCustomTheme(name, config) {
    try {
      this.validateThemeConfig(config);
      this.customThemes.set(name, config);
      this.saveCustomThemes();
      
      if (!this.availableThemes.includes(name)) {
        this.availableThemes.push(name);
      }
      
      logger.info(`Created custom theme: ${name}`);
      return true;
    } catch (error) {
      logger.error(`Failed to create custom theme: ${name}`, { error: error.message });
      return false;
    }
  }

  validateThemeConfig(config) {
    const requiredProperties = [
      'primary', 'secondary', 'background', 'text', 'border'
    ];
    
    for (const prop of requiredProperties) {
      if (!config[prop]) {
        throw new Error(`Missing required theme property: ${prop}`);
      }
    }
  }

  applyCustomTheme(themeName = 'custom') {
    const config = this.customThemes.get(themeName);
    if (!config) {
      logger.warn(`Custom theme not found: ${themeName}`);
      return false;
    }

    const root = document.documentElement;
    Object.entries(config).forEach(([key, value]) => {
      if (typeof value === 'object') {
        Object.entries(value).forEach(([subKey, subValue]) => {
          root.style.setProperty(`--theme-${key}-${subKey}`, subValue);
        });
      } else {
        root.style.setProperty(`--theme-${key}`, value);
      }
    });

    return true;
  }

  deleteCustomTheme(name) {
    if (this.customThemes.has(name)) {
      this.customThemes.delete(name);
      this.availableThemes = this.availableThemes.filter(theme => theme !== name);
      this.saveCustomThemes();
      
      if (this.currentTheme === name) {
        this.changeTheme('light');
      }
      
      logger.info(`Deleted custom theme: ${name}`);
      return true;
    }
    return false;
  }

  saveCustomThemes() {
    try {
      const themesData = Object.fromEntries(this.customThemes);
      localStorage.setItem('customThemes', JSON.stringify(themesData));
    } catch (error) {
      logger.error('Failed to save custom themes', { error: error.message });
    }
  }

  loadCustomThemes() {
    try {
      const savedThemes = localStorage.getItem('customThemes');
      if (savedThemes) {
        const themesData = JSON.parse(savedThemes);
        Object.entries(themesData).forEach(([name, config]) => {
          this.customThemes.set(name, config);
          if (!this.availableThemes.includes(name)) {
            this.availableThemes.push(name);
          }
        });
        logger.info(`Loaded ${this.customThemes.size} custom themes`);
      }
    } catch (error) {
      logger.error('Failed to load custom themes', { error: error.message });
    }
  }

  addListener(callback) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  notifyListeners(newTheme, oldTheme) {
    const resolvedTheme = this.resolveTheme(newTheme);
    this.listeners.forEach(callback => {
      try {
        callback(newTheme, oldTheme, resolvedTheme);
      } catch (error) {
        logger.error('Error in theme change listener', { error: error.message });
      }
    });
  }

  getCurrentTheme() {
    return this.currentTheme;
  }

  getResolvedTheme() {
    return this.resolveTheme(this.currentTheme);
  }

  getAvailableThemes() {
    return [...this.availableThemes];
  }

  getCustomThemes() {
    return new Map(this.customThemes);
  }

  isSystemDarkMode() {
    return this.mediaQuery.matches;
  }

  getThemeInfo(theme = this.currentTheme) {
    const resolved = this.resolveTheme(theme);
    return {
      name: theme,
      resolved: resolved,
      isCustom: this.customThemes.has(theme),
      isSystem: theme === 'auto',
      isDark: resolved === 'dark'
    };
  }

  getCSSVariable(variable) {
    return getComputedStyle(document.documentElement)
      .getPropertyValue(variable).trim();
  }

  setCSSVariable(variable, value) {
    document.documentElement.style.setProperty(variable, value);
  }

  detectSystemTheme() {
    return this.mediaQuery.matches ? 'dark' : 'light';
  }

  exportTheme(themeName) {
    const theme = this.customThemes.get(themeName);
    if (!theme) {
      return null;
    }

    return {
      name: themeName,
      config: theme,
      version: '1.0',
      created: new Date().toISOString()
    };
  }

  importTheme(themeData) {
    try {
      if (!themeData.name || !themeData.config) {
        throw new Error('Invalid theme data format');
      }

      return this.createCustomTheme(themeData.name, themeData.config);
    } catch (error) {
      logger.error('Failed to import theme', { error: error.message });
      return false;
    }
  }

  getContrastRatio(color1, color2) {
    const luminance1 = this.getLuminance(color1);
    const luminance2 = this.getLuminance(color2);
    
    const lighter = Math.max(luminance1, luminance2);
    const darker = Math.min(luminance1, luminance2);
    
    return (lighter + 0.05) / (darker + 0.05);
  }

  getLuminance(color) {
    return 0.5;
  }

  preloadTheme(theme) {
    if (theme === 'light' || theme === 'dark') {
      return Promise.resolve();
    }
    
    if (this.customThemes.has(theme)) {
      return Promise.resolve();
    }
    
    return Promise.reject(new Error(`Theme not found: ${theme}`));
  }

  getStats() {
    return {
      currentTheme: this.currentTheme,
      resolvedTheme: this.getResolvedTheme(),
      availableThemes: this.availableThemes.length,
      customThemes: this.customThemes.size,
      listeners: this.listeners.size,
      systemDarkMode: this.isSystemDarkMode()
    };
  }
}

const themeService = new ThemeService();

export default themeService;
