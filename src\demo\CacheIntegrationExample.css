.cache-example-app {
  min-height: 100vh;
  background: var(--theme-bg-primary, #ffffff);
  color: var(--theme-text-primary, #1a1a1a);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.app-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background: var(--theme-bg-card, #f8f9fa);
  border-bottom: 1px solid var(--theme-border-primary, #e9ecef);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--theme-primary, #007bff);
}

.header-controls {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-controls label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-weight: 500;
}

.header-controls select {
  padding: 0.25rem 0.5rem;
  border: 1px solid var(--theme-border-primary, #ced4da);
  border-radius: 4px;
  background: var(--theme-input-bg, #ffffff);
  color: var(--theme-input-text, #495057);
}

.header-controls button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--theme-primary, #007bff);
  border-radius: 4px;
  background: transparent;
  color: var(--theme-primary, #007bff);
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.header-controls button:hover {
  background: var(--theme-primary, #007bff);
  color: white;
}

.header-controls button.active {
  background: var(--theme-primary, #007bff);
  color: white;
}

.app-main {
  padding: 2rem;
}

.content-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  margin-bottom: 2rem;
}

/* User Profile Styles */
.user-profile {
  background: var(--theme-bg-card, #ffffff);
  border: 1px solid var(--theme-border-primary, #e9ecef);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--theme-border-primary, #e9ecef);
}

.avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  border: 2px solid var(--theme-primary, #007bff);
}

.profile-info {
  flex: 1;
}

.profile-info h2 {
  margin: 0 0 0.25rem 0;
  font-size: 1.25rem;
  color: var(--theme-text-primary, #1a1a1a);
}

.profile-info p {
  margin: 0.25rem 0;
  color: var(--theme-text-secondary, #6c757d);
  font-size: 0.875rem;
}

.stale-indicator {
  display: inline-block;
  padding: 0.125rem 0.5rem;
  background: var(--color-warning-100, #fff3cd);
  color: var(--color-warning-800, #856404);
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.refresh-btn {
  padding: 0.5rem 1rem;
  border: 1px solid var(--theme-primary, #007bff);
  border-radius: 4px;
  background: var(--theme-primary, #007bff);
  color: white;
  cursor: pointer;
  font-size: 0.875rem;
  transition: background-color 0.2s ease;
}

.refresh-btn:hover {
  background: var(--theme-primary-hover, #0056b3);
}

.preferences-section h3 {
  margin: 0 0 1rem 0;
  color: var(--theme-text-primary, #1a1a1a);
}

.preferences-grid {
  display: grid;
  gap: 1rem;
}

.preferences-grid label {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  font-weight: 500;
  color: var(--theme-text-primary, #1a1a1a);
}

.preferences-grid select,
.preferences-grid input[type="checkbox"] {
  padding: 0.5rem;
  border: 1px solid var(--theme-border-primary, #ced4da);
  border-radius: 4px;
  background: var(--theme-input-bg, #ffffff);
  color: var(--theme-input-text, #495057);
}

.preferences-grid input[type="checkbox"] {
  width: auto;
  margin-right: 0.5rem;
}

/* Product List Styles */
.product-list {
  background: var(--theme-bg-card, #ffffff);
  border: 1px solid var(--theme-border-primary, #e9ecef);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.product-list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--theme-border-primary, #e9ecef);
}

.product-list-header h2 {
  margin: 0;
  color: var(--theme-text-primary, #1a1a1a);
}

.product-controls {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.product-controls select,
.product-controls button {
  padding: 0.375rem 0.75rem;
  border: 1px solid var(--theme-border-primary, #ced4da);
  border-radius: 4px;
  font-size: 0.875rem;
  cursor: pointer;
}

.product-controls select {
  background: var(--theme-input-bg, #ffffff);
  color: var(--theme-input-text, #495057);
}

.product-controls button {
  background: var(--theme-button-secondary-bg, #6c757d);
  color: white;
  border-color: var(--theme-button-secondary-bg, #6c757d);
  transition: background-color 0.2s ease;
}

.product-controls button:hover {
  background: var(--theme-button-secondary-hover, #545b62);
}

.stale-notice {
  padding: 0.5rem 1rem;
  background: var(--color-info-100, #d1ecf1);
  color: var(--color-info-800, #0c5460);
  border-radius: 4px;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  text-align: center;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.product-card {
  background: var(--theme-bg-secondary, #f8f9fa);
  border: 1px solid var(--theme-border-primary, #e9ecef);
  border-radius: 6px;
  padding: 1rem;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.product-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.product-card h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  color: var(--theme-text-primary, #1a1a1a);
}

.product-card p {
  margin: 0.25rem 0;
  font-size: 0.875rem;
  color: var(--theme-text-secondary, #6c757d);
}

/* Cache Analytics Panel */
.cache-analytics-panel {
  background: var(--theme-bg-card, #ffffff);
  border: 1px solid var(--theme-border-primary, #e9ecef);
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.cache-analytics-panel h2 {
  margin: 0 0 1rem 0;
  color: var(--theme-text-primary, #1a1a1a);
}

.analytics-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.metric {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem;
  background: var(--theme-bg-secondary, #f8f9fa);
  border-radius: 6px;
  border: 1px solid var(--theme-border-primary, #e9ecef);
}

.metric label {
  font-weight: 500;
  color: var(--theme-text-secondary, #6c757d);
  font-size: 0.875rem;
}

.metric span {
  font-weight: 600;
  font-size: 1rem;
}

.metric span.good {
  color: var(--color-success-600, #198754);
}

.metric span.warning {
  color: var(--color-warning-600, #ffc107);
}

.metric span.error {
  color: var(--color-error-600, #dc3545);
}

.analytics-actions {
  display: flex;
  gap: 0.5rem;
}

.analytics-actions button {
  padding: 0.5rem 1rem;
  border: 1px solid var(--theme-primary, #007bff);
  border-radius: 4px;
  background: transparent;
  color: var(--theme-primary, #007bff);
  cursor: pointer;
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.analytics-actions button:hover {
  background: var(--theme-primary, #007bff);
  color: white;
}

/* Loading and Error States */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  color: var(--theme-text-secondary, #6c757d);
  font-style: italic;
}

.loading::before {
  content: '';
  width: 20px;
  height: 20px;
  border: 2px solid var(--theme-border-primary, #e9ecef);
  border-top-color: var(--theme-primary, #007bff);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

.error {
  padding: 1rem;
  background: var(--color-error-50, #f8d7da);
  color: var(--color-error-800, #721c24);
  border: 1px solid var(--color-error-200, #f5c6cb);
  border-radius: 4px;
  text-align: center;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .app-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .header-controls {
    justify-content: center;
  }

  .content-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .app-main {
    padding: 1rem;
  }

  .profile-header {
    flex-direction: column;
    text-align: center;
  }

  .product-list-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .product-controls {
    justify-content: center;
  }

  .products-grid {
    grid-template-columns: 1fr;
  }

  .analytics-summary {
    grid-template-columns: 1fr;
  }

  .analytics-actions {
    flex-direction: column;
  }
}
