@import './base/reset.css';
@import './base/typography.css';
@import './base/utilities.css';

@import './themes/light/theme.css';
@import './themes/dark/theme.css';

@import './responsive/breakpoints.css';
@import './responsive/mobile.css';
@import './responsive/tablet.css';
@import './responsive/desktop.css';

@import './components/buttons.css';
@import './components/forms.css';
@import './components/navigation.css';
@import './components/cards.css';

:root {
  --max-width: 1200px;
  --border-radius: 8px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;

  --transition-fast: 150ms ease;
  --transition-normal: 250ms ease;
  --transition-slow: 350ms ease;

  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal: 1040;
  --z-popover: 1050;
  --z-tooltip: 1060;
}

* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-base);
  line-height: var(--line-height-base);
  color: var(--color-text-primary);
  background-color: var(--color-background);
  transition: background-color var(--transition-normal),
              color var(--transition-normal);
}

/* Responsive typography */
@media (max-width: 768px) {
  html {
    font-size: 14px;
  }
}