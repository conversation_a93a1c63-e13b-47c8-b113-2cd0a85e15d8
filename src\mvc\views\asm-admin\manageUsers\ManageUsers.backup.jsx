import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { <PERSON><PERSON> } from "primereact/button";
import { InputText } from "primereact/inputtext";
import { Badge } from "primereact/badge";
import DateInput from "../../../Components/Common/DateInput";
import FilterBy from "../../../Components/Common/FilterBy";
import { InputSwitch } from "primereact/inputswitch";
import { Paginator } from "primereact/paginator";
import { TabView, TabPanel } from "primereact/tabview";

import { Checkbox } from "primereact/checkbox";
import { Toast } from "primereact/toast";
import userRoleController from "../../../controllers/userRole/userRoleController";
// removed direct axios usage; using controller/service instead
import "../../../styles/glassy-ui.css";
import GlassyWhiteButton from "../../../Components/Common/Buttons/GlassyWhiteButton";

const ManageUsers = () => {
  const navigate = useNavigate();
  const [users, setUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [searchValue, setSearchValue] = useState("");
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [fromDate, setFromDate] = useState(null);
  const [toDate, setToDate] = useState(null);
  const [loading, setLoading] = useState(false);
  const [totalRecords, setTotalRecords] = useState(0);
  const [departmentStats, setDepartmentStats] = useState({});
  const [first, setFirst] = useState(0);
  const [rows, setRows] = useState(10);
  const toast = React.useRef(null);
  const [updatingUserId, setUpdatingUserId] = useState(null);

  // ========================================
  // API METHODS
  // ========================================

  /**
   * Load users from API - ASM Employee Role
   */
  const loadUsers = async (department = "all", search = "") => {
    setLoading(true);
    try {
      let result;

      if (search.trim()) {
        // Search users by term
        result = await userRoleController.searchUsers(search, "asm_employee");
      } else {
        // Get all ASM employees
        result = await userRoleController.getUsersByRole("asm_employee");
      }

      if (result.success) {
        // Validate data structure
        const validatedUsers = Array.isArray(result.data) ? result.data : [];

        setUsers(validatedUsers);
        setTotalRecords(result.total || validatedUsers.length);

        // Compute department stats from returned users so we don't need a second API call
        const stats = {
          sales: 0,
          finance: 0,
          "fuel-supply": 0,
          operational: 0,
          marketing: 0,
          legal: 0,
        };

        validatedUsers.forEach((u) => {
          const deptRaw = String(
            u.department || u.departmentName || u.departmentKey || u.role || ""
          ).toLowerCase();
          if (deptRaw.includes("sales")) stats.sales += 1;
          else if (deptRaw.includes("finance") || deptRaw.includes("account"))
            stats.finance += 1;
          else if (deptRaw.includes("fuel")) stats["fuel-supply"] += 1;
          else if (deptRaw.includes("oper") || deptRaw.includes("operation"))
            stats.operational += 1;
          else if (deptRaw.includes("market")) stats.marketing += 1;
          else if (deptRaw.includes("legal")) stats.legal += 1;
        });

        setDepartmentStats(stats);
      } else {
        console.error("API Error:", result.error);
        toast.current?.show({
          severity: "error",
          summary: "Error",
          detail: result.error || "Failed to load users from API",
        });

        // Set empty data on error
        setUsers([]);
        setTotalRecords(0);
      }
    } catch (error) {
      console.error("Error loading users:", error);
      toast.current?.show({
        severity: "error",
        summary: "Error",
        detail: "Network error: Failed to connect to API",
      });

      // Set empty data on error
      setUsers([]);
      setTotalRecords(0);
    } finally {
      setLoading(false);
    }
  };

  // department stats are derived from users now inside loadUsers

  /**
   * Handle user status update
   * REPLACE THIS: When you have real API, this will call your actual update endpoint
   */
  const handleStatusUpdate = async (userId, newStatusLabel) => {
    if (!userId) return;

    const payloadStatus = newStatusLabel === "Active" ? "ACTIVE" : "INACTIVE";
    try {
      setUpdatingUserId(userId);
      const result = await userRoleController.updateUserStatus(
        userId,
        payloadStatus
      );

      if (result.success) {
        const updatedUsers = users.map((user) =>
          user.id === userId
            ? {
                ...user,
                status: payloadStatus === "ACTIVE",
                statusName: payloadStatus,
              }
            : user
        );
        setUsers(updatedUsers);
        toast.current?.show({
          severity: "success",
          summary: "Success",
          detail: "User status updated",
        });
      } else {
        throw new Error(result.error || "Failed to update status");
      }
    } catch (err) {
      console.error("Error updating user status:", err);
      toast.current?.show({
        severity: "error",
        summary: "Error",
        detail: err.message || "Status update failed",
      });
      // reload users to keep UI consistent
      // loadUsers(selectedFilter, searchValue);
    } finally {
      setUpdatingUserId(null);
    }
  };

  // Department filter options (dynamic based on API data)
  const departmentOptions = [
    { label: `All Employees (${totalRecords || 0})`, value: "all" },
    { label: `Sales (${departmentStats.sales || 0})`, value: "sales" },
    { label: `Finance (${departmentStats.finance || 0})`, value: "finance" },
    {
      label: `Fuel Supply (${departmentStats["fuel-supply"] || 0})`,
      value: "fuel-supply",
    },
    {
      label: `Operational (${departmentStats.operational || 0})`,
      value: "operational",
    },
    {
      label: `Marketing (${departmentStats.marketing || 0})`,
      value: "marketing",
    },
    { label: `Legal (${departmentStats.legal || 0})`, value: "legal" },
  ];

  // Load initial data when component mounts
  useEffect(() => {
    loadUsers();
  }, []);

  // Load users when filter changes (but not on initial mount)
  useEffect(() => {
    // Only reload if filter actually changed and it's not the initial 'all' value
    if (selectedFilter !== "all") {
      console.log("Filter changed, reloading users:", selectedFilter);
      loadUsers(selectedFilter, searchValue);
    }
  }, [selectedFilter]);

  // Search users when search value changes (with debounce)
  useEffect(() => {
    if (searchValue.trim() === "") {
      // If search is cleared, don't make another API call if we already have data
      return;
    }

    const timeoutId = setTimeout(() => {
      console.log("Search triggered:", searchValue);
      loadUsers(selectedFilter, searchValue);
    }, 500); // 500ms debounce

    return () => clearTimeout(timeoutId);
  }, [searchValue]);

  // Template functions
  const checkboxTemplate = (rowData) => {
    return (
      <Checkbox
        checked={selectedUsers.includes(rowData)}
        onChange={(e) => {
          if (e.checked) {
            setSelectedUsers([...selectedUsers, rowData]);
          } else {
            setSelectedUsers(
              selectedUsers.filter((user) => user.id !== rowData.id)
            );
          }
        }}
      />
    );
  };

  const statusTemplate = (rowData) => (
    <InputSwitch
      checked={rowData?.statusName === "ACTIVE" || !!rowData?.status}
      onChange={(e) =>
        handleStatusUpdate(rowData.id, e.value ? "Active" : "Inactive")
      }
      className="glass-toggle"
    />
  );

  const activityTemplate = (rowData) => {
    return (
      <Button
        label="View Logs"
        className="glass-badge"
        onClick={() => console.log("View logs for:", rowData.id)}
      />
    );
  };

  const actionTemplate = (rowData) => {
    return (
      <div className="action-buttons">
        {/* <Button
          icon="pi pi-eye"
          className="p-button-rounded p-button-text"
          onClick={() => {
            // TODO: Implement view user details
            console.log('View user:', rowData.id);
            toast.current?.show({
              severity: 'info',
              summary: 'Info',
              detail: `Viewing details for ${rowData.firstName} ${rowData.lastName}`
            });
          }}
          tooltip="View User"
        /> */}

        <Button
          icon="pi pi-pencil"
          className="glass-btn-icon"
          onClick={() => {
            // TODO: Implement edit user functionality
            console.log("Edit user:", rowData.id);
            toast.current?.show({
              severity: "info",
              summary: "Info",
              detail: `Editing ${rowData.firstName} ${rowData.lastName}`,
            });
          }}
          tooltip="Edit User"
        />
        {/* <Button
          icon="pi pi-trash"
          className="p-button-rounded p-button-text"
          onClick={() => {
            if (window.confirm(`Are you sure you want to delete ${rowData.firstName} ${rowData.lastName}?`)) {
              handleDeleteUser(rowData.id);
            }
          }}
          tooltip="Delete User"
        /> */}
      </div>
    );
  };

  return (
    <div className="glassy-page">
      <Toast ref={toast} />
      {/* Page Header */}
      <div className="glass-header">
        <div className="glass-header-left">
          <Button
            icon="pi pi-arrow-left"
            className="glass-btn-text"
            onClick={() => window.history.back()}
          />
          <span
            style={{ color: "#193276", fontSize: "14px", fontWeight: "400" }}
          >
            Dashboard / <span>Manage Users</span>
          </span>
        </div>
        {/* <div className="glass-header-right">
          <Button icon="pi pi-bell" className="glass-btn-text" />
          <Button icon="pi pi-cog" className="glass-btn-text" />
        </div> */}
      </div>

      {/* Content Above Table */}
      <div className="glass-content-section">
        {/* Controls */}
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "20px",
            gap: "16px",
            flexWrap: "wrap",
          }}
        >
          <div
            style={{
              display: "flex",
              alignItems: "center",
              gap: "16px",
              flexWrap: "wrap",
            }}
          >
            <div className="glass-search-container">
              <i className="pi pi-search glass-search-icon"></i>
              <InputText
                placeholder="Search using title/name"
                value={searchValue}
                onChange={(e) => setSearchValue(e.target.value)}
                className="glass-search-input"
              />
            </div>
            <div className="glass-dropdown">
              <FilterBy
                value={null}
                options={[
                  { label: "All Users", value: "all" },
                  { label: "Active", value: "active" },
                  { label: "Inactive", value: "inactive" },
                  { label: "Recent", value: "recent" },
                ]}
                placeholder="Filter by"
              />
            </div>
            <DateInput
              fromDate={fromDate}
              toDate={toDate}
              onFromDateChange={setFromDate}
              onToDateChange={setToDate}
            />
          </div>
          <div style={{ display: "flex", gap: "12px" }}>
            {/* <Button
              label="Add Employee"
              icon="pi pi-plus"
              className="glass-btn-primary"
              onClick={() => navigate('/manage-users/add-employee')}
            /> */}
            <GlassyWhiteButton label="Export" icon="pi pi-download" />
            <GlassyWhiteButton 
              label="Import"
              icon="pi pi-upload"
              className="glass-btn-secondary"
              onClick={() => navigate("/manage-users/add-user")}
            />
          </div>
        </div>

        {/* Department Filter Tabs */}
        <div className="glass-tabview">
          <TabView
            activeIndex={activeTabIndex}
            onTabChange={(e) => {
              console.log("Tab changed:", e.index, departmentOptions[e.index]);
              if (e.index === 0) {
                const selectedDept = departmentOptions[e.index];
                setSelectedFilter(selectedDept.value);
                loadUsers(selectedDept.value, searchValue);
              } else {
                setUsers([]);
              }
              setActiveTabIndex(e.index);
            }}
            className="custom-department-tabview"
          >
            {departmentOptions.map((dept) => (
              <TabPanel
                key={dept.value}
                header={dept.label}
                className="custom-tab-panel"
              >
                {/* Tab content can be empty as we're just using tabs for filtering */}
              </TabPanel>
            ))}
          </TabView>
        </div>
      </div>

      {/* Table Section */}
      <div className="glass-table-container">
        {loading && (
          <div
            style={{
              display: "flex",
              flexDirection: "column",
              alignItems: "center",
              justifyContent: "center",
              padding: "2rem",
              color: "#666",
              background: "rgba(255, 255, 255, 0.9)",
              borderRadius: "8px",
              marginBottom: "1rem",
            }}
          >
            <i
              className="pi pi-spin pi-spinner"
              style={{ fontSize: "2rem" }}
            ></i>
            <p style={{ margin: "0.5rem 0 0 0", fontSize: "1rem" }}>
              Loading users...
            </p>
          </div>
        )}

        <DataTable
          value={users}
          className="glass-table"
          loading={loading}
          emptyMessage={
            <div
              style={{
                display: "flex",
                flexDirection: "column",
                alignItems: "center",
                justifyContent: "center",
                padding: "3rem",
                color: "#666",
              }}
            >
              <i
                className="pi pi-users"
                style={{ fontSize: "3rem", color: "#ccc" }}
              ></i>
              <p
                style={{
                  margin: "1rem 0 0.5rem 0",
                  fontSize: "1.1rem",
                  fontWeight: "500",
                }}
              >
                No users found
              </p>
              <small style={{ color: "#999", fontSize: "0.9rem" }}>
                Try adjusting your search criteria or filters
              </small>
            </div>
          }
          showGridlines
          stripedRows
        >
          <Column body={checkboxTemplate} style={{ width: "3rem" }} />
          <Column field="employeeCode" header="Employee ID" sortable />
          <Column field="firstName" header="First Name" sortable />
          <Column field="lastName" header="Last Name" sortable />
          <Column field="role" header="Role" sortable />
          <Column field="email" header="Email" sortable />
          <Column field="lastLoginDate" header="Last Login Date" sortable />
          <Column header="Activity" body={activityTemplate} />
          <Column header="Status" body={statusTemplate} />
          <Column header="Action" body={actionTemplate} />
        </DataTable>

        {/* Pagination */}
        <Paginator
          first={first}
          rows={rows}
          totalRecords={totalRecords}
          onPageChange={(e) => {
            setFirst(e.first);
            setRows(e.rows);
          }}
          template="PrevPageLink PageLinks NextPageLink"
          className="glass-paginator"
        />
      </div>
    </div>
  );
};

export default ManageUsers;
