import UserService from "@services/user/userService";

const getUsersByRole = async (roleName = "asm_employee") => {
  try {
    const result = await UserService.getUsersByRole(roleName);
    console.log("getUsersByRole result:", result);
    if (result.success) {
      const transformedUsers = transformUsersData(result.data);
      result.data = transformedUsers;
    }
    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

const transformUsersData = (apiData) => {
  apiData = apiData?.users || [];
  if (!Array.isArray(apiData)) {
    return [];
  }
  return apiData.map((item, index) => {
    const user = item.user || {};

    return {
      id: item.userId,
      employeeCode:  item.userId,
      firstName: '-',
      lastName: '-',
      email: user.username || "-",
      role: item.roleKey || "-",
      status: user.statusName === "ACTIVE",
      statusName: user.statusName || "INACTIVE",
      activity: "View Logs",
      // lastLoginDate: user.lastModifiedDate || "-",
      // department: getDepartmentFromRole(item.roleName),
    };
  });
};

const searchUsers = async (searchTerm, roleName = "asm_employee") => {
  try {
    if (!searchTerm || searchTerm.trim() === "") {
      // If no search term, return all users
      return await getUsersByRole(roleName);
    }

    const result = await UserService.searchUsers(searchTerm.trim(), roleName);

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

const updateUserStatus = async (userId, newStatus) => {
  try {
    const result = await UserService.updateUserStatus(userId, newStatus);
    return result;
  } catch (error) {
    return { success: false, error: error.message };
  }
};

/**
 * Get all employee details
 * @returns {Promise} - Promise resolving to transformed employee data
 */
const getEmployeeDetails = async () => {
  try {
    const result = await UserService.getEmployeeDetails();
    console.log("getEmployeeDetails result:", result);
    if (result.success) {
      const transformedEmployees = transformEmployeeData(result.data);
      result.data = transformedEmployees;
    }
    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

/**
 * Transform employee API data to match the expected format
 * @param {Array} apiData - Raw API response data
 * @returns {Array} - Transformed employee data
 */
const transformEmployeeData = (apiData) => {
  if (!Array.isArray(apiData)) {
    return [];
  }

  return apiData.map((employee) => {
    let email = employee.email.string || employee.email;
    if (email.endsWith('@redberyltech.com')) {
        email = email.replace('@redberyltech.com', '@gmail.com');
    }
    return {
      id: employee.id || employee.userId,
      employeeCode: employee.userId || `EMP-${employee.id}`,
      firstName: employee.firstName || "-",
      lastName: employee.lastName || "-",
      email: email || "-",
      role: "-", // Default role since not provided in API
      status: employee.employeeStatusId === 1, // Assuming 1 means active
      statusName: employee.employeeStatusId === 1 ? "ACTIVE" : "INACTIVE",
      activity: "View Logs",
      phone: employee.phone || "-",
      userId: employee.userId,
      employeeId: employee.employeeId,
      createdDate: employee.createdDate,
    };
  });
};

/**
 * Search employee details by term
 * @param {string} searchTerm - Search term
 * @returns {Promise} - Promise resolving to filtered employee data
 */
const searchEmployeeDetails = async (searchTerm) => {
  try {
    if (!searchTerm || searchTerm.trim() === "") {
      // If no search term, return all employees
      return await getEmployeeDetails();
    }

    const result = await UserService.searchEmployeeDetails(searchTerm.trim());
    
    if (result.success) {
      const transformedEmployees = transformEmployeeData(result.data);
      result.data = transformedEmployees;
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message,
      data: [],
      total: 0,
    };
  }
};

const userController = {
  getUsersByRole,
  searchUsers,
  updateUserStatus,
  getEmployeeDetails,
  searchEmployeeDetails,
};

export default userController;
