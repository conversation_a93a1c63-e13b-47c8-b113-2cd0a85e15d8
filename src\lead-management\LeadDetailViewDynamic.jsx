import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Steps } from 'primereact/steps';
import { TabView, TabPanel } from 'primereact/tabview';
import { <PERSON><PERSON> } from 'primereact/button';
import { Toast } from 'primereact/toast';
import { Card } from 'primereact/card';
import { Dropdown } from 'primereact/dropdown';
import DynamicComponent from '../components/framework/core/DynamicComponent';
import componentRegistry from '../components/framework/core/ComponentRegistry';
import ContactInformationTab from './tabs/ContactInformationTab';
import AddressTab from '../crm/tabs/AddressTab';
import ContactsTab from '../crm/tabs/ContactsTab';
import ComplianceInformationTab from '../crm/tabs/ComplianceTabs'
// import ComplianceInformationTab from './tabs/ComplianceInformationTab';
import NotesMessagesTab from './tabs/NotesMessagesTab';
import ActivityLogsTab from '../crm/tabs/ActivityLogTab';
import DocumentationTab from '../crm/tabs/DocumentationTab';
import '../styles/glassy/glassy-ui.css'
import'../styles/glassy/global.css'
import GlassyTabs from '../components/common/GlassyTabs';
import Paginator from '../components/common/Paginator';
import GlassyBlueButton from '../components/common/GlassyBlueButton';
import GlassyWhiteButton from '../components/common/GlassyWhiteButton';

const LeadDetailViewDynamic = () => {
  const { leadId } = useParams();
  const navigate = useNavigate();
  const toast = useRef(null);
  const [activeStep, setActiveStep] = useState(0);
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [activeActionTab, setActiveActionTab] = useState(0);

  useEffect(() => {
    if (!componentRegistry.initialized) {
      componentRegistry.init();
    }
  }, []);

  const [formData, setFormData] = useState({
    company: 'Mahan Air',
    contact: 'John Doe',
    email: '<EMAIL>',
    phone: '+98 21 4884 5000',
    // rating: 2,
    opportunity: '1 Ton Jet Fuel',
    expectedRevenue: '$7000',
    priority: 'Medium',
    leadSource: 'Event',
    tags: '',
    salesPerson: 'Jessica Marlowe',
    leadStatus: 'Introduction',
    blacklistLead: 'No',
    reason: ''
  });

  // Define tabs for GlassyTabs component
  const tabs = [
    { label: 'Address', value: 'address', icon: 'pi pi-map-marker' },
    { label: 'Contacts', value: 'contacts', icon: 'pi pi-users' },
    { label: 'Compliance Information', value: 'compliance', icon: 'pi pi-shield' },
    { label: 'Notes & Messages', value: 'notes', icon: 'pi pi-comments' },
    { label: 'Activity Logs', value: 'activity', icon: 'pi pi-history' },
    { label: 'Documentation', value: 'documentation', icon: 'pi pi-file' }
  ];

  // Define main action tabs
  const mainActionTabs = [
    { label: 'Edit', value: 'edit', icon: 'pi pi-pencil' },
    { label: 'Enrich Data', value: 'enrich', icon: 'pi pi-database' },
    { label: 'Create Opportunity', value: 'opportunity', icon: 'pi pi-plus' },
    { label: 'Mark as Won', value: 'won', icon: 'pi pi-check' },
    { label: 'Mark as Lost', value: 'lost', icon: 'pi pi-times' },
    { label: 'Convert to Prospect', value: 'convert', icon: 'pi pi-arrow-right' }
  ];

  const secondaryActionTabs = [
    { label: 'Activities 3/5', value: 'activities', icon: 'pi pi-clock' },
    { label: 'Delete', value: 'delete', icon: 'pi pi-trash' }
  ];

  const handleActionClick = (actionValue) => {
    switch(actionValue) {
      case 'edit':
        showToast('info', 'Edit Mode', 'Editing lead information');
        break;
      case 'enrich':
        showToast('info', 'Enrich Data', 'Enriching lead data...');
        break;
      case 'opportunity':
        showToast('success', 'Opportunity Created', 'Opportunity has been created successfully');
        break;
      case 'won':
        showToast('success', 'Marked as Won', 'Lead has been marked as won. Converting to prospect...');
        setTimeout(() => navigate('/prospects'), 1500);
        break;
      case 'lost':
        showToast('warn', 'Marked as Lost', 'Lead has been marked as lost');
        break;
      case 'convert':
        handleConvertToProspect();
        break;
      case 'activities':
        showToast('info', 'Activities', 'View activities');
        break;
      case 'delete':
        showToast('warn', 'Delete Lead', 'Are you sure you want to delete this lead?');
        // Handle delete action
        break;
      default:
        break;
    }
  };

// inside LeadDetailViewDynamic component:
const itemRenderer = (item, itemIndex) => {
  const isActiveItem = activeStep === itemIndex;

  return (
    <div
      className="flex flex-column align-items-center cursor-pointer stepper-item"
      onClick={() => setActiveStep(itemIndex)}
    >
      {/* Circle with icon */}
      <span
        className={`stepper-circle ${
          isActiveItem ? 'stepper-circle--active' : 'stepper-circle--inactive'
        }`}
      >
        <i className={`${item.icon} text-xl`} />
      </span>

      {/* Label below circle */}
      <span
        className={`mt-2 text-sm ${
          isActiveItem ? 'stepper-label--active' : 'stepper-label--inactive'
        }`}
      >
        {item.label}
      </span>
    </div>
  );
};
  
const steps = [
  { icon: 'pi pi-user', label: 'New Lead', template: (item) => itemRenderer(item, 0) },
  { icon: 'pi pi-briefcase', label: 'Opportunity', template: (item) => itemRenderer(item, 1) },
  { icon: 'pi pi-check-circle', label: 'Won/Lost', template: (item) => itemRenderer(item, 2) }
];

  

  const showToast = useCallback((severity, summary, detail) => {
    toast.current?.show({ severity, summary, detail, life: 3000 });
  }, []);

  const handleFormChange = useCallback((data) => {
    setFormData(prev => ({ ...prev, ...data }));
  }, []);

  const handleFormSubmit = useCallback((data) => {
    console.log('Form submitted:', data);
    showToast('success', 'Saved', 'Lead information saved successfully');
  }, [showToast]);

  const handleFormError = useCallback((error) => {
    console.error('Form error:', error);
    showToast('error', 'Form Error', error.message || 'An error occurred');
  }, [showToast]);

  const handleConvertToProspect = useCallback(() => {
    showToast('success', 'Converting to Prospect', 'Lead is being converted to prospect...');
    setTimeout(() => navigate('/prospects'), 1500);
  }, [navigate, showToast]);

  const generalFields = [
    {
      name: 'company',
      type: 'input-text',
      label: 'Company',
      required: true,
      props: { 
        placeholder: 'Enter company name',
        maxLength: 100
      }
    },
    {
      name: 'contact',
      type: 'input-text',
      label: 'Contact',
      required: true,
      props: { 
        placeholder: 'Enter contact name',
        maxLength: 100
      }
    },
    {
      name: 'email',
      type: 'input-text',
      label: 'Email',
      required: true,
      props: { 
        placeholder: 'Enter email address',
        keyfilter: 'email'
      }
    },
    {
      name: 'phone',
      type: 'input-text',
      label: 'Phone',
      props: { 
        placeholder: 'Enter phone number'
      }
    },
    {
      name: 'opportunity',
      type: 'input-text',
      label: 'Opportunity',
      props: { 
        placeholder: 'Enter opportunity'
      }
    },
    {
      name: 'expectedRevenue',
      type: 'input-text',
      label: 'Expected Revenue',
      props: { 
        placeholder: 'Enter expected revenue'
      }
    },
    {
      name: 'priority',
      type: 'dropdown',
      label: 'Priority',
      props: {
        options: [
          { label: 'Low', value: 'Low' },
          { label: 'Medium', value: 'Medium' },
          { label: 'High', value: 'High' }
        ],
        placeholder: 'Select priority'
      }
    },
    {
      name: 'leadSource',
      type: 'dropdown',
      label: 'Lead Source',
      props: {
        options: [
          { label: 'Event', value: 'Event' },
          { label: 'Website', value: 'Website' },
          { label: 'Referral', value: 'Referral' },
          { label: 'Cold Call', value: 'Cold Call' }
        ],
        placeholder: 'Select lead source'
      }
    },
    {
      name: 'tags',
      type: 'input-text',
      label: 'Tags',
      props: { 
        placeholder: 'Enter tags'
      }
    },
    {
      name: 'salesPerson',
      type: 'dropdown',
      label: 'Sales Person',
      props: {
        options: [
          { label: 'John Doe', value: 'John Doe' },
          { label: 'Jane Smith', value: 'Jane Smith' },
          { label: 'Mike Johnson', value: 'Mike Johnson' }
        ],
        placeholder: 'Select sales person'
      }
    },
    {
      name: 'leadStatus',
      type: 'dropdown',
      label: 'Lead Status',
      props: {
        options: [
          { label: 'Introduction', value: 'Introduction' },
          { label: 'Qualification', value: 'Qualification' },
          { label: 'Proposal', value: 'Proposal' },
          { label: 'Negotiation', value: 'Negotiation' }
        ],
        placeholder: 'Select lead status'
      }
    },
    {
      name: 'blacklistLead',
      type: 'dropdown',
      label: 'Blacklist Lead',
      props: {
        options: [
          { label: 'Yes', value: 'Yes' },
          { label: 'No', value: 'No' }
        ],
        placeholder: 'Select option'
      }
    },
    {
      name: 'reason',
      type: 'textarea',
      label: 'Reason',
      props: { 
        placeholder: 'Enter reason',
        rows: 3,
        autoResize: true
      }
    },
    {
      name: 'rating',
      type: 'rating',
      label: 'Ratings',
      props: { 
        stars: 5,
        cancel: false
      }
    }
  ];

  return (
    <div className="">
    <Toast ref={toast} />
    
    {/* Header - Keep it outside */}
    <div className="lead-header glass-header">
      <div className="lead-breadcrumb">
        <Button 
          icon="pi pi-arrow-left" 
          text 
          className="p-button-sm" 
          onClick={() => navigate('/')}
        />
        <span className="breadcrumb-text">
          Pipeline / {formData.company} - {leadId}
        </span>
      </div>
      <div className="company-selector">
        <label>Company</label>
        <Dropdown 
        value="Aviation Services Management, Dubai" 
        options={[{ label: 'Aviation Services Management, Dubai', value: 'Aviation Services Management, Dubai' }]}
        className="company-dropdown"
        panelClassName="glass-dropdown-panel"
      />
      </div>
    </div>
  
    {/* New wrapper div for the rest */}
    <div className="p-4">
      <div className="">
        <Steps 
          model={steps} 
          activeIndex={activeStep} 
          onSelect={(e) => setActiveStep(e.index)}
          readOnly={false}
        />
      </div>
  
      <div className="glass-card-global mt-6 p-5" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: '0.5rem', flexWrap: 'wrap' }}>
        <div style={{ display: 'flex', gap: '0.5rem', flex: '1 1 auto', flexWrap: 'wrap' }}>
          <GlassyBlueButton
            label="Edit"
            onClick={() => handleActionClick('edit')}
          />
          <GlassyBlueButton
            label="Enrich Data"
            onClick={() => handleActionClick('enrich')}
          />
          <GlassyBlueButton
            label="Create Opportunity"
            onClick={() => handleActionClick('opportunity')}
          />
          <GlassyBlueButton
            label="Mark as Won"
            onClick={() => handleActionClick('won')}
          />
          <GlassyBlueButton
            label="Mark as Lost"
            onClick={() => handleActionClick('lost')}
          />
        </div>
        <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
          <span style={{ color: '#6B7280', fontSize: '14px', cursor: 'pointer' }} onClick={() => handleActionClick('activities')}>
            <i className="pi pi-clock" style={{ marginRight: '4px' }}></i>
            Activities 3/5
          </span>
          <Button
            icon="pi pi-trash"
            className="p-button-text p-button-danger"
            onClick={() => handleActionClick('delete')}
            style={{ color: '#EF4444' }}
          />
        </div>
      </div>
      
      <div  className="glass-card-doc-management p-4" style={{ marginTop: '1.5rem', borderRadius: '12px', position: 'relative', zIndex: 100, color: 'black' }}>
      <h2 className='p-card-title color-gray-500'>General</h2>
        <DynamicComponent
          type="form"
          config={{
            config: {
              fields: generalFields,
              layout: { 
                columns: 3,
                showProgress: false,
                showResetButton: true,
                submitButtonText: 'Save Lead'
              }
            },
            initialData: formData,
            onChange: handleFormChange,
            onSubmit: handleFormSubmit,
            onError: handleFormError
          }}
          debug={false}
        />
      </div>
  
     <div className="glass-card-doc-management p-4" style={{ marginTop: '1.5rem', position: 'relative', zIndex: 1 }}>
  {/* Tabs using GlassyTabs Component */}
  <GlassyTabs
    tabs={tabs}
    activeIndex={activeTabIndex}
    onTabChange={(index, tab) => setActiveTabIndex(index)}
    showBadges={false}
    hidePanels={true}
    className="w-full"
  />

  {/* Tabs Content */}
  <div className="tab-content p-4 rounded-xl">
  {activeTabIndex === 0 && (
      <div className="tab-panel  rounded-xl">
        <AddressTab />
      </div>
    )}
      {activeTabIndex === 1 && (
      <div className="tab-panel  rounded-xl">
        <ContactsTab />
      </div>
    )}
    {activeTabIndex === 2 && (
      <div className="tab-panel  rounded-xl">
        <ComplianceInformationTab />
      </div>
    )}
    {activeTabIndex === 3 && (
      <div className="tab-panel  rounded-xl">
        <NotesMessagesTab />
      </div>
    )}
    {activeTabIndex === 4 && (
      <div className="tab-panel rounded-xl">
        <ActivityLogsTab />
      </div>
    )}
    {activeTabIndex === 5 && (
      <div className="tab-panel rounded-xl">
        <DocumentationTab />
      </div>
    )}
  </div>
    </div>

    </div>
  </div>
  
  );
};

export default LeadDetailViewDynamic;

