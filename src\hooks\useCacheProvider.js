import React, { createContext, useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import cacheController from '../mvc/controllers/CacheController';
import { loadPersistedCaches } from '../store/middleware/persistenceMiddleware';

const CacheContext = createContext(null);

export const CacheProvider = ({ children, config = {} }) => {
  const dispatch = useDispatch();
  const [isInitialized, setIsInitialized] = useState(false);
  const [error, setError] = useState(null);
  const [cacheStats, setCacheStats] = useState(null);

  const {
    autoCleanup = true,
    monitoring = true,
    persistence = true,
    cleanupInterval = 5 * 60 * 1000, 
    monitoringInterval = 30 * 1000, 
    preloadCommonData = true
  } = config;

  useEffect(() => {
    const initializeCache = async () => {
      try {
        await cacheController.initialize();

        if (persistence) {
          await loadPersistedCaches({ dispatch });
        }

        cacheController.updateConfig({
          autoCleanupEnabled: autoCleanup,
          monitoringEnabled: monitoring,
          cleanupInterval,
          monitoringInterval
        });

        if (preloadCommonData) {
          await preloadCommonCacheData();
        }

        const unsubscribe = cacheController.addEventListener('statsRefreshed', (data) => {
          setCacheStats(data);
        });

        setIsInitialized(true);

        return () => {
          unsubscribe();
        };

      } catch (error) {
        console.error('Failed to initialize cache:', error);
        setError(error.message);
      }
    };

    initializeCache();
  }, [dispatch, autoCleanup, monitoring, persistence, cleanupInterval, monitoringInterval, preloadCommonData]);

  const preloadCommonCacheData = async () => {
    try {
      const commonThemes = ['light', 'dark'];
      for (const theme of commonThemes) {
        await cacheController.set('themeCache', `theme:${theme}:config`, {
          name: theme,
          type: 'built-in',
          preloaded: true
        }, {
          ttl: 24 * 60 * 60 * 1000,
          tags: ['theme', 'preload']
        });
      }

      const commonLocales = ['en', 'es'];
      for (const locale of commonLocales) {
        await cacheController.set('i18nCache', `i18n:${locale}:metadata`, {
          locale,
          direction: locale === 'ar' ? 'rtl' : 'ltr',
          preloaded: true
        }, {
          ttl: 60 * 60 * 1000, 
          tags: ['i18n', 'preload']
        });
      }

      console.log('[Cache] Common data preloaded');
    } catch (error) {
      console.error('[Cache] Failed to preload common data:', error);
    }
  };

  const contextValue = {
    isInitialized,
    error,
    cacheStats,
    cacheController,
    config: {
      autoCleanup,
      monitoring,
      persistence,
      cleanupInterval,
      monitoringInterval,
      preloadCommonData
    }
  };

  return (
    <CacheContext.Provider value={contextValue}>
      {children}
    </CacheContext.Provider>
  );
};

export const useCacheContext = () => {
  const context = useContext(CacheContext);
  
  if (!context) {
    throw new Error('useCacheContext must be used within a CacheProvider');
  }
  
  return context;
};

export const useCacheConfig = () => {
  const { cacheController, config } = useCacheContext();
  const [currentConfig, setCurrentConfig] = useState(config);

  const updateConfig = (newConfig) => {
    const updatedConfig = { ...currentConfig, ...newConfig };
    cacheController.updateConfig(updatedConfig);
    setCurrentConfig(updatedConfig);
  };

  const resetConfig = () => {
    const defaultConfig = {
      autoCleanup: true,
      monitoring: true,
      persistence: true,
      cleanupInterval: 5 * 60 * 1000,
      monitoringInterval: 30 * 1000,
      preloadCommonData: true
    };
    
    cacheController.updateConfig(defaultConfig);
    setCurrentConfig(defaultConfig);
  };

  return {
    config: currentConfig,
    updateConfig,
    resetConfig
  };
};

export const useCacheHealth = () => {
  const { cacheController } = useCacheContext();
  const [healthStatus, setHealthStatus] = useState({
    status: 'unknown',
    issues: [],
    lastCheck: null
  });

  useEffect(() => {
    const unsubscribe = cacheController.addEventListener('healthIssues', (data) => {
      setHealthStatus({
        status: data.issues.length > 0 ? 'warning' : 'healthy',
        issues: data.issues,
        lastCheck: Date.now()
      });
    });

    const performHealthCheck = async () => {
      try {
        const stats = await cacheController.refreshStats();
        const analytics = stats.analytics;
        
        const issues = [];
        
        if (analytics && analytics.summary.hitRate < 70) {
          issues.push({
            type: 'performance',
            severity: 'warning',
            message: `Low cache hit rate: ${analytics.summary.hitRate.toFixed(1)}%`
          });
        }
        
        if (analytics && analytics.summary.errorRate > 5) {
          issues.push({
            type: 'reliability',
            severity: 'error',
            message: `High error rate: ${analytics.summary.errorRate.toFixed(1)}%`
          });
        }
        
        setHealthStatus({
          status: issues.length > 0 ? 'warning' : 'healthy',
          issues,
          lastCheck: Date.now()
        });
      } catch (error) {
        setHealthStatus({
          status: 'error',
          issues: [{ type: 'system', severity: 'error', message: error.message }],
          lastCheck: Date.now()
        });
      }
    };

    performHealthCheck();

    return unsubscribe;
  }, [cacheController]);

  const performHealthCheck = async () => {
    try {
      await cacheController.refreshStats();
    } catch (error) {
      console.error('Health check failed:', error);
    }
  };

  return {
    healthStatus,
    performHealthCheck
  };
};

export const useCachePerformance = () => {
  const { cacheController } = useCacheContext();
  const [performance, setPerformance] = useState({
    hitRate: 0,
    missRate: 0,
    errorRate: 0,
    averageResponseTime: 0,
    totalRequests: 0,
    uptime: 0
  });

  useEffect(() => {
    const updatePerformance = async () => {
      try {
        const analytics = await cacheController.getAnalytics();
        
        if (analytics && analytics.summary) {
          setPerformance({
            hitRate: analytics.summary.hitRate || 0,
            missRate: analytics.summary.missRate || 0,
            errorRate: analytics.summary.errorRate || 0,
            averageResponseTime: analytics.summary.averageResponseTime || 0,
            totalRequests: analytics.summary.totalRequests || 0,
            uptime: analytics.summary.uptime || 0
          });
        }
      } catch (error) {
        console.error('Failed to update performance metrics:', error);
      }
    };

    updatePerformance();

    const interval = setInterval(updatePerformance, 30000);

    return () => clearInterval(interval);
  }, [cacheController]);

  return performance;
};

export const useCacheStorage = () => {
  const { cacheController } = useCacheContext();
  const [storageInfo, setStorageInfo] = useState({
    totalSize: 0,
    availableSpace: 0,
    usagePercentage: 0,
    caches: {}
  });

  useEffect(() => {
    const updateStorageInfo = async () => {
      try {
        const storageStats = await cacheController.getStorageStats();
        
        if (storageStats) {
          const quota = storageStats.quota;
          const totalSize = storageStats.totalSize;
          const availableSpace = quota ? quota.quota - quota.usage : 0;
          const usagePercentage = quota ? (quota.usage / quota.quota) * 100 : 0;
          
          setStorageInfo({
            totalSize,
            availableSpace,
            usagePercentage,
            caches: storageStats.caches || {}
          });
        }
      } catch (error) {
        console.error('Failed to update storage info:', error);
      }
    };

    updateStorageInfo();

    const interval = setInterval(updateStorageInfo, 60000);

    return () => clearInterval(interval);
  }, [cacheController]);

  const clearStorage = async (cacheType = null) => {
    try {
      await cacheController.clear(cacheType);
      setTimeout(async () => {
        const storageStats = await cacheController.getStorageStats();
        if (storageStats) {
          setStorageInfo(prev => ({
            ...prev,
            totalSize: storageStats.totalSize,
            caches: storageStats.caches || {}
          }));
        }
      }, 1000);
    } catch (error) {
      console.error('Failed to clear storage:', error);
    }
  };

  return {
    storageInfo,
    clearStorage
  };
};

export default CacheProvider;
