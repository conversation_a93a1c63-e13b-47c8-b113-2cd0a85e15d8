import React, { useState } from 'react';
import DynamicServiceOrderTable from './DynamicServiceOrderTable';
import tableConfig from './serviceOrderTableConfig.json';
import tableData from './serviceOrderTableData.json';
import { InputTextarea } from 'primereact/inputtextarea';
import { TabView, TabPanel } from 'primereact/tabview';
import { Dropdown } from 'primereact/dropdown';
import { Calendar } from 'primereact/calendar';
import { Button } from 'primereact/button';
import { FileUpload } from 'primereact/fileupload';
import '../../../components/custom/ServiceOrdersDataTable.css';

const DynamicServiceOrderTableDemo = () => {
  const [data, setData] = useState(tableData);
  const [config, setConfig] = useState(tableConfig);

  const enhancedConfig = {
    ...config,
    data: data, 
    expansion: {
      ...config.expansion,
      tabs: config.expansion.tabs.map((tab, index) => ({
        ...tab,
        render: (rowData) => renderTabContent(rowData, index, tab.header),
      })),
    },
  };

  const renderTabContent = (rowData, tabIndex, tabName) => {
    if (tabIndex === 0) {
      return (
        <div className="info-card">
          <h3>
            <i className="pi pi-car"></i>
            Transport Information
          </h3>
          <p>Manage transportation services for crew and passengers.</p>
          <div className="form-row">
            <label>Service</label>
            <Dropdown
              options={[
                { label: 'Not Required', value: 'NR' },
                { label: 'To Be Determined', value: 'TBD' },
                { label: 'Confirmed', value: 'Ok' },
              ]}
              placeholder="Select service status"
              className="w-full"
            />
          </div>
          <div className="form-row">
            <label>Provider</label>
            <InputTextarea rows={3} className="w-full" placeholder="Enter provider details..." />
          </div>
          <div className="form-actions">
            <Button label="Save" className="p-button-sm" />
            <Button label="Cancel" className="p-button-outlined p-button-sm" />
          </div>
        </div>
      );
    }

    if (tabIndex === 1) {
      return (
        <div className="info-card">
          <h3>
            <i className="pi pi-building"></i>
            Hotel Accommodations
          </h3>
          <p>Manage hotel bookings for crew and passengers.</p>
          <div className="form-row">
            <label>Hotel Name</label>
            <InputTextarea rows={2} className="w-full" placeholder="Enter hotel name..." />
          </div>
          <div className="form-row">
            <label>Check-in Date</label>
            <Calendar className="w-full" showIcon />
          </div>
          <div className="form-row">
            <label>Check-out Date</label>
            <Calendar className="w-full" showIcon />
          </div>
          <div className="form-actions">
            <Button label="Save" className="p-button-sm" />
            <Button label="Cancel" className="p-button-outlined p-button-sm" />
          </div>
        </div>
      );
    }

    if (tabIndex === 2) {
      return (
        <div className="catering-panel">
          <div className="catering-section">
            <div className="catering-header">
              <i className="pi pi-shopping-cart"></i>
              Catering - DMDW
            </div>
            <div className="catering-form">
              <div className="form-row">
                <label>Service</label>
                <Dropdown
                  options={[
                    { label: 'Not Required', value: 'NR' },
                    { label: 'To Be Determined', value: 'TBD' },
                    { label: 'Confirmed', value: 'Ok' },
                  ]}
                  placeholder="Select"
                  className="w-full"
                />
              </div>
              <div className="form-row">
                <label>For</label>
                <Dropdown
                  options={[
                    { label: 'Aviation Services1', value: 'as1' },
                    { label: 'Aviation Services2', value: 'as2' },
                  ]}
                  placeholder="Select provider"
                  className="w-full"
                />
              </div>
              <div className="form-row">
                <label>Pax</label>
                <Dropdown
                  options={[
                    { label: 'TBD', value: 'tbd' },
                    { label: 'Fax', value: 'fax' },
                  ]}
                  placeholder="Select"
                  className="w-full"
                />
              </div>
              <div className="form-row">
                <label>Order</label>
                <InputTextarea rows={3} className="w-full" />
              </div>
              <div className="file-upload-area">
                <p>
                  Drop your file(s) here or <a>browse</a>
                </p>
              </div>
              <div className="form-actions">
                <Button label="Send" className="p-button-sm" />
                <Button label="Cancel" className="p-button-outlined p-button-sm" />
                <Button label="Send Quote Only" className="p-button-outlined p-button-sm" />
              </div>
              <div className="expenses-section">
                <a href="#">
                  <i className="pi pi-plus"></i> Add Expense
                </a>
                <a href="#">
                  <i className="pi pi-eye"></i> Expenses (0)
                </a>
              </div>
            </div>
          </div>

          <div className="catering-section">
            <div className="catering-header">
              <i className="pi pi-shopping-cart"></i>
              Catering - DRSV
            </div>
            <div className="catering-form">
              <div className="form-row">
                <label>Service</label>
                <Dropdown
                  options={[
                    { label: 'Not Required', value: 'NR' },
                    { label: 'To Be Determined', value: 'TBD' },
                    { label: 'Confirmed', value: 'Ok' },
                  ]}
                  placeholder="Select"
                  className="w-full"
                />
              </div>
              <div className="form-row">
                <label>For</label>
                <Dropdown
                  options={[
                    { label: 'Aviation Services1', value: 'as1' },
                    { label: 'Aviation Services2', value: 'as2' },
                  ]}
                  placeholder="Select provider"
                  className="w-full"
                />
              </div>
              <div className="form-row">
                <label>Pax</label>
                <Dropdown
                  options={[
                    { label: 'TBD', value: 'tbd' },
                    { label: 'Fax', value: 'fax' },
                  ]}
                  placeholder="Select"
                  className="w-full"
                />
              </div>
              <div className="form-row">
                <label>Order</label>
                <InputTextarea rows={3} className="w-full" />
              </div>
              <div className="file-upload-area">
                <p>
                  Drop your file(s) here or <a>browse</a>
                </p>
              </div>
              <div className="form-actions">
                <Button label="Send" className="p-button-sm" />
                <Button label="Cancel" className="p-button-outlined p-button-sm" />
                <Button label="Send Quote Only" className="p-button-outlined p-button-sm" />
              </div>
              <div className="expenses-section">
                <a href="#">
                  <i className="pi pi-plus"></i> Add Expense
                </a>
                <a href="#">
                  <i className="pi pi-eye"></i> Expenses (0)
                </a>
              </div>
            </div>
          </div>
        </div>
      );
    }

    return (
      <div className="info-card">
        <h3>
          <i className={tab.icon}></i>
          {tabName}
        </h3>
        <p>Content for {tabName} tab.</p>
        <div className="form-row">
          <label>Status</label>
          <Dropdown
            options={[
              { label: 'Not Required', value: 'NR' },
              { label: 'To Be Determined', value: 'TBD' },
              { label: 'Confirmed', value: 'Ok' },
              { label: 'Not Applicable', value: 'NA' },
            ]}
            placeholder="Select status"
            className="w-full"
          />
        </div>
        <div className="form-row">
          <label>Notes</label>
          <InputTextarea rows={4} className="w-full" placeholder="Enter notes..." />
        </div>
        <div className="form-actions">
          <Button label="Save" className="p-button-sm" />
          <Button label="Cancel" className="p-button-outlined p-button-sm" />
        </div>
      </div>
    );
  };

  const handleEvent = (eventType, eventData) => {
    console.log('Event:', eventType, eventData);

    switch (eventType) {
      case 'statusClick':
        console.log('Status clicked:', eventData.rowData, 'Tab:', eventData.tabIndex);
        break;
      case 'action':
        console.log('Action clicked:', eventData.action, 'Row:', eventData.rowData);
        break;
      case 'headerAction':
        console.log('Header action:', eventData.action);
        if (eventData.action === 'addServiceOrder') {
          alert('Add new service order');
        }
        break;
      case 'tabChange':
        console.log('Tab changed:', eventData.tabIndex);
        break;
      default:
        console.log('Unknown event:', eventType, eventData);
    }
  };

  return (
    <div className="dynamic-service-order-table-demo">
      <DynamicServiceOrderTable
        config={enhancedConfig}
        data={data}
        onEvent={handleEvent}
      />
    </div>
  );
};

export default DynamicServiceOrderTableDemo;

