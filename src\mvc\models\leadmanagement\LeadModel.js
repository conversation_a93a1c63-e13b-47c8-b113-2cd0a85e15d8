class LeadModel {
	constructor({
		id,
		leadNumber,
		companyName,
		displayName,
		websiteUrl,
		email,
		phone,
		leadRatingId,
		leadSourceId,
		leadStatusId,
		priorityId,
		assignedTo,
		expectedRevenue,
		description,
		opportunityDetails,
		isBlacklisted,
		remarks,
		contact,
		status,
		priority,
		stages,
		revenue,
		source,
		salesPerson,
		rating,
		createdDate
	}) {
		this.id = id;
		this.leadNumber = leadNumber;
		this.companyName = companyName;
		this.displayName = displayName;
		this.websiteUrl = websiteUrl;
		this.email = email;
		this.phone = phone;
		this.leadRatingId = leadRatingId;
		this.leadSourceId = leadSourceId;
		this.leadStatusId = leadStatusId;
		this.priorityId = priorityId;
		this.assignedTo = assignedTo;
		this.expectedRevenue = expectedRevenue;
		this.description = description;
		this.opportunityDetails = opportunityDetails;
		this.isBlacklisted = isBlacklisted;
		this.remarks = remarks;
		this.contact = contact;
		this.status = status;
		this.priority = priority;
		this.stages = stages;
		this.revenue = revenue;
		this.source = source;
		this.salesPerson = salesPerson;
		this.rating = rating;
		this.createdDate = createdDate;
	}
}

export default LeadModel;
