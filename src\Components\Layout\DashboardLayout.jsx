import React, { useState, useEffect, Suspense } from "react";
import { Outlet } from "react-router-dom";
import Sidebar from "./Sidebar";
import DashboardNavbar from "./DashboardNavbar";
import { useAuthContext } from "@contexts/AuthContext";
import PageHeader from "@components/Common/Master/PageHeader";

const DashboardLayout = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { isAuthenticated } = useAuthContext();
console.log("isAuthenticated",isAuthenticated);

  useEffect(() => {
    // Temporarily commented out for testing - uncomment for production
    if (!isAuthenticated) {
      window.location.href = "/login";
    }
  }, [isAuthenticated]);

  const handleMenuToggle = () => {
    setIsCollapsed(!isCollapsed);
  };
  const ContentLoadingFallback = () => (
    <div
      className="flex justify-content-center align-items-center p-4"
      style={{ minHeight: "100vh" }}
    >
      <div className="flex flex-column align-items-center gap-2">
        <i
          className="pi pi-spin pi-spinner"
          style={{ fontSize: "2rem", color: "#6366f1" }}
        ></i>
        <span className="text-sm text-600">Loading...</span>
      </div>
    </div>
  );
  return (
    <div className="dashboard-layout">
      {console.log('DashboardLayout rendered')}
      <DashboardNavbar
        onMenuToggle={handleMenuToggle}
        isCollapsed={isCollapsed}
      />
      <Sidebar isCollapsed={isCollapsed} setIsCollapsed={setIsCollapsed} />
      <div
        className={`main-content ${
          isCollapsed ? "sidebar-collapsed" : "sidebar-expanded"
        }`}
      >
        <div className="glassy-page">
          <PageHeader onBack={() => window.history.back()} />

          <Suspense fallback={<ContentLoadingFallback />}>
            <Outlet />
          </Suspense>
        </div>
      </div>
    </div>
  );
};

export default DashboardLayout;
