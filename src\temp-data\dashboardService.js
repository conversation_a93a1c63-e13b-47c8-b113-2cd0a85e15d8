// Dashboard Service - Replace with your actual API endpoints

/**
 * Fake API service for dashboard data
 * TODO: Replace these functions with actual API calls to your backend
 */

// Mock data for demonstration
const mockTickets = [
  {
    id: 'TK-0000001',
    subject: 'Lorem ipsum dolor sit amet',
    ticketType: 'Requesting Password',
    priority: 'High',
    raisedBy: 'Tim D',
    createdOn: '02/01/2024',
    resolvedOn: '-',
    status: 'Pending',
    category: 'today'
  },
  {
    id: 'TK-0000003',
    subject: 'Lorem ipsum dolor sit amet',
    ticketType: 'Deletion of Record',
    priority: 'Medium',
    raisedBy: '<PERSON>',
    createdOn: '02/01/2024',
    resolvedOn: '-',
    status: 'Pending',
    category: 'overdue'
  },
  {
    id: 'TK-0000044',
    subject: 'Lorem ipsum dolor sit amet',
    ticketType: 'Need Access',
    priority: 'Low',
    raisedBy: 'Tim D',
    createdOn: '02/01/2024',
    resolvedOn: '-',
    status: 'Pending',
    category: 'open'
  },
  {
    id: 'TK-0024405',
    subject: 'Lorem ipsum dolor sit amet',
    ticketType: 'Requesting Password',
    priority: 'Medium',
    raisedBy: '<PERSON>',
    createdOn: '02/01/2024',
    resolvedOn: '-',
    status: 'Pending',
    category: 'critical'
  },
  {
    id: 'TK-0090006',
    subject: 'Lorem ipsum dolor sit amet',
    ticketType: 'Deletion of Record',
    priority: 'Low',
    raisedBy: 'Tim D',
    createdOn: '02/01/2024',
    resolvedOn: '-',
    status: 'Pending',
    category: 'open'
  },
  {
    id: 'TK-0000007',
    subject: 'System maintenance request',
    ticketType: 'System Maintenance',
    priority: 'High',
    raisedBy: 'Sarah Johnson',
    createdOn: '01/01/2024',
    resolvedOn: '-',
    status: 'In Progress',
    category: 'critical'
  },
  {
    id: 'TK-0000008',
    subject: 'User account activation',
    ticketType: 'Account Management',
    priority: 'Medium',
    raisedBy: 'Alex Smith',
    createdOn: '03/01/2024',
    resolvedOn: '-',
    status: 'Pending',
    category: 'today'
  },
  {
    id: 'TK-0000009',
    subject: 'Database backup issue',
    ticketType: 'Technical Support',
    priority: 'High',
    raisedBy: 'Mike Wilson',
    createdOn: '31/12/2023',
    resolvedOn: '-',
    status: 'Overdue',
    category: 'overdue'
  }
];

const mockDashboardStats = {
  todayActiveUsers: 90,
  monthlyActiveUsers: 169,
  totalRoles: 8,
  overallActiveUsers: 250,
  projectConversation: 65,
  todayTickets: 315,
  overdueTickets: 5,
  criticalTickets: 3,
  allOpenTickets: 3
};

/**
 * Fetch all tickets
 * TODO: Replace with actual API call
 * Example: const response = await fetch('YOUR_API_BASE_URL/api/tickets');
 */
export const fetchTickets = async () => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // TODO: Replace with actual API call
    // const response = await fetch('YOUR_API_BASE_URL/api/tickets', {
    //   method: 'GET',
    //   headers: {
    //     'Authorization': `Bearer ${getAuthToken()}`,
    //     'Content-Type': 'application/json'
    //   }
    // });
    // 
    // if (!response.ok) {
    //   throw new Error('Failed to fetch tickets');
    // }
    // 
    // const data = await response.json();
    // return data;
    
    // Return mock data for now
    return {
      success: true,
      data: mockTickets,
      total: mockTickets.length
    };
  } catch (error) {
    console.error('Error fetching tickets:', error);
    throw error;
  }
};

/**
 * Fetch dashboard statistics
 * TODO: Replace with actual API call
 * Example: const response = await fetch('YOUR_API_BASE_URL/api/dashboard/stats');
 */
export const fetchDashboardStats = async () => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // TODO: Replace with actual API call
    // const response = await fetch('YOUR_API_BASE_URL/api/dashboard/stats', {
    //   method: 'GET',
    //   headers: {
    //     'Authorization': `Bearer ${getAuthToken()}`,
    //     'Content-Type': 'application/json'
    //   }
    // });
    // 
    // if (!response.ok) {
    //   throw new Error('Failed to fetch dashboard stats');
    // }
    // 
    // const data = await response.json();
    // return data;
    
    // Return mock data for now
    return {
      success: true,
      data: mockDashboardStats
    };
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    throw error;
  }
};

/**
 * Fetch tickets by filter
 * TODO: Replace with actual API call
 * Example: const response = await fetch(`YOUR_API_BASE_URL/api/tickets?filter=${filter}`);
 */
export const fetchTicketsByFilter = async (filter) => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    let filteredTickets = [...mockTickets];
    
    switch (filter) {
      case 'today':
        filteredTickets = mockTickets.filter(ticket => ticket.category === 'today');
        break;
      case 'overdue':
        filteredTickets = mockTickets.filter(ticket => ticket.category === 'overdue');
        break;
      case 'critical':
        filteredTickets = mockTickets.filter(ticket => ticket.category === 'critical');
        break;
      case 'open':
        filteredTickets = mockTickets.filter(ticket => ticket.status === 'Pending');
        break;
      default:
        filteredTickets = mockTickets;
    }
    
    // TODO: Replace with actual API call
    // const response = await fetch(`YOUR_API_BASE_URL/api/tickets?filter=${filter}`, {
    //   method: 'GET',
    //   headers: {
    //     'Authorization': `Bearer ${getAuthToken()}`,
    //     'Content-Type': 'application/json'
    //   }
    // });
    // 
    // if (!response.ok) {
    //   throw new Error('Failed to fetch filtered tickets');
    // }
    // 
    // const data = await response.json();
    // return data;
    
    // Return mock data for now
    return {
      success: true,
      data: filteredTickets,
      total: filteredTickets.length
    };
  } catch (error) {
    console.error('Error fetching filtered tickets:', error);
    throw error;
  }
};

/**
 * Search tickets
 * TODO: Replace with actual API call
 * Example: const response = await fetch(`YOUR_API_BASE_URL/api/tickets/search?q=${query}`);
 */
export const searchTickets = async (query) => {
  try {
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 300));
    
    const filteredTickets = mockTickets.filter(ticket =>
      Object.values(ticket).some(field =>
        field.toString().toLowerCase().includes(query.toLowerCase())
      )
    );
    
    // TODO: Replace with actual API call
    // const response = await fetch(`YOUR_API_BASE_URL/api/tickets/search?q=${encodeURIComponent(query)}`, {
    //   method: 'GET',
    //   headers: {
    //     'Authorization': `Bearer ${getAuthToken()}`,
    //     'Content-Type': 'application/json'
    //   }
    // });
    // 
    // if (!response.ok) {
    //   throw new Error('Failed to search tickets');
    // }
    // 
    // const data = await response.json();
    // return data;
    
    // Return mock data for now
    return {
      success: true,
      data: filteredTickets,
      total: filteredTickets.length
    };
  } catch (error) {
    console.error('Error searching tickets:', error);
    throw error;
  }
};

/**
 * Helper function to get auth token
 * TODO: Implement based on your authentication system
 */
const getAuthToken = () => {
  // TODO: Replace with your actual token retrieval logic
  // return localStorage.getItem('authToken') || sessionStorage.getItem('authToken');
  return 'mock-token';
};

/**
 * API Configuration
 * TODO: Replace with your actual API base URL
 */
export const API_CONFIG = {
  BASE_URL: 'YOUR_API_BASE_URL', // Replace with your actual API base URL
  ENDPOINTS: {
    TICKETS: '/api/tickets',
    DASHBOARD_STATS: '/api/dashboard/stats',
    SEARCH_TICKETS: '/api/tickets/search'
  }
};

export default {
  fetchTickets,
  fetchDashboardStats,
  fetchTicketsByFilter,
  searchTickets,
  API_CONFIG
};
