import { logger } from '../logging/logger.js';

class HTTPSService {
  constructor() {
    this.isSecureContext = this.checkSecureContext();
    this.securityHeaders = new Map();
    this.trustedDomains = new Set([
      'localhost',
      '127.0.0.1',
    ]);
    
    this.init();
  }

  init() {
    if (typeof window === 'undefined') {
      return; 
    }

    if (import.meta.env?.MODE === 'production') {
      this.enforceHTTPS();
    }

    this.setupSecureDefaults();
    
    this.monitorMixedContent();
    
    this.setupHSTS();
    
    logger.info('HTTPSService initialized', {
      isSecureContext: this.isSecureContext,
      protocol: window.location.protocol,
      host: window.location.host
    });
  }

  checkSecureContext() {
    if (typeof window === 'undefined') {
      return true; 
    }

    if (window.isSecureContext !== undefined) {
      return window.isSecureContext;
    }

    return window.location.protocol === 'https:' || 
           window.location.hostname === 'localhost' ||
           window.location.hostname === '127.0.0.1';
  }

  enforceHTTPS() {
    if (typeof window === 'undefined') {
      return;
    }

    if (window.location.protocol !== 'https:' && 
        !this.trustedDomains.has(window.location.hostname)) {
      
      logger.warn('Redirecting to HTTPS', {
        currentUrl: window.location.href,
        targetUrl: window.location.href.replace('http:', 'https:')
      });
      
      window.location.replace(window.location.href.replace('http:', 'https:'));
      return;
    }
    this.enforceSecureCookies();
  }

  enforceSecureCookies() {
    const originalCookieDescriptor = Object.getOwnPropertyDescriptor(Document.prototype, 'cookie') ||
                                   Object.getOwnPropertyDescriptor(HTMLDocument.prototype, 'cookie');

    if (originalCookieDescriptor && originalCookieDescriptor.set) {
      Object.defineProperty(document, 'cookie', {
        set: function(value) {
          if (!value.toLowerCase().includes('secure') && window.location.protocol === 'https:') {
            value += '; Secure';
          }
          
          if (!value.toLowerCase().includes('samesite')) {
            value += '; SameSite=Strict';
          }
          
          return originalCookieDescriptor.set.call(this, value);
        },
        get: originalCookieDescriptor.get
      });
    }
  }

  setupSecureDefaults() {
    if (typeof window === 'undefined') {
      return;
    }

    const originalFetch = window.fetch;
    window.fetch = (url, options = {}) => {
      const secureOptions = this.addSecurityHeaders(options);
      return originalFetch(url, secureOptions);
    };

    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
      HTTPSService.instance.validateRequestURL(url);
      return originalXHROpen.call(this, method, url, async, user, password);
    };

    const originalXHRSend = XMLHttpRequest.prototype.send;
    XMLHttpRequest.prototype.send = function(data) {
      this.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
      
      if (!this.getResponseHeader('Content-Type')) {
        this.setRequestHeader('Content-Type', 'application/json');
      }
      
      return originalXHRSend.call(this, data);
    };
  }

  addSecurityHeaders(options) {
    const headers = new Headers(options.headers);
    
    const csrfToken = this.getCSRFToken();
    if (csrfToken) {
      headers.set('X-CSRF-Token', csrfToken);
    }
    
    headers.set('X-Requested-With', 'XMLHttpRequest');
    
    if (options.method && ['POST', 'PUT', 'PATCH'].includes(options.method.toUpperCase())) {
      if (!headers.has('Content-Type')) {
        headers.set('Content-Type', 'application/json');
      }
    }
    
    return {
      ...options,
      headers: headers
    };
  }

  validateRequestURL(url) {
    try {
      const urlObj = new URL(url, window.location.origin);
      
      if (urlObj.protocol !== 'https:' && urlObj.protocol !== 'http:') {
        throw new Error(`Unsafe protocol: ${urlObj.protocol}`);
      }
      
      if (import.meta.env?.MODE === 'production' &&
          urlObj.origin !== window.location.origin &&
          urlObj.protocol !== 'https:') {
        throw new Error('External requests must use HTTPS in production');
      }
      
      const suspiciousPatterns = [
        /javascript:/i,
        /data:/i,
        /vbscript:/i,
        /file:/i
      ];
      
      suspiciousPatterns.forEach(pattern => {
        if (pattern.test(url)) {
          throw new Error(`Suspicious URL pattern detected: ${url}`);
        }
      });
      
    } catch (error) {
      logger.security('unsafe_request_url', {
        url: url,
        error: error.message
      });
      throw error;
    }
  }

  monitorMixedContent() {
    if (typeof window === 'undefined') {
      return;
    }

    const originalConsoleWarn = console.warn;
    console.warn = (...args) => {
      const message = args.join(' ');
      if (message.toLowerCase().includes('mixed content')) {
        logger.security('mixed_content_detected', {
          message: message,
          url: window.location.href
        });
      }
      return originalConsoleWarn.apply(console, args);
    };

    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              this.checkElementSecurity(node);
            }
          });
        }
      });
    });

    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  }

  checkElementSecurity(element) {
    const resourceAttributes = ['src', 'href', 'action'];
    
    resourceAttributes.forEach(attr => {
      const value = element.getAttribute(attr);
      if (value && value.startsWith('http://') && window.location.protocol === 'https:') {
        logger.security('mixed_content_element', {
          element: element.tagName,
          attribute: attr,
          value: value
        });
      }
    });

    element.querySelectorAll('*').forEach(child => {
      this.checkElementSecurity(child);
    });
  }

  setupHSTS() {
    if (typeof window === 'undefined') {
      return;
    }

    fetch(window.location.href, { method: 'HEAD' })
      .then(response => {
        const hstsHeader = response.headers.get('Strict-Transport-Security');
        if (!hstsHeader && window.location.protocol === 'https:') {
          logger.warn('HSTS header not found', {
            url: window.location.href,
            recommendation: 'Add Strict-Transport-Security header'
          });
        } else if (hstsHeader) {
          logger.info('HSTS header detected', {
            header: hstsHeader
          });
        }
      })
      .catch(error => {
        logger.debug('Could not check HSTS header', { error: error.message });
      });
  }

  getCSRFToken() {
    
    const metaToken = document.querySelector('meta[name="csrf-token"]');
    if (metaToken) {
      return metaToken.getAttribute('content');
    }
    
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === 'csrf_token' || name === 'XSRF-TOKEN') {
        return decodeURIComponent(value);
      }
    }
    
    const storageToken = localStorage.getItem('csrf_token');
    if (storageToken) {
      return storageToken;
    }
    
    return null;
  }

  setCSRFToken(token) {
    let metaTag = document.querySelector('meta[name="csrf-token"]');
    if (!metaTag) {
      metaTag = document.createElement('meta');
      metaTag.name = 'csrf-token';
      document.head.appendChild(metaTag);
    }
    metaTag.content = token;
    
    localStorage.setItem('csrf_token', token);
  }

  createSecureRequest(url, options = {}) {
    const secureOptions = {
      ...options,
      credentials: 'same-origin', 
      mode: 'cors',
      cache: 'no-cache'
    };
    
    return this.addSecurityHeaders(secureOptions);
  }

  validateCertificate(response) {
    if (response.url.startsWith('https://')) {
      logger.info('HTTPS request validated', {
        url: response.url,
        status: response.status
      });
    }
    
    return response;
  }

  getSecurityStatus() {
    return {
      isSecureContext: this.isSecureContext,
      protocol: typeof window !== 'undefined' ? window.location.protocol : 'unknown',
      httpsEnforced: import.meta.env?.MODE === 'production',
      mixedContentBlocked: this.isSecureContext,
      secureHeaders: Array.from(this.securityHeaders.entries())
    };
  }
}

HTTPSService.instance = new HTTPSService();

export default HTTPSService.instance;
