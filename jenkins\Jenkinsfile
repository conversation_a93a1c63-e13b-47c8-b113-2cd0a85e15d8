pipeline {
  agent any
  environment {
    NODE_VERSION = '18.x'
    DOCKER_IMAGE = 'enterprise-react-app'
    DOCKER_TAG = "${env.BUILD_ID}"
  }
  stages {
    stage('Checkout') {
      steps {
        git branch: 'main', url: 'https://github.com/your-org/enterprise-react-app.git'
      }
    }
    stage('Install Dependencies') {
      steps {
        sh 'npm ci --prefer-offline'
      }
    }
    stage('Static Analysis') {
      steps {
        sh 'npm run lint'
        sh 'npm run security:scan'
      }
    }
    stage('Security Scan') {
      parallel {
        stage('Dependency Security') {
          steps {
            sh 'npm run security:dependency-check'
          }
        }
        stage('SAST Scan') {
          steps {
            sh 'npm run security:sast'
          }
        }
        stage('DAST Scan') {
          steps {
            sh 'npm run security:dast'
          }
        }
        stage('Secrets Detection') {
          steps {
            sh 'npm run security:secrets'
          }
        }
      }
    }
    stage('Security Gate') {
      steps {
        script {
          def securityReport = readJSON file: 'security/reports/security-summary.json'
          if (securityReport.criticalIssues > 0) {
            error 'Critical security issues found. Build failed.'
          }
          if (securityReport.highIssues > 2) {
            error 'Too many high severity issues. Build failed.'
          }
        }
      }
    }
    stage('Unit Tests') {
      steps {
        sh 'npm test -- --coverage --watchAll=false'
      }
    }
    stage('Build') {
      steps {
        sh 'npm run build'
        // Obfuscate and package
        sh 'npm run package'
      }
    }
    stage('Functional Tests') {
      steps {
        sh 'npm run functional-tests -- --profile=ci'
      }
    }
    stage('Performance Tests') {
      steps {
        sh 'npm run performance-tests'
      }
    }
    stage('Build Docker Image') {
      steps {
        script {
          docker.build("${DOCKER_IMAGE}:${DOCKER_TAG}", "--build-arg NODE_ENV=production .")
        }
      }
    }
    stage('Push to Registry') {
      steps {
        script {
          docker.withRegistry('https://registry.example.com', 'docker-credentials') {
            docker.image("${DOCKER_IMAGE}:${DOCKER_TAG}").push()
          }
        }
      }
    }
    stage('Deploy to Staging') {
      steps {
        sh "kubectl set image deployment/enterprise-react-app app=${DOCKER_IMAGE}:${DOCKER_TAG}"
      }
    }
  }
  post {
    always {
      junit 'test-results/**/*.xml'
      archiveArtifacts 'dist/**/*'
      archiveArtifacts 'security/reports/**/*'
      cleanWs()
    }
  }
}