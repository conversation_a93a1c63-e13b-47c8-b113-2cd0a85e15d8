import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Panel } from 'primereact/panel';
import DynamicComponent from '../../core/DynamicComponent';

const DynamicPanel = ({
  header,
  headerTemplate,
  footerTemplate,
  toggleable = false,
  collapsed = false,
  onToggle,
  onExpand,
  onCollapse,
  className = '',
  style = {},
  headerClassName = '',
  headerStyle = {},
  contentClassName = '',
  contentStyle = {},
  footerClassName = '',
  footerStyle = {},
  icons,
  id,
  children,
  content = [],
  config = {},
  size = 'normal',
  variant = 'default',
  bordered = true,
  shadow = false,
  onEvent,
  debug = false,
  ...props
}) => {
  const [isCollapsed, setIsCollapsed] = useState(collapsed);

  const handleToggle = useCallback((e) => {
    const newCollapsed = e.value;
    setIsCollapsed(newCollapsed);

    if (debug) {
      console.log('DynamicPanel toggle:', { collapsed: newCollapsed });
    }

    if (onToggle) {
      onToggle(e);
    }

    if (newCollapsed) {
      if (onCollapse) {
        onCollapse(e);
      }
      if (onEvent) {
        onEvent('collapse', { collapsed: newCollapsed, event: e });
      }
    } else {
      if (onExpand) {
        onExpand(e);
      }
      if (onEvent) {
        onEvent('expand', { collapsed: newCollapsed, event: e });
      }
    }

    if (onEvent) {
      onEvent('toggle', { collapsed: newCollapsed, event: e });
    }
  }, [onToggle, onExpand, onCollapse, onEvent, debug]);

  const panelClasses = [
    'dynamic-panel',
    `dynamic-panel--${size}`,
    `dynamic-panel--${variant}`,
    bordered ? 'dynamic-panel--bordered' : '',
    shadow ? 'dynamic-panel--shadow' : '',
    isCollapsed ? 'dynamic-panel--collapsed' : '',
    className
  ].filter(Boolean).join(' ');

  const renderHeaderTemplate = useCallback((options) => {
    if (headerTemplate && typeof headerTemplate === 'function') {
      return headerTemplate(options);
    }

    return (
      <div className="panel-header-content">
        {typeof header === 'string' ? (
          <span className="panel-title">{header}</span>
        ) : (
          header
        )}
        {icons && (
          <div className="panel-icons">
            {icons.map((icon, index) => (
              <button
                key={index}
                type="button"
                className={`panel-icon ${icon.className || ''}`}
                onClick={icon.onClick}
                title={icon.tooltip}
              >
                <i className={icon.icon} />
              </button>
            ))}
          </div>
        )}
        {toggleable && (
          <button
            type="button"
            className="panel-toggle"
            onClick={options.onTogglerClick}
            title={isCollapsed ? 'Expand' : 'Collapse'}
          >
            <i className={`pi ${isCollapsed ? 'pi-plus' : 'pi-minus'}`} />
          </button>
        )}
      </div>
    );
  }, [headerTemplate, header, icons, toggleable, isCollapsed]);

  const renderFooterTemplate = useCallback(() => {
    if (footerTemplate && typeof footerTemplate === 'function') {
      return footerTemplate();
    }

    if (config.footer) {
      return (
        <div className="panel-footer-content">
          {config.footer.map((item, index) => (
            <DynamicComponent
              key={index}
              type={item.type}
              config={item.config}
              data={item.data}
              onEvent={onEvent}
              debug={debug}
            />
          ))}
        </div>
      );
    }

    return null;
  }, [footerTemplate, config.footer, onEvent, debug]);

  const renderContent = useCallback(() => {
    if (children) {
      return children;
    }

    if (Array.isArray(content) && content.length > 0) {
      return (
        <div className="panel-dynamic-content">
          {content.map((item, index) => (
            <DynamicComponent
              key={index}
              type={item.type}
              config={item.config}
              data={item.data}
              onEvent={onEvent}
              debug={debug}
            />
          ))}
        </div>
      );
    }

    if (content && typeof content === 'object' && content.type) {
      return (
        <DynamicComponent
          type={content.type}
          config={content.config}
          data={content.data}
          onEvent={onEvent}
          debug={debug}
        />
      );
    }

    return null;
  }, [children, content, onEvent, debug]);

  const panelProps = {
    id,
    header: typeof header === 'string' ? header : null,
    headerTemplate: renderHeaderTemplate,
    footerTemplate: renderFooterTemplate,
    toggleable,
    collapsed: isCollapsed,
    onToggle: handleToggle,
    className: panelClasses,
    style,
    headerClassName,
    headerStyle,
    contentClassName,
    contentStyle,
    footerClassName,
    footerStyle,
    'data-component-type': 'panel',
    'data-panel-size': size,
    'data-panel-variant': variant,
    ...props
  };

  Object.keys(panelProps).forEach(key => {
    if (panelProps[key] === undefined) {
      delete panelProps[key];
    }
  });

  return (
    <div className="dynamic-panel-wrapper">
      {debug && (
        <div className="dynamic-panel__debug">
          <small>
            Size: {size} | 
            Variant: {variant} | 
            Collapsed: {isCollapsed ? 'Yes' : 'No'} | 
            Content Items: {Array.isArray(content) ? content.length : (content ? 1 : 0)}
          </small>
        </div>
      )}
      <Panel {...panelProps}>
        {renderContent()}
      </Panel>
    </div>
  );
};

DynamicPanel.propTypes = {
  header: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  headerTemplate: PropTypes.func,
  footerTemplate: PropTypes.func,
  toggleable: PropTypes.bool,
  collapsed: PropTypes.bool,
  onToggle: PropTypes.func,
  onExpand: PropTypes.func,
  onCollapse: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object,
  headerClassName: PropTypes.string,
  headerStyle: PropTypes.object,
  contentClassName: PropTypes.string,
  contentStyle: PropTypes.object,
  footerClassName: PropTypes.string,
  footerStyle: PropTypes.object,
  icons: PropTypes.arrayOf(PropTypes.shape({
    icon: PropTypes.string.isRequired,
    onClick: PropTypes.func,
    className: PropTypes.string,
    tooltip: PropTypes.string
  })),
  id: PropTypes.string,
  children: PropTypes.node,
  content: PropTypes.oneOfType([
    PropTypes.array,
    PropTypes.object
  ]),
  config: PropTypes.object,
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  variant: PropTypes.oneOf(['default', 'primary', 'secondary', 'success', 'info', 'warning', 'danger']),
  bordered: PropTypes.bool,
  shadow: PropTypes.bool,
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicPanel;
