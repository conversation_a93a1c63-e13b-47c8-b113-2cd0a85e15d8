import React from 'react';
import '../../styles/glassy-ui.css';
import '../../styles/global.css';
import { Button } from 'primereact/button';

/*
  PdfReport
  - Matches the provided mock: header bar + glassy card with content and actions
  - Uses `glass-card-global` class for glassy UI (as in KeyDataDocVerify)
  - Container style per user request
*/

export default function PdfReport({
  title = 'Compliance Report',
  subtitle = 'Detailed compliance verification report with all checks and validations',
  onDownload = () => {},
  onPreview = () => {},
}) {
  const containerStyle = {
    display: 'flex',
    width: '660px',
    height: '261px',
    padding: '1px',
    flexDirection: 'column',
    alignItems: 'flex-start',
  };

  return (
    <section style={containerStyle} className="pdf-report-wrapper">
      {/* Header bar */}
      <div
        className="flex align-items-center justify-content-between w-full"
        style={{
          background: '#fdeaea',
          borderRadius: '8px 8px 0 0',
          padding: '12px 16px',
        }}
      >
        <div className="flex align-items-center gap-2">
          <i className="pi pi-file text-red-500" aria-hidden />
          <span className="text-900 font-medium">PDF Report</span>
        </div>
        <div className="flex align-items-center gap-3 text-500">
          <i className="pi pi-angle-left" aria-hidden />
          <i className="pi pi-angle-right" aria-hidden />
        </div>
      </div>

      {/* Body glass card */}
      <div className="glass-card-global glass-shadow-light w-full flex-1" style={{ padding: '32px 24px' }}>
        <div className="flex align-items-center h-full">
          {/* Left icon rail */}
          <div className="flex align-items-start" style={{ paddingRight: 16 }}>
            <span
              className="flex align-items-center justify-content-center"
              style={{
                width: 40,
                height: 40,
                borderRadius: 8,
                background: '#fff5f5',
                color: '#ef4444',
                border: '1px solid #fee2e2',
              }}
              aria-hidden
            >
              <i className="pi pi-file" style={{ fontSize: '1.25rem' }} />
            </span>
          </div>

          {/* Main content */}
          <div className="flex flex-column align-items-center justify-content-center text-center w-full" style={{ gap: 12 }}>
            <h3 className="m-0 text-900" style={{ fontWeight: 600, fontSize: '1.25rem' }}>{title}</h3>
            <p className="m-0 text-600" style={{ maxWidth: 520, fontSize: '0.875rem' }}>{subtitle}</p>

            <div className="flex align-items-center gap-2" style={{ marginTop: 8 }}>
              <Button
                label="Download PDF"
                icon="pi pi-download"
                severity="danger"
                onClick={onDownload}
                style={{ fontWeight: 500 }}
              />
              <Button
                label="Preview"
                icon="pi pi-eye"
                severity="secondary"
                outlined
                onClick={onPreview}
                style={{ fontWeight: 500 }}
              />
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
