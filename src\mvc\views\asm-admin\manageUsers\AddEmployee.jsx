import React, { useState, useRef, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { InputText } from 'primereact/inputtext';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { Checkbox } from 'primereact/checkbox';
import { Toast } from 'primereact/toast';
import { useNavigate } from 'react-router-dom';
import userRoleController from '../../../controllers/userRole/userRoleController';
import { fetchRoles, selectRoleOptions, selectRolesLoading, selectRolesError } from '../../../redux/roleSlice';
import '../../../styles/AddEmployee.css';

const AddEmployee = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const toast = useRef(null);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    employeeCode: 'ASME-000001',
    firstName: '',
    lastName: '',
    emailId: '',
    designation: null,
    role: null,
    entity: null,
    password: '', // Add password field
    isSuperUser: false,
    profileImage: null
  });

  const [imagePreview, setImagePreview] = useState(null);

  // Redux selectors
  const roleOptions = useSelector(selectRoleOptions);
  const rolesLoading = useSelector(selectRolesLoading);
  const rolesError = useSelector(selectRolesError);

  // Load roles on component mount
  useEffect(() => {
    dispatch(fetchRoles());
  }, [dispatch]);

  const designationOptions = [
    { label: 'Select designation', value: null },
    { label: 'Manager', value: 'manager' },
    { label: 'Developer', value: 'developer' },
    { label: 'Analyst', value: 'analyst' },
    { label: 'Coordinator', value: 'coordinator' }
  ];

  // Role options now come from Redux store

  const entityOptions = [
    { label: 'Select entity', value: null },
    { label: 'Sales', value: 'sales' },
    { label: 'Legal', value: 'legal' },
    { label: 'HR', value: 'hr' },
    { label: 'Finance', value: 'finance' }
  ];

  // Helper function to determine if password is required based on role
  const isPasswordRequired = (roleKey) => {
    return roleKey === 'client_employee' || roleKey === 'client';
  };

  // Helper function to check if current role requires password
  const shouldShowPasswordField = () => {
    return isPasswordRequired(formData.role);
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setFormData(prev => ({ ...prev, profileImage: file }));
      const reader = new FileReader();
      reader.onloadend = () => {
        setImagePreview(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.firstName || !formData.lastName || !formData.emailId) {
      toast.current?.show({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please fill in all required fields (First Name, Last Name, Email)'
      });
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.emailId)) {
      toast.current?.show({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please enter a valid email address'
      });
      return;
    }

    // Validate role selection
    if (!formData.role) {
      toast.current?.show({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Please select a role'
      });
      return;
    }

    // Validate password for client roles
    if (isPasswordRequired(formData.role) && !formData.password.trim()) {
      toast.current?.show({
        severity: 'error',
        summary: 'Validation Error',
        detail: 'Password is required for this role'
      });
      return;
    }

    setLoading(true);

    try {
      // Prepare API payload according to the specified format
      const apiPayload = {
        username: formData.emailId, // Using email as username
        status: "ACTIVE",
        roles: [formData.role] // Use selected role key
      };

      // Add password only for client roles
      if (isPasswordRequired(formData.role)) {
        apiPayload.password = formData.password;
      } else {
        apiPayload.password = null; // Explicitly set to null for ASM roles
      }

      console.log('Submitting user data:', apiPayload);

      const result = await userRoleController.createUser(apiPayload);

      if (result.success) {
        toast.current?.show({
          severity: 'success',
          summary: 'Success',
          detail: result.message || 'User created successfully'
        });

        // Wait a moment for user to see the success message, then navigate
        setTimeout(() => {
          navigate('/manage-users');
        }, 2000);

      } else {
        toast.current?.show({
          severity: 'error',
          summary: 'Error',
          detail: result.error || 'Failed to create user'
        });
      }

    } catch (error) {
      console.error('Error creating user:', error);
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: 'Network error: Failed to create user'
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="add-employee-container">
      <Toast ref={toast} />

      {/* Header */}
      <div className="header">
        <div className="breadcrumb">
          <span 
            className="breadcrumb-item"
            onClick={() => navigate('/manage-users')}
            style={{ cursor: 'pointer' }}
          >
            Manage User
          </span>
          <span className="breadcrumb-separator">/</span>
          <span className="breadcrumb-item active">Add User</span>
        </div>
        <div className="employee-code">{formData.employeeCode}</div>
      </div>

      <form onSubmit={handleSubmit} className="employee-form">
        <div className="form-left">
          <div className="form-group">
            <label htmlFor="employeeCode">Employee Code</label>
            <InputText
              id="employeeCode"
              value={formData.employeeCode}
              onChange={(e) => handleInputChange('employeeCode', e.target.value)}
              placeholder="Enter employee code"
              className="form-input"
              disabled
            />
          </div>

          <div className="form-group">
            <label htmlFor="firstName">First Name</label>
            <InputText
              id="firstName"
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              placeholder="Enter first name"
              className="form-input"
            />
          </div>

          <div className="form-group">
            <label htmlFor="lastName">Last Name</label>
            <InputText
              id="lastName"
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              placeholder="Enter last name"
              className="form-input"
            />
          </div>

          <div className="form-group">
            <label htmlFor="emailId">Email ID</label>
            <InputText
              id="emailId"
              value={formData.emailId}
              onChange={(e) => handleInputChange('emailId', e.target.value)}
              placeholder="Enter email address"
              className="form-input"
            />
          </div>

          <div className="form-group">
            <label htmlFor="designation">Designation</label>
            <Dropdown
              id="designation"
              value={formData.designation}
              options={designationOptions}
              onChange={(e) => handleInputChange('designation', e.value)}
              placeholder="Select designation"
              className="form-dropdown"
            />
          </div>

          <div className="form-group">
            <label htmlFor="role">Role</label>
            <Dropdown
              id="role"
              value={formData.role}
              options={roleOptions}
              onChange={(e) => handleInputChange('role', e.value)}
              placeholder={rolesLoading ? "Loading roles..." : "Select role"}
              className="form-dropdown"
              disabled={rolesLoading}
            />
            {rolesError && (
              <small className="error-text">Error loading roles: {rolesError}</small>
            )}
          </div>

          {/* Conditional Password Field */}
          {shouldShowPasswordField() && (
            <div className="form-group">
              <label htmlFor="password">Password</label>
              <InputText
                id="password"
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                placeholder="Enter password"
                className="form-input"
              />
              <small className="field-note">Password is required for client roles</small>
            </div>
          )}

          <div className="form-group">
            <label htmlFor="entity">Entity</label>
            <Dropdown
              id="entity"
              value={formData.entity}
              options={entityOptions}
              onChange={(e) => handleInputChange('entity', e.value)}
              placeholder="Select entity"
              className="form-dropdown"
            />
          </div>

          <div className="form-group checkbox">
            <Checkbox
              inputId="isSuperUser"
              checked={formData.isSuperUser}
              onChange={(e) => handleInputChange('isSuperUser', e.checked)}
              className="super-user-checkbox"
            />
            <label htmlFor="isSuperUser" className="checkbox-label">
              Is Super User?
            </label>
          </div>
        </div>

        <div className="form-right">
          <div className="profile-image-section">
            <div className="profile-image-container">
              {imagePreview ? (
                <img src={imagePreview} alt="Profile Preview" />
              ) : (
                <div className="upload-placeholder">
                  <i className="pi pi-user"></i>
                </div>
              )}
            </div>
            <div className="profile-upload-info">
              <p className="profile-text">Profile Image</p>
              <p className="upload-hint">Max size 5MB</p>
              <p className="upload-hint">Supported: JPG, PNG, GIF</p>
              <input
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                style={{ display: 'none' }}
                id="profile-image-input"
              />
              <label htmlFor="profile-image-input" className="upload-button">
                Upload Image
              </label>
            </div>
          </div>
        </div>

        <div className="form-actions">
          <Button
            label="Cancel"
            onClick={() => navigate('/manage-users')}
            className="cancel-button p-button-secondary"
            disabled={loading}
          />
          <Button
            label={loading ? "Creating..." : "Save"}
            type="submit"
            className="save-button"
            disabled={loading}
            loading={loading}
          />
        </div>
      </form>
    </div>
  );
};

export default AddEmployee;
