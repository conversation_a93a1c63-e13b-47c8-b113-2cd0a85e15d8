import axios from "axios";
import authHeader from "./AuthHeader";
import { getAxiosInstance } from "@utils/Loader/axiosInstance";

const TENANTID = JSON.parse(localStorage.getItem("X-TenantID"));

const getAllCollection = (data) => {
  // const axios = getAxiosInstance();

  const payload = {
    endpoint: "/api/public/simplified-collections/getAll",
    payload: data,
    type: "get",
    tenantId: TENANTID,
    auth: authHeader(),
  };
  try {
    return axios.get(
      `/api/public/simplified-collections/getAll`,

      {
        headers: {
          // ...authHeader(),
          "X-TenantID": "rfiling_com",
        },
      }
    );
  } catch (error) {
    return [];
  }
};

const getAllCollectionByAPIId = (apiId) => {
  const axios = getAxiosInstance();
  try {
    return axios.get(
      `http://192.168.1.100:5000/api/public/simplified-collections/api/${apiId}`,
      // provided on 8-8-25 http://192.168.1.100:5000/api/public/simplified-collections/api/${apiId}
      //changed on 7-8-25=> return axios.get(`/api/public/simplified-collections/getByApiId/${apiId}`,
      {
        headers: {
          // ...authHeader(),
          "X-TenantID": "rfiling_com",
        },
      }
    );
  } catch (error) {
    return [];
  }
};

const getAllDynamicQuery = (data) => {
  const axios = getAxiosInstance();
  // const queryData = data;
  // queryData["tenantName"] = TENANTID;
  // const payload = {
  //   endpoint: "/order/api/getEntitiesByDynamicQuery",
  //   payload: queryData,
  //   type: "post",
  //   tenantId: TENANTID,
  //   auth: authHeader(),
  // };
  try {
    return axios.post(
      `/api/execute-query`,
      data,

      { headers: authHeader() }
    );
  } catch (error) {
    return [];
  }
};

const RCMSService = {
  getAllCollection,
  getAllDynamicQuery,
  getAllCollectionByAPIId,
};

export default RCMSService;
