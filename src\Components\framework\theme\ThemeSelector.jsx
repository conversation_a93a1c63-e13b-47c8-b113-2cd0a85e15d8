import React, { useState, useCallback } from 'react';
import PropTypes from 'prop-types';
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { Dialog } from 'primereact/dialog';
import { ColorPicker } from 'primereact/colorpicker';
import { InputText } from 'primereact/inputtext';
import { Card } from 'primereact/card';
import { Divider } from 'primereact/divider';
import { useTheme } from './ThemeProvider';

const ThemeSelector = ({
  showLabel = true,
  showDarkModeToggle = true,
  showCustomThemeButton = true,
  variant = 'dropdown',
  size = 'normal',
  className = '',
  style = {},
  onThemeChange,
  debug = false
}) => {
  const {
    currentTheme,
    themes,
    setTheme,
    isDarkMode,
    toggleDarkMode,
    customTheme,
    setCustomTheme,
    enableCustomThemes,
    enableDarkModeToggle
  } = useTheme();

  const [showCustomDialog, setShowCustomDialog] = useState(false);
  const [customColors, setCustomColors] = useState({
    primary: '#3B82F6',
    secondary: '#6B7280',
    success: '#10B981',
    info: '#3B82F6',
    warning: '#F59E0B',
    danger: '#EF4444',
    surface: '#FFFFFF',
    text: '#1F2937'
  });

  const handleThemeChange = useCallback((themeName) => {
    setTheme(themeName);
    
    if (onThemeChange) {
      onThemeChange(themeName);
    }

    if (debug) {
      console.log('Theme changed via selector:', themeName);
    }
  }, [setTheme, onThemeChange, debug]);

  const handleDarkModeToggle = useCallback(() => {
    if (toggleDarkMode) {
      toggleDarkMode();
    }
  }, [toggleDarkMode]);

  const applyCustomTheme = useCallback(() => {
    const customThemeData = {
      name: 'custom',
      label: 'Custom Theme',
      type: isDarkMode ? 'dark' : 'light',
      variables: {
        '--primary-color': customColors.primary,
        '--primary-color-text': '#ffffff',
        '--surface-card': customColors.surface,
        '--surface-border': '#e9ecef',
        '--text-color': customColors.text,
        '--text-color-secondary': '#6c757d'
      }
    };

    if (setCustomTheme) {
      setCustomTheme(customThemeData);
    }

    setShowCustomDialog(false);

    if (debug) {
      console.log('Custom theme applied:', customThemeData);
    }
  }, [customColors, isDarkMode, setCustomTheme, debug]);

  const lightThemes = themes.filter(theme => theme.type === 'light');
  const darkThemes = themes.filter(theme => theme.type === 'dark');

  const themeOptions = themes.map(theme => ({
    label: theme.label,
    value: theme.name,
    icon: theme.type === 'dark' ? 'pi pi-moon' : 'pi pi-sun',
    className: `theme-option theme-option--${theme.type}`
  }));

  const currentThemeData = themes.find(t => t.name === currentTheme);

  const renderDropdown = () => (
    <div className={`theme-selector theme-selector--dropdown ${className}`} style={style}>
      {showLabel && (
        <label className="theme-selector__label">
          <i className="pi pi-palette" />
          Theme
        </label>
      )}
      
      <div className="theme-selector__controls">
        <Dropdown
          value={currentTheme}
          options={themeOptions}
          onChange={(e) => handleThemeChange(e.value)}
          placeholder="Select Theme"
          className="theme-dropdown"
          itemTemplate={(option) => (
            <div className="theme-option">
              <i className={option.icon} />
              <span>{option.label}</span>
              <div 
                className="theme-color-preview" 
                style={{ 
                  backgroundColor: themes.find(t => t.name === option.value)?.primary 
                }}
              />
            </div>
          )}
          valueTemplate={(option) => (
            <div className="theme-option">
              <i className={option?.icon || (isDarkMode ? 'pi pi-moon' : 'pi pi-sun')} />
              <span>{option?.label || currentThemeData?.label}</span>
            </div>
          )}
        />

        {enableDarkModeToggle && showDarkModeToggle && (
          <Button
            icon={isDarkMode ? 'pi pi-sun' : 'pi pi-moon'}
            onClick={handleDarkModeToggle}
            className="p-button-text theme-toggle-btn"
            tooltip={isDarkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
            tooltipOptions={{ position: 'bottom' }}
          />
        )}

        {enableCustomThemes && showCustomThemeButton && (
          <Button
            icon="pi pi-cog"
            onClick={() => setShowCustomDialog(true)}
            className="p-button-text custom-theme-btn"
            tooltip="Customize Theme"
            tooltipOptions={{ position: 'bottom' }}
          />
        )}
      </div>
    </div>
  );

  const renderButtons = () => (
    <div className={`theme-selector theme-selector--buttons ${className}`} style={style}>
      {showLabel && (
        <label className="theme-selector__label">
          <i className="pi pi-palette" />
          Theme
        </label>
      )}
      
      <div className="theme-selector__button-group">
        <div className="theme-buttons">
          {themes.slice(0, 6).map(theme => (
            <Button
              key={theme.name}
              className={`theme-btn ${currentTheme === theme.name ? 'active' : ''}`}
              onClick={() => handleThemeChange(theme.name)}
              tooltip={theme.label}
              tooltipOptions={{ position: 'bottom' }}
              style={{ 
                backgroundColor: theme.primary,
                borderColor: theme.primary
              }}
            />
          ))}
        </div>

        <div className="theme-controls">
          {enableDarkModeToggle && showDarkModeToggle && (
            <Button
              icon={isDarkMode ? 'pi pi-sun' : 'pi pi-moon'}
              onClick={handleDarkModeToggle}
              className="p-button-outlined theme-toggle-btn"
              size="small"
            />
          )}

          {enableCustomThemes && showCustomThemeButton && (
            <Button
              icon="pi pi-cog"
              onClick={() => setShowCustomDialog(true)}
              className="p-button-outlined custom-theme-btn"
              size="small"
            />
          )}
        </div>
      </div>
    </div>
  );

  const renderGrid = () => (
    <div className={`theme-selector theme-selector--grid ${className}`} style={style}>
      {showLabel && (
        <label className="theme-selector__label">
          <i className="pi pi-palette" />
          Select Theme
        </label>
      )}
      
      <div className="theme-grid">
        <div className="theme-section">
          <h6>Light Themes</h6>
          <div className="theme-grid__items">
            {lightThemes.map(theme => (
              <div
                key={theme.name}
                className={`theme-card ${currentTheme === theme.name ? 'active' : ''}`}
                onClick={() => handleThemeChange(theme.name)}
              >
                <div 
                  className="theme-preview" 
                  style={{ backgroundColor: theme.primary }}
                />
                <span className="theme-name">{theme.label}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="theme-section">
          <h6>Dark Themes</h6>
          <div className="theme-grid__items">
            {darkThemes.map(theme => (
              <div
                key={theme.name}
                className={`theme-card ${currentTheme === theme.name ? 'active' : ''}`}
                onClick={() => handleThemeChange(theme.name)}
              >
                <div 
                  className="theme-preview" 
                  style={{ backgroundColor: theme.primary }}
                />
                <span className="theme-name">{theme.label}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderCustomThemeDialog = () => (
    <Dialog
      visible={showCustomDialog}
      onHide={() => setShowCustomDialog(false)}
      header="Customize Theme"
      style={{ width: '500px' }}
      modal
    >
      <Card title="Color Customization" className="custom-theme-card">
        <div className="color-inputs">
          {Object.entries(customColors).map(([key, value]) => (
            <div key={key} className="color-input-group">
              <label className="color-label">
                {key.charAt(0).toUpperCase() + key.slice(1)}
              </label>
              <div className="color-input-wrapper">
                <ColorPicker
                  value={value}
                  onChange={(e) => setCustomColors(prev => ({
                    ...prev,
                    [key]: `#${e.value}`
                  }))}
                />
                <InputText
                  value={value}
                  onChange={(e) => setCustomColors(prev => ({
                    ...prev,
                    [key]: e.target.value
                  }))}
                  className="color-text-input"
                />
              </div>
            </div>
          ))}
        </div>

        <Divider />

        <div className="custom-theme-actions">
          <Button
            label="Cancel"
            icon="pi pi-times"
            onClick={() => setShowCustomDialog(false)}
            className="p-button-text"
          />
          <Button
            label="Apply Theme"
            icon="pi pi-check"
            onClick={applyCustomTheme}
            className="p-button-primary"
          />
        </div>
      </Card>
    </Dialog>
  );

  const renderSelector = () => {
    switch (variant) {
      case 'buttons':
        return renderButtons();
      case 'grid':
        return renderGrid();
      default:
        return renderDropdown();
    }
  };

  return (
    <>
      {renderSelector()}
      {enableCustomThemes && renderCustomThemeDialog()}
    </>
  );
};

ThemeSelector.propTypes = {
  showLabel: PropTypes.bool,
  showDarkModeToggle: PropTypes.bool,
  showCustomThemeButton: PropTypes.bool,
  variant: PropTypes.oneOf(['dropdown', 'buttons', 'grid']),
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  className: PropTypes.string,
  style: PropTypes.object,
  onThemeChange: PropTypes.func,
  debug: PropTypes.bool
};

export default ThemeSelector;
