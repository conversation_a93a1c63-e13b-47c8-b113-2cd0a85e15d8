import React from 'react';
import { Rating } from 'primereact/rating';
import { Button } from 'primereact/button';

const FinancialDocumentCard = ({ 
  documentName, 
  rating, 
  internalNotes,
  onEdit,
  onDelete 
}) => {
  return (
    <div className="glass-card-doc-management p-4" style={{ 
      marginBottom: '1.5rem',
      position: 'relative',
      zIndex: 1
    }}>
      {/* Document Name Header */}
      <div style={{ 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        marginBottom: '1rem',
        paddingBottom: '0.75rem',
        borderBottom: '1px solid #E5E7EB',
        position: 'relative',
        zIndex: 2
      }}>
        <div>
          <span style={{ 
            fontSize: '0.875rem', 
            fontWeight: '600', 
            color: '#111827',
            marginRight: '0.5rem'
          }}>
            Document Name:
          </span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <span style={{ 
            fontSize: '0.875rem', 
            color: '#6B7280',
            marginRight: '1rem'
          }}>
            {documentName}
          </span>
          <Button
            icon="pi pi-file"
            className="p-button-sm p-button-text p-button-secondary"
            tooltip="View File"
            style={{ width: '2rem', height: '2rem' }}
          />
          <Button
            icon="pi pi-eye"
            className="p-button-sm p-button-text p-button-secondary"
            tooltip="Preview"
            style={{ width: '2rem', height: '2rem' }}
          />
          <Button
            icon="pi pi-download"
            className="p-button-sm p-button-text p-button-secondary"
            tooltip="Download"
            style={{ width: '2rem', height: '2rem' }}
          />
          <Button
            icon="pi pi-trash"
            className="p-button-sm p-button-text p-button-danger"
            tooltip="Delete"
            onClick={onDelete}
            style={{ width: '2rem', height: '2rem' }}
          />
        </div>
      </div>

      {/* Rating Section */}
      <div style={{ marginBottom: '1rem' }}>
        <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
          <span style={{ 
            fontSize: '0.875rem', 
            fontWeight: '500', 
            color: '#6B7280',
            marginRight: '0.5rem'
          }}>
            Rating:
          </span>
          <Rating
            value={rating}
            readOnly
            stars={5}
            cancel={false}
            style={{ fontSize: '1rem' }}
          />
        </div>
      </div>

      {/* Internal Notes Section */}
      <div>
        <div style={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          marginBottom: '0.5rem'
        }}>
          <span style={{ 
            fontSize: '0.875rem', 
            fontWeight: '500', 
            color: '#6B7280'
          }}>
            Internal Notes:
          </span>
          <div style={{ display: 'flex', gap: '0.25rem' }}>
            <Button
              icon="pi pi-pencil"
              className="p-button-sm p-button-text p-button-secondary"
              tooltip="Edit"
              onClick={onEdit}
              style={{ width: '2rem', height: '2rem' }}
            />
            <Button
              icon="pi pi-trash"
              className="p-button-sm p-button-text p-button-danger"
              tooltip="Delete"
              onClick={onDelete}
              style={{ width: '2rem', height: '2rem' }}
            />
          </div>
        </div>
        <div style={{ 
          fontSize: '0.875rem', 
          color: '#6B7280',
          lineHeight: '1.5',
          padding: '0.75rem',
          backgroundColor: '#F9FAFB',
          borderRadius: '0.375rem',
          border: '1px solid #E5E7EB'
        }}>
          {internalNotes}
        </div>
      </div>
    </div>
  );
};

export default FinancialDocumentCard;
