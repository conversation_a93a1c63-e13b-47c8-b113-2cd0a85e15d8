// api.js
import axios from "axios";

// Add request interceptor to update token in headers
privateApiInstance.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem("token");
    if (token) {
      config.headers.Authorization = `Bear<PERSON> ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// API instance with headers
export const privateApiInstance = axios.create({
  baseURL: import.meta.env.PRIVATE_VITE_API_URL,
  headers: {
    Authorization: `Bearer ${localStorage.getItem("token")}`,
  },
});

// API instance without headers
export const publicApiInstance = axios.create({
  baseURL: "https://your-api.com",
});
