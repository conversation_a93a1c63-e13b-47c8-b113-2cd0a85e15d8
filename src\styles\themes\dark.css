/* Dark Theme */
[data-theme="dark"] {
  /* Primary Theme Colors */
  --theme-primary: var(--color-primary-400);
  --theme-primary-hover: var(--color-primary-300);
  --theme-primary-active: var(--color-primary-200);
  --theme-primary-light: var(--color-primary-900);
  --theme-primary-dark: var(--color-primary-100);

  /* Secondary Theme Colors */
  --theme-secondary: var(--color-secondary-400);
  --theme-secondary-hover: var(--color-secondary-300);
  --theme-secondary-active: var(--color-secondary-200);
  --theme-secondary-light: var(--color-secondary-800);
  --theme-secondary-dark: var(--color-secondary-200);

  /* Background Colors */
  --theme-bg-primary: var(--color-gray-900);
  --theme-bg-secondary: var(--color-gray-800);
  --theme-bg-tertiary: var(--color-gray-700);
  --theme-bg-accent: var(--color-primary-900);
  --theme-bg-overlay: rgba(0, 0, 0, 0.7);
  --theme-bg-modal: var(--color-gray-800);
  --theme-bg-card: var(--color-gray-800);
  --theme-bg-input: var(--color-gray-700);
  --theme-bg-disabled: var(--color-gray-700);

  /* Text Colors */
  --theme-text-primary: var(--color-gray-100);
  --theme-text-secondary: var(--color-gray-300);
  --theme-text-tertiary: var(--color-gray-400);
  --theme-text-accent: var(--color-primary-400);
  --theme-text-inverse: var(--color-gray-900);
  --theme-text-disabled: var(--color-gray-500);
  --theme-text-placeholder: var(--color-gray-500);
  --theme-text-link: var(--color-primary-400);
  --theme-text-link-hover: var(--color-primary-300);

  /* Border Colors */
  --theme-border-primary: var(--color-gray-700);
  --theme-border-secondary: var(--color-gray-600);
  --theme-border-accent: var(--color-primary-700);
  --theme-border-focus: var(--color-primary-400);
  --theme-border-error: var(--color-error-400);
  --theme-border-success: var(--color-success-400);
  --theme-border-warning: var(--color-warning-400);
  --theme-border-info: var(--color-info-400);

  /* Status Colors */
  --theme-success: var(--color-success-400);
  --theme-success-bg: rgba(34, 197, 94, 0.1);
  --theme-success-border: var(--color-success-700);
  --theme-success-text: var(--color-success-300);

  --theme-warning: var(--color-warning-400);
  --theme-warning-bg: rgba(245, 158, 11, 0.1);
  --theme-warning-border: var(--color-warning-700);
  --theme-warning-text: var(--color-warning-300);

  --theme-error: var(--color-error-400);
  --theme-error-bg: rgba(239, 68, 68, 0.1);
  --theme-error-border: var(--color-error-700);
  --theme-error-text: var(--color-error-300);

  --theme-info: var(--color-info-400);
  --theme-info-bg: rgba(6, 182, 212, 0.1);
  --theme-info-border: var(--color-info-700);
  --theme-info-text: var(--color-info-300);

  /* Component Specific Colors */
  --theme-navbar-bg: var(--color-gray-800);
  --theme-navbar-text: var(--color-gray-200);
  --theme-navbar-border: var(--color-gray-700);

  --theme-sidebar-bg: var(--color-gray-900);
  --theme-sidebar-text: var(--color-gray-300);
  --theme-sidebar-border: var(--color-gray-700);
  --theme-sidebar-hover: var(--color-gray-800);
  --theme-sidebar-active: rgba(59, 130, 246, 0.1);

  --theme-footer-bg: var(--color-gray-800);
  --theme-footer-text: var(--color-gray-400);
  --theme-footer-border: var(--color-gray-700);

  --theme-button-primary-bg: var(--color-primary-600);
  --theme-button-primary-text: var(--color-white);
  --theme-button-primary-hover: var(--color-primary-500);
  --theme-button-primary-active: var(--color-primary-400);
  --theme-button-primary-disabled: var(--color-gray-600);

  --theme-button-secondary-bg: var(--color-gray-700);
  --theme-button-secondary-text: var(--color-gray-200);
  --theme-button-secondary-border: var(--color-gray-600);
  --theme-button-secondary-hover: var(--color-gray-600);
  --theme-button-secondary-active: var(--color-gray-500);

  --theme-input-bg: var(--color-gray-700);
  --theme-input-text: var(--color-gray-100);
  --theme-input-border: var(--color-gray-600);
  --theme-input-focus: var(--color-primary-400);
  --theme-input-placeholder: var(--color-gray-400);
  --theme-input-disabled: var(--color-gray-800);

  --theme-table-bg: var(--color-gray-800);
  --theme-table-header-bg: var(--color-gray-700);
  --theme-table-border: var(--color-gray-600);
  --theme-table-hover: var(--color-gray-700);
  --theme-table-stripe: rgba(255, 255, 255, 0.02);

  --theme-card-bg: var(--color-gray-800);
  --theme-card-border: var(--color-gray-700);
  --theme-card-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --theme-card-hover-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);

  --theme-modal-bg: var(--color-gray-800);
  --theme-modal-overlay: rgba(0, 0, 0, 0.7);
  --theme-modal-border: var(--color-gray-700);

  --theme-dropdown-bg: var(--color-gray-800);
  --theme-dropdown-border: var(--color-gray-700);
  --theme-dropdown-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --theme-dropdown-hover: var(--color-gray-700);

  --theme-tooltip-bg: var(--color-gray-700);
  --theme-tooltip-text: var(--color-gray-100);
  --theme-tooltip-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);

  --theme-progress-bg: var(--color-gray-700);
  --theme-progress-fill: var(--color-primary-500);

  --theme-switch-bg: var(--color-gray-600);
  --theme-switch-thumb: var(--color-white);
  --theme-switch-active: var(--color-primary-500);

  --theme-checkbox-bg: var(--color-gray-700);
  --theme-checkbox-border: var(--color-gray-600);
  --theme-checkbox-checked: var(--color-primary-500);
  --theme-checkbox-checkmark: var(--color-white);

  --theme-radio-bg: var(--color-gray-700);
  --theme-radio-border: var(--color-gray-600);
  --theme-radio-checked: var(--color-primary-500);

  --theme-slider-track: var(--color-gray-700);
  --theme-slider-thumb: var(--color-primary-500);
  --theme-slider-fill: var(--color-primary-500);

  /* Scrollbar Colors */
  --theme-scrollbar-track: var(--color-gray-800);
  --theme-scrollbar-thumb: var(--color-gray-600);
  --theme-scrollbar-thumb-hover: var(--color-gray-500);

  /* Focus Ring */
  --theme-focus-ring: 0 0 0 3px rgba(96, 165, 250, 0.2);
  --theme-focus-ring-offset: 2px;

  /* Elevation Shadows */
  --theme-elevation-1: 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --theme-elevation-2: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
  --theme-elevation-3: 0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2);
  --theme-elevation-4: 0 10px 15px -3px rgba(0, 0, 0, 0.3), 0 4px 6px -2px rgba(0, 0, 0, 0.2);
  --theme-elevation-5: 0 20px 25px -5px rgba(0, 0, 0, 0.3), 0 10px 10px -5px rgba(0, 0, 0, 0.2);

  /* Code Syntax Highlighting */
  --theme-code-bg: var(--color-gray-800);
  --theme-code-text: var(--color-gray-200);
  --theme-code-keyword: var(--color-primary-400);
  --theme-code-string: var(--color-success-400);
  --theme-code-comment: var(--color-gray-500);
  --theme-code-number: var(--color-warning-400);
  --theme-code-function: var(--color-info-400);

  /* Selection Colors */
  --theme-selection-bg: rgba(96, 165, 250, 0.3);
  --theme-selection-text: var(--color-gray-100);

  /* Print Styles */
  --theme-print-bg: var(--color-white);
  --theme-print-text: var(--color-black);
  --theme-print-border: var(--color-gray-400);
}

/* Dark theme specific adjustments */
[data-theme="dark"] {
  color-scheme: dark;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  [data-theme="dark"] {
    --theme-border-primary: var(--color-gray-500);
    --theme-border-secondary: var(--color-gray-400);
    --theme-text-secondary: var(--color-gray-200);
    --theme-text-tertiary: var(--color-gray-300);
    --theme-bg-secondary: var(--color-gray-900);
    --theme-bg-tertiary: var(--color-gray-800);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  [data-theme="dark"] {
    --transition-all: none;
    --transition-colors: none;
    --transition-opacity: none;
    --transition-shadow: none;
    --transition-transform: none;
  }
}

/* Dark theme scrollbar styling */
[data-theme="dark"] ::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

[data-theme="dark"] ::-webkit-scrollbar-track {
  background: var(--theme-scrollbar-track);
}

[data-theme="dark"] ::-webkit-scrollbar-thumb {
  background: var(--theme-scrollbar-thumb);
  border-radius: 4px;
}

[data-theme="dark"] ::-webkit-scrollbar-thumb:hover {
  background: var(--theme-scrollbar-thumb-hover);
}

/* Dark theme selection styling */
[data-theme="dark"] ::selection {
  background: var(--theme-selection-bg);
  color: var(--theme-selection-text);
}

[data-theme="dark"] ::-moz-selection {
  background: var(--theme-selection-bg);
  color: var(--theme-selection-text);
}
