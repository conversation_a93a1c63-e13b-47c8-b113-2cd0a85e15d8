import React, { useCallback, useState } from 'react';
import PropTypes from 'prop-types';
import { Button } from 'primereact/button';

const DynamicButton = ({
  label,
  icon,
  iconPos = 'left', 
  badge,
  badgeClassName = 'p-badge-secondary',
  loading = false,
  loadingIcon = 'pi pi-spin pi-spinner',
  disabled = false,
  visible = true,
  type = 'button', 
  severity = 'primary', 
  size = 'normal',
  variant = 'filled', 
  raised = false,
  rounded = false,
  text = false,
  outlined = false,
  link = false,
  plain = false,
  className = '',
  style = {},
  onClick,
  onFocus,
  onBlur,
  onMouseEnter,
  onMouseLeave,
  onKeyDown,
  tooltip,
  tooltipOptions,
  autoFocus = false,
  id,
  name,
  value,
  form,
  formAction,
  formEncType,
  formMethod,
  formNoValidate,
  formTarget,
  tabIndex,
  ariaLabel,
  ariaDescribedBy,
  onEvent,
  debug = false,
  ...props
}) => {
  const [isPressed, setIsPressed] = useState(false);
  const [isFocused, setIsFocused] = useState(false);
  const [isHovered, setIsHovered] = useState(false);

  const handleClick = useCallback((e) => {
    if (disabled || loading) {
      e.preventDefault();
      return;
    }

    if (debug) {
      console.log('DynamicButton click:', { name, value, type });
    }

    if (onClick) {
      onClick(e);
    }

    if (onEvent) {
      onEvent('click', { name, value, type, event: e });
    }
  }, [onClick, onEvent, name, value, type, disabled, loading, debug]);

  const handleFocus = useCallback((e) => {
    setIsFocused(true);
    
    if (onFocus) {
      onFocus(e);
    }

    if (onEvent) {
      onEvent('focus', { name, value, event: e });
    }
  }, [onFocus, onEvent, name, value]);

  const handleBlur = useCallback((e) => {
    setIsFocused(false);
    
    if (onBlur) {
      onBlur(e);
    }

    if (onEvent) {
      onEvent('blur', { name, value, event: e });
    }
  }, [onBlur, onEvent, name, value]);

  const handleMouseEnter = useCallback((e) => {
    setIsHovered(true);
    
    if (onMouseEnter) {
      onMouseEnter(e);
    }

    if (onEvent) {
      onEvent('mouseEnter', { name, value, event: e });
    }
  }, [onMouseEnter, onEvent, name, value]);

  const handleMouseLeave = useCallback((e) => {
    setIsHovered(false);
    
    if (onMouseLeave) {
      onMouseLeave(e);
    }

    if (onEvent) {
      onEvent('mouseLeave', { name, value, event: e });
    }
  }, [onMouseLeave, onEvent, name, value]);

  const handleMouseDown = useCallback(() => {
    setIsPressed(true);
  }, []);

  const handleMouseUp = useCallback(() => {
    setIsPressed(false);
  }, []);

  const handleKeyDown = useCallback((e) => {
    if (onKeyDown) {
      onKeyDown(e);
    }

    if (onEvent) {
      onEvent('keyDown', { 
        name, 
        value, 
        key: e.key, 
        keyCode: e.keyCode,
        event: e 
      });
    }
  }, [onKeyDown, onEvent, name, value]);

  const buttonClasses = [
    'dynamic-button',
    `dynamic-button--${size}`,
    `dynamic-button--${variant}`,
    `dynamic-button--${severity}`,
    isFocused ? 'dynamic-button--focused' : '',
    isHovered ? 'dynamic-button--hovered' : '',
    isPressed ? 'dynamic-button--pressed' : '',
    loading ? 'dynamic-button--loading' : '',
    disabled ? 'dynamic-button--disabled' : '',
    raised ? 'dynamic-button--raised' : '',
    rounded ? 'dynamic-button--rounded' : '',
    className
  ].filter(Boolean).join(' ');

  const getButtonSeverity = () => {
    if (variant === 'text' || text) return null;
    if (variant === 'outlined' || outlined) return severity;
    return severity;
  };

  const buttonProps = {
    id: id || name,
    name,
    value,
    type,
    label,
    icon,
    iconPos,
    badge,
    badgeClassName,
    loading,
    loadingIcon,
    disabled,
    visible,
    severity: getButtonSeverity(),
    size: size === 'normal' ? undefined : size,
    raised,
    rounded,
    text: variant === 'text' || text,
    outlined: variant === 'outlined' || outlined,
    link,
    plain,
    className: buttonClasses,
    style,
    onClick: handleClick,
    onFocus: handleFocus,
    onBlur: handleBlur,
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave,
    onMouseDown: handleMouseDown,
    onMouseUp: handleMouseUp,
    onKeyDown: handleKeyDown,
    tooltip,
    tooltipOptions,
    autoFocus,
    form,
    formAction,
    formEncType,
    formMethod,
    formNoValidate,
    formTarget,
    tabIndex,
    'aria-label': ariaLabel || label,
    'aria-describedby': ariaDescribedBy,
    'data-component-type': 'button',
    'data-button-name': name,
    'data-button-type': type,
    'data-button-severity': severity,
    'data-button-variant': variant,
    ...props
  };

  Object.keys(buttonProps).forEach(key => {
    if (buttonProps[key] === undefined) {
      delete buttonProps[key];
    }
  });

  if (!visible) {
    return null;
  }

  return (
    <div className="dynamic-button-wrapper">
      {debug && (
        <div className="dynamic-button__debug">
          <small>
            Type: {type} | 
            Severity: {severity} | 
            Variant: {variant} | 
            State: {loading ? 'Loading' : disabled ? 'Disabled' : 'Normal'}
          </small>
        </div>
      )}
      <Button {...buttonProps} />
    </div>
  );
};

DynamicButton.propTypes = {
  label: PropTypes.string,
  icon: PropTypes.string,
  iconPos: PropTypes.oneOf(['left', 'right', 'top', 'bottom']),
  badge: PropTypes.string,
  badgeClassName: PropTypes.string,
  loading: PropTypes.bool,
  loadingIcon: PropTypes.string,
  disabled: PropTypes.bool,
  visible: PropTypes.bool,
  type: PropTypes.oneOf(['button', 'submit', 'reset']),
  severity: PropTypes.oneOf(['primary', 'secondary', 'success', 'info', 'warning', 'help', 'danger']),
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  variant: PropTypes.oneOf(['filled', 'outlined', 'text']),
  raised: PropTypes.bool,
  rounded: PropTypes.bool,
  text: PropTypes.bool,
  outlined: PropTypes.bool,
  link: PropTypes.bool,
  plain: PropTypes.bool,
  className: PropTypes.string,
  style: PropTypes.object,
  onClick: PropTypes.func,
  onFocus: PropTypes.func,
  onBlur: PropTypes.func,
  onMouseEnter: PropTypes.func,
  onMouseLeave: PropTypes.func,
  onKeyDown: PropTypes.func,
  tooltip: PropTypes.string,
  tooltipOptions: PropTypes.object,
  autoFocus: PropTypes.bool,
  id: PropTypes.string,
  name: PropTypes.string,
  value: PropTypes.any,
  form: PropTypes.string,
  formAction: PropTypes.string,
  formEncType: PropTypes.string,
  formMethod: PropTypes.string,
  formNoValidate: PropTypes.bool,
  formTarget: PropTypes.string,
  tabIndex: PropTypes.number,
  ariaLabel: PropTypes.string,
  ariaDescribedBy: PropTypes.string,
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicButton;
