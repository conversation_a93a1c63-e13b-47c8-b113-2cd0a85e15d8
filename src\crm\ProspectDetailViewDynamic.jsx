import React, { useState, useRef, useEffect, useCallback } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Toast } from 'primereact/toast';
import { Steps } from 'primereact/steps';
import { TabView, TabPanel } from 'primereact/tabview';
import { Card } from 'primereact/card';
import { Button } from 'primereact/button';
import DynamicComponent from '../components/framework/core/DynamicComponent';
import componentRegistry from '../components/framework/core/ComponentRegistry';
import AddressTab from './tabs/AddressTab';
import ContactsTab from './tabs/ContactsTab';
import ComplianceTab from '../crm/tabs/ComplianceTabs';
import KYCTab from './tabs/KYCTab';
import FinancialsTab from './tabs/FinancialsTab';
// import CommunicationTab from './tabs/CommunicationTab';
import NotesMessagesTab from '../lead-management/tabs/NotesMessagesTab';
import TransactionLogsTab from './tabs/TransactionLogsTab';
import DocumentationTab from '../crm/tabs/DocumentationTab';
import './CRMViews.css';
import '../styles/glassy/glassy-ui.css';
import '../styles/glassy/global.css';
import GlassyTabs from '../components/common/GlassyTabs';
import GlassyBlueButton from '../components/common/GlassyBlueButton';

const ProspectDetailViewDynamic = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const toast = useRef(null);
  const [activeStep, setActiveStep] = useState(0);
  const [activeTabIndex, setActiveTabIndex] = useState(0);
  const [activeActionTab, setActiveActionTab] = useState(0);

  useEffect(() => {
    if (!componentRegistry.initialized) {
      componentRegistry.init();
    }
  }, []);

  const [formData, setFormData] = useState({
    account: 'Emirates Airlines',
    language: 'English',
    tags: 'VIP, Priority',
    name: 'Sarah Smith',
    email: '<EMAIL>',
    vat: '********',
    phone: '+971 4 214 4444',
    website: 'www.emirates.com',
    vatNo: 'AE********9',
    mobileNumber: '+971 50 123 4567',
    tradeLicense: 'TL-123456',
    customerGroup: 'Enterprise',
    openingTime: '09:00',
    closingTime: '18:00',
    businessType: 'Aviation'
  });

  // Custom stepper item renderer
  const itemRenderer = (item, itemIndex) => {
    const isActiveItem = activeStep === itemIndex;

    return (
      <div
        className="flex flex-column align-items-center cursor-pointer stepper-item"
        onClick={() => setActiveStep(itemIndex)}
      >
        {/* Circle with icon */}
        <span
          className={`stepper-circle ${
            isActiveItem ? 'stepper-circle--active' : 'stepper-circle--inactive'
          }`}
        >
          <i className={`${item.icon} text-xl`} />
        </span>

        {/* Label below circle */}
        <span
          className={`mt-2 text-sm ${
            isActiveItem ? 'stepper-label--active' : 'stepper-label--inactive'
          }`}
        >
          {item.label}
        </span>
      </div>
    );
  };

  const stepsModel = [
    { icon: 'pi pi-user', label: 'New Lead', template: (item) => itemRenderer(item, 0) },
    { icon: 'pi pi-briefcase', label: 'Opportunity', template: (item) => itemRenderer(item, 1) },
    { icon: 'pi pi-check-circle', label: 'Won/Lost', template: (item) => itemRenderer(item, 2) }
  ];


  // Define detail tabs
  const detailTabs = [
    { label: 'Address', value: 'address', icon: 'pi pi-map-marker' },
    { label: 'Contacts', value: 'contacts', icon: 'pi pi-users' },
    { label: 'Compliance', value: 'compliance', icon: 'pi pi-shield' },
    { label: 'KYC', value: 'kyc', icon: 'pi pi-id-card' },
    { label: 'Financials', value: 'financials', icon: 'pi pi-dollar' },
    // { label: 'Communication', value: 'communication', icon: 'pi pi-comments' },
    { label: 'Notes & Messages', value: 'notes', icon: 'pi pi-comment' },
    { label: 'Transaction Logs', value: 'logs', icon: 'pi pi-history' },
    { label: 'Documentation', value: 'documentation', icon: 'pi pi-file' }
  ];

  const handleActionClick = (actionValue) => {
    switch(actionValue) {
      case 'save':
        handleSave();
        break;
      case 'enrich':
        showToast('info', 'Enrich Data', 'Enriching prospect data...');
        break;
      case 'opportunity':
        showToast('success', 'Opportunity', 'Creating opportunity...');
        break;
      case 'won':
        showToast('success', 'Won', 'Marked as won');
        break;
      case 'lost':
        showToast('warn', 'Lost', 'Marked as lost');
        break;
      case 'customer':
        handleCreateCustomer();
        break;
      case 'vendor':
        handleCreateVendor();
        break;
      case 'activities':
        showToast('info', 'Activities', 'View activities');
        break;
      case 'delete':
        showToast('warn', 'Delete Prospect', 'Are you sure you want to delete this prospect?');
        break;
      default:
        break;
    }
  };

  const generalFields = [
    {
      name: 'account',
      type: 'input-text',
      label: 'Account',
      required: true,
      props: { placeholder: 'Enter account name' }
    },
    {
      name: 'language',
      type: 'dropdown',
      label: 'Language',
      props: {
        options: [
          { label: 'English', value: 'English' },
          { label: 'Arabic', value: 'Arabic' },
          { label: 'French', value: 'French' }
        ],
        placeholder: 'Select language'
      }
    },
    {
      name: 'tags',
      type: 'input-text',
      label: 'Tags',
      props: { placeholder: 'Enter tags' }
    },
    {
      name: 'name',
      type: 'input-text',
      label: 'Name',
      required: true,
      props: { placeholder: 'Enter name' }
    },
    {
      name: 'email',
      type: 'input-text',
      label: 'Email',
      required: true,
      props: { placeholder: 'Enter email', keyfilter: 'email' }
    },
    {
      name: 'vat',
      type: 'input-text',
      label: 'VAT',
      props: { placeholder: 'Enter VAT' }
    },
    {
      name: 'phone',
      type: 'input-text',
      label: 'Phone',
      props: { placeholder: 'Enter phone' }
    },
    {
      name: 'website',
      type: 'input-text',
      label: 'Website',
      props: { placeholder: 'Enter website' }
    },
    {
      name: 'vatNo',
      type: 'input-text',
      label: 'VAT No.',
      props: { placeholder: 'Enter VAT number' }
    },
    {
      name: 'mobileNumber',
      type: 'input-text',
      label: 'Mobile Number',
      props: { placeholder: 'Enter mobile number' }
    },
    {
      name: 'tradeLicense',
      type: 'input-text',
      label: 'Trade License',
      props: { placeholder: 'Enter trade license' }
    },
    {
      name: 'openingTime',
      type: 'input-text',
      label: 'Opening Time',
      props: { placeholder: 'Enter opening time' }
    },
    {
      name: 'closingTime',
      type: 'input-text',
      label: 'Closing Time',
      props: { placeholder: 'Enter closing time' }
    },
    {
      name: 'businessType',
      type: 'dropdown',
      label: 'Business Type',
      props: {
        options: [
          { label: 'Aviation', value: 'Aviation' },
          { label: 'Logistics', value: 'Logistics' },
          { label: 'Manufacturing', value: 'Manufacturing' }
        ],
        placeholder: 'Select business type'
      }
    }
  ];

  const otherInfoFields = [
    {
      name: 'salesPerson',
      type: 'dropdown',
      label: 'Sales Person',
      props: {
        options: [
          { label: 'John Doe', value: 'John Doe' },
          { label: 'Jane Smith', value: 'Jane Smith' },
          { label: 'Bob Johnson', value: 'Bob Johnson' }
        ],
        placeholder: 'Select sales person'
      }
    },
    {
      name: 'lineOfBusiness',
      type: 'dropdown',
      label: 'Line of Business',
      props: {
        options: [
          { label: 'Aviation Services', value: 'Aviation Services' },
          { label: 'Logistics', value: 'Logistics' },
          { label: 'Manufacturing', value: 'Manufacturing' }
        ],
        placeholder: 'Select line of business'
      }
    },
    {
      name: 'segment',
      type: 'dropdown',
      label: 'Segment',
      props: {
        options: [
          { label: 'Commercial Aviation', value: 'Commercial Aviation' },
          { label: 'Cargo', value: 'Cargo' },
          { label: 'Defense', value: 'Defense' }
        ],
        placeholder: 'Select segment'
      }
    },
   
     {
      name: 'subSegment',
      type: 'dropdown',
      label: 'Sub Segment',
      props: {
        options: [
          { label: 'Fuel Supply', value: 'Fuel Supply' },
          { label: 'Services', value: 'Services' },
          { label: 'Manufacturing', value: 'Manufacturing' }
        ],
        placeholder: 'Select business type'
      }
    }
  ];

  const membershipFields = [
    {
      name: 'iata',
      type: 'input-text',
      label: 'IATA',
      props: { placeholder: 'Enter IATA' }
    },
    {
      name: 'icao',
      type: 'input-text',
      label: 'ICAO',
      props: { placeholder: 'Enter ICAO' }
    },
    {
      name: 'iataNumber',
      type: 'input-text',
      label: 'IATA Number',
      props: { placeholder: 'Enter IATA Number' }
    },
    {
      name: 'blacklistProspect',
      type: 'dropdown',
      label: 'Blacklist Prospect',
      props: {
        options: [
          { label: 'Yes', value: 'Yes' },
          { label: 'No', value: 'No' }
        ],
        placeholder: 'Select blacklist prospect'
      }
    },
   
  ];



  const showToast = useCallback((severity, summary, detail) => {
    toast.current?.show({ severity, summary, detail, life: 3000 });
  }, []);

  const handleFormChange = useCallback((data) => {
    setFormData(prev => ({ ...prev, ...data }));
  }, []);

  const handleFormSubmit = useCallback((data) => {
    console.log('Form submitted:', data);
    showToast('success', 'Saved', 'Prospect information saved successfully');
  }, [showToast]);

  const handleFormError = useCallback((error) => {
    console.error('Form error:', error);
    showToast('error', 'Form Error', error.message || 'An error occurred');
  }, [showToast]);

  const handleSave = useCallback(() => {
    showToast('success', 'Saved', 'Prospect information saved successfully');
  }, [showToast]);

  const handleCreateCustomer = useCallback(() => {
    showToast('success', 'Converting', 'Converting prospect to customer...');
    setTimeout(() => navigate('/customers'), 1500);
  }, [navigate, showToast]);

  const handleCreateVendor = useCallback(() => {
    showToast('success', 'Converting', 'Converting prospect to vendor...');
    setTimeout(() => navigate('/vendors'), 1500);
  }, [navigate, showToast]);

  return (
    <div className="">
      <Toast ref={toast} />

      {/* Header */}
      <div className="lead-header glass-header">
        <div className="lead-breadcrumb">
          <Button
            icon="pi pi-arrow-left"
            text
            className="p-button-sm"
            onClick={() => navigate('/prospects')}
          />
          <span className="breadcrumb-text">Prospects / {formData.account} - {id}</span>
        </div>
      </div>

      {/* Main content wrapper */}
      <div className="p-4">
        <div className="">
          <Steps
            model={stepsModel}
            activeIndex={activeStep}
            onSelect={(e) => setActiveStep(e.index)}
            readOnly={false}
          />
        </div>

        <div className="glass-card-global mt-6 p-5" style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', gap: '0.5rem', flexWrap: 'wrap' }}>
          <div style={{ display: 'flex', gap: '0.5rem', flex: '1 1 auto', flexWrap: 'wrap' }}>
            <GlassyBlueButton
              label="Save"
              onClick={() => handleActionClick('save')}
            />
            <GlassyBlueButton
              label="Enrich Data"
              onClick={() => handleActionClick('enrich')}
            />
            <GlassyBlueButton
              label="Create Opportunity"
              onClick={() => handleActionClick('opportunity')}
            />
            <GlassyBlueButton
              label="Mark as Won"
              onClick={() => handleActionClick('won')}
            />
            <GlassyBlueButton
              label="Mark as Lost"
              onClick={() => handleActionClick('lost')}
            />
            <GlassyBlueButton
              label="Create Customer"
              onClick={() => handleActionClick('customer')}
            />
            <GlassyBlueButton
              label="Create Vendor"
              onClick={() => handleActionClick('vendor')}
            />
          </div>
          <div style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
            <span style={{ color: '#6B7280', fontSize: '14px', cursor: 'pointer' }} onClick={() => handleActionClick('activities')}>
              <i className="pi pi-clock" style={{ marginRight: '4px' }}></i>
              Activities 3/5
            </span>
            <Button
              icon="pi pi-trash"
              className="p-button-text p-button-danger"
              onClick={() => handleActionClick('delete')}
              style={{ color: '#EF4444' }}
            />
          </div>
        </div>

        <div title="General" className="glass-card-doc-management p-4" style={{ marginTop: '1.5rem', borderRadius: '12px', position: 'relative', zIndex: 100, color: 'black'}}>
        <h2 className='p-card-title color-gray-500'>General</h2>
        
        <DynamicComponent
          type="form"
          config={{
            config: {
              fields: generalFields,
              layout: {
                columns: 3,
                showProgress: false,
                showResetButton: false,
                // submitButtonText: 'Save',
                showSubmitButton: false
              }
            },
            initialData: formData,
            onChange: handleFormChange,
            onSubmit: handleFormSubmit,
            onError: handleFormError
          }}
          debug={false}
        />
        </div>

        <div title="" className="glass-card-doc-management p-4" style={{ marginTop: '1.5rem', borderRadius: '12px', position: 'relative', zIndex: 1, color: '#2563EB' }}>
        <h4 className='p-card-title color-gray-500'>Sales Demographics</h4>
        <DynamicComponent
          type="form"
          config={{
            config: {
              fields: otherInfoFields,
              layout: {
                columns: 4,
                showProgress: false,
                showResetButton: false,
                // submitButtonText: 'Save',
                showSubmitButton: false
              }
            },
            initialData: formData,
            onChange: handleFormChange,
            onSubmit: handleFormSubmit,
            onError: handleFormError
          }}
          debug={false}
        />
        </div>

        <div title="" className="glass-card-doc-management p-4" style={{ marginTop: '1.5rem', borderRadius: '12px', position: 'relative', zIndex: 1, color: '#2563EB' }}>
        <h4 className='p-card-title color-gray-500'>Membership</h4>
        <DynamicComponent
          type="form"
          config={{
            config: {
              fields: membershipFields,
              layout: {
                columns: 4,
                showProgress: false,
                showResetButton: false,
                // submitButtonText: 'Save',
                showSubmitButton: false
              }
            },
            initialData: formData,
            onChange: handleFormChange,
            onSubmit: handleFormSubmit,
            onError: handleFormError
          }}
          debug={false}
        />
        </div>

        <div className="glass-card-doc-management p-4" style={{ marginTop: '1.5rem', position: 'relative', zIndex: 1 }}>
          <GlassyTabs
            tabs={detailTabs}
            activeIndex={activeTabIndex}
            onTabChange={(index, tab) => setActiveTabIndex(index)}
            showBadges={false}
            hidePanels={true}
            className="w-full"
          />

          {/* Tabs Content */}
          <div className="tab-content p-4 rounded-xl ">
            {activeTabIndex === 0 && (
              <div className="tab-panel rounded-xl">
                <AddressTab prospectId={id} />
              </div>
            )}
            {activeTabIndex === 1 && (
              <div className="tab-panel rounded-xl">
                <ContactsTab prospectId={id} />
              </div>
            )}
            {activeTabIndex === 2 && (
              <div className="tab-panel rounded-xl">
                <ComplianceTab prospectId={id} />
              </div>
            )}
            {activeTabIndex === 3 && (
              <div className="tab-panel rounded-xl">
                <KYCTab prospectId={id} />
              </div>
            )}
            {activeTabIndex === 4 && (
              <div className="tab-panel rounded-xl">
                <FinancialsTab prospectId={id} />
              </div>
            )}
            {/* {activeTabIndex === 5 && (
              <div className="tab-panel rounded-xl">
                <CommunicationTab prospectId={id} />
              </div>
            )} */}
            {activeTabIndex === 5 && (
              <div className="tab-panel rounded-xl">
                <NotesMessagesTab prospectId={id} />
              </div>
            )}
            {activeTabIndex === 6 && (
              <div className="tab-panel rounded-xl">
                <TransactionLogsTab prospectId={id} />
              </div>
            )}
            {activeTabIndex === 7 && (
              <div className="tab-panel rounded-xl">
                <DocumentationTab prospectId={id} />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProspectDetailViewDynamic;

