import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { Card } from 'primereact/card';
import DynamicComponent from '../../core/DynamicComponent';

const DynamicCard = ({
  title,
  subTitle,
  header,
  footer,
  headerTemplate,
  footerTemplate,
  className = '',
  style = {},
  headerClassName = '',
  headerStyle = {},
  bodyClassName = '',
  bodyStyle = {},
  footerClassName = '',
  footerStyle = '',
  id,
  children,
  content = [],
  config = {},
  size = 'normal',
  variant = 'default',
  bordered = true,
  shadow = true,
  hoverable = false,
  clickable = false,
  onClick,
  onMouseEnter,
  onMouseLeave,
  actions = [],
  onEvent,
  debug = false,
  ...props
}) => {
  const handleClick = useCallback((e) => {
    if (!clickable) return;

    if (onClick) {
      onClick(e);
    }

    if (onEvent) {
      onEvent('click', { event: e });
    }
  }, [clickable, onClick, onEvent]);

  const handleMouseEnter = useCallback((e) => {
    if (onMouseEnter) {
      onMouseEnter(e);
    }

    if (onEvent) {
      onEvent('mouseEnter', { event: e });
    }
  }, [onMouseEnter, onEvent]);

  const handleMouseLeave = useCallback((e) => {
    if (onMouseLeave) {
      onMouseLeave(e);
    }

    if (onEvent) {
      onEvent('mouseLeave', { event: e });
    }
  }, [onMouseLeave, onEvent]);

  const cardClasses = [
    'dynamic-card',
    `dynamic-card--${size}`,
    `dynamic-card--${variant}`,
    bordered ? 'dynamic-card--bordered' : '',
    shadow ? 'dynamic-card--shadow' : '',
    hoverable ? 'dynamic-card--hoverable' : '',
    clickable ? 'dynamic-card--clickable' : '',
    className
  ].filter(Boolean).join(' ');

  const renderHeaderTemplate = useCallback(() => {
    if (headerTemplate && typeof headerTemplate === 'function') {
      return headerTemplate();
    }

    if (header) {
      if (typeof header === 'string') {
        return <div className="card-header-text">{header}</div>;
      }
      return header;
    }

    if (config.header) {
      return (
        <div className="card-header-content">
          {config.header.map((item, index) => (
            <DynamicComponent
              key={index}
              type={item.type}
              config={item.config}
              data={item.data}
              onEvent={onEvent}
              debug={debug}
            />
          ))}
        </div>
      );
    }

    return null;
  }, [headerTemplate, header, config.header, onEvent, debug]);

  const renderFooterTemplate = useCallback(() => {
    if (footerTemplate && typeof footerTemplate === 'function') {
      return footerTemplate();
    }

    if (footer) {
      if (typeof footer === 'string') {
        return <div className="card-footer-text">{footer}</div>;
      }
      return footer;
    }

    if (actions && actions.length > 0) {
      return (
        <div className="card-actions">
          {actions.map((action, index) => (
            <DynamicComponent
              key={index}
              type="button"
              config={{
                label: action.label,
                icon: action.icon,
                severity: action.severity || 'secondary',
                size: 'small',
                onClick: action.onClick,
                disabled: action.disabled,
                className: `card-action ${action.className || ''}`
              }}
              onEvent={onEvent}
              debug={debug}
            />
          ))}
        </div>
      );
    }

    if (config.footer) {
      return (
        <div className="card-footer-content">
          {config.footer.map((item, index) => (
            <DynamicComponent
              key={index}
              type={item.type}
              config={item.config}
              data={item.data}
              onEvent={onEvent}
              debug={debug}
            />
          ))}
        </div>
      );
    }

    return null;
  }, [footerTemplate, footer, actions, config.footer, onEvent, debug]);

  const renderContent = useCallback(() => {
    if (children) {
      return children;
    }

    if (Array.isArray(content) && content.length > 0) {
      return (
        <div className="card-dynamic-content">
          {content.map((item, index) => (
            <DynamicComponent
              key={index}
              type={item.type}
              config={item.config}
              data={item.data}
              onEvent={onEvent}
              debug={debug}
            />
          ))}
        </div>
      );
    }

    if (content && typeof content === 'object' && content.type) {
      return (
        <DynamicComponent
          type={content.type}
          config={content.config}
          data={content.data}
          onEvent={onEvent}
          debug={debug}
        />
      );
    }

    return null;
  }, [children, content, onEvent, debug]);

  const cardProps = {
    id,
    title,
    subTitle,
    header: renderHeaderTemplate(),
    footer: renderFooterTemplate(),
    className: cardClasses,
    style: {
      ...style,
      cursor: clickable ? 'pointer' : style.cursor
    },
    headerClassName,
    headerStyle,
    bodyClassName,
    bodyStyle,
    footerClassName,
    footerStyle,
    onClick: handleClick,
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave,
    'data-component-type': 'card',
    'data-card-size': size,
    'data-card-variant': variant,
    'data-card-clickable': clickable,
    ...props
  };

  Object.keys(cardProps).forEach(key => {
    if (cardProps[key] === undefined) {
      delete cardProps[key];
    }
  });

  return (
    <div className="dynamic-card-wrapper">
      {debug && (
        <div className="dynamic-card__debug">
          <small>
            Size: {size} | 
            Variant: {variant} | 
            Clickable: {clickable ? 'Yes' : 'No'} | 
            Actions: {actions.length} |
            Content Items: {Array.isArray(content) ? content.length : (content ? 1 : 0)}
          </small>
        </div>
      )}
      <Card {...cardProps}>
        {renderContent()}
      </Card>
    </div>
  );
};

DynamicCard.propTypes = {
  title: PropTypes.string,
  subTitle: PropTypes.string,
  header: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  footer: PropTypes.oneOfType([PropTypes.string, PropTypes.node]),
  headerTemplate: PropTypes.func,
  footerTemplate: PropTypes.func,
  className: PropTypes.string,
  style: PropTypes.object,
  headerClassName: PropTypes.string,
  headerStyle: PropTypes.object,
  bodyClassName: PropTypes.string,
  bodyStyle: PropTypes.object,
  footerClassName: PropTypes.string,
  footerStyle: PropTypes.object,
  id: PropTypes.string,
  children: PropTypes.node,
  content: PropTypes.oneOfType([
    PropTypes.array,
    PropTypes.object
  ]),
  config: PropTypes.object,
  size: PropTypes.oneOf(['small', 'normal', 'large']),
  variant: PropTypes.oneOf(['default', 'primary', 'secondary', 'success', 'info', 'warning', 'danger']),
  bordered: PropTypes.bool,
  shadow: PropTypes.bool,
  hoverable: PropTypes.bool,
  clickable: PropTypes.bool,
  onClick: PropTypes.func,
  onMouseEnter: PropTypes.func,
  onMouseLeave: PropTypes.func,
  actions: PropTypes.arrayOf(PropTypes.shape({
    label: PropTypes.string,
    icon: PropTypes.string,
    severity: PropTypes.string,
    onClick: PropTypes.func,
    disabled: PropTypes.bool,
    className: PropTypes.string
  })),
  onEvent: PropTypes.func,
  debug: PropTypes.bool
};

export default DynamicCard;
