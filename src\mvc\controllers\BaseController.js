import { logger } from '../../core/logging/logger.js';
import apiService from '../services/ApiService.js';

class BaseController {
  constructor() {
    this.listeners = new Set();
    this.state = {};
    this.loading = false;
    this.error = null;
    this.initialized = false;
  }

  async initialize() {
    if (this.initialized) {
      return;
    }

    try {
      await this.onInitialize();
      this.initialized = true;
      logger.info(`${this.constructor.name} initialized`);
    } catch (error) {
      logger.error(`Failed to initialize ${this.constructor.name}`, { 
        error: error.message 
      });
      this.setError(error);
    }
  }

  async onInitialize() {
  }

  setLoading(loading) {
    if (this.loading !== loading) {
      this.loading = loading;
      this.notifyListeners('loading', loading);
    }
  }

  setError(error) {
    this.error = error;
    this.notifyListeners('error', error);
    
    if (error) {
      logger.error(`Error in ${this.constructor.name}`, { 
        error: error.message,
        stack: error.stack
      });
    }
  }

  clearError() {
    this.setError(null);
  }

  setState(updates) {
    const oldState = { ...this.state };
    this.state = { ...this.state, ...updates };
    
    Object.keys(updates).forEach(key => {
      if (oldState[key] !== this.state[key]) {
        this.notifyListeners(key, this.state[key], oldState[key]);
      }
    });
    
    this.notifyListeners('state', this.state, oldState);
  }

  getState() {
    return { ...this.state };
  }

  getStateProperty(key) {
    return this.state[key];
  }

  isLoading() {
    return this.loading;
  }

  hasError() {
    return !!this.error;
  }

  getError() {
    return this.error;
  }

  async executeAction(actionName, actionFn, ...args) {
    try {
      this.setLoading(true);
      this.clearError();
      
      logger.debug(`Executing action: ${actionName}`, { 
        controller: this.constructor.name,
        args: args.length
      });
      
      const result = await actionFn.apply(this, args);
      
      logger.debug(`Action completed: ${actionName}`, { 
        controller: this.constructor.name
      });
      
      return result;
    } catch (error) {
      logger.error(`Action failed: ${actionName}`, { 
        controller: this.constructor.name,
        error: error.message
      });
      
      this.setError(error);
      throw error;
    } finally {
      this.setLoading(false);
    }
  }

  async apiRequest(method, endpoint, data, options = {}) {
    return apiService.request(method, endpoint, data, {
      ...options,
      controller: this.constructor.name
    });
  }

  async apiGet(endpoint, params, options) {
    return this.apiRequest('GET', endpoint, params, options);
  }

  async apiPost(endpoint, data, options) {
    return this.apiRequest('POST', endpoint, data, options);
  }

  async apiPut(endpoint, data, options) {
    return this.apiRequest('PUT', endpoint, data, options);
  }

  async apiPatch(endpoint, data, options) {
    return this.apiRequest('PATCH', endpoint, data, options);
  }

  async apiDelete(endpoint, options) {
    return this.apiRequest('DELETE', endpoint, null, options);
  }

  addEventListener(callback) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  removeEventListener(callback) {
    this.listeners.delete(callback);
  }

  notifyListeners(type, newValue, oldValue) {
    this.listeners.forEach(callback => {
      try {
        callback({
          type,
          newValue,
          oldValue,
          controller: this.constructor.name,
          timestamp: new Date().toISOString()
        });
      } catch (error) {
        logger.error('Error in controller listener', { 
          error: error.message,
          controller: this.constructor.name,
          type
        });
      }
    });
  }

  validateInput(data, rules) {
    const errors = {};
    
    Object.keys(rules).forEach(field => {
      const rule = rules[field];
      const value = data[field];
      
      if (rule.required && (value === null || value === undefined || value === '')) {
        errors[field] = errors[field] || [];
        errors[field].push(`${field} is required`);
      }
      
      if (value !== null && value !== undefined && rule.type) {
        if (!this.validateType(value, rule.type)) {
          errors[field] = errors[field] || [];
          errors[field].push(`${field} must be of type ${rule.type}`);
        }
      }
      
      if (rule.validator && typeof rule.validator === 'function') {
        const customError = rule.validator(value, data);
        if (customError) {
          errors[field] = errors[field] || [];
          errors[field].push(customError);
        }
      }
    });
    
    return {
      isValid: Object.keys(errors).length === 0,
      errors
    };
  }

  validateType(value, expectedType) {
    switch (expectedType) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'email':
        return typeof value === 'string' && /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value);
      case 'url':
        try {
          new URL(value);
          return true;
        } catch {
          return false;
        }
      case 'date':
        return value instanceof Date || !isNaN(Date.parse(value));
      default:
        return true;
    }
  }

  debounce(func, wait) {
    let timeout;
    return (...args) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(this, args), wait);
    };
  }

  throttle(func, limit) {
    let inThrottle;
    return (...args) => {
      if (!inThrottle) {
        func.apply(this, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }

  formatError(error) {
    if (typeof error === 'string') {
      return error;
    }
    
    if (error && error.message) {
      return error.message;
    }
    
    return 'An unknown error occurred';
  }

  getMetadata() {
    return {
      name: this.constructor.name,
      initialized: this.initialized,
      loading: this.loading,
      hasError: this.hasError(),
      stateKeys: Object.keys(this.state),
      listenerCount: this.listeners.size
    };
  }

  destroy() {
    this.listeners.clear();
    this.state = {};
    this.loading = false;
    this.error = null;
    this.initialized = false;
    
    logger.info(`${this.constructor.name} destroyed`);
  }

  reset() {
    this.state = {};
    this.loading = false;
    this.error = null;
    this.notifyListeners('reset', true);
    
    logger.info(`${this.constructor.name} reset`);
  }

  getStats() {
    return {
      name: this.constructor.name,
      initialized: this.initialized,
      loading: this.loading,
      hasError: this.hasError(),
      stateSize: Object.keys(this.state).length,
      listenerCount: this.listeners.size,
      lastError: this.error ? this.error.message : null
    };
  }
}

export default BaseController;
